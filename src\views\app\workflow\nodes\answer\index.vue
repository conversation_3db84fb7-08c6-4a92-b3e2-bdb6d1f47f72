<template>
  <div :style="{ width: data.width, height: data.height }" class="custom-node">
    <div class="truncate flex items-center h-[46px]">
      <div class="custom-node-icon" :style="{ backgroundColor: '#f79009' }">
        <AiSvgIcon style="width: 18px; height: 18px" :name="`workflow-${type}`" />
      </div>
      <div class="custom-node-text overflow-ellipsis overflow-hidden whitespace-nowrap flex-1">
        {{ data.title }}
      </div>
      <Handle id="target" type="target" :position="Position.Left" />
      <NodeList class="custom-node-add" :popoverInstance="true" :nodeId="props.id" :nodeProps="props">
        <Handle id="source" type="source" class="custom-node-handle" :position="Position.Right">
          <icon-plus :style="{ pointerEvents: 'none' }" />
        </Handle>
      </NodeList>
    </div>
    <!-- 显示回复内容预览 -->
    <div class="mb-1 py-1">
      <div class="answer-preview">
        <div class="answer-title">回复</div>
        <div class="answer-content" v-html="formattedAnswer" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue'
import { Handle, Position } from '@vue-flow/core'
import type { NodeProps } from '@vue-flow/core'
import NodeList from '../node-list.vue'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import { VarType } from '../../types/workflow'
import {
  getBeforeNodesInSameBranchIncludeParent,
  toNodeAvailableVars,
  filterVar as filterVarByType
} from '../../utils/variable'

const props = defineProps<NodeProps>()

// 获取真实的变量数据
const nodesStore = useNodesStore()
const availableVars = ref<any[]>([])

// 变量分组映射，用于存储变量路径到分组标题的映射
const variableGroupMap = ref<Map<string, string>>(new Map())

// 获取可用的变量
function getAvailableVars() {
  try {
    const beforeNodes = getBeforeNodesInSameBranchIncludeParent(props.id)
    const parentNode = nodesStore.parentNode

    const outputVars = toNodeAvailableVars(parentNode, beforeNodes, false, filterVarByType(VarType.string), [], [])

    // 构建变量分组映射
    buildVariableGroupMap(outputVars)

    return outputVars
  } catch (error) {
    console.error('Error getting available vars:', error)
    return []
  }
}

// 构建变量分组映射
function buildVariableGroupMap(vars: any[]) {
  variableGroupMap.value.clear()

  vars.forEach((group) => {
    if (group.vars && Array.isArray(group.vars)) {
      group.vars.forEach((varItem: any) => {
        // 构建变量的完整路径
        let variablePath = ''
        if (group.nodeId === 'sys' || group.nodeId === 'env' || group.nodeId === 'conversation') {
          variablePath = varItem.variable
        } else {
          variablePath = `${group.nodeId}.${varItem.variable}`
        }

        // 存储路径到分组标题的映射
        variableGroupMap.value.set(variablePath, group.title || group.nodeId)
      })
    }
  })
}

// 组件挂载时获取变量数据
onMounted(() => {
  availableVars.value = getAvailableVars()
})

// 格式化回复内容用于显示
const formattedAnswer = computed(() => {
  // 优先使用新的 contextEditorContent，兼容旧的 answer 属性
  const content = props.data.contextEditorContent || props.data.answer || ''
  if (!content) return ''

  let processedContent = content

  // 限制显示长度
  if (processedContent.length > 100) {
    processedContent = processedContent.substring(0, 100) + '...'
  }

  processedContent = processedContent.replace(/\{\{#([^#]+)#\}\}/g, (_match: string, variable: string) => {
    // 首先尝试从变量分组映射中获取真实的分组名称
    let groupName = variableGroupMap.value.get(variable)
    let variableName = variable

    if (!groupName) {
      // 如果映射中没有找到，则使用默认解析逻辑
      const parts = variable.split('.')
      if (parts.length >= 2) {
        if (parts[0] === 'sys') {
          groupName = '开始'
          variableName = parts.slice(1).join('.')
        } else if (parts[0] === 'env') {
          groupName = '环境'
          variableName = parts.slice(1).join('.')
        } else if (parts[0] === 'conversation') {
          groupName = '对话'
          variableName = parts.slice(1).join('.')
        } else {
          // 普通节点变量，使用节点ID作为分组名
          groupName = parts[0]
          variableName = parts.slice(1).join('.')
        }
      } else {
        groupName = '未知分组'
        variableName = variable
      }
    } else {
      // 从映射中获取到了分组标题，解析变量名
      const parts = variable.split('.')
      if (parts.length >= 2) {
        if (parts[0] === 'sys' || parts[0] === 'env' || parts[0] === 'conversation') {
          variableName = parts.slice(1).join('.')
        } else {
          variableName = parts.slice(1).join('.')
        }
      }
    }

    // 截断过长的名称
    const shortGroupName = groupName.length > 8 ? groupName.substring(0, 8) + '...' : groupName
    const shortVariableName = variableName.length > 12 ? variableName.substring(0, 12) + '...' : variableName

    return `<span class="variable-tag">
      <span class="variable-group">${shortGroupName}</span>
      <span class="variable-separator">/</span>
      <span class="variable-icon">{x}</span>
      <span class="variable-name">${shortVariableName}</span>
    </span>`
  })

  // 兼容旧格式变量引用 {{变量名}}
  processedContent = processedContent.replace(/\{\{([^}]+)\}\}/g, (_match: string, varName: string) => {
    const shortVarName = varName.length > 20 ? varName.substring(0, 20) + '...' : varName
    return `<span class="variable-tag-old">${shortVarName}</span>`
  })

  // 替换换行符
  processedContent = processedContent.replace(/\n/g, '<br>')

  return processedContent
})
</script>
<style scoped lang="scss">
:deep(.vue-flow__handle-left) {
  top: auto;
}

.custom-node {
  min-width: 240px;
  padding: 4px 14px;
  background-color: var(--color-bg-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 12px;

  &-icon {
    margin-right: 8px;
    height: 24px;
    width: 24px;
    border-radius: 8px;
    background-color: var(--color-fill-3);
    text-align: center;
    line-height: 24px;
    color: var(--color-text-1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-text {
    font-size: 18px;
    font-weight: 500;
    color: var(--color-text-2);
  }

  &-handle {
    background: rgb(var(--primary-6));
    height: 10px;
    width: 2px;
    border-radius: 0;
    border: none;
    min-width: 2px;

    .arco-icon {
      display: none;
    }
  }

  &:hover {
    .custom-node-handle {
      background-color: rgb(var(--primary-6));
      border-radius: 50%;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
      cursor: pointer;
    }

    .arco-icon {
      display: inline-block;
      width: 14px;
      height: 14px;
      color: var(--color-white);
    }
  }

  &-add {
    position: absolute;
    right: 0;
    pointer-events: none;
  }
}

.answer-preview {
  .answer-title {
    font-size: 12px;
    font-weight: 600;
    color: var(--color-text-3);
    margin-bottom: 6px;
  }

  .answer-content {
    font-size: 12px;
    line-height: 1.4;
    color: var(--color-text-2);
    background: var(--color-fill-1);
    padding: 8px 10px;
    border-radius: 6px;
    border: 1px solid var(--color-border-2);

    :deep(.variable-tag) {
      display: inline-flex !important;
      align-items: center !important;
      gap: 3px !important;
      padding: 0 8px !important;
      background: var(--color-fill-2) !important;
      color: var(--color-primary-6, #165dff) !important;
      border: 1px solid var(--color-primary-light-3, #bedaff) !important;
      border-radius: 6px !important;
      font-size: 12px !important;
      font-weight: 500 !important;
      cursor: default !important;
      user-select: none !important;
      white-space: nowrap !important;
      vertical-align: middle !important;
      margin: 0 2px 2px 2px;
      .variable-icon {
        color: var(--color-primary-6, #165dff);
        margin-right: 2px;
        flex-shrink: 0;
      }

      .variable-group {
        max-width: 60px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
        flex-shrink: 0 !important;
        color: var(--color-text-2, #4e5969) !important;
      }

      .variable-separator {
        color: var(--color-text-3, #86909c) !important;
        margin: 0 !important;
        flex-shrink: 0 !important;
      }

      .variable-name {
        max-width: 60px !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        white-space: nowrap !important;
        flex-shrink: 0 !important;
        color: var(--color-primary-6, #165dff) !important;
        font-weight: 600 !important;
      }
    }

    // 兼容旧格式变量标签样式
    :deep(.variable-tag-old) {
      display: inline-block;
      background: var(--color-warning-light-1);
      color: var(--color-warning-6);
      padding: 1px 4px;
      border-radius: 3px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 11px;
      font-weight: 500;
      margin: 0 1px;
      border: 1px solid var(--color-warning-light-3);
    }
  }
}
</style>
