<template>
  <a-card
    class="general-card"
    title="快捷操作"
    :header-style="{ paddingBottom: '0' }"
    :body-style="{ padding: '24px 20px 16px 20px' }"
  >
    <a-row :gutter="8">
      <!-- <a-empty v-if="!has.hasPermOr(links.map((item) => item.permission))" /> -->
      <a-col
        v-for="link in links"
        :key="link.text"
        :span="8"
        class="wrapper"
        @click="router.replace({ path: link.path })"
      >
        <div class="icon">
          <AiSvgIcon :name="link.icon" />
        </div>
        <a-typography-paragraph class="text">
          {{ link.text }}
        </a-typography-paragraph>
      </a-col>
    </a-row>
  </a-card>
</template>

<script setup lang="ts">
import has from '@/utils/has'

const router = useRouter()

const links = [
  { text: '应用市场', icon: 'common', path: '/apps/store' },
  { text: '应用管理', icon: 'common', path: '/apps/manage' }
]
</script>

<style scoped lang="less"></style>
