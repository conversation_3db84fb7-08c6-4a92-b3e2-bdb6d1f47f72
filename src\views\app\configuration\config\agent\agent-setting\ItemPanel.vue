<template>
  <div class="item-panel flex h-12 items-center justify-between rounded-lg bg-background-section-burn px-3">
    <div class="flex items-center">
      <slot name="icon" />
      <div class="ml-3 mr-1 font-semibold leading-6 text-text-secondary">{{ $attrs.name }}</div>
      <a-tooltip :content="$attrs.description">
        <icon-question-circle v-if="$attrs.description" />
      </a-tooltip>
    </div>

    <div>
      <slot />
    </div>
  </div>
</template>
<script setup lang="ts">
const $attrs = useAttrs()
</script>
<style scoped lang="scss">
.bg-background-section-burn {
  //background: var(--color-background-section-burn);
  background: #f2f4f7;
}
</style>
