/**
 * EvalReport
 * 评测报告表
 */
export interface EvalReport {
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string
  /** 创建人ID */
  createdBy?: string
  /** 详细指标数据Json */
  detailedMetricsJson?: string
  /**
   * 详细指标数据Text
   * @minLength 0
   * @maxLength 65535
   */
  detailedMetricsText?: string
  echoMap?: object
  /** 关联的评测任务 */
  evalTask?: EvalTask
  /** 关联的智能助手 */
  evalTaskApp?: EvalTaskApp
  /**
   * 报告文件ID
   * @minLength 0
   * @maxLength 36
   */
  fileId?: string
  /**
   * 报告格式：PDF/HTML/WORD等
   * @minLength 0
   * @maxLength 20
   */
  fileType?: string
  /** 主键 */
  id?: string
  /** 总体评分 */
  overallScore?: number
  /**
   * 报告名称
   * @minLength 0
   * @maxLength 100
   */
  reportName?: string
  /**
   * 报告状态：1-正常，2-异常，0-已删除
   * @format int32
   */
  reportStatus?: number
  /**
   * 评测总结
   * @minLength 0
   * @maxLength 65535
   */
  summary?: string
  /**
   * 关联评测任务ID
   * @format int64
   */
  taskId?: number
  /**
   * 最后修改时间
   * @format date-time
   */
  updateTime?: string
  /** 最后修改人ID */
  updatedBy?: string
}

/**
 * EvalReportPageQuery
 * 评测报告表
 */
export interface EvalReportPageQuery {
  /** 详细指标数据Json */
  detailedMetricsJson?: string
  /** 详细指标数据Text */
  detailedMetricsText?: string
  /** 报告文件ID */
  fileId?: string
  /** 报告格式：PDF/HTML/WORD等 */
  fileType?: string
  /** 总体评分 */
  overallScore?: number
  /** 报告名称 */
  reportName?: string
  /**
   * 报告状态：1-正常，2-异常，0-已删除
   * @format int32
   */
  reportStatus?: number
  /** 评测总结 */
  summary?: string
  /**
   * 关联评测任务ID
   * @format int64
   */
  taskId?: number
}

/**
 * EvalReportSaveDTO
 * 评测报告表
 */
export interface EvalReportSaveDTO {
  /** 详细指标数据Json */
  detailedMetricsJson?: string
  /**
   * 详细指标数据Text
   * @minLength 0
   * @maxLength 65535
   */
  detailedMetricsText?: string
  /**
   * 报告文件ID
   * @minLength 0
   * @maxLength 36
   */
  fileId?: string
  /**
   * 报告格式：PDF/HTML/WORD等
   * @minLength 0
   * @maxLength 20
   */
  fileType?: string
  /** 总体评分 */
  overallScore?: number
  /**
   * 报告名称
   * @minLength 0
   * @maxLength 100
   */
  reportName?: string
  /**
   * 报告状态：1-正常，2-异常，0-已删除
   * @format int32
   */
  reportStatus?: number
  /**
   * 评测总结
   * @minLength 0
   * @maxLength 65535
   */
  summary?: string
  /**
   * 关联评测任务ID
   * @format int64
   */
  taskId?: number
}

/**
 * EvalReportUpdateDTO
 * 评测报告表
 */
export interface EvalReportUpdateDTO {
  /** 详细指标数据Json */
  detailedMetricsJson?: string
  /**
   * 详细指标数据Text
   * @minLength 0
   * @maxLength 65535
   */
  detailedMetricsText?: string
  /**
   * 报告文件ID
   * @minLength 0
   * @maxLength 36
   */
  fileId?: string
  /**
   * 报告格式：PDF/HTML/WORD等
   * @minLength 0
   * @maxLength 20
   */
  fileType?: string
  /** 主键 */
  id?: string
  /** 总体评分 */
  overallScore?: number
  /**
   * 报告名称
   * @minLength 0
   * @maxLength 100
   */
  reportName?: string
  /**
   * 报告状态：1-正常，2-异常，0-已删除
   * @format int32
   */
  reportStatus?: number
  /**
   * 评测总结
   * @minLength 0
   * @maxLength 65535
   */
  summary?: string
  /**
   * 关联评测任务ID
   * @format int64
   */
  taskId?: number
}

/**
 * EvalTask
 * 评测任务表
 */
export interface EvalTask {
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string
  /** 创建人ID */
  createdBy?: string
  echoMap?: object
  /**
   * 完成时间
   * @format date-time
   */
  endTime?: string
  /**
   * 评测助手ID
   * @minLength 0
   * @maxLength 36
   */
  evalAppId?: string
  /**
   * 评测助手版本号
   * @minLength 0
   * @maxLength 20
   */
  evalAppVersion?: string
  /**
   * 评测规则字典ID
   * @format int64
   */
  evalRuleId?: number
  /**
   * 评测规则提示词
   * @minLength 0
   * @maxLength 65535
   */
  evalRulePromptWord?: string
  /** 关联的智能助手数据 */
  evalTaskApp?: EvalTaskApp
  /** 关联的评测集数据（自动评测） */
  evalTaskDataset?: EvalTaskDataset
  /** 关联的评测数据列表（人工评测） */
  evalTaskTestDataList?: EvalTaskTestData[]
  /**
   * 执行耗时（秒）
   * @format int32
   */
  executionDuration?: number
  /** 主键 */
  id?: string
  /**
   * 意图
   * @minLength 0
   * @maxLength 200
   */
  intention?: string
  /**
   * 输出格式
   * @minLength 0
   * @maxLength 20
   */
  outputFormat?: string
  /** 执行进度百分比 */
  progress?: number
  /**
   * 质量要求
   * @minLength 0
   * @maxLength 200
   */
  qualityRequirement?: string
  /**
   * 开始执行时间
   * @format date-time
   */
  startTime?: string
  /**
   * 任务状态：0-待执行，1-执行中，2-已完成，3-失败，4-已终止
   * @format int32
   */
  status?: number
  /**
   * 任务名称
   * @minLength 0
   * @maxLength 100
   */
  taskName?: string
  /**
   * 任务类型：1-自动评测，2-人工评测
   * @format int32
   */
  taskType?: number
  /**
   * 测试分析方法
   * @minLength 0
   * @maxLength 100
   */
  testMethod?: string
  /**
   * 最后修改时间
   * @format date-time
   */
  updateTime?: string
  /** 最后修改人ID */
  updatedBy?: string
}

/**
 * EvalTaskApp
 * 评测任务与智能助手关系表
 */
export interface EvalTaskApp {
  /**
   * 智能助手ID
   * @minLength 0
   * @maxLength 36
   */
  appId?: string
  /**
   * 智能助手输出结果文件Id
   * @minLength 0
   * @maxLength 36
   */
  appResultFile?: string
  /** 智能助手输出结果Json */
  appResultJson?: string
  /**
   * 智能助手输出结果Text
   * @minLength 0
   * @maxLength 65535
   */
  appResultText?: string
  /**
   * 智能助手版本号
   * @minLength 0
   * @maxLength 20
   */
  appVersion?: string
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string
  /** 创建人ID */
  createdBy?: string
  echoMap?: object
  /** 主键 */
  id?: string
  /**
   * 评测任务ID
   * @format int64
   */
  taskId?: number
}

/**
 * EvalTaskDataset
 * 自动评测任务和评测集关联表
 */
export interface EvalTaskDataset {
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string
  /**
   * 创建人ID
   * @format int64
   */
  createdBy?: number
  /**
   * 评测集ID
   * @format int64
   */
  datasetId?: number
  echoMap?: object
  /**
   * 评测任务ID
   * @format int64
   */
  taskId?: number
}

/**
 * EvalTaskTestData
 * 人工评测任务和评测数据表
 */
export interface EvalTaskTestData {
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string
  /**
   * 创建人ID
   * @format int64
   */
  createdBy?: number
  echoMap?: object
  /**
   * 评测数据文件ID
   * @minLength 0
   * @maxLength 36
   */
  fileId?: string
  /**
   * 评测数据文件类型
   * @minLength 0
   * @maxLength 20
   */
  fileType?: string
  /**
   * 评测任务ID
   * @format int64
   */
  taskId?: number
}

/** IPage«EvalReport» */
export interface IPageEvalReport {
  /** @format int64 */
  current?: number
  /** @format int64 */
  pages?: number
  records?: EvalReport[]
  /** @format int64 */
  size?: number
  /** @format int64 */
  total?: number
}

/**
 * PageParams«EvalReportPageQuery»
 * 分页参数
 */
export interface PageParamsEvalReportPageQuery {
  /**
   * 当前页
   * @format int64
   * @example 1
   */
  current?: number
  /** 扩展参数 */
  extra?: object
  /** 查询参数 */
  model: EvalReportPageQuery
  /**
   * 排序规则, 默认descending
   * @example "descending"
   */
  order?: 'descending' | 'ascending'
  /**
   * 页面大小
   * @format int64
   * @example 10
   */
  size?: number
  /**
   * 排序,默认createTime
   * @example "id"
   */
  sort?: 'id' | 'createTime' | 'updateTime'
}

/** R«EvalReport» */
export interface REvalReport {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: EvalReport
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}

/** R«IPage«EvalReport»» */
export interface RIPageEvalReport {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: IPageEvalReport
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}

/** R«List«EvalReport»» */
export interface RListEvalReport {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: EvalReport[]
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}

/** R«boolean» */
export interface RBoolean {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: boolean
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}
