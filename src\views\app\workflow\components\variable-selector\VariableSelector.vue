<template>
  <div ref="selectorRef" class="variable-selector">
    <a-popover
      :content-style="{ padding: 0 }"
      :scroll-to-close-distance="300"
      show-arrow
      trigger="click"
      position="bottom"
      @popup-visible-change="handlePopupVisibleChange"
    >
      <div class="variable-selector-trigger" :class="{ readonly: readonly }" @click="handleTriggerClick">
        <template v-if="hasValue">
          <VariableTag
            :value-selector="valueSelector"
            :available-nodes="availableNodes"
            :nodes-output-vars="nodesOutputVars"
            :show-node-name="isShowNodeName"
          />
        </template>
        <template v-else>
          <div class="variable-selector-placeholder">
            {{ placeholder || '选择变量' }}
          </div>
        </template>
      </div>
      <template #content>
        <VariableList
          :vars="nodesOutputVars"
          :var-type="varType"
          :filter-var="filterVar"
          @select="handleVariableSelect"
        />
      </template>
    </a-popover>
  </div>
</template>

<script setup lang="ts">
// import { ref, computed, onMounted, watch } from 'vue'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import {
  getBeforeNodesInSameBranchIncludeParent,
  toNodeAvailableVars,
  filterVar as filterVarByType
} from '@/views/app/workflow/utils/variable'
import VariableTag from './VariableTag.vue'
import VariableList from './VariableList.vue'
import { VarType } from '@/views/app/workflow/types/workflow'

const props = withDefaults(
  defineProps<{
    nodeId: string
    valueSelector?: string[]
    varType?: VarType
    readonly?: boolean
    isShowNodeName?: boolean
    placeholder?: string
    popupContainer?: string
    availableNodes?: any[]
    nodesOutputVars?: any[]
    filterVar?: (v: any) => boolean
  }>(),
  {
    valueSelector: () => [],
    varType: VarType.string,
    readonly: false,
    isShowNodeName: true,
    placeholder: '选择变量',
    popupContainer: 'body',
    availableNodes: () => [],
    nodesOutputVars: () => [],
    filterVar: () => true
  }
)

const emit = defineEmits<{
  (e: 'change', valueSelector: string[], varItem: any): void
  (e: 'open'): void
  (e: 'update:valueSelector', valueSelector: string[]): void
}>()

const selectorRef = ref<HTMLElement | null>(null)
const nodesStore = useNodesStore()
const isOpen = ref(false)

// Computed properties
const hasValue = computed(() => {
  return props.valueSelector && props.valueSelector.length > 0
})

// Get available nodes and variables
const getAvailableNodesAndVars = () => {
  if (props.availableNodes.length > 0 && props.nodesOutputVars.length > 0) {
    return {
      availableNodes: props.availableNodes,
      nodesOutputVars: props.nodesOutputVars
    }
  }

  const beforeNodes = getBeforeNodesInSameBranchIncludeParent(props.nodeId)
  const parentNode = nodesStore.parentNode

  const outputVars = toNodeAvailableVars(
    parentNode,
    beforeNodes,
    false,
    props.filterVar || filterVarByType(props.varType),
    [],
    []
  )

  return {
    availableNodes: beforeNodes,
    nodesOutputVars: outputVars
  }
}

// Initialize data
const availableNodes = ref<any[]>([])
const nodesOutputVars = ref<any[]>([])

onMounted(() => {
  const { availableNodes: nodes, nodesOutputVars: vars } = getAvailableNodesAndVars()
  availableNodes.value = nodes
  nodesOutputVars.value = vars
})

// Watch for changes in nodeId to update available nodes and variables
watch(
  () => props.nodeId,
  () => {
    console.log(props.nodeId, props.availableNodes, '---')
    const { availableNodes: nodes, nodesOutputVars: vars } = getAvailableNodesAndVars()
    availableNodes.value = nodes
    nodesOutputVars.value = vars
  }
)

// Event handlers
const handleTriggerClick = () => {
  if (props.readonly) return
  emit('open')
}

const handlePopupVisibleChange = (visible: boolean) => {
  isOpen.value = visible
}

const variableId = ref<string>('')
// const handleVariableSelect = (valueSelector: string[], varItem: any, variableId: string) => {
const handleVariableSelect = (valueSelector: any, varItem: any) => {
  const selector = [valueSelector.nodeId]
  variableId.value = valueSelector.nodeId
  let isCurrentMatched: string[] = []
  const variableArr = varItem.variable.split('.')
  const [first] = variableArr
  if (first === 'sys' || first === 'env' || first === 'conversation') {
    isCurrentMatched = variableArr
  } else {
    isCurrentMatched = [...selector, ...variableArr]
  }
  emit('update:valueSelector', isCurrentMatched)
  emit('change', isCurrentMatched, varItem)
}
</script>

<style scoped lang="scss">
.variable-selector {
  width: 100%;

  &-trigger {
    border: 1px solid var(--color-border-3);
    height: 32px;
    border-radius: 4px;
    padding: 4px 6px;
    display: flex;
    align-items: center;
    cursor: pointer;

    &.readonly {
      cursor: default;
      background-color: var(--color-fill-2);
    }
  }

  &-placeholder {
    color: var(--color-text-3);
  }
}
</style>
