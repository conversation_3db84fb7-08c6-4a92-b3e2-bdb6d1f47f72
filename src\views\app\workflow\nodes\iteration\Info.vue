<template>
  <div class="field-info">
    <a-form size="small" :model="form" layout="vertical">
      <a-form-item field="iterator_selector" label="输入" :required="true" asterisk-position="end">
        <VariableSelector v-model:value-selector="form.iterator_selector" :nodeId="nodeId" />
      </a-form-item>
      <a-form-item field="output_selector" label="输出变量" :required="true" asterisk-position="end">
        <VariableSelector v-model:value-selector="form.output_selector" :nodeId="nodeId" />
      </a-form-item>
      <a-form :model="form" layout="horizontal" auto-label-width>
        <a-form-item field="is_parallel" label="并行模式" tooltip="在并行模式下，迭代中的任务支持并行执行。">
          <a-checkbox v-model="form.is_parallel" />
        </a-form-item>
        <a-form-item
          v-if="form.is_parallel"
          field="parallel_nums"
          label="最大并行度"
          :rules="[{ type: 'number', message: '' }]"
          tooltip="最大并行度用于控制单次迭代中同时执行的任务数量。"
        >
          <a-input-number v-model="form.parallel_nums" :min="1" :max="10" class="w-[100px] mr-[16px]" />
          <a-slider v-model="form.parallel_nums" :max="10" :min="1" />
        </a-form-item>
      </a-form>
    </a-form>

    <a-divider />

    <Field fieldTitle="错误响应方法">
      <a-select v-model="form.error_handle_mode">
        <a-option v-for="option in errorResList" :key="option.value" :label="option.label" :value="option.value" />
      </a-select>
    </Field>
  </div>
</template>

<script setup lang="ts">
import VariableSelector from '@/views/app/workflow/components/variable-selector'
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'
import { ErrorHandleMode } from '@/views/app/workflow/types/workflow'

const props = withDefaults(
  defineProps<{
    node: any
    nodeId: any
  }>(),
  {
    node: {},
    nodeId: ''
  }
)
const defaultList = ref([
  {
    type: 'string',
    variable: 'sys.user_id',
    value: ['sys', 'user_id']
  },
  {
    type: 'array[file]',
    variable: 'sys.files',
    value: ['sys', 'files']
  },
  {
    type: 'string',
    variable: 'sys.app_id',
    value: ['sys', 'app_id']
  },
  {
    type: 'string',
    variable: 'sys.workflow_id',
    value: ['sys', 'workflow_id']
  },
  {
    type: 'string',
    variable: 'sys.workflow_run_id',
    value: ['sys', 'workflow_run_id']
  }
])

const form = ref({
  iterator_selector: [],
  output_selector: [],
  is_parallel: false,
  parallel_nums: 10,
  error_handle_mode: false
})
form.value = props.node

const errorResList = [
  { label: '错误时终止', value: ErrorHandleMode.Terminated },
  { label: '忽略错误并继续', value: ErrorHandleMode.ContinueOnError },
  { label: '移除错误输出', value: ErrorHandleMode.RemoveAbnormalOutput }
]
</script>
<style scoped lang="scss">
.field-info {
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-1);
}

:deep(.arco-select-option-content) {
  width: 100%;
}
</style>
