<template>
  <div class="metadata-condition-list">
    <!-- 添加条件按钮 -->
    <div class="add-condition-section">
      <a-button type="primary" size="small" @click="showAddConditionModal = true">添加过滤条件</a-button>
    </div>

    <!-- 条件列表 -->
    <div v-if="conditions.length > 0" class="conditions-list">
      <div v-if="conditions.length > 1" class="logical-operator">
        <a-radio-group v-model="logicalOperator" @change="handleLogicalOperatorChange">
          <a-radio value="and">AND (所有条件都满足)</a-radio>
          <a-radio value="or">OR (任一条件满足)</a-radio>
        </a-radio-group>
      </div>

      <div v-for="(condition, index) in conditions" :key="condition.id" class="condition-item">
        <div class="condition-content">
          <div class="condition-field">
            <label>字段:</label>
            <a-select
              v-model="condition.metadata_key"
              placeholder="选择元数据字段"
              :options="metadataFieldOptions"
              @change="() => handleConditionUpdate(condition.id)"
            />
          </div>

          <div class="condition-operator">
            <label>操作符:</label>
            <a-select
              v-model="condition.operator"
              placeholder="选择操作符"
              :options="operatorOptions"
              @change="() => handleConditionUpdate(condition.id)"
            />
          </div>

          <div class="condition-value">
            <label>值:</label>
            <div class="value-input-group">
              <a-radio-group
                v-model="condition.value_type"
                size="small"
                @change="() => handleConditionUpdate(condition.id)"
              >
                <a-radio value="constant">常量</a-radio>
                <a-radio value="variable">变量</a-radio>
              </a-radio-group>

              <div class="value-input">
                <a-input
                  v-if="condition.value_type === 'constant'"
                  v-model="condition.value"
                  placeholder="输入值"
                  @change="() => handleConditionUpdate(condition.id)"
                />
                <VariableSelector
                  v-else
                  :node-id="nodeId"
                  :value-selector="condition.value_selector"
                  @change="(valueSelector) => handleVariableChange(condition.id, valueSelector)"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="condition-actions">
          <a-button type="text" size="small" status="danger" @click="handleRemoveCondition(condition.id)">
            删除
          </a-button>
        </div>
      </div>
    </div>

    <div v-else class="empty-conditions">
      <div class="empty-text">暂无过滤条件</div>
      <div class="empty-desc">添加元数据过滤条件来精确控制检索结果</div>
    </div>

    <!-- 添加条件弹窗 -->
    <a-modal
      v-model:visible="showAddConditionModal"
      title="选择元数据字段"
      width="500px"
      @ok="handleAddCondition"
      @cancel="showAddConditionModal = false"
    >
      <div class="metadata-field-list">
        <div v-if="metadataList.length === 0" class="empty-fields">
          <div class="empty-text">暂无可用的元数据字段</div>
          <div class="empty-desc">请先选择包含元数据的知识库</div>
        </div>
        <div v-else class="field-items">
          <div
            v-for="field in metadataList"
            :key="field.name"
            class="metadata-field-item"
            :class="{ active: selectedMetadataField?.name === field.name }"
            @click="selectedMetadataField = field"
          >
            <div class="field-info">
              <div class="field-name">{{ field.name }}</div>
              <div class="field-type">{{ field.type }}</div>
            </div>
            <div class="field-description">
              {{ field.description || '暂无描述' }}
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import VariableSelector from '@/views/app/workflow/components/variable-selector/VariableSelector.vue'

const props = defineProps<{
  conditions: any[]
  metadataList: any[]
  nodeId?: string
}>()

const emit = defineEmits<{
  (e: 'add-condition', metadataItem: any): void
  (e: 'remove-condition', conditionId: string): void
  (e: 'update-condition', conditionId: string, condition: any): void
  (e: 'toggle-logical-operator'): void
}>()

const showAddConditionModal = ref(false)
const selectedMetadataField = ref(null)
const logicalOperator = ref('and')

const metadataFieldOptions = computed(() =>
  props.metadataList.map((field) => ({
    label: `${field.name} (${field.type})`,
    value: field.name
  }))
)

const operatorOptions = [
  { label: '包含', value: 'contains' },
  { label: '不包含', value: 'not_contains' },
  { label: '等于', value: 'equals' },
  { label: '不等于', value: 'not_equals' },
  { label: '开始于', value: 'starts_with' },
  { label: '结束于', value: 'ends_with' },
  { label: '为空', value: 'is_empty' },
  { label: '不为空', value: 'is_not_empty' },
  { label: '大于', value: 'greater_than' },
  { label: '小于', value: 'less_than' },
  { label: '大于等于', value: 'greater_than_or_equal' },
  { label: '小于等于', value: 'less_than_or_equal' }
]

const handleAddCondition = () => {
  if (selectedMetadataField.value) {
    emit('add-condition', selectedMetadataField.value)
    showAddConditionModal.value = false
    selectedMetadataField.value = null
  }
}

const handleRemoveCondition = (conditionId: string) => {
  emit('remove-condition', conditionId)
}

const handleConditionUpdate = (conditionId: string) => {
  const condition = props.conditions.find((c) => c.id === conditionId)
  if (condition) {
    emit('update-condition', conditionId, condition)
  }
}

const handleVariableChange = (conditionId: string, valueSelector: string[]) => {
  const condition = props.conditions.find((c) => c.id === conditionId)
  if (condition) {
    condition.value_selector = valueSelector
    emit('update-condition', conditionId, condition)
  }
}

const handleLogicalOperatorChange = () => {
  emit('toggle-logical-operator')
}
</script>

<style scoped lang="scss">
.metadata-condition-list {
  .add-condition-section {
    margin-bottom: 16px;
  }

  .conditions-list {
    .logical-operator {
      margin-bottom: 16px;
      padding: 8px 12px;
      background-color: var(--color-bg-3);
      border-radius: 4px;
      border: 1px solid var(--color-border-2);
    }

    .condition-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 12px;
      padding: 12px;
      background-color: var(--color-bg-2);
      border-radius: 6px;
      border: 1px solid var(--color-border-2);

      .condition-content {
        flex: 1;

        .condition-field,
        .condition-operator,
        .condition-value {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          label {
            min-width: 60px;
            font-size: 13px;
            color: var(--color-text-2);
            margin-right: 8px;
          }

          .arco-select,
          .arco-input {
            flex: 1;
          }
        }

        .condition-value {
          .value-input-group {
            flex: 1;

            .arco-radio-group {
              margin-bottom: 8px;
            }

            .value-input {
              width: 100%;
            }
          }
        }
      }

      .condition-actions {
        margin-left: 12px;
      }
    }
  }

  .empty-conditions {
    text-align: center;
    padding: 24px;
    color: var(--color-text-3);

    .empty-text {
      font-size: 14px;
      margin-bottom: 4px;
    }

    .empty-desc {
      font-size: 12px;
    }
  }

  .metadata-field-list {
    max-height: 400px;
    overflow-y: auto;

    .empty-fields {
      text-align: center;
      padding: 40px 20px;
      color: var(--color-text-3);

      .empty-text {
        font-size: 14px;
        margin-bottom: 4px;
      }

      .empty-desc {
        font-size: 12px;
      }
    }

    .field-items {
      .metadata-field-item {
        padding: 12px;
        margin-bottom: 8px;
        border: 1px solid var(--color-border-2);
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;
        background-color: var(--color-bg-1);

        &:hover {
          border-color: var(--color-primary-light-4);
          background-color: var(--color-primary-light-1);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.active {
          border-color: var(--color-primary);
          background-color: var(--color-primary-light-1);
          box-shadow: 0 2px 8px rgba(var(--color-primary-6), 0.2);
        }

        .field-info {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 4px;

          .field-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--color-text-1);
          }

          .field-type {
            font-size: 12px;
            color: var(--color-text-3);
            background-color: var(--color-bg-3);
            padding: 2px 6px;
            border-radius: 3px;
          }
        }

        .field-description {
          font-size: 12px;
          color: var(--color-text-3);
          line-height: 1.4;
        }
      }
    }
  }
}
</style>
