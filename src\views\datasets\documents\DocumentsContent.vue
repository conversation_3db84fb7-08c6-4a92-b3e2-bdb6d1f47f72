<template>
  <div class="documents-container">
    <div class="documents-header">
      <div class="header-left">
        <h2>文档</h2>
      </div>
    </div>

    <div class="search-container">
      <a-input-search
        v-model="searchKeyword"
        placeholder="搜索"
        allow-clear
        @search="handleSearch"
        @change="handleSearch"
      />
      <div class="view-options">
        <a-button type="text" @click="openMetadataModal">
          <template #icon><icon-apps /></template>
          元数据
        </a-button>
        <a-button type="primary" @click="goToDocumentCreate">
          <template #icon><icon-plus /></template>
          添加文件
        </a-button>
      </div>
    </div>
    <!-- 文档列表为空时 -->
    <div v-if="!documentsList.length" class="empty-documents-wrapper">
      <div class="empty-documents">
        <div class="empty-icon">
          <icon-folder-add />
        </div>
        <h3 class="empty-title">还没有文档</h3>
        <p class="empty-desc">您可以上传文件，从网站同步，或者从网络应用程序（如概念、GitHub 等）同步。</p>
        <a-button type="outline" class="add-file-btn" @click="goToDocumentCreate">
          <template #icon>
            <icon-plus />
          </template>
          添加文件
        </a-button>
      </div>
    </div>
    <div v-else class="documents-table">
      <a-table
        :columns="columns"
        :data="documentsList"
        :pagination="pagination"
        :row-selection="rowSelection"
        :loading="loading"
        @page-change="onPageChange"
        @page-size-change="onPageSizeChange"
        @row-click="handleRowClick"
      >
        <template #index="{ rowIndex }">
          {{ rowIndex + 1 + (pagination.current - 1) * pagination.pageSize }}
        </template>

        <template #name="{ record }">
          <div class="file-info">
            <div class="file-icon">
              <icon-file-pdf v-if="getFileExtension(record.name) === 'pdf'" />
              <icon-file v-else />
            </div>
            <div class="file-name-size">
              <span class="file-name">{{ record.name }}</span>
            </div>
            <div v-if="!record.archived" class="file-name-edit" @click.stop="openEditDialog(record)">
              <a-tooltip content="重命名" position="top" mini>
                <icon-edit />
              </a-tooltip>
            </div>
          </div>
        </template>
        <template #shareMode>
          <!-- <span>{{ record.shareMode }}</span> -->
          <a-button type="outline" size="mini">
            <template #icon>
              <icon-common />
            </template>
            <template #default>通用</template>
          </a-button>
        </template>
        <template #bytes="{ record }">
          <span>{{ formatFileSize(record.size) }}</span>
        </template>
        <template #recalls="{ record }">
          <span>{{ record.recalls }}</span>
        </template>
        <template #uploadTime="{ record }">
          <span>{{ record.uploadTime }}</span>
        </template>
        <template #status="{ record }">
          <div class="status-container">
            <a-tag v-if="record.archived" color="gray">已归档</a-tag>
            <span v-else>
              <a-tag v-if="record.display_status === 'disabled'" color="gray">已禁用</a-tag>
              <a-tag v-else-if="record.display_status === 'available'" color="green">可用</a-tag>
              <a-tag v-else-if="record.display_status === 'indexing'" color="blue">索引中</a-tag>
              <a-tag v-else-if="record.display_status === 'error'" color="red">失败</a-tag>
            </span>
          </div>
        </template>
        <template #operations="{ record }">
          <div class="operations-container">
            <a-switch v-if="record.archived" type="round" size="small" disabled />
            <a-switch
              v-else
              v-model="record.available"
              type="round"
              size="small"
              @change="(value) => toggleAvailability(record, Boolean(value))"
            />
            <a-divider direction="vertical" />
            <a-button size="mini" class="btn1" @click.stop="(e) => goToDocumentsChunkSetting(e, record)">
              <template #icon>
                <a-tooltip content="分段设置" position="top" mini>
                  <icon-ordered-list />
                </a-tooltip>
              </template>
            </a-button>
            <a-dropdown trigger="click">
              <a-button type="text" size="small" @click.stop>
                <template #icon><icon-more /></template>
              </a-button>
              <template #content>
                <a-doption v-if="!record.archived" @click.stop="openEditDialog(record)">
                  <template #icon>
                    <icon-edit />
                  </template>
                  <template #default>重命名</template>
                </a-doption>
                <a-doption @click.stop="ArchiveDocument(record)">
                  <template #icon>
                    <icon-archive />
                  </template>
                  <template #default>{{ record.archived ? '撤销归档' : '归档' }}</template>
                </a-doption>
                <a-doption class="delete-option" @click.stop="showDeleteConfirm(record)">
                  <template #icon>
                    <icon-delete />
                  </template>
                  <template #default>删除</template>
                </a-doption>
              </template>
            </a-dropdown>
          </div>
        </template>
      </a-table>
    </div>
  </div>

  <!-- 删除确认弹窗 -->
  <a-modal
    v-model:visible="deleteConfirmVisible"
    title="确认删除吗？"
    :unmount-on-close="true"
    title-align="start"
    modal-class="rename-modal"
    ok-text="我确认"
    @cancel="cancelDelete"
    @ok="confirmDelete"
  >
    <p>如果您需要稍后恢复处理，您将从您离开的地方继续</p>
  </a-modal>

  <!-- 重命名弹窗 -->
  <a-modal
    v-model:visible="renameModalVisible"
    title="重命名"
    title-align="start"
    :unmount-on-close="true"
    ok-text="保存"
    modal-class="rename-modal"
    @cancel="cancelRename"
    @ok="confirmRename"
  >
    <div class="rename-content">
      <p>名称</p>
      <a-input v-model="newFileName" placeholder="请输入文件名" />
    </div>
  </a-modal>

  <!-- 元数据模态框 -->
  <metadata-modal
    v-model:visible="metadataModalVisible"
    :dataset-id="props.datasetId"
    @add-metadata="handleAddMetadata"
    @refresh="fetchDocumentsList"
  />
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useRouter } from 'vue-router'
import {
  getDatasetDocuments,
  uploadFile,
  renameDocument,
  enableDocuments,
  disableDocuments,
  archiveDocuments,
  unArchiveDocuments,
  deleteDocument
} from '@/apis/datasets'
import MetadataModal from './MetadataModal.vue'

// 定义props
const props = defineProps({
  datasetId: {
    type: String,
    required: true
  }
})

// 定义emit
const emit = defineEmits(['refresh-dataset'])

// 获取路由实例
const router = useRouter()

// 定义文档接口
interface Document {
  id: string
  name: string
  size: number
  shareMode: string
  recalls: number
  uploadTime: string
  available: boolean
  archived: boolean
  display_status?: string
}

// 表格列定义
const columns = [
  {
    title: '序号',
    slotName: 'index',
    width: 60
  },
  {
    title: '名称',
    dataIndex: 'name',
    slotName: 'name'
  },
  {
    title: '分段模式',
    dataIndex: 'shareMode',
    slotName: 'shareMode'
  },
  {
    title: '字符数',
    dataIndex: 'bytes',
    slotName: 'bytes'
  },
  {
    title: '召回次数',
    dataIndex: 'recalls',
    slotName: 'recalls'
  },
  {
    title: '上传时间',
    dataIndex: 'uploadTime',
    slotName: 'uploadTime'
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status'
  },
  {
    title: '操作',
    dataIndex: 'operations',
    slotName: 'operations',
    fixed: 'right',
    width: 100
  }
]

// 分页设置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showJumper: true,
  pageSizeOptions: [10, 25, 50],
  showPageSize: true
})

// 行选择配置
const rowSelection = {
  type: 'checkbox',
  showCheckedAll: true,
  onlyCurrent: false
}

// 状态变量
const loading = ref(false)
const searchKeyword = ref('')
const documentsList = ref<Document[]>([])
const uploadModalVisible = ref(false)
const uploadFileList = ref([])
const deleteConfirmVisible = ref(false)
const currentDocument = ref<Document | null>(null)
const renameModalVisible = ref(false)
const newFileName = ref('')
const metadataModalVisible = ref(false)

// 生命周期钩子
onMounted(() => {
  if (props.datasetId) {
    fetchDocumentsList()
  }
})

// 获取文档列表
const fetchDocumentsList = async () => {
  loading.value = true
  try {
    const params = {
      page: pagination.current,
      limit: pagination.pageSize,
      keyword: searchKeyword.value
    }

    const response = await getDatasetDocuments(props.datasetId, params)
    documentsList.value = response.data.map((doc) => ({
      id: doc.id,
      name: doc.name || (doc as any).data_source_detail_dict?.upload_file?.name || '未命名文档',
      size: (doc as any).data_source_detail_dict?.upload_file?.size || 0,
      shareMode: '通用', // 默认值
      recalls: (doc as any).hit_count || 0,
      uploadTime: formatTimeStamp(doc.created_at),
      available: (doc as any).enabled || false,
      archived: (doc as any).archived || false,
      display_status: (doc as any).display_status || 'available'
    }))

    pagination.total = response.total
    console.log('文档列表:', documentsList.value)
  } catch (error) {
    console.error('获取文档列表失败:', error)
    Message.error('获取文档列表失败')
  } finally {
    loading.value = false
  }
}

// 时间戳格式化
const formatTimeStamp = (timestamp: number): string => {
  if (!timestamp) return ''
  const date = new Date(timestamp * 1000)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 文件类型处理
const getFileExtension = (filename: string): string => {
  return filename.split('.').pop()?.toLowerCase() || ''
}

// 文件大小格式化
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 搜索处理
const handleSearch = (value?: string) => {
  if (value !== undefined) {
    searchKeyword.value = value
  }
  pagination.current = 1
  fetchDocumentsList()
  console.log('执行搜索，关键词:', searchKeyword.value)
}

// 分页处理
const onPageChange = (page: number) => {
  pagination.current = page
  fetchDocumentsList()
}

const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize
  pagination.current = 1
  fetchDocumentsList()
}

// 上传文件变化处理
const onUploadChange = (fileList: any) => {
  uploadFileList.value = fileList
}

// 自定义上传处理
const handleUpload = async (options: any) => {
  const { file, onProgress, onSuccess, onError } = options

  try {
    const response = await uploadFile(file, (percent: number) => {
      onProgress(percent)
    })
    onSuccess(response)
  } catch (error) {
    console.error('文件上传失败:', error)
    onError(error)
  }
}

// 取消上传
const cancelUpload = () => {
  uploadModalVisible.value = false
}

// 确认上传
const confirmUpload = async () => {
  if (uploadFileList.value.length === 0) {
    Message.warning('请先选择要上传的文件')
    return
  }

  try {
    // 这里应该是提交所有已上传文件ID的处理逻辑
    Message.success('文件上传成功')
    uploadModalVisible.value = false
    fetchDocumentsList() // 刷新文档列表
    emit('refresh-dataset')
  } catch (error) {
    console.error('文件处理失败:', error)
    Message.error('文件处理失败')
  }
}

// 切换文件可用状态：若是已归档的文档，不可编辑
const toggleAvailability = async (document: Document, value: boolean) => {
  if (document.archived) {
    Message.warning('已归档文档不能修改状态')
    document.available = !value // 恢复状态
    return
  }

  try {
    // 根据value值调用不同API
    if (value) {
      // 启用文档
      await enableDocuments(props.datasetId, document.id)
    } else {
      // 禁用文档
      await disableDocuments(props.datasetId, document.id)
    }

    document.available = value
    Message.success(`${value ? '启用' : '禁用'}文件成功`)

    // 刷新文档列表
    fetchDocumentsList()
    // 刷新数据集信息
    emit('refresh-dataset')
  } catch (error) {
    console.error('更新文件状态失败:', error)
    Message.error('更新文件状态失败')
    document.available = !value // 恢复状态
  }
}

// 归档/撤销归档
const ArchiveDocument = async (document: Document) => {
  try {
    if (document.archived) {
      // 撤销归档
      await unArchiveDocuments(props.datasetId, document.id)
      Message.success('撤销归档成功')
    } else {
      // 归档文档
      await archiveDocuments(props.datasetId, document.id)
      Message.success('归档成功')
    }

    // 刷新文档列表
    fetchDocumentsList()
    // 刷新数据集信息
    emit('refresh-dataset')
  } catch (error) {
    console.error('操作失败:', error)
    Message.error(`${document.archived ? '撤销归档' : '归档'}失败`)
  }
}

// 删除文档
const showDeleteConfirm = (document: Document) => {
  currentDocument.value = document
  deleteConfirmVisible.value = true
}

// 取消删除
const cancelDelete = () => {
  deleteConfirmVisible.value = false
  currentDocument.value = null
}

// 确认删除
const confirmDelete = async () => {
  if (!currentDocument.value) return

  try {
    // 调用删除文档API
    await deleteDocument(props.datasetId, currentDocument.value.id)

    Message.success('文件删除成功')
    deleteConfirmVisible.value = false
    currentDocument.value = null

    // 刷新文档列表
    fetchDocumentsList()
    // 刷新数据集信息
    emit('refresh-dataset')
  } catch (error) {
    console.error('删除文件失败:', error)
    Message.error('删除文件失败')
  }
}

// 取消重命名
const cancelRename = () => {
  renameModalVisible.value = false
}

// 确认重命名
const confirmRename = async () => {
  if (!currentDocument.value) return

  if (!newFileName.value.trim()) {
    Message.warning('文件名不能为空')
    return
  }

  try {
    // 调用重命名API
    await renameDocument(props.datasetId, currentDocument.value.id, { name: newFileName.value.trim() })

    // 更新本地状态
    currentDocument.value.name = newFileName.value.trim()
    Message.success('文件重命名成功')
    renameModalVisible.value = false

    // 刷新文档列表
    fetchDocumentsList()
    emit('refresh-dataset')
  } catch (error) {
    console.error('重命名文件失败:', error)
    Message.error('重命名文件失败')
  }
}

// 打开编辑对话框
const openEditDialog = (document: Document) => {
  currentDocument.value = document
  renameModalVisible.value = true
  newFileName.value = document.name
}

// 跳转到文档创建页面
const goToDocumentCreate = () => {
  router.push({
    path: '/datasets/create',
    query: { from: 'documents', datasetId: props.datasetId }
  })

  console.log('跳转到文档创建页面，传递知识库ID:', props.datasetId)
}

// 处理表格行点击事件，跳转到文档分块设置详情页面
const handleRowClick = (record: Document) => {
  if (!record.archived) {
    console.log('跳转到文档分块详情页面，知识库ID:', props.datasetId, '文档ID:', record.id)
    router.push({
      name: 'documentsChunkDetail',
      params: {
        datasetId: props.datasetId,
        documentId: record.id
      }
    })
  }
}

// 跳转到文档分段设置页面
const goToDocumentsChunkSetting = (e: Event, record: Document) => {
  // 阻止事件冒泡，防止同时触发行点击事件
  e.stopPropagation()

  router.push({
    name: 'documentsChunkSetting',
    params: { datasetId: props.datasetId },
    query: { documentId: record.id }
  })

  console.log('跳转到文档分段设置页面，知识库ID:', props.datasetId, '文档ID:', record.id)
}

// 打开元数据模态框
const openMetadataModal = () => {
  metadataModalVisible.value = true
}

// 处理添加元数据事件
const handleAddMetadata = () => {
  // 跳转到新建元数据页面或打开新建元数据模态框
  // 这里可以根据实际需求实现
  console.log('打开添加元数据界面')
}

// 暴露方法供父组件调用
defineExpose({
  fetchDocumentsList
})
</script>

<style scoped lang="scss">
.documents-container {
  // max-width: 1200px;
  margin: 0 auto;

  .documents-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;

    .header-left {
      h2 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .description {
        color: var(--color-text-3);
        font-size: 14px;
      }
    }
  }

  .search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .arco-input-search {
      width: 320px;
    }

    .view-options {
      display: flex;
      gap: 8px;
    }
  }

  .documents-table {
    background-color: #fff;
    border-radius: 8px;
    overflow: hidden;

    .file-info {
      display: flex;
      align-items: center;
      gap: 12px;
      position: relative;

      .file-icon {
        color: #f56c6c;
        font-size: 20px;
      }

      .file-name-size {
        display: flex;
        flex-direction: column;

        .file-name {
          font-weight: 500;
        }

        .file-size {
          font-size: 12px;
          color: var(--color-text-3);
        }
      }
      .file-name-edit {
        position: absolute;
        right: 10px;
        width: 30px;
        display: flex;
        flex-direction: column;
        display: none;
        cursor: pointer;
      }
    }
    .file-info:hover {
      .file-name-edit {
        display: block;
      }
    }

    .status-container {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .operations-container {
      display: flex;
      justify-content: center;
      align-items: center;
      .btn1 {
        margin-right: 10px;
      }
    }
  }
}

.upload-content {
  padding: 24px;
  text-align: center;

  .upload-icon {
    font-size: 32px;
    color: var(--color-text-3);
    margin-bottom: 16px;
  }

  .upload-text {
    font-size: 14px;
    margin-bottom: 8px;
  }

  .upload-hint {
    font-size: 12px;
    color: var(--color-text-3);
  }
}

:deep(.delete-option) {
  color: var(--color-danger);
}

.empty-documents-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 10%;
}
.empty-documents {
  padding: 16px 20px;
  border-radius: 16px;
  text-align: left;
  box-sizing: border-box;
  height: -moz-fit-content;
  height: fit-content;
  width: 560px;
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));

  .empty-icon {
    font-size: 20px;
    color: var(--color-text-3);
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--color-fill-2);
    border-radius: 8px;
    margin-bottom: 16px;
  }

  .empty-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: var(--color-text-1);
  }

  .empty-desc {
    font-size: 14px;
    color: var(--color-text-3);
    margin-bottom: 24px;
    max-width: 420px;
  }

  .add-file-btn {
    min-width: 120px;
  }
}
</style>
<style lang="scss">
.rename-modal {
  :deep(.arco-modal) {
    border-radius: 16px;
    overflow: hidden;
  }
  .arco-modal-header {
    border-bottom: none;
  }
  .arco-modal-footer {
    border-top: none;
  }
  .arco-modal-header .arco-modal-title {
    font-size: 18px;
    font-weight: 600;
    line-height: 1.2;
  }
  .rename-content {
    p {
      margin-bottom: 12px;
      text-align: left;
      font-weight: 500;
      line-height: 21px;
    }
    .arco-input-wrapper {
      border-radius: 8px;
      background-color: var(--color-neutral-2);
    }
  }
}
</style>
