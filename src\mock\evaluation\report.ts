import Mock from 'mockjs'
import { defineMock } from '../_base'
import { getDelayTime, resultSuccess } from '../_utils'

const data = Mock.mock({
  'list|5-10': [
    {
      id: '@id',
      name: '@ctitle(5, 10)',
      'type|1': ['对话类', '工作流类'],
      taskId: '@id',
      taskName: '@ctitle(5, 10)',
      datasetId: '@id',
      datasetName: '@ctitle(5, 10)',
      'evaluationType|1': ['auto', 'manual'],
      score: '@float(60, 100, 2, 2)',
      description: '@csentence(10, 20)',
      content: {
        summary: {
          totalScore: '@float(60, 100, 2, 2)',
          'metrics|3-5': [
            {
              name: '@ctitle(3, 5)',
              score: '@float(60, 100, 2, 2)',
              weight: '@float(0, 1, 2, 2)'
            }
          ],
          passRate: '@float(60, 100, 2, 2)'
        },
        'details|5-10': [
          {
            input: '@csentence(10, 20)',
            output: '@csentence(20, 50)',
            score: '@float(60, 100, 2, 2)',
            'metrics|3-5': [
              {
                name: '@ctitle(3, 5)',
                score: '@float(60, 100, 2, 2)',
                comment: '@csentence(10, 20)'
              }
            ]
          }
        ],
        analysis: {
          'problems|2-4': [
            {
              type: '@ctitle(3, 5)',
              count: '@integer(1, 10)',
              'examples|1-3': ['@csentence(10, 20)']
            }
          ],
          'suggestions|3-5': ['@csentence(10, 20)']
        }
      },
      createUserString: '@cname',
      createTime: '@datetime',
      updateUserString: '@cname',
      updateTime: '@datetime'
    }
  ]
})

export default defineMock([
  {
    url: '/evaluation/report',
    method: 'get',
    timeout: getDelayTime(),
    response: ({ query }) => {
      return resultSuccess({
        list: data.list,
        total: data.list.length
      })
    }
  },
  {
    url: '/evaluation/report/:id',
    method: 'get',
    timeout: getDelayTime(),
    response: ({ query }) => {
      const item = data.list.find((item) => item.id === query.id) || data.list[0]
      return resultSuccess(item)
    }
  },
  {
    url: '/evaluation/report/:id/compare/:targetId',
    method: 'get',
    timeout: getDelayTime(),
    response: ({ query }) => {
      const source = data.list.find((item) => item.id === query.id) || data.list[0]
      const target = data.list.find((item) => item.id === query.targetId) || data.list[1]
      const diff = {
        scoreDiff: target.score - source.score,
        metricsDiff: source.content.summary.metrics.map((metric, index) => ({
          name: metric.name,
          sourceDiff: metric.score - source.content.summary.totalScore,
          targetDiff: target.content.summary.metrics[index]?.score - target.content.summary.totalScore
        })),
        problemsDiff: source.content.analysis.problems.map((problem, index) => ({
          type: problem.type,
          sourceCount: problem.count,
          targetCount: target.content.analysis.problems[index]?.count || 0
        }))
      }
      return resultSuccess({
        source,
        target,
        diff
      })
    }
  }
])
