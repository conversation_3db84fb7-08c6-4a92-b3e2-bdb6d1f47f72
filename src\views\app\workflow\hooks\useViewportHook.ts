import { type Connection, type GraphNode, useVueFlow } from '@vue-flow/core'

import { findMaxXY } from '../utils/props'
import { NodeType } from '../types/node'

import useEdgeHook from './useEdgeHook'
import useNodeHook from './useNodeHook'
const state: {
  dragDom: Ref<HTMLElement | undefined>
  copyDom: Ref<HTMLElement | undefined>
  parentDom: Ref<HTMLElement | undefined>
  draggedType: Ref<NodeType | undefined>
  draggedInfo: Ref<any>
  isDragOver: Ref<boolean>
  isDragging: Ref<boolean>
  paddingDrop: Ref<boolean>
  appId: string
  instance: any
} = {
  dragDom: ref(),
  copyDom: ref(),
  parentDom: ref(),
  draggedType: ref(),
  draggedInfo: ref(),
  isDragOver: ref(false),
  isDragging: ref(false),
  paddingDrop: ref(false),
  appId: '',
  instance: null
}

export default function useViewportHook(instance?, appId?) {
  if (instance) state.instance = instance
  if (appId) state.appId = appId

  const { dragDom, copyDom, parentDom, draggedType, draggedInfo, isDragOver, isDragging, paddingDrop } = state
  const { findNode, updateEdge, screenToFlowCoordinate, getEdges, getNodes } = useVueFlow(state.appId)
  const { addNodesFn, onNodeClick } = useNodeHook(state.instance, state.appId)
  const { addEdgesFn } = useEdgeHook(state.instance, state.appId)

  /**
   * 画布节点拖拽事件
   */
  watch(isDragging, (dragging) => {
    document.body.style.userSelect = dragging || paddingDrop.value ? 'none' : ''
  })

  function onDragStart(event, info, type: NodeType, opType: 'click' | 'drag') {
    if (opType === 'click') {
      if (dragDom.value) onDragEnd()
      paddingDrop.value = true
      dragDom.value = event.currentTarget as HTMLElement
      parentDom.value = dragDom.value.parentElement!

      document.onclick = (e) => {
        if (!dragDom.value) return false
        onDragEnd()
      }
    }

    if (event.dataTransfer) {
      event.dataTransfer.setData('application/vueflow', type)
      event.dataTransfer.effectAllowed = 'move'
    }
    draggedInfo.value = info
    draggedType.value = type
    isDragging.value = true

    document.addEventListener('drop', onDragEnd)
  }

  function onMousemove(e) {
    if (!dragDom.value) return false
    if (!copyDom.value) {
      copyDom.value = parentDom.value?.insertBefore(
        dragDom.value.cloneNode(true),
        dragDom.value.nextSibling
      ) as HTMLElement
      copyDom.value!.style.cursor = 'move'
      copyDom.value!.style.position = 'fixed'
      copyDom.value!.style.maxWidth = dragDom.value.clientWidth + 'px'
      copyDom.value!.style.borderStyle = 'solid'
      copyDom.value!.style.borderWidth = '1px'
      copyDom.value!.style.borderColor = '#dfe4fd'
    }

    onDragOver(e)
    if (copyDom.value) {
      // 移动当前元素
      copyDom.value!.style.opacity = '0.8'
      copyDom.value!.style.left = e.clientX + 'px'
      copyDom.value!.style.top = e.clientY + 'px'
    }
  }

  function onDragOver(event) {
    event.preventDefault()

    if (draggedType.value) {
      isDragOver.value = true

      if (event.dataTransfer) {
        event.dataTransfer.dropEffect = 'move'
      }
    }
  }

  function onDragLeave() {
    isDragOver.value = false

    if (!paddingDrop.value) return false
    copyDom.value && (copyDom.value!.style.opacity = '0')
  }

  function onDragEnd() {
    resetDragStatus()
    isDragging.value = false
    isDragOver.value = false
    draggedType.value = void 0
  }

  /**
   * Handles the drop event.
   *
   * @param {DragEvent} event
   */
  async function onDrop(
    event: DragEvent,
    type: 'drag' | 'click',
    cb?: (arg: { type: NodeType; [key: string]: any }[]) => GraphNode[]
  ) {
    if (type === 'click' && !paddingDrop.value) return false
    const position = screenToFlowCoordinate({
      x: event.clientX,
      y: event.clientY
    })
    const addNode = { type: draggedType.value!, position }
    if (draggedType.value === NodeType.工具) {
      const { icon, label, name, provider_id, provider_name, provider_type } = draggedInfo.value
      Object.assign(addNode, {
        label,
        name,
        icon,
        provider_id,
        provider_name,
        provider_type
      })
    }

    if (cb) {
      const [target] = cb!([addNode])
      await nextTick()
      onNodeClick(target)
    }
    paddingDrop.value && onDragEnd()
    paddingDrop.value = false
  }

  function resetDragStatus() {
    copyDom.value &&
      parentDom.value &&
      parentDom.value.contains(copyDom.value) &&
      parentDom.value!.removeChild(copyDom.value)
    dragDom.value = void 0
    copyDom.value = void 0
    parentDom.value = void 0
    paddingDrop.value = false
  }

  async function insertNodeClick(e, info, type, parent?, parentType?: 'node' | 'edge' | 'next-step-add') {
    switch (type) {
      case 'node': {
        if (['node', 'next-step-add'].includes(parentType!)) {
          const sourceNode = parent
          const isChildNode = !!sourceNode.parentNode

          const position = {
            x: sourceNode.position.x + sourceNode.dimensions.width + 80,
            y: sourceNode.position.y
          }
          const addNode: { type: NodeType; [key: string]: any } = {
            type: info.value as NodeType,
            position
          }

          /**
           * @description 嵌套节点处理
           * 1、嵌套节点内部节点需要添加 parentNode 属性指向容器节点的 id
           * 2、嵌套节点内部节点拖拽不能超出容器范围，添加 extent 属性可以限制其拖拽范围
           */
          if (isChildNode) {
            addNode.parentNode = sourceNode.parentNode
            addNode.extent = 'parent'
          }

          const [targetNode] = addNodesFn([addNode])
          // 因为对changes事件做了拦截处理，所以此处需要等待节点添加完毕再添加线
          await nextTick()
          const addEdge: any = {
            source: sourceNode.id,
            target: targetNode.id
          }

          /**
           * @description 嵌套节点线处理
           * 1、嵌套节点内部节点之间连线需要提高zIndex层级
           */
          if (isChildNode) {
            addEdge.zIndex = 1001
          }

          if (info.sourceHandle) Object.assign(addEdge, { sourceHandle: info.sourceHandle })
          addEdgesFn([addEdge])
          translationPosition(sourceNode, 'node', 1, targetNode)
          await nextTick()
          onNodeClick(targetNode)
        } else if (parentType === 'edge') {
          const oldEdge = parent
          const sourceNode = findNode(oldEdge.source)!
          const isChildNode = !!sourceNode.parentNode
          const oldTargetNode = findNode(oldEdge.target)!
          const position = {
            x: sourceNode.position.x + sourceNode.dimensions.width + 80,
            y: sourceNode.position.y
          }
          const addNode: { type: NodeType; [key: string]: any } = {
            type: info.value as NodeType,
            position
          }

          /**
           * @description 嵌套节点处理
           * 1、嵌套节点内部节点需要添加 parentNode 属性指向容器节点的 id
           * 2、嵌套节点内部节点拖拽不能超出容器范围，添加 extent 属性可以限制其拖拽范围
           */
          if (isChildNode) {
            addNode.parentNode = sourceNode.parentNode
            addNode.extent = 'parent'
          }

          const [targetNode] = addNodesFn([addNode])
          // 因为对changes事件做了拦截处理，所以此处需要等待节点添加完毕再添加线
          await nextTick()
          const updateEdgeInfo: Connection = {
            source: sourceNode.id,
            sourceHandle: oldEdge.sourceHandle,
            target: targetNode.id,
            targetHandle: `${targetNode.id}-target-handle`
          }
          updateEdge(oldEdge, updateEdgeInfo)
          const addEdge2: any = {
            source: targetNode.id,
            target: oldTargetNode.id
          }

          /**
           * @description 嵌套节点线处理
           * 1、嵌套节点内部节点之间连线需要提高zIndex层级
           */
          if (isChildNode) {
            addEdge2.zIndex = 1001
          }
          if (targetNode.data.type === NodeType.条件分支) {
            Reflect.set(addEdge2, 'sourceHandle', 'true')
          } else if (targetNode.data.type === NodeType.问题分类) {
            const { classes } = targetNode.data
            Reflect.set(addEdge2, 'sourceHandle', classes[0].id)
          }
          addEdgesFn([addEdge2])
          translationPosition(sourceNode, 'edge', 1, targetNode)
          await nextTick()
          onNodeClick(targetNode)
        }
        break
      }
      case 'tool': {
        if (['node', 'next-step-add'].includes(parentType!)) {
          const sourceNode = parent
          const isChildNode = !!sourceNode.parentNode
          const position = {
            x: sourceNode.position.x + sourceNode.dimensions.width + 80,
            y: sourceNode.position.y
          }
          const { icon, label, name, provider_id, provider_name, provider_type } = info
          const addNode: { type: NodeType; [key: string]: any } = {
            type: NodeType.工具,
            label,
            name,
            icon,
            position,
            provider_id,
            provider_name,
            provider_type
          }

          /**
           * @description 嵌套节点处理
           * 1、嵌套节点内部节点需要添加 parentNode 属性指向容器节点的 id
           * 2、嵌套节点内部节点拖拽不能超出容器范围，添加 extent 属性可以限制其拖拽范围
           */
          if (isChildNode) {
            addNode.parentNode = sourceNode.parentNode
            addNode.extent = 'parent'
          }

          const [targetNode] = addNodesFn([addNode])
          await nextTick()
          const addEdge: any = {
            source: sourceNode.id,
            target: targetNode.id
          }

          /**
           * @description 嵌套节点线处理
           * 1、嵌套节点内部节点之间连线需要提高zIndex层级
           */
          if (isChildNode) {
            addEdge.zIndex = 1001
          }

          if (info.sourceHandle) Object.assign(addEdge, { sourceHandle: info.sourceHandle })
          addEdgesFn([addEdge])
          translationPosition(sourceNode, 'node', 1, targetNode)
          await nextTick()
          onNodeClick(targetNode)
        } else {
          const oldEdge = parent
          const sourceNode = findNode(oldEdge.source)!
          const isChildNode = !!sourceNode.parentNode
          const oldTargetNode = findNode(oldEdge.target)!
          const position = {
            x: sourceNode.position.x + sourceNode.dimensions.width + 80,
            y: sourceNode.position.y
          }
          const { icon, label, name, provider_id, provider_name, provider_type } = info
          const addNode: { type: NodeType; [key: string]: any } = {
            type: NodeType.工具,
            label,
            name,
            icon,
            position,
            provider_id,
            provider_name,
            provider_type
          }

          /**
           * @description 嵌套节点处理
           * 1、嵌套节点内部节点需要添加 parentNode 属性指向容器节点的 id
           * 2、嵌套节点内部节点拖拽不能超出容器范围，添加 extent 属性可以限制其拖拽范围
           */
          if (isChildNode) {
            addNode.parentNode = sourceNode.parentNode
            addNode.extent = 'parent'
          }

          const [targetNode] = addNodesFn([addNode])
          await nextTick()
          const updateEdgeInfo: Connection = {
            source: sourceNode.id,
            sourceHandle: oldEdge.sourceHandle,
            target: targetNode.id,
            targetHandle: `${targetNode.id}-target-handle`
          }

          updateEdge(oldEdge, updateEdgeInfo)
          const addEdge2: any = {
            source: targetNode.id,
            target: oldTargetNode.id
          }

          /**
           * @description 嵌套节点线处理
           * 1、嵌套节点内部节点之间连线需要提高zIndex层级
           */
          if (isChildNode) {
            addEdge2.zIndex = 1001
          }

          addEdgesFn([addEdge2])
          translationPosition(sourceNode, 'edge', 1, targetNode)
          await nextTick()
          onNodeClick(targetNode)
        }
        break
      }
    }
  }

  /**
   * 添加节点处理后续所有节点的位置
   */
  function translationPosition(source: GraphNode, type: 'edge' | 'node', level: number, target?: GraphNode) {
    nextTick(() => {
      switch (type) {
        case 'edge': {
          const source_next_edges = getEdges.value.filter((edge) => edge.source === source.id)

          if (Array.isArray(source_next_edges) && source_next_edges.length > 1 && target) {
            const brother_node_ids = source_next_edges.map((edge) => edge.target)
            const brother_nodes = brother_node_ids.map((id) => findNode(id)!)
            const max_y_node = brother_nodes.reduce((max, node) => {
              return node.position.y > max.position.y ? node : max
            })
            const { x, y } = max_y_node.position
            target!.position.x = x
            target!.position.y = y + max_y_node.dimensions.height + 20
          }

          source_next_edges.forEach((edge, index) => {
            const next_node = findNode(edge.target)
            if (next_node) {
              const { x } = level === 1 ? source.position : next_node.position
              Object.assign(next_node.position, {
                x: x + source.dimensions.width + 80
              })
              translationPosition(next_node, type, level + 1)
            }
          })
          break
        }
        case 'node': {
          const source_next_edges = getEdges.value.filter((edge) => edge.source === source.id)

          if (Array.isArray(source_next_edges) && source_next_edges.length > 1) {
            const brother_node_ids = source_next_edges.map((edge) => edge.target)
            const brother_nodes = brother_node_ids.map((id) => findNode(id)!)
            const max_y_node = brother_nodes.reduce((max, node) => {
              return node.position.y > max.position.y ? node : max
            })
            const { x, y } = max_y_node.position
            target!.position.x = x
            target!.position.y = y + max_y_node.dimensions.height + 20
          }

          break
        }
      }
    })
  }

  /**
   * 整理节点
   */
  async function arrangeViewport() {
    const processed_id: string[] = []

    function deepLevel(
      nodes,
      column_num,
      is_inner: boolean,
      arrangeInfo?: {
        rowInfo: { [key: string]: { height: number; first_y: number; max_y: number } }
        columnInfo: { [key: string]: { end_x: number } }
        row_level_max: { first_y: number; column: number }[]
      }
    ) {
      return new Promise((deepResolve) => {
        const rowInfo = arrangeInfo?.rowInfo || {}
        const columnInfo = arrangeInfo?.columnInfo || {}
        const row_level_max = arrangeInfo?.row_level_max || []

        function getCenterLine(node_info, column, index) {
          return new Promise((resolve) => {
            const { id, level, height, node } = node_info
            if (processed_id.includes(id)) return resolve([])

            processed_id.push(id)

            if (!rowInfo[`row_${level}_${column}`]) {
              const last_level_row_info = rowInfo[`row_${level - 1}_${column}`]
              const first_y = index === 0 ? (is_inner ? 68 : 0) : last_level_row_info.max_y
              rowInfo[`row_${level}_${column}`] = {
                height: 0,
                first_y: first_y,
                max_y: first_y
              }
            }

            const now_row_info = rowInfo[`row_${level}_${column}`]
            // console.log(
            //   `[ ${node.data.title}_${node.id} level: ${level} column: ${column} index: ${index} now_row_info.max_y ] >`,
            //   now_row_info.max_y,
            // );
            if (index === 0) {
              node.position.y = is_inner ? 68 : 0
              now_row_info.max_y = node.position.y + height
              now_row_info.height = height
            } else {
              node.position.y = now_row_info.max_y + 20
              now_row_info.max_y = node.position.y + height
              now_row_info.height = height
            }
            // console.log('[ node.position.y ] >', node.position.y);

            const next_edges = getEdges.value.filter((e) => e.source === id)
            const next_nodes = next_edges.map((e) => {
              const n = e.targetNode
              return {
                level,
                ...n.position,
                ...n.dimensions,
                title: n.data.title,
                id: n.id,
                node: n,
                column: column + 1
              }
            })

            // console.log('[ get next_nodes ] >', next_nodes);

            resolve({
              node,
              next_nodes
            })
          })
        }

        const last_column_end_x = column_num === 0 ? 0 : columnInfo[`column${column_num - 1}`].end_x
        const column_max_width = Math.max(...nodes.map((n) => n.width))
        const middle_x = last_column_end_x ? last_column_end_x + 80 + column_max_width / 2 : column_max_width / 2

        const now_column_info = {
          end_x: 0
        }
        columnInfo[`column${column_num}`] = now_column_info

        const task_list_map: Map<string, Promise<any>> = new Map()
        nodes.forEach((n, idx) => {
          const { node, width } = n
          node.position.x = middle_x - width / 2 + (is_inner ? 24 : 0)
          if (middle_x + width / 2 > now_column_info.end_x) {
            now_column_info.end_x = middle_x + width / 2
          }

          if (!row_level_max[n.level]) {
            row_level_max[n.level] = {
              first_y: 0,
              column: column_num
            }
          }

          task_list_map.set(`${node.data.title}_${node.id}`, getCenterLine(n, column_num, idx))
        })

        // console.log('[ task_list_map ] >', task_list_map);
        Promise.allSettled(task_list_map.values()).then(async (res) => {
          const next_column = column_num + 1
          const next: any[] = []
          res.forEach((item) => {
            if (item.status === 'fulfilled') {
              const { next_nodes } = item.value
              if (Array.isArray(next_nodes) && next_nodes.length > 0) {
                next.push(...next_nodes)
              }
            } else {
              console.log('[ 报错内容 ]', item.reason)
            }
          })

          if (next.length) {
            await deepLevel(next, next_column, is_inner, {
              rowInfo,
              columnInfo,
              row_level_max
            })
          } else {
            for (const key in rowInfo) {
              const rowInfoArr = key.split('_')
              const level = rowInfoArr[1]
              const column = +rowInfoArr[2]
              const { first_y } = rowInfo[key]

              if (key.startsWith(`row_${level}`)) {
                const now_value = row_level_max[level]?.first_y || (is_inner ? 68 : 0)
                if (now_value < first_y) {
                  row_level_max[level] = {
                    first_y: first_y + 20,
                    column
                  }
                }
              }
              // console.log(`[ row_level_max[level] ] >`, structuredClone(row_level_max));
            }
          }

          deepResolve(true)

          const level_offset: number[] = []
          nodes.forEach(({ level, column, node }) => {
            const { first_y, column: max_y_column } = row_level_max[level] || {}
            // console.log(
            //   `[ before ${node.data.title}_${node.id} node.position.y: ${node.position.y} first_y: ${first_y} max_y_column: ${max_y_column} column: ${column} ]`,
            // );
            if (node.position.y < first_y && max_y_column !== column) {
              if (!level_offset[level]) {
                level_offset[level] = first_y - node.position.y
              }
            }
          })

          nodes.forEach(({ level, column, node }) => {
            const { column: max_y_column } = row_level_max[level] || {}
            if (
              typeof level_offset[level] === 'number' &&
              max_y_column !== column &&
              ![(NodeType.开始, NodeType.迭代开始, NodeType.循环开始)].includes(node.data.type)
            ) {
              node.position.y += level_offset[level]
            }
          })
        })
      })
    }

    const has_source_nodes: string[] = []
    getEdges.value.forEach((edge) => {
      has_source_nodes.push(edge.target)
    })

    const out_no_source_nodes: any[] = []
    const sameParentInnerNodesMap: Map<string, any[]> = new Map()
    getNodes.value.forEach((n) => {
      if (has_source_nodes.includes(n.id)) return false

      if (!n.parentNode) {
        out_no_source_nodes.push(n)
      } else {
        if (!sameParentInnerNodesMap.has(n.parentNode)) {
          sameParentInnerNodesMap.set(n.parentNode, [])
        }
        const parentNodes = sameParentInnerNodesMap.get(n.parentNode)!
        parentNodes.push(n)
      }
    })

    const inner_nodes_arr = Array.from(sameParentInnerNodesMap.values())
    const inner_nodes_deal = inner_nodes_arr.map((inner_nodes) => {
      return deepLevel(
        inner_nodes.map((n, index) => {
          return {
            level: index,
            column: 0,
            ...n.position,
            ...n.dimensions,
            title: n.data.title,
            id: n.id,
            node: n
          }
        }),
        0,
        true
      )
    })

    // 如果涉及到存在有迭代/循环这种容器节点的，节点内部整理完成后有可能影响到容器本身的宽高, 所以需要整理完成后，手动更新容器节点的宽高
    Promise.allSettled(inner_nodes_deal).then(async () => {
      const inner_nodes_parents = Array.from(sameParentInnerNodesMap.keys())
      inner_nodes_parents.forEach(async (id, index) => {
        const parent_node = findNode(id)
        if (!parent_node) return
        // 容器节点（迭代/循环）的最小尺寸
        const minDimension = {
          width: 258,
          height: 152
        }
        const list: { x: number; y: number; width: number; height: number }[] = []
        for (let i = 0; i < getNodes.value.length; i++) {
          const n = getNodes.value[i]
          if (!n.parentNode || n.parentNode !== parent_node.id) continue

          list.push({
            ...n.dimensions,
            ...n.position
          })
        }
        let { max_x, max_y } = findMaxXY(list)
        const canvasOuterWidth = 24 + 8
        const canvasOuterHeight = 24 + 8
        max_x = Math.max(max_x, 258)
        max_y = Math.max(max_y, 152)

        minDimension.width = max_x + canvasOuterWidth
        minDimension.height = max_y + canvasOuterHeight

        const { width, height } = parent_node!.dimensions
        if (width < minDimension.width || height < minDimension.height) {
          parent_node!.dimensions.width = minDimension.width - 0.1
          parent_node!.dimensions.height = minDimension.height - 0.1
        }
      })

      await nextTick()

      deepLevel(
        out_no_source_nodes.map((n, index) => {
          return {
            level: index,
            column: 0,
            ...n.position,
            ...n.dimensions,
            title: n.data.title,
            id: n.id,
            node: n
          }
        }),
        0,
        false
      )
    })
  }

  return {
    draggedType,
    isDragOver,
    isDragging,
    onDragStart,
    onDragLeave,
    onDragOver,
    onMousemove,
    onDragEnd,
    onDrop,
    insertNodeClick,
    arrangeViewport
  }
}
