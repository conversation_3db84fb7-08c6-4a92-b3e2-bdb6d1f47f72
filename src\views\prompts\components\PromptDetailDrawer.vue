<template>
  <a-drawer
    v-model:visible="visible"
    title="提示词详情"
    width="650px"
    :footer="false"
    :unmount-on-close="true"
    @cancel="handleCancel"
  >
    <a-descriptions :data="descriptionsData" :column="1" :label-style="{ width: '100px' }" bordered />

    <a-divider>提示词内容</a-divider>
    <div class="prompt-content">
      <a-typography-paragraph class="content-text">
        {{ promptDetail.content }}
      </a-typography-paragraph>
      <a-button class="copy-btn" type="outline" size="small" @click="copyContent(promptDetail.content)">
        <template #icon><icon-copy /></template>
        <template #default>复制</template>
      </a-button>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { getUsingGet } from '@/apis/prompts'
import type { Prompt } from '@/apis/prompts/type'
import { Message } from '@arco-design/web-vue'

defineOptions({ name: 'PromptDetailDrawer' })

const props = defineProps<{
  detailVisible: boolean
  promptId?: string | null
}>()

const emit = defineEmits<{
  'hide-drawer': []
}>()

const visible = ref(props.detailVisible)
const promptDetail = reactive<Partial<Prompt>>({})

const descriptionsData = computed(() => {
  return [
    {
      label: '名称',
      value: promptDetail.name || '-'
    },
    {
      label: '类型',
      value: promptDetail.type === 'scene' ? '场景提示词' : promptDetail.type === 'block' ? '提示词块' : '-'
    },
    {
      label: '状态',
      value: promptDetail.status === 1 ? '草稿' : promptDetail.status === 2 ? '已发布' : '-'
    },
    {
      label: '标签',
      value: promptDetail.tags || '-'
    },
    {
      label: '描述',
      value: promptDetail.description || '-'
    },
    {
      label: '创建人',
      value: promptDetail.createdBy || '-'
    },
    {
      label: '创建时间',
      value: promptDetail.createTime || '-'
    },
    {
      label: '修改人',
      value: promptDetail.updatedBy || '-'
    },
    {
      label: '修改时间',
      value: promptDetail.updateTime || '-'
    }
  ]
})

// 获取详情
const getDetail = async () => {
  if (props.promptId) {
    try {
      const res = await getUsingGet(props.promptId)
      if (res.data) {
        Object.assign(promptDetail, res.data)
      }
    } catch (error) {
      console.error('获取提示词详情失败', error)
    }
  }
}

// 复制内容到剪贴板
const copyContent = (content?: string) => {
  if (!content) return

  navigator.clipboard.writeText(content).then(
    () => {
      Message.success('复制成功')
    },
    () => {
      Message.error('复制失败')
    }
  )
}

// 取消
const handleCancel = () => {
  emit('hide-drawer')
}

// 监听抽屉显示状态
watch(
  () => props.detailVisible,
  (val) => {
    visible.value = val
    if (val && props.promptId) {
      getDetail()
    }
  }
)
</script>

<style scoped lang="scss">
.prompt-content {
  position: relative;
  padding: 16px;
  background-color: var(--color-fill-2);
  border-radius: 4px;
  margin-top: 16px;

  .content-text {
    margin-bottom: 0;
    white-space: pre-wrap;
    word-break: break-word;
  }

  .copy-btn {
    position: absolute;
    top: 16px;
    right: 16px;
  }
}
</style>
