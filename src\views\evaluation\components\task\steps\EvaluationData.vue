<template>
  <div class="evaluation-data">
    <AiForm ref="formRef" v-model="localFormData" :columns="columns">
      <template #fileId>
        <div class="w-full flex flex-col">
          <div>
            <AiFileUpload
              v-model="localFormData.fileId"
              inputsKey="fileId"
              :tips="'请上传 docx、pdf、xlsx、png、png 等格式文件，最多1个文件，文件大小5M以内'"
              :accept="'.docx,.pdf,.xlsx,.png,.jpg'"
              :remove-file-fun="removeFile"
              @uploadFile="onUploadFile"
            />
          </div>
        </div>
      </template>
    </AiForm>

    <div class="action-buttons">
      <a-space>
        <a-button @click="handlePrev">上一步</a-button>
        <a-button type="primary" @click="runEvaluation">开始生成</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { addTask } from '@/apis/evaluation/task'
import { EvalTaskSaveDTO } from '@/apis/evaluation/task-types'
import { uploadFile } from '@/apis/upload'
import { ColumnItem } from '@/components/AiForm'

const props = defineProps({
  formData: {
    type: Object as () => EvalTaskSaveDTO,
    required: true
  }
})

const emit = defineEmits(['prev', 'saveForm'])

const router = useRouter()
const route = useRoute()

const localFormData = ref({
  fileId: ''
})

const defaultEvalRuleIds = ref([1, 2, 3, 4])
const customEvalRules = ref('')
const evalTags = ref([])
const columns: ColumnItem[] = [
  {
    label: '上传评测数据',
    field: 'fileId',
    span: 24,
    rules: [{ required: true, message: '请上传评测数据' }]
  }
]

watchEffect(() => {
  if (props.formData) {
    localFormData.value = { ...localFormData.value, ...props.formData }
  }
})

// 处理文件上传
const onUploadFile = async (
  file: File,
  onProgress?: (percent: number) => void,
  key?: string,
  transfer_method?: string,
  callback?: (res) => void
) => {
  const response = await uploadFile(file, (percent) => {
    onProgress && onProgress(percent)
  })
  localStorage.fileId = response.id
  if (callback) {
    callback(response)
  }
}

const removeFile = (fileItem, key) => {
  localStorage.fileId = undefined
}
const handlePrev = () => {
  emit('saveForm', localFormData.value)
  emit('prev')
}

const runEvaluation = async () => {
  const params = JSON.parse(
    JSON.stringify({
      ...props.formData,
      ...localFormData.value,
      taskType: Number(route.query.taskType),
      status: 0
    })
  )
  const res = await addTask(params)
  if (res.isSuccess) {
    router.go(-1)
  }
}
</script>

<style scoped lang="scss">
.evaluation-data {
  width: 1000px;
  height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  .arco-form {
    flex: 1;
  }
}

/* 底部按钮 */
.action-buttons {
  text-align: right;
}
</style>
