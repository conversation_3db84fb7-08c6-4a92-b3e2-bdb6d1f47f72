<template>
  <div class="flex flex-col mt-4 mb-4 pb-1">
    <a-tabs default-active-key="1">
      <a-tab-pane key="1" title="结果">
        <AiMarkdown :content="resultText" />
      </a-tab-pane>
      <a-tab-pane key="2" title="详情">
        <Code :title="'输出'" :inputs="JSON.stringify(outputs || {})" />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
import Code from './code.vue'

defineOptions({
  name: 'Markdown'
})
const props = defineProps<{
  title?: string
  inputs: string
  outputs: string
  resultText: string
}>()
const text = ref('')
</script>

<style scoped lang="scss">
.code {
  background-color: #c8ceda24;
  border: 1px solid #d0d5dc;
  border-radius: 5px;

  &-header {
    font-size: 12px;
    color: #354052;
    font-weight: 600;
  }
}
</style>
