<template>
  <div class="var-list-container">
    <!--变量的下拉List-->
    <div v-for="(group, groupIndex) in varList" :key="groupIndex" class="w-full var-list-item">
      <!--分组的title-->
      <a-typography-text class="group-title">{{ group.title }}</a-typography-text>
      <div class="pop-var-item-container">
        <a-space direction="vertical" fill>
          <!--hover:bg-state-base-hover-->
          <div
            v-for="(option, optionIndex) in group.vars"
            :key="optionIndex"
            class="pop-var-item flex justify-between pr-[18px] relative h-6 w-full cursor-pointer items-center rounded-md pl-3"
            @click="selectValue(group, option)"
          >
            <span>{{ option.variable }}</span>
            <span>{{ option.type }}</span>
          </div>
        </a-space>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
// 变量弹框的list
import { useNodesStore } from '@/stores/modules/workflow/nodes'

const emits = defineEmits(['handleSelectVar'])
const varList = ref<any[]>([]) // 变量varList从store中获取
const nodesStore = useNodesStore()
onMounted(() => {
  varList.value = nodesStore.parentNodesVarList
})

const selectValue = (group, option) => {
  emits('handleSelectVar', group, option)
}
</script>

<style scoped lang="scss">
.var-list-container {
  max-height: 300px;
  overflow: auto;
  width: 400px;

  .group-title {
    font-size: 16px;
    margin-top: 4px;
    margin-bottom: 4px;
    display: inline-block;
  }

  .pop-var-item {
    line-height: 32px;
    height: 32px;

    &:hover {
      background: rgba(203, 206, 218, 0.4);
    }
  }
}
</style>
