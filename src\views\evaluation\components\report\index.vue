<template>
  <AiTable
    row-key="id"
    :data="dataList || []"
    :columns="columns"
    :loading="loading"
    :scroll="{ x: '100%', y: '100%' }"
    :pagination="pagination"
    :disabled-tools="['size']"
    :disabled-column-keys="['name']"
    @refresh="search"
  >
    <template #toolbar-left>
      <a-space>
        <a-input-search v-model="queryForm.reportName" placeholder="搜索名称" allow-clear @search="search" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </a-space>
    </template>
    <template #overallScore="{ record }">
      <span>{{ Number(record.overallScore)?.toFixed(0) || '-' }}</span>
    </template>
    <template #action="{ record }">
      <a-space>
        <!-- <a-link title="评分" @click="onCompare(record)">评分</a-link> -->
        <a-link title="详情" @click="onDetail(record)">详情</a-link>
      </a-space>
    </template>
  </AiTable>
  <ReportDetailDrawer v-if="detailVisible" :detailVisible="detailVisible" :reportId="reportId" @hide-drawer="close" />
</template>

<script setup lang="ts">
import ReportDetailDrawer from './ReportDetailDrawer.vue'
import { getReportPage } from '@/apis/evaluation/report'
import { useTable } from '@/hooks'
import { EvalReportPageQuery, RIPageEvalReport } from '@/apis/evaluation/report-type'

defineOptions({ name: 'ReportTab' })

const queryForm = reactive<EvalReportPageQuery>({})
const detailVisible = ref(false)
const reportId = ref<string>()
const {
  tableData: dataList,
  loading,
  pagination,
  search
} = useTable<RIPageEvalReport>((page) => getReportPage({ model: queryForm, ...page }), { immediate: true })

const columns = [
  { title: '报告名称', dataIndex: 'reportName', ellipsis: true, tooltip: true },
  { title: '关联任务', dataIndex: 'evalTask[taskName]', ellipsis: true, tooltip: true, minWidth: 200 },
  { title: '评测对象', dataIndex: 'evalTaskApp[appName]', ellipsis: true, tooltip: true, minWidth: 220 },
  { title: '总体评分', dataIndex: 'overallScore', slotName: 'overallScore', width: 100 },
  { title: '生成时间', dataIndex: 'createTime', width: 180 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 80,
    align: 'left'
  }
]

// 重置
const reset = () => {
  queryForm.reportName = undefined
  search()
}

// 详情
const onDetail = (record) => {
  detailVisible.value = true
  reportId.value = record.id
}

const close = () => {
  reportId.value = undefined
  detailVisible.value = false
}
</script>

<style scoped lang="scss">
.report-tab {
  height: 100%;
}
</style>
