<template>
  <a-modal
    v-model:visible="visible"
    title="评测集使用指南"
    :width="width >= 600 ? 600 : '100%'"
    :footer="false"
    @close="close"
  >
    <div>
      <a-typography>
        <a-typography-title :heading="6" style="margin-top: 0">对话流类评测集</a-typography-title>
        <a-typography-paragraph style="font-size: 12px">
          针对对话流型智能体应用，包含输入prompt和理想输出结果。支持Excel格式导入多条测试用例，每条测试用例包含SessionID、queryID、Input和Output字段。
        </a-typography-paragraph>
        <a-typography-paragraph style="margin-top: 8px">
          <a-button size="mini">
            <icon-download />
            下载对话类模版
          </a-button>
        </a-typography-paragraph>
        <a-typography-title :heading="6">工作流类评测集</a-typography-title>
        <a-typography-paragraph style="font-size: 12px">
          针对工作流型智能体应用，包含输入文档、参考文档和理想输出文档。支持docx、pdf、xlsx等格式，可同时上传多个参考文档。
        </a-typography-paragraph>
        <a-typography-paragraph style="margin-top: 8px">
          <a-button size="mini">
            <icon-download />
            下载工作流类模版
          </a-button>
        </a-typography-paragraph>
      </a-typography>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core'

const { width } = useWindowSize()

const visible = ref(false)

const open = () => {
  visible.value = true
}

const close = () => {
  visible.value = false
}

defineExpose({ open })
</script>
