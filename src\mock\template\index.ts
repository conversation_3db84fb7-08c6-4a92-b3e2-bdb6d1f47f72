import Mock from 'mockjs'
import { defineMock } from '../_base'
import { getDelayTime, resultSuccess } from '../_utils'

const data = Mock.mock({
  'list|5-10': [
    {
      id: '@id',
      name: '@name',
      accessKey: '@string(15, 15)',
      expireTime: '@datetime()',
      description: '@csentence(7, 10)',
      'status|1': [1, 2],
      createUserString: '@name',
      createTime: '@datetime()',
      updateUserString: '@name',
      updateTime: '@datetime()'
    }
  ]
})

export default defineMock([
  {
    url: '/template',
    method: 'get',
    timeout: getDelayTime(),
    response: ({ query }) => {
      return resultSuccess(data)
    }
  }
])
