<template>
  <a-modal
    v-model:visible="visible"
    title="添加变量"
    title-align="start"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 500 ? 500 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <AiForm ref="formRef" v-model="form" layout="vertical" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { AiForm, type ColumnItem } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'
import { createApp } from '@/apis'

const emit = defineEmits<{
  (e: 'save-success', form): void
}>()

const { width } = useWindowSize()

const dataId = ref('')
const visible = ref(false)
const isUpdate = computed(() => !!dataId.value)
const formRef = ref<InstanceType<typeof AiForm>>()

const [form, resetForm] = useResetReactive({
  label: '',
  max_length: 48,
  options: [],
  required: [],
  type: 'text-input',
  variable: ''
})
const columns = reactive<ColumnItem[]>([
  {
    label: '字段类型',
    field: 'type',
    type: 'radio-group',
    required: true,
    span: 24,
    cascader: ['name'],
    props: {
      type: 'radio',
      options: [
        { label: '文本', value: 'text-input' },
        { label: '段落', value: 'paragraph' },
        { label: '下拉选项', value: 'select' },
        { label: '数字', value: 'number' },
        { label: '单文件', value: 'file' },
        { label: '文件列表', value: 'file-list' }
      ]
    }
  },
  {
    label: '变量名称',
    field: 'variable',
    type: 'input',
    span: 24,
    required: true,
    props: {
      maxLength: 100
    }
  },
  {
    label: '显示名称',
    field: 'label',
    type: 'input',
    span: 24,
    required: true,
    props: {
      maxLength: 100
    }
  },
  {
    label: '最大长度',
    field: 'max_length',
    type: 'input-number',
    span: 24,
    required: true,
    props: {
      maxLength: 100
    }
  },
  {
    label: '',
    field: 'required',
    type: 'checkbox-group',
    span: 24,
    props: {
      type: 'checkbox',
      options: [{ label: '必填', value: 'false' }]
    }
  }
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    // await createApp(form)
    Message.success('新建成功')
    emit('save-success', form)
    return true
  } catch (error) {
    return false
  }
}

// 新增
const onAdd = () => {
  reset()
  visible.value = true
}

defineExpose({ onAdd })
</script>
