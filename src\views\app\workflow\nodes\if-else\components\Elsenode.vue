<template>
  <div class="else-node">
    <div class="else-title">
      <div class="first-title">ELSE</div>
      <div class="seconed-title">用于定义当 if 条件不满足时应执行的逻辑。</div>
    </div>
    <a-divider />
    <!-- <div style="font-weight: bold;">下一步</div> -->
  </div>
</template>
<script setup lang="ts"></script>
<style scoped lang="scss">
.else-title {
  .first-title {
    font-weight: bold;
  }
  .seconed-title {
    font-size: 12px;
    color: #676f83;
  }
}
</style>
