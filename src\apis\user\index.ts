import http from '@/utils/http'
import type { RoleType, UserListInterface } from '@/apis/user/type.ts'
const BASE_URL = 'console/api'
/**
 * 用户管理：获取用户List
 * @param params
 */
export const getUserListHttp = (params) => {
  /**
   * "id": "043b8064-6ebb-4215-a5f0-6125eadaa1f7",
   * "name": "jettech",
   * "avatar": null,
   * "avatar_url": null,
   * "email": "<EMAIL>",
   * "last_login_at": 1746601813,
   * "last_active_at": 1746603647,
   * "created_at": 1742806834,
   * "role": "owner",
   * "status": "active"
   */
  return http.get<UserListInterface>(`${BASE_URL}/workspaces/current/members`, params)
}

/**
 * 更新用户的角色
 * @param id 用户的id字段，string
 * @param params {role:string}
 */
export const updateUserRoleHttp = (id: string, params: RoleType) => {
  return http.put(`${BASE_URL}/workspaces/current/members/${id}/update-role`, params)
}

/**
 * 邀请用户
 * @param params {'emails':['<EMAIL>'],'role':'normal','language':'zh-Hans'}
 */
export const inviteUserHttp = (params: any) => {
  return http.post<any>(`${BASE_URL}/workspaces/current/members/invite-email`, params)
}

// 移出团队
// http://jettodify.jettech.com/console/api/workspaces/current/members/1d29455c-d1f2-4f63-8851-e9ec5e91e2c4
export const deleteUserRoleHttp = (id: string) => {
  return http.del(`${BASE_URL}/workspaces/current/members/${id}`)
}
