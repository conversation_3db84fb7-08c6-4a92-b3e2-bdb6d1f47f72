import { BodyType, Method } from '@/views/app/workflow/nodes/http/types'
import { AuthorizationType } from '@/views/app/workflow/constant/common'

const nodeDefault: any = {
  defaultValue: {
    variables: [],
    method: Method.get,
    url: '',
    authorization: {
      type: AuthorizationType.none,
      config: null
    },
    headers: '',
    params: '',
    body: {
      type: BodyType.none,
      data: []
    },
    timeout: {
      max_connect_timeout: 0,
      max_read_timeout: 0,
      max_write_timeout: 0
    },
    retry_config: {
      retry_enabled: true,
      max_retries: 3,
      retry_interval: 100
    }
  }
}

export default nodeDefault
