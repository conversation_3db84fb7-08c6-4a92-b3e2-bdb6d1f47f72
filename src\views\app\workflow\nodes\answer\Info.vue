<template>
  <div class="answer-config">
    <div class="config-form">
      <a-form :model="formData" layout="vertical">
        <a-form-item>
          <div class="min-item-box" :class="{ 'max-item-box': isMaximized }">
            <ContextEditor
              v-model:value="formData.answer"
              v-model:edition-type="nodeData.editionType"
              :nodeId="nodeId"
              :mode="'workflow'"
              :filterType="filterType"
              :show-jinja="true"
              :show-remove="false"
              placeholder="请输入回复内容，输入 '/' 可快速插入变量"
              @change="handleContextEditorChange"
              @edition-type-change="handleEditionTypeChange"
              @maximize-click="handleMaximizeClick"
            >
              <template #toolbarLeft>
                <span class="editor-title">回复内容</span>
              </template>
            </ContextEditor>
          </div>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import ContextEditor from '../../components/context-editor/index.vue'
import { EditionType } from '../../types/workflow'

interface Props {
  node?: any
  nodeId?: string
  readonly?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  node: () => ({}),
  readonly: false
})

const emit = defineEmits<{
  (e: 'change', data: any): void
}>()

// 响应式数据
const nodeData = ref({
  contextEditorContent: '',
  editionType: EditionType.basic
})
const formData = ref({
  answer: '',
  desc: '',
  selected: true,
  title: '直接回复',
  type: 'answer',
  variables: []
})
// 全屏状态
const isMaximized = ref(false)

const filterType = ref(['string', 'number'])

// 计算属性

// Context Editor 相关方法
const handleContextEditorChange = (value: string) => {
  formData.value.answer = value
  emit('change', formData.value)
}

const handleEditionTypeChange = (checked: boolean) => {
  nodeData.value.editionType = checked ? EditionType.jinja2 : EditionType.basic
}

const handleMaximizeClick = () => {
  isMaximized.value = !isMaximized.value
}

// 监听数据变化
watch(
  () => props.node,
  (newNode) => {
    if (newNode) {
      Object.assign(formData.value, newNode)
    }
  },
  { immediate: true, deep: true }
)
</script>

<style scoped lang="scss">
.answer-config {
  position: relative;
  height: 100%;
  .config-header {
    margin-bottom: 24px;

    .config-title {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--color-text-1);
    }

    .config-description {
      margin: 0;
      font-size: 14px;
      color: var(--color-text-3);
      line-height: 1.5;
    }
  }

  .config-form {
    height: 100%;
    // 全屏编辑器样式 - 基于web-dev项目的实现
    .min-item-box {
      position: static;
      width: 100%;
      &:not(:first-child) {
        margin-top: 8px;
      }

      &.max-item-box {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 100;
        padding: 12;
        background-color: transparent;

        &::before {
          content: '';
          box-sizing: border-box;
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: calc(100% - 60px);
          background-color: var(--color-bg-1);
          border-radius: 8px;
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }

        :deep(.toolbar-textarea) {
          height: calc(100% - 60px);
          overflow: hidden;
          position: relative;
          z-index: 1;
          border-radius: 6px;

          .textarea-input-box {
            height: calc(100% - 40px);
            overflow-y: auto;
            .rich-editor {
              height: 100% !important;
            }
            .textarea-input {
              height: 100%;

              .arco-textarea {
                height: 100%;
                resize: none;
              }
            }

            .code-mirror-box {
              height: 100%;

              :deep(.cm-editor) {
                height: 100%;
              }

              :deep(.cm-scroller) {
                overflow: auto;
              }
            }
          }
        }
      }
    }

    // 编辑器标题样式
    .editor-title {
      font-size: 12px;
      font-weight: 600;
      color: var(--color-text-2);
      background: var(--color-fill-2);
      padding: 4px 8px;
      border-radius: 4px;
    }

    .editor-container {
      width: 100%;
      border: 1px solid var(--color-border-2);
      border-radius: 6px;
      overflow: hidden;
      transition: all 0.3s ease;

      &.editor-expanded {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 80vw;
        height: 80vh;
        z-index: 1000;
        background: var(--color-bg-1);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

        .editor-content {
          height: calc(100% - 48px);

          .answer-textarea {
            height: 100%;

            :deep(.arco-textarea) {
              height: 100%;
              resize: none;
            }
          }
        }
      }

      .editor-toolbar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 12px;
        background: var(--color-fill-2);
        border-bottom: 1px solid var(--color-border-2);

        .toolbar-left {
          .editor-label {
            font-size: 12px;
            font-weight: 600;
            color: var(--color-text-2);
          }
        }

        .toolbar-right {
          display: flex;
          gap: 4px;

          .toolbar-btn {
            padding: 4px 8px;
            font-size: 12px;

            &:hover {
              background: var(--color-fill-3);
            }
          }
        }
      }

      .editor-content {
        padding: 12px;

        .answer-textarea {
          width: 100%;

          :deep(.arco-textarea) {
            width: 100%;
            border: none;
            box-shadow: none;
            background: transparent;

            &:focus {
              border: none;
              box-shadow: none;
            }
          }
        }
      }
    }
  }
}

// 全屏编辑器样式
.fullscreen-editor {
  .fullscreen-toolbar {
    display: flex;
    justify-content: flex-end;
    padding: 8px 0;
    margin-bottom: 12px;
    border-bottom: 1px solid var(--color-border-2);

    .toolbar-btn {
      padding: 4px 8px;
      font-size: 12px;

      &:hover {
        background: var(--color-fill-3);
      }
    }
  }

  .fullscreen-textarea {
    width: 100%;

    :deep(.arco-textarea) {
      width: 100%;
      border: 1px solid var(--color-border-2);
      border-radius: 6px;

      &:focus {
        border-color: var(--color-primary-6);
        box-shadow: 0 0 0 2px var(--color-primary-light-1);
      }
    }
  }

  .modal-variable-picker {
    position: absolute;
    background: var(--color-bg-1);
    border: 1px solid var(--color-border-2);
    border-radius: 6px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    max-height: 350px;
    overflow-y: auto;
    z-index: 1002;
    width: 300px;
  }
}

// 模态框样式覆盖
:deep(.arco-modal) {
  .arco-modal-header {
    border-bottom: 1px solid var(--color-border-2);
  }

  .arco-modal-body {
    padding: 20px;
  }
}
</style>
