<template>
  <div class="assistant-card p-4 rounded-lg">
    <!-- 助手图标和名称 -->
    <div class="flex items-start mb-3">
      <div class="mr-4">
        <img :src="getFileUrl(assistant.icon)" class="w-16 h-16 rounded-lg object-cover" alt="助手图标" />
      </div>
      <div class="flex-1">
        <div class="flex items-center justify-between mb-1">
          <h3 class="text-base font-medium text-gray-900">{{ assistant.name }}</h3>
          <div class="version-tag px-2 py-0.5 text-xs rounded">{{ assistant.version }}</div>
        </div>
        <div class="flex items-start">
          <span class="type-tag px-2 py-0.5 text-xs rounded mr-2">{{ assistant.type }}</span>
        </div>
      </div>
    </div>

    <!-- 助手描述 -->
    <div class="mb-4 text-sm text-gray-600 description">
      {{ assistant.description }}
    </div>

    <!-- 底部统计信息 -->
    <div class="flex justify-between text-xs text-gray-500">
      <div>
        <div>最近更新</div>
        <div>创建用例</div>
      </div>
      <div class="text-right">
        <div>{{ assistant.updateTime }}</div>
        <div>{{ assistant.count }}个</div>
      </div>
    </div>

    <!-- 使用按钮 -->
    <div class="use-button-container">
      <a-button type="primary" size="small" class="use-button" @click="handleUse">使用</a-button>
    </div>

    <!-- 更多操作按钮 -->
    <div class="absolute top-4 right-4 cursor-pointer more-icon">
      <svg width="3" height="12" viewBox="0 0 3 12" fill="none" xmlns="http://www.w3.org/2000/svg">
        <path
          d="M1.5 3C2.32843 3 3 2.32843 3 1.5C3 0.671573 2.32843 0 1.5 0C0.671573 0 0 0.671573 0 1.5C0 2.32843 0.671573 3 1.5 3Z"
          fill="#3A3F4F"
        />
        <path
          d="M1.5 7.5C2.32843 7.5 3 6.82843 3 6C3 5.17157 2.32843 4.5 1.5 4.5C0.671573 4.5 0 5.17157 0 6C0 6.82843 0.671573 7.5 1.5 7.5Z"
          fill="#3A3F4F"
        />
        <path
          d="M1.5 12C2.32843 12 3 11.3284 3 10.5C3 9.67157 2.32843 9 1.5 9C0.671573 9 0 9.67157 0 10.5C0 11.3284 0.671573 12 1.5 12Z"
          fill="#3A3F4F"
        />
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getFileUrl } from '@/utils'

interface Assistant {
  id: string
  name: string
  description: string
  icon: string
  updateTime: string
  count: string
  version: string
  type: string
}

const props = defineProps({
  assistant: {
    type: Object as () => Assistant,
    required: true
  }
})

const handleUse = () => {
  // 处理使用按钮点击事件
  console.log('使用助手:', props.assistant.id)
}
</script>

<style scoped>
.assistant-card {
  height: 100%;
  cursor: pointer;
  position: relative;
  background-color: #ffffff;
  box-shadow: 0px 1px 4px 0px rgba(0, 0, 0, 0.1);
  border: 1px solid #d9d9d9;
  transition: all 0.3s ease;
}

.assistant-card:hover {
  box-shadow: 0px 2px 8px 0px rgba(81, 71, 255, 0.2);
  border: 2px solid #aad4ff;
  margin: -1px;
}

.version-tag {
  background-color: #ffe4d3;
  color: #f07b19;
}

.type-tag {
  background-color: #efeeff;
  color: #5147ff;
}

.description {
  height: 40px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.use-button-container {
  position: absolute;
  right: 16px;
  bottom: 16px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.use-button {
  padding: 2px 12px;
  height: 24px;
  font-size: 12px;
  line-height: 20px;
}

.more-icon {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.assistant-card:hover .more-icon,
.assistant-card:hover .use-button-container {
  opacity: 1;
}
</style>
