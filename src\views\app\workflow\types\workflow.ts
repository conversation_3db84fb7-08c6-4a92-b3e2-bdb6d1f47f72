import type { Edge, Node, ViewportTransform as Viewport } from '@vue-flow/core'
import type { TransferMethod } from './app'

export enum nodeColor {
  'start' = '#f79009',
  'end' = '#f79009',
  'answer' = '#f79009',
  'llm' = '#6172f3',
  'knowledge-retrieval' = '#06aed4',
  'question-classifier' = '#06aed4',
  'if-else' = '#06aed4',
  'code' = '#2e90fa',
  'template-transform' = '#06aed4',
  'http-request' = '#875bf7',
  'variable-assigner' = '#2e90fa',
  'variable-aggregator' = '#2e90fa',
  'assigner' = '#2e90fa',
  'iteration-start' = '#06aed4',
  'iteration' = '#06aed4',
  'parameter-extractor' = '#06aed4',
  'document-extractor' = '#17b26a',
  'list-operator' = 'List Operator',
  'agent' = '#06aed4',
  'loop-start' = '#06aed4',
  'loop' = '#06aed4',
  'loop-end' = '#06aed4'
}
export enum comparisonOperator {
  'contains' = '包含',
  'not contains' = '不包含',
  'start with' = '开始是',
  'end with' = '结束是',
  'is' = '是',
  'is not' = '不是',
  'empty' = '为空',
  'not empty' = '不为空',
  'null' = '空',
  'not null' = '不为空',
  'in' = '是',
  'not in' = '不是',
  'all of' = '全部是',
  'exists' = '存在',
  'not exists' = '不存在',
  'before' = '早于',
  'after' = '晚于'
}
export enum BlockEnum {
  Start = 'start',
  End = 'end',
  Answer = 'answer',
  LLM = 'llm',
  KnowledgeRetrieval = 'knowledge-retrieval',
  QuestionClassifier = 'question-classifier',
  IfElse = 'if-else',
  Code = 'code',
  TemplateTransform = 'template-transform',
  HttpRequest = 'http-request',
  VariableAssigner = 'variable-assigner',
  VariableAggregator = 'variable-aggregator',
  Tool = 'tool',
  ParameterExtractor = 'parameter-extractor',
  Iteration = 'iteration',
  DocExtractor = 'document-extractor',
  ListFilter = 'list-operator',
  IterationStart = 'iteration-start',
  Assigner = 'assigner',
  Agent = 'agent',
  Loop = 'loop',
  LoopStart = 'loop-start',
  LoopEnd = 'loop-end'
}

export enum ControlMode {
  Pointer = 'pointer',
  Hand = 'hand'
}
export enum ErrorHandleMode {
  Terminated = 'terminated',
  ContinueOnError = 'continue-on-error',
  RemoveAbnormalOutput = 'remove-abnormal-output'
}
export interface Branch {
  id: string
  name: string
}

export interface CommonEdgeType {
  _hovering?: boolean
  _connectedNodeIsHovering?: boolean
  _connectedNodeIsSelected?: boolean
  _isBundled?: boolean
  _sourceRunningStatus?: NodeRunningStatus
  _targetRunningStatus?: NodeRunningStatus
  _waitingRun?: boolean
  isInIteration?: boolean
  iteration_id?: string
  isInLoop?: boolean
  loop_id?: string
  sourceType: BlockEnum
  targetType: BlockEnum
}

export type ValueSelector = string[] // [nodeId, key | obj key path]

export interface EnvironmentVariable {
  id: string
  name: string
  value: any
  value_type: 'string' | 'number' | 'secret'
}

export interface GlobalVariable {
  name: string
  value_type: 'string' | 'number'
  description: string
}

export interface VariableWithValue {
  key: string
  value: string
}

export enum InputVarType {
  textInput = 'text-input',
  paragraph = 'paragraph',
  select = 'select',
  number = 'number',
  url = 'url',
  files = 'files',
  json = 'json', // obj, array
  contexts = 'contexts', // knowledge retrieval
  iterator = 'iterator', // iteration input
  singleFile = 'file',
  multiFiles = 'file-list',
  loop = 'loop' // loop input
}

export interface ModelConfig {
  provider: string
  name: string
  mode: string
  completion_params: Record<string, any>
}

export enum PromptRole {
  system = 'system',
  user = 'user',
  assistant = 'assistant'
}

export enum EditionType {
  basic = 'basic',
  jinja2 = 'jinja2'
}

export interface PromptItem {
  id?: string
  role?: PromptRole
  text: string
  edition_type?: EditionType
  jinja2_text?: string
}

export enum MemoryRole {
  user = 'user',
  assistant = 'assistant'
}

export interface RolePrefix {
  user: string
  assistant: string
}

export interface Memory {
  role_prefix?: RolePrefix
  window: {
    enabled: boolean
    size: number | string | null
  }
  query_prompt_template: string
}

export enum VarType {
  string = 'string',
  number = 'number',
  secret = 'secret',
  boolean = 'boolean',
  object = 'object',
  file = 'file',
  array = 'array',
  arrayString = 'array[string]',
  arrayNumber = 'array[number]',
  arrayObject = 'array[object]',
  arrayFile = 'array[file]',
  any = 'any'
}

export enum ValueType {
  variable = 'variable',
  constant = 'constant'
}

export interface Var {
  variable: string
  type: VarType
  children?: Var[] // if type is obj, has the children struct
  isParagraph?: boolean
  isSelect?: boolean
  options?: string[]
  required?: boolean
  des?: string
  isException?: boolean
  isLoopVariable?: boolean
  nodeId?: string
}

export interface NodeOutPutVar {
  nodeId: string
  title: string
  vars: Var[]
  isStartNode?: boolean
  isLoop?: boolean
}

export interface Block {
  classification?: string
  type: BlockEnum
  title: string
  description?: string
}

export interface NodeDefault<T> {
  defaultValue: Partial<T>
  getAvailablePrevNodes: (isChatMode: boolean) => BlockEnum[]
  getAvailableNextNodes: (isChatMode: boolean) => BlockEnum[]
  checkValid: (payload: T, t: any, moreDataForCheckValid?: any) => { isValid: boolean; errorMessage?: string }
}

export enum WorkflowRunningStatus {
  Waiting = 'waiting',
  Running = 'running',
  Succeeded = 'succeeded',
  Failed = 'failed',
  Stopped = 'stopped'
}

export enum WorkflowVersion {
  Draft = 'draft',
  Latest = 'latest'
}

export enum NodeRunningStatus {
  NotStart = 'not-start',
  Waiting = 'waiting',
  Running = 'running',
  Succeeded = 'succeeded',
  Failed = 'failed',
  Exception = 'exception',
  Retry = 'retry'
}

export interface CheckValidRes {
  isValid: boolean
  errorMessage?: string
}

export interface HistoryWorkflowData {
  id: string
  sequence_number: number
  status: string
  conversation_id?: string
}

export enum ChangeType {
  changeVarName = 'changeVarName',
  remove = 'remove'
}

export interface MoreInfo {
  type: ChangeType
  payload?: {
    beforeKey: string
    afterKey?: string
  }
}

export enum SupportUploadFileTypes {
  image = 'image',
  document = 'document',
  audio = 'audio',
  video = 'video',
  custom = 'custom'
}

export enum WorkflowVersionFilterOptions {
  all = 'all',
  onlyYours = 'onlyYours'
}

export enum VersionHistoryContextMenuOptions {
  restore = 'restore',
  edit = 'edit',
  delete = 'delete'
}

export interface AgentLogItem {
  node_execution_id: string
  id: string
  node_id: string
  parent_id?: string
  label: string
  data: object // debug data
  error?: string
  status: string
  metadata?: {
    elapsed_time?: number
    provider?: string
    icon?: string
  }
}

export type AgentLogItemWithChildren = AgentLogItem & {
  hasCircle?: boolean
  children: AgentLogItemWithChildren[]
}

export enum ErrorHandleTypeEnum {
  none = 'none',
  failBranch = 'fail-branch',
  defaultValue = 'default-value'
}

export interface NodeTracing {
  type: any
  retries: any
  request: any
  id: string
  index: number
  predecessor_node_id: string
  node_id: string
  iteration_id?: string
  loop_id?: string
  node_type: BlockEnum
  title: string
  inputs: any
  process_data: any
  outputs?: any
  status: string
  parallel_run_id?: string
  error?: string
  elapsed_time: number
  execution_metadata?: {
    total_tokens: number
    total_price: number
    currency: string
    iteration_id?: string
    iteration_index?: number
    loop_id?: string
    loop_index?: number
    parallel_id?: string
    parallel_start_node_id?: string
    parent_parallel_id?: string
    parent_parallel_start_node_id?: string
    parallel_mode_run_id?: string
    iteration_duration_map?: IterationDurationMap
    loop_duration_map?: LoopDurationMap
    error_strategy?: ErrorHandleTypeEnum
    agent_log?: AgentLogItem[]
    tool_info?: {
      agent_strategy?: string
      icon?: string
    }
    loop_variable_map?: Record<string, any>
  }
  metadata: {
    iterator_length: number
    iterator_index: number
    loop_length: number
    loop_index: number
  }
  created_at: number
  created_by: {
    id: string
    name: string
    email: string
  }
  iterDurationMap?: IterationDurationMap
  loopDurationMap?: LoopDurationMap
  finished_at: number
  extras?: any
  expand?: boolean // for UI
  details?: NodeTracing[][] // iteration or loop detail
  retryDetail?: NodeTracing[] // retry detail
  retry_index?: number
  parallelDetail?: {
    // parallel detail. if is in parallel, this field will be set
    isParallelStartNode?: boolean
    parallelTitle?: string
    branchTitle?: string
    children?: NodeTracing[]
  }
  parallel_id?: string
  parallel_start_node_id?: string
  parent_parallel_id?: string
  parent_parallel_start_node_id?: string
  agentLog?: AgentLogItemWithChildren[] // agent log
}
export enum ChatVarType {
  Number = 'number',
  String = 'string',
  Object = 'object',
  ArrayString = 'array[string]',
  ArrayNumber = 'array[number]',
  ArrayObject = 'array[object]'
}

export interface ConversationVariable {
  id: string
  name: string
  value_type: ChatVarType
  value: any
  description: string
}

export interface FetchWorkflowDraftResponse {
  id: string
  graph: {
    nodes: Node[]
    edges: Edge[]
    viewport?: Viewport
  }
  features?: any
  created_at: number
  created_by: {
    id: string
    name: string
    email: string
  }
  hash: string
  updated_at: number
  updated_by: {
    id: string
    name: string
    email: string
  }
  tool_published: boolean
  environment_variables?: EnvironmentVariable[]
  conversation_variables?: ConversationVariable[]
  version: string
  marked_name: string
  marked_comment: string
}

export type VersionHistory = FetchWorkflowDraftResponse

export interface FetchWorkflowDraftPageParams {
  appId: string
  initialPage: number
  limit: number
  userId?: string
  namedOnly?: boolean
}

export interface FetchWorkflowDraftPageResponse {
  items: VersionHistory[]
  has_more: boolean
  page: number
}

export interface NodeTracingListResponse {
  data: NodeTracing[]
}

export interface WorkflowStartedResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    id: string
    workflow_id: string
    sequence_number: number
    created_at: number
  }
}

export interface WorkflowFinishedResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    id: string
    workflow_id: string
    status: string
    outputs: any
    error: string
    elapsed_time: number
    total_tokens: number
    total_steps: number
    created_at: number
    created_by: {
      id: string
      name: string
      email: string
    }
    finished_at: number
    files?: FileResponse[]
  }
}

export interface NodeStartedResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: NodeTracing
}

export interface FileResponse {
  related_id: string
  extension: string
  filename: string
  size: number
  mime_type: string
  transfer_method: TransferMethod
  type: string
  url: string
  upload_file_id: string
}

export interface NodeFinishedResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: NodeTracing
}

export interface IterationStartedResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: NodeTracing
}

export interface IterationNextResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: NodeTracing
}

export interface IterationFinishedResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: NodeTracing
}

export interface LoopStartedResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: NodeTracing
}

export interface LoopNextResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: NodeTracing
}

export interface LoopFinishedResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: NodeTracing
}

export interface ParallelBranchStartedResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: NodeTracing
}

export interface ParallelBranchFinishedResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: NodeTracing
}

export interface TextChunkResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    text: string
  }
}

export interface TextReplaceResponse {
  task_id: string
  workflow_run_id: string
  event: string
  data: {
    text: string
  }
}

export interface AgentLogResponse {
  task_id: string
  event: string
  data: AgentLogItemWithChildren
}

export interface WorkflowRunHistory {
  id: string
  sequence_number: number
  version: string
  conversation_id?: string
  message_id?: string
  graph: {
    nodes: Node[]
    edges: Edge[]
    viewport?: Viewport
  }
  inputs: Record<string, string>
  status: string
  outputs: Record<string, any>
  error?: string
  elapsed_time: number
  total_tokens: number
  total_steps: number
  created_at: number
  finished_at: number
  created_by_account: {
    id: string
    name: string
    email: string
  }
}
export interface WorkflowRunHistoryResponse {
  data: WorkflowRunHistory[]
}

export interface ChatRunHistoryResponse {
  data: WorkflowRunHistory[]
}

export interface NodesDefaultConfigsResponse {
  type: string
  config: any
}
;[]

export interface ConversationVariableResponse {
  data: (ConversationVariable & { updated_at: number; created_at: number })[]
  has_more: boolean
  limit: number
  total: number
  page: number
}

export type IterationDurationMap = Record<string, number>
export type LoopDurationMap = Record<string, number>
export type LoopVariableMap = Record<string, any>

export interface WorkflowConfigResponse {
  parallel_depth_limit: number
}

export interface PublishWorkflowParams {
  title: string
  releaseNotes: string
}

export interface UpdateWorkflowParams {
  workflowId: string
  title: string
  releaseNotes: string
}
export interface MessageReplace {
  id: string
  task_id: string
  answer: string
  conversation_id: string
}
export interface CitationItem {
  content: string
  data_source_type: string
  dataset_name: string
  dataset_id: string
  document_id: string
  document_name: string
  hit_count: number
  index_node_hash: string
  segment_id: string
  segment_position: number
  score: number
  word_count: number
}

export interface Metadata {
  retriever_resources?: CitationItem[]
  annotation_reply: {
    id: string
    account: {
      id: string
      name: string
    }
  }
}
export interface MessageEnd {
  id: string
  metadata: Metadata
  files?: FileResponse[]
}
export interface Thought {
  id: string
  tool: string // plugin or dataset. May has multi.
  thought: string
  tool_input: string
  tool_labels?: Record<string, string>
  message_id: string
  observation: string
  position: number
  files?: string[]
  message_files?: any[]
}
