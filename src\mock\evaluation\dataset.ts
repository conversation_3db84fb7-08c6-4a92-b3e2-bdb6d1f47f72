import Mock from 'mockjs'
import { defineMock } from '../_base'
import { getDelayTime, resultSuccess } from '../_utils'

const data = Mock.mock({
  'list|5-10': [
    {
      id: '@id',
      name: '@ctitle(5, 10)',
      'type|1': ['对话类', '工作流类'],
      'status|1': [1, 2],
      version: 'v1.0.0',
      description: '@csentence(10, 20)',
      fileList: [
        {
          name: '@word(5,10).xlsx',
          url: '@url',
          size: '@integer(1000, 10000)'
        }
      ],
      createUserString: '@cname',
      createTime: '@datetime',
      updateUserString: '@cname',
      updateTime: '@datetime'
    }
  ]
})

export default defineMock([
  {
    url: '/evaluation/dataset',
    method: 'get',
    timeout: getDelayTime(),
    response: ({ query }) => {
      return resultSuccess({
        list: data.list,
        total: data.list.length
      })
    }
  },
  {
    url: '/evaluation/dataset/:id',
    method: 'get',
    timeout: getDelayTime(),
    response: ({ query }) => {
      return resultSuccess(data.list[0])
    }
  }
])
