import { useProviderStore } from '@/stores/modules/workflow/provider'
import { ModelStatusEnum, ModelTypeEnum } from '@/apis/model-mgmt/type'
import { MetadataFilteringModeEnum } from '@/views/app/workflow/types/node'
import { uniq, xorBy } from 'lodash-es'
import { RETRIEVE_METHOD, DATASET_DEFAULT, DEFAULT_WEIGHTED_SCORE } from '@/views/app/workflow/constant/configuration'
import { RerankingModeEnum } from '@/views/app/configuration/types'
import { getDefaultModelHttp, getModelListHttp } from '@/apis/workflow/configuration'
import type { ToolParameter } from '@/views/app/workflow/types/configuration'

export const correctModelProvider = (provider: string) => {
  if (!provider) return ''

  if (provider.includes('/')) return provider

  if (['google'].includes(provider)) return 'langgenius/gemini/google'

  return `langgenius/${provider}/${provider}`
}
export const correctToolProvider = (provider: string, toolInCollectionList?: boolean) => {
  if (!provider) return ''

  if (toolInCollectionList) return provider

  if (provider.includes('/')) return provider

  if (['stepfun', 'jina', 'siliconflow', 'gitee_ai'].includes(provider))
    return `langgenius/${provider}_tool/${provider}`

  return `langgenius/${provider}/${provider}`
}

export const useTextGenerationCurrentProviderAndModelAndModelList = (defaultModel?: any) => {
  const providerStore = useProviderStore()
  const textGenerationModelList = providerStore.provider.textGenerationModelList
  const activeTextGenerationModelList = textGenerationModelList.filter(
    (model) => model.status === ModelStatusEnum.active
  )
  const { currentProvider, currentModel } = useCurrentProviderAndModel(textGenerationModelList, defaultModel)
  return {
    currentProvider,
    currentModel,
    textGenerationModelList,
    activeTextGenerationModelList
  }
}

export const useCurrentProviderAndModel = (modelList, defaultModel) => {
  const currentProvider = modelList.find((provider) => provider.provider === defaultModel?.provider)
  const currentModel = currentProvider?.models.find((model) => model.model === defaultModel?.model)

  return {
    currentProvider,
    currentModel
  }
}
/**
 * 获取模型名称
 * @param obj
 */
export const renderName = (obj: Record<string, string>) => {
  if (!obj) return ''
  if (obj?.['zh_Hans']) return obj['zh_Hans']
  if (obj?.en_US) return obj.en_US
  return Object.values(obj)[0]
}

export const sizeFormat = (size: number) => {
  const remainder = Math.floor(size / 1000)
  if (remainder < 1) return `${size}`
  else return `${remainder}K`
}
export const modelTypeFormat = (modelType: ModelTypeEnum) => {
  if (modelType === ModelTypeEnum.textEmbedding) return 'TEXT EMBEDDING'

  return modelType.toLocaleUpperCase()
}
export const renderI18nObject = (obj: Record<string, string>, language: string = 'zh_Hans') => {
  if (!obj) return ''
  if (obj?.[language]) return obj[language]
  if (obj?.en_US) return obj.en_US
  return Object.values(obj)[0]
}

export const INDEXING_TECHNIQUE = {
  high_quality: '高质量',
  economy: '经济'
}

export const INDEXING_METHOD = {
  semantic_search: '向量检索',
  full_text_search: '全文检索',
  hybrid_search: '混合检索',
  invertedIndex: '倒排索引'
}

export const formatIndexingTechnique = (indexingTechnique: string) => {
  return INDEXING_TECHNIQUE[indexingTechnique]
}
export const formatIndexingMethod = (indexingMethod: string, isEco?: boolean) => {
  if (isEco) return '倒排索引'
  return INDEXING_METHOD[indexingMethod]
}
export const formatIndexingTechniqueAndMethod = (indexingTechnique: string, indexingMethod: string) => {
  let result = formatIndexingTechnique(indexingTechnique)
  if (indexingMethod) {
    result += ` · ${formatIndexingMethod(indexingMethod, indexingTechnique === 'economy')}`
  }
  return result
}
export const METADATA_SELECT = [
  { key: MetadataFilteringModeEnum.disabled, value: '禁用', desc: '禁用元数据过滤' },
  { key: MetadataFilteringModeEnum.automatic, value: '自动', desc: '根据用户查询自动生成元数据过滤条件' },
  { key: MetadataFilteringModeEnum.manual, value: '手动', desc: '手动添加元数据过滤条件' }
]

// DataSet
export const getSelectedDatasetsMode = (datasets: any[] = []) => {
  if (datasets === null)
    datasets = []
  let allHighQuality = true
  let allHighQualityVectorSearch = true
  let allHighQualityFullTextSearch = true
  let allEconomic = true
  let mixtureHighQualityAndEconomic = true
  let allExternal = true
  let allInternal = true
  let mixtureInternalAndExternal = true
  let inconsistentEmbeddingModel = false
  if (!datasets.length) {
    allHighQuality = false
    allHighQualityVectorSearch = false
    allHighQualityFullTextSearch = false
    allEconomic = false
    mixtureHighQualityAndEconomic = false
    allExternal = false
    allInternal = false
    mixtureInternalAndExternal = false
  }
  datasets.forEach((dataset) => {
    if (dataset.indexing_technique === 'economy') {
      allHighQuality = false
      allHighQualityVectorSearch = false
      allHighQualityFullTextSearch = false
    }
    if (dataset.indexing_technique === 'high_quality') {
      allEconomic = false

      if (dataset.retrieval_model_dict.search_method !== RETRIEVE_METHOD.semantic)
        allHighQualityVectorSearch = false

      if (dataset.retrieval_model_dict.search_method !== RETRIEVE_METHOD.fullText)
        allHighQualityFullTextSearch = false
    }
    if (dataset.provider !== 'external') {
      allExternal = false
    } else {
      allInternal = false
      allHighQuality = false
      allHighQualityVectorSearch = false
      allHighQualityFullTextSearch = false
      mixtureHighQualityAndEconomic = false
    }
  })

  if (allExternal || allInternal)
    mixtureInternalAndExternal = false

  if (allHighQuality || allEconomic)
    mixtureHighQualityAndEconomic = false

  if (allHighQuality)
    inconsistentEmbeddingModel = uniq(datasets.map(item => item.embedding_model)).length > 1

  return {
    allHighQuality,
    allHighQualityVectorSearch,
    allHighQualityFullTextSearch,
    allEconomic,
    mixtureHighQualityAndEconomic,
    allInternal,
    allExternal,
    mixtureInternalAndExternal,
    inconsistentEmbeddingModel
  } as any // SelectedDatasetsMode
}

// DataSet[]
export const getMultipleRetrievalConfig = (
  multipleRetrievalConfig: any, // MultipleRetrievalConfig
  selectedDatasets: any[],
  originalDatasets: any[],
  validRerankModel?: { provider?: string; model?: string }
) => {

  console.log('multipleRetrievalConfig:', multipleRetrievalConfig, selectedDatasets, originalDatasets, validRerankModel)
  const shouldSetWeightDefaultValue = xorBy(selectedDatasets, originalDatasets, 'id').length > 0
  const rerankModelIsValid = validRerankModel?.provider && validRerankModel?.model

  const {
    allHighQuality,
    allHighQualityVectorSearch,
    allHighQualityFullTextSearch,
    allEconomic,
    mixtureHighQualityAndEconomic,
    allInternal,
    allExternal,
    mixtureInternalAndExternal,
    inconsistentEmbeddingModel
  } = getSelectedDatasetsMode(selectedDatasets)

  const {
    top_k = DATASET_DEFAULT.top_k,
    score_threshold,
    reranking_mode,
    reranking_model,
    weights,
    reranking_enable
  } = multipleRetrievalConfig || { top_k: DATASET_DEFAULT.top_k }

  const result = {
    top_k,
    score_threshold,
    reranking_mode,
    reranking_model,
    weights,
    reranking_enable: ((allInternal && allEconomic) || allExternal) ? reranking_enable : shouldSetWeightDefaultValue
  }

  const setDefaultWeights = () => {
    result.weights = {
      vector_setting: {
        vector_weight: allHighQualityVectorSearch
          ? DEFAULT_WEIGHTED_SCORE.allHighQualityVectorSearch.semantic
          : allHighQualityFullTextSearch
            ? DEFAULT_WEIGHTED_SCORE.allHighQualityFullTextSearch.semantic
            : DEFAULT_WEIGHTED_SCORE.other.semantic,
        embedding_provider_name: selectedDatasets[0].embedding_model_provider,
        embedding_model_name: selectedDatasets[0].embedding_model
      },
      keyword_setting: {
        keyword_weight: allHighQualityVectorSearch
          ? DEFAULT_WEIGHTED_SCORE.allHighQualityVectorSearch.keyword
          : allHighQualityFullTextSearch
            ? DEFAULT_WEIGHTED_SCORE.allHighQualityFullTextSearch.keyword
            : DEFAULT_WEIGHTED_SCORE.other.keyword
      }
    }
  }

  if (allEconomic || mixtureHighQualityAndEconomic || inconsistentEmbeddingModel || allExternal || mixtureInternalAndExternal) {
    result.reranking_mode = RerankingModeEnum.RerankingModel
    if (!result.reranking_model?.provider || !result.reranking_model?.model) {
      if (rerankModelIsValid) {
        result.reranking_enable = reranking_enable !== false

        result.reranking_model = {
          provider: validRerankModel?.provider || '',
          model: validRerankModel?.model || ''
        }
      } else {
        result.reranking_model = {
          provider: '',
          model: ''
        }
      }
    } else {
      result.reranking_enable = reranking_enable !== false
    }
  }

  if (allHighQuality && !inconsistentEmbeddingModel && allInternal) {
    if (!reranking_mode) {
      if (validRerankModel?.provider && validRerankModel?.model) {
        result.reranking_mode = RerankingModeEnum.RerankingModel
        result.reranking_enable = reranking_enable !== false

        result.reranking_model = {
          provider: validRerankModel.provider,
          model: validRerankModel.model
        }
      } else {
        result.reranking_mode = RerankingModeEnum.WeightedScore
        setDefaultWeights()
      }
    }

    if (reranking_mode === RerankingModeEnum.WeightedScore && !weights)
      setDefaultWeights()

    if (reranking_mode === RerankingModeEnum.WeightedScore && weights && shouldSetWeightDefaultValue) {
      if (rerankModelIsValid) {
        result.reranking_mode = RerankingModeEnum.RerankingModel
        result.reranking_enable = reranking_enable !== false

        result.reranking_model = {
          provider: validRerankModel.provider || '',
          model: validRerankModel.model || ''
        }
      } else {
        setDefaultWeights()
      }
    }
    if (reranking_mode === RerankingModeEnum.RerankingModel && !rerankModelIsValid && shouldSetWeightDefaultValue) {
      result.reranking_mode = RerankingModeEnum.WeightedScore
      setDefaultWeights()
    }
  }

  return result
}
export const useModelList = async (type: ModelTypeEnum) => {
  const { data, mutate, isLoading } = await getModelListHttp(type)
  return { data }
}
export const useDefaultModel = async (type: ModelTypeEnum) => {
  const { data, mutate, isLoading } = await getDefaultModelHttp(type)
  return { data }
}
export const useModelListAndDefaultModel = async (type: ModelTypeEnum) => {
  const { data: modelList } = await useModelList(type)
  const { data: defaultModel } = await useDefaultModel(type)
  return {
    modelList,
    defaultModel
  }
}
export const useModelListAndDefaultModelAndCurrentProviderAndModel = async (type: ModelTypeEnum) => {
  const { modelList, defaultModel } = await useModelListAndDefaultModel(type)
  const {
    currentProvider,
    currentModel
  } = useCurrentProviderAndModel(modelList, {
    provider: defaultModel?.provider.provider || '',
    model: defaultModel?.model || ''
  })

  return {
    modelList,
    defaultModel,
    currentProvider,
    currentModel
  }
}
export const canFindTool = (providerId: string, oldToolId?: string) => {
  return providerId === oldToolId
    || providerId === `langgenius/${oldToolId}/${oldToolId}`
    || providerId === `langgenius/${oldToolId}_tool/${oldToolId}`
}
export const getType = (type: string) => {
  if (type === 'number-input') return '数字'
  if (type === 'text-input') return '字符串'
  if (type === 'file') return '文件'
  return type
}

export const toType = (type: string) => {
  switch (type) {
    case 'string':
      return 'text-input'
    case 'number':
      return 'number-input'
    default:
      return type
  }
}
export const toolParametersToFormSchemas = (parameters: ToolParameter[]) => {
  if (!parameters) return []

  const formSchemas = parameters.map((parameter) => {
    return {
      ...parameter,
      variable: parameter.name,
      type: toType(parameter.type),
      _type: parameter.type,
      show_on: [],
      options: parameter.options?.map((option) => {
        return {
          ...option,
          show_on: []
        }
      }),
      tooltip: parameter.human_description
    }
  })
  return formSchemas
}
export const addDefaultValue = (value: Record<string, any>, formSchemas: { variable: string; default?: any }[]) => {
  const newValues = { ...value }
  formSchemas.forEach((formSchema) => {
    const itemValue = value[formSchema.variable]
    if ((formSchema.default !== undefined) && (value === undefined || itemValue === null || itemValue === '' || itemValue === undefined))
      newValues[formSchema.variable] = formSchema.default
  })
  return newValues
}
