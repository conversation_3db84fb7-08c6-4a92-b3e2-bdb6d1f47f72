<template>
  <div class="variable-textarea">
    <!-- 编辑器容器 -->
    <div class="editor-container">
      <!-- 工具栏 -->
      <div class="editor-toolbar">
        <div class="toolbar-left">
          <span class="editor-label">{{ label }}</span>
        </div>
        <div class="toolbar-right">
          <a-button type="text" size="mini" class="toolbar-btn" :disabled="readonly" @click.stop="insertVariable">
            <template #icon>
              <icon-plus />
            </template>
            插入变量
          </a-button>
          <a-button type="text" size="mini" class="toolbar-btn" @click="openFullscreenModal">
            <template #icon>
              <icon-fullscreen />
            </template>
            全屏编辑
          </a-button>
        </div>
      </div>

      <!-- 编辑器内容 -->
      <div class="editor-content">
        <a-textarea
          ref="textareaRef"
          v-model="textValue"
          :placeholder="placeholder"
          :readonly="readonly"
          :auto-size="autoSize"
          class="variable-textarea-input"
          @keyup="handleKeyup"
          @input="handleInput"
        />
      </div>
    </div>

    <!-- 全屏编辑模态框 -->
    <a-modal
      v-model:visible="isFullscreenModalOpen"
      width="60vw"
      :footer="false"
      :mask-closable="false"
      :closable="false"
      @cancel="closeFullscreenModal"
    >
      <template #title>
        <div class="modal-header">
          <span class="modal-title">全屏编辑</span>
          <div class="modal-toolbar">
            <a-button
              type="text"
              size="mini"
              class="toolbar-btn"
              :disabled="readonly"
              @click.stop="insertVariableInModal"
            >
              <template #icon>
                <icon-plus />
              </template>
              插入变量
            </a-button>
            <a-button type="text" size="mini" class="toolbar-btn" @click.stop="closeFullscreenModal">
              <template #icon>
                <icon-fullscreen-exit />
              </template>
              退出全屏
            </a-button>
          </div>
        </div>
      </template>

      <div class="fullscreen-editor">
        <a-textarea
          ref="modalTextareaRef"
          v-model="textValue"
          :placeholder="placeholder"
          :auto-size="{ minRows: 20, maxRows: 25 }"
          :readonly="readonly"
          class="fullscreen-textarea"
          @keyup="handleModalKeyup"
          @input="handleInput"
        />
      </div>
    </a-modal>

    <!-- 简化的变量选择器 -->
    <Teleport to="body">
      <div
        v-if="showVariablePicker"
        ref="variablePickerRef"
        class="simple-variable-picker"
        :style="pickerStyle"
        @click.stop
      >
        <div class="picker-header">
          <span>选择变量</span>
          <a-button type="text" size="mini" @click.stop="closeVariablePicker">
            <template #icon>
              <icon-close />
            </template>
          </a-button>
        </div>
        <div class="picker-content">
          <VariableList
            :vars="nodesOutputVars"
            :filter-var="filterVar"
            @select="handleVariableSelect"
            @close="closeVariablePicker"
          />
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'
import VariableList from './variable-selector/VariableList.vue'
import { VarType } from '@/views/app/workflow/types/workflow'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import {
  getBeforeNodesInSameBranchIncludeParent,
  toNodeAvailableVars,
  filterVar as filterVarByType
} from '@/views/app/workflow/utils/variable'

interface VariableTextareaProps {
  modelValue: string
  nodeId: string
  label?: string
  placeholder?: string
  readonly?: boolean
  autoSize?: boolean | { minRows?: number; maxRows?: number }
  varType?: VarType
  filterVar?: (v: any) => boolean
}

const props = withDefaults(defineProps<VariableTextareaProps>(), {
  label: '内容',
  placeholder: '请输入内容，输入 "/" 可快速插入变量',
  readonly: false,
  autoSize: () => ({ minRows: 4, maxRows: 12 }),
  varType: VarType.string,
  filterVar: () => true
})

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string): void
}>()

const textareaRef = ref<any>(null)
const modalTextareaRef = ref<any>(null)
const variablePickerRef = ref<HTMLElement | null>(null)
const isFullscreenModalOpen = ref(false)
const showVariablePicker = ref(false)
const triggerPosition = ref(0)
const modalTriggerPosition = ref(0)
const pickerStyle = ref<Record<string, any>>({})

// 获取节点store和变量
const nodesStore = useNodesStore()
const availableNodes = ref<any[]>([])
const nodesOutputVars = ref<any[]>([])

// 获取可用节点和变量的函数
const getAvailableNodesAndVars = () => {
  const beforeNodes = getBeforeNodesInSameBranchIncludeParent(props.nodeId)
  const parentNode = nodesStore.parentNode

  const outputVars = toNodeAvailableVars(
    parentNode,
    beforeNodes,
    false,
    props.filterVar || filterVarByType(props.varType),
    [],
    []
  )

  return {
    availableNodes: beforeNodes,
    nodesOutputVars: outputVars
  }
}

// 双向绑定的文本值
const textValue = computed({
  get: () => props.modelValue,
  set: (value: string) => {
    emit('update:modelValue', value)
    emit('change', value)
  }
})

// 打开全屏模态框
const openFullscreenModal = () => {
  isFullscreenModalOpen.value = true
  nextTick(() => {
    if (modalTextareaRef.value && modalTextareaRef.value.$el) {
      const textarea = modalTextareaRef.value.$el.querySelector('textarea')
      if (textarea) {
        textarea.focus()
      }
    }
  })
}

// 关闭全屏模态框
const closeFullscreenModal = () => {
  isFullscreenModalOpen.value = false
}

// 插入变量按钮点击
const insertVariable = () => {
  if (props.readonly) {
    return
  }

  const textarea = getTextareaElement()
  if (textarea) {
    textarea.focus()
    const position = textarea.selectionStart || 0

    // 设置触发位置为当前光标位置
    triggerPosition.value = position

    // 延迟显示选择器，避免立即被点击外部事件关闭
    setTimeout(() => {
      showVariablePicker.value = true
      showVariablePickerAt(textarea)
    }, 50)
  }
}

// 显示变量选择器
const showVariablePickerAt = (textarea: HTMLTextAreaElement) => {
  showVariablePicker.value = true

  nextTick(() => {
    updatePickerPosition(textarea)
  })
}

// 关闭变量选择器
const closeVariablePicker = () => {
  showVariablePicker.value = false
}

// 获取光标在屏幕上的位置
const getCursorPosition = (textarea: HTMLTextAreaElement) => {
  try {
    // 获取当前光标位置
    let cursorPosition = textarea.selectionStart || 0

    // 如果没有光标位置，设置到文本末端
    if (cursorPosition === 0 && textarea.value.length > 0) {
      cursorPosition = textarea.value.length
      textarea.setSelectionRange(cursorPosition, cursorPosition)
      textarea.focus()
    }

    // 创建一个临时的div来测量文本位置
    const div = document.createElement('div')
    const style = window.getComputedStyle(textarea)

    // 复制textarea的样式到临时div
    div.style.position = 'absolute'
    div.style.visibility = 'hidden'
    div.style.whiteSpace = 'pre-wrap'
    div.style.overflowWrap = 'break-word'
    div.style.font = style.font
    div.style.fontSize = style.fontSize
    div.style.fontFamily = style.fontFamily
    div.style.fontWeight = style.fontWeight
    div.style.lineHeight = style.lineHeight
    div.style.letterSpacing = style.letterSpacing
    div.style.padding = style.padding
    div.style.border = style.border
    div.style.width = style.width
    div.style.boxSizing = style.boxSizing
    div.style.top = '-9999px'
    div.style.left = '-9999px'

    document.body.appendChild(div)

    // 获取光标前的文本
    const textBeforeCursor = textarea.value.substring(0, cursorPosition)
    div.textContent = textBeforeCursor

    // 创建一个span来标记光标位置
    const span = document.createElement('span')
    span.textContent = '|'
    div.appendChild(span)

    // 获取span的位置（即光标位置）
    const spanRect = span.getBoundingClientRect()
    const textareaRect = textarea.getBoundingClientRect()
    const divRect = div.getBoundingClientRect()

    // 清理临时元素
    document.body.removeChild(div)

    // 计算光标相对于textarea的位置
    const relativeX = spanRect.left - divRect.left
    const relativeY = spanRect.top - divRect.top

    // 计算光标在屏幕上的绝对位置
    const cursorX = textareaRect.left + relativeX
    const cursorY = textareaRect.top + relativeY + 20 // 在光标下方显示选择器

    return {
      x: cursorX,
      y: cursorY
    }
  } catch (error) {
    // 如果获取光标位置失败，使用textarea的位置
    const rect = textarea.getBoundingClientRect()
    return {
      x: rect.left,
      y: rect.bottom + 8
    }
  }
}

// 更新选择器位置
const updatePickerPosition = (textarea: HTMLTextAreaElement) => {
  try {
    // 获取光标位置
    const cursorPos = getCursorPosition(textarea)

    let top = cursorPos.y
    let left = cursorPos.x

    // 边界检测
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const pickerWidth = 300
    const pickerHeight = 350

    // 水平边界检测
    if (left + pickerWidth > viewportWidth) {
      left = viewportWidth - pickerWidth - 20
    }
    if (left < 10) {
      left = 10
    }

    // 垂直边界检测
    if (top + pickerHeight > viewportHeight) {
      // 如果下方空间不够，显示在光标上方
      top = cursorPos.y - pickerHeight - 25
    }
    if (top < 10) {
      top = 10
    }

    pickerStyle.value = {
      position: 'fixed',
      top: `${Math.max(10, top)}px`,
      left: `${Math.max(10, left)}px`,
      zIndex: 99999
    }
  } catch (error) {
    // 后备位置：使用textarea的位置
    const rect = textarea.getBoundingClientRect()
    pickerStyle.value = {
      position: 'fixed',
      top: `${rect.bottom + 8}px`,
      left: `${rect.left}px`,
      zIndex: 99999
    }
  }
}

// 模态框中插入变量按钮点击
const insertVariableInModal = () => {
  if (props.readonly) {
    return
  }

  const textarea = getModalTextareaElement()
  if (textarea) {
    textarea.focus()
    const position = textarea.selectionStart || 0

    // 设置触发位置为当前光标位置
    modalTriggerPosition.value = position

    // 延迟显示选择器，避免立即被点击外部事件关闭
    setTimeout(() => {
      showVariablePicker.value = true
      showVariablePickerAt(textarea)
    }, 50)
  }
}

// 获取textarea元素
const getTextareaElement = (): HTMLTextAreaElement | null => {
  if (textareaRef.value && textareaRef.value.$el) {
    return textareaRef.value.$el.querySelector('textarea')
  }
  return null
}

// 获取模态框textarea元素
const getModalTextareaElement = (): HTMLTextAreaElement | null => {
  if (modalTextareaRef.value && modalTextareaRef.value.$el) {
    return modalTextareaRef.value.$el.querySelector('textarea')
  }
  return null
}

// 键盘事件处理
const handleKeyup = (event: KeyboardEvent) => {
  const target = event.target as HTMLTextAreaElement

  // 检测是否输入了变量触发符 "/"
  if (event.key === '/') {
    // 记录触发位置（/ 字符的位置）
    triggerPosition.value = target.selectionStart - 1

    // 显示变量选择器
    showVariablePickerAt(target)
  } else if (event.key === 'Escape') {
    // ESC键关闭变量选择器
    closeVariablePicker()
  }
}

// 模态框键盘事件处理
const handleModalKeyup = (event: KeyboardEvent) => {
  const target = event.target as HTMLTextAreaElement

  // 检测是否输入了变量触发符 "/"
  if (event.key === '/') {
    // 记录触发位置（/ 字符的位置）
    modalTriggerPosition.value = target.selectionStart - 1

    // 显示变量选择器
    showVariablePickerAt(target)
  } else if (event.key === 'Escape') {
    // ESC键关闭变量选择器
    closeVariablePicker()
  }
}

// 输入事件处理
const handleInput = (value: string) => {
  textValue.value = value
}

// 变量选择处理
const handleVariableSelect = (_group: any, variable: any) => {
  // 生成变量文本
  let variableText = ''

  // 检查是否是系统变量
  const isSystemVar =
    variable.variable.startsWith('sys.') ||
    variable.variable.startsWith('env.') ||
    variable.variable.startsWith('conversation.')

  if (isSystemVar) {
    // 系统变量格式：{{#sys.userid#}}
    variableText = `{{#${variable.variable}#}}`
  } else {
    // 普通变量格式：{{#nodeId.variable#}}
    variableText = `{{#${variable.nodeId}.${variable.variable}#}}`
  }

  // 检查是否在模态框中
  const isInModal = isFullscreenModalOpen.value
  const textarea = isInModal ? getModalTextareaElement() : getTextareaElement()
  const currentTriggerPosition = isInModal ? modalTriggerPosition.value : triggerPosition.value

  if (!textarea) {
    closeVariablePicker()
    return
  }

  const currentValue = textValue.value
  const currentCursorPosition = textarea.selectionStart || 0

  let beforeText: string
  let afterText: string
  let insertPosition: number

  if (currentTriggerPosition < currentCursorPosition && currentValue.charAt(currentTriggerPosition) === '/') {
    beforeText = currentValue.substring(0, currentTriggerPosition)
    afterText = currentValue.substring(currentCursorPosition)
    insertPosition = currentTriggerPosition
  } else {
    // 通过按钮触发：在当前光标位置插入
    beforeText = currentValue.substring(0, currentTriggerPosition)
    afterText = currentValue.substring(currentTriggerPosition)
    insertPosition = currentTriggerPosition
  }
  // 更新文本值
  textValue.value = beforeText + variableText + afterText

  // 设置新的光标位置
  nextTick(() => {
    textarea.focus()
    const newPosition = insertPosition + variableText.length
    textarea.setSelectionRange(newPosition, newPosition)
  })

  // 关闭选择器
  closeVariablePicker()
}

// 点击外部关闭选择器
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement

  // 检查是否点击了插入变量按钮
  const isInsertButton = target.closest('.toolbar-btn') || target.closest('.insert-variable-btn')

  if (
    showVariablePicker.value &&
    variablePickerRef.value &&
    !variablePickerRef.value.contains(target) &&
    !isInsertButton
  ) {
    closeVariablePicker()
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('click', handleClickOutside)

  // 初始化获取节点变量
  const { availableNodes: nodes, nodesOutputVars: vars } = getAvailableNodesAndVars()
  availableNodes.value = nodes
  nodesOutputVars.value = vars
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 监听nodeId变化，重新获取变量
watch(
  () => props.nodeId,
  () => {
    const { availableNodes: nodes, nodesOutputVars: vars } = getAvailableNodesAndVars()
    availableNodes.value = nodes
    nodesOutputVars.value = vars
  }
)
</script>

<style scoped lang="scss">
.variable-textarea {
  position: relative;
  width: 100%;

  .editor-container {
    width: 100%;
    border: 1px solid var(--color-border-2);
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.3s ease;

    .editor-toolbar {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      background: var(--color-fill-2);
      border-bottom: 1px solid var(--color-border-2);

      .toolbar-left {
        .editor-label {
          font-size: 12px;
          font-weight: 600;
          color: var(--color-text-2);
        }
      }

      .toolbar-right {
        display: flex;
        gap: 4px;

        .toolbar-btn {
          padding: 4px 8px;
          font-size: 12px;

          &:hover {
            background: var(--color-fill-3);
          }
        }
      }
    }

    .editor-content {
      padding: 12px;

      .variable-textarea-input {
        width: 100%;

        :deep(.arco-textarea) {
          width: 100%;
          border: none;
          box-shadow: none;
          background: transparent;

          &:focus {
            border: none;
            box-shadow: none;
          }
        }
      }
    }
  }

  // 全屏编辑模态框样式
  .fullscreen-editor {
    .fullscreen-textarea {
      width: 100%;

      :deep(.arco-textarea) {
        width: 100%;
        border: 1px solid var(--color-border-2);
        border-radius: 6px;
        font-size: 14px;
        line-height: 1.5;

        &:focus {
          border-color: var(--color-primary-6);
          box-shadow: 0 0 0 2px var(--color-primary-light-2);
        }
      }
    }
  }
}
// 模态框头部样式
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 100%;

  .modal-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--color-text-1);
    line-height: 1;
  }

  .modal-toolbar {
    display: flex;
    gap: 4px;
    align-items: center;

    .toolbar-btn {
      padding: 4px 8px;
      font-size: 12px;
      height: auto;
      line-height: 1;

      &:hover {
        background: var(--color-fill-3);
      }
    }
  }
}
// 变量选择器容器样式
.simple-variable-picker {
  background: #ffffff;
  border: 1px solid var(--color-border-2);
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  overflow: hidden;

  .picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--color-fill-2);
    border-bottom: 1px solid var(--color-border-2);
    font-size: 12px;
    font-weight: 600;
    color: var(--color-text-2);
  }

  .picker-content {
    padding: 0;

    // 确保VariableList组件样式正常显示
    :deep(.variable-list) {
      border: none;
      box-shadow: none;
      border-radius: 0;
    }
  }
}
</style>
