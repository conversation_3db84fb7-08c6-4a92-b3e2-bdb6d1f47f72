<template>
  <div id="ksNode" class="rich-text">
    <a-popover trigger="click" class="custom-rich-text" style="user-select: none">
      <span class="add-btn" style="user-select: none">
        添加变量
        <icon-plus />
      </span>

      <template #content>
        <VarList @handleSelectVar="handleSelectVar" />
      </template>
    </a-popover>
    <!--min-h-[200px]-->
    <div
      ref="editorRef"
      contenteditable="true"
      class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all rich-text-content"
      placeholder="在此输入内容，或从左侧选择变量插入..."
      @input="handleInput"
    />
    <!--@click="handleClick"-->
    <!--@keydown="handleKeydown"-->
  </div>
</template>
<script setup lang="ts">
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import VarList from '@/views/app/workflow/nodes/http/components/rich-text/VarList.vue'
import { Message } from '@arco-design/web-vue'

defineOptions({ name: 'RichText' })
const emits = defineEmits(['handleInputVar'])
const props = withDefaults(
  defineProps<{
    nodeInfo?: any
    varItem?: any
    varString?: any
  }>(),
  {
    nodeInfo: () => ({}),
    varItem: () => ({}),
    varString: ''
  }
)
// 处理编辑器输入
const handleInput = () => {
  updateRawContent()
}

// 编辑器引用
const editorRef: any = ref(null)
// 原始文本内容
const rawContent = ref('')
// 更新原始内容
const updateRawContent = () => {
  const editor = editorRef.value
  if (!editor) return

  // 提取文本内容，保留变量标签格式
  let content = ''
  const nodes = Array.from(editor.childNodes)

  nodes.forEach((node: any) => {
    if (node.nodeType === Node.TEXT_NODE) {
      content += node.textContent
    } else if (node.hasAttribute('data-variable')) {
      content += `{{#${node.getAttribute('data-group-variable')}.${node.getAttribute('data-variable')}#}}`
    } else {
      // 处理其他元素（如换行）
      content += node.textContent || ''
    }
  })

  rawContent.value = content

  // console.log('代码值：', rawContent.value)
  // updatePreview();

  emits('handleInputVar', rawContent.value)
}

const getTags = (group, option) => {
  // 创建变量标签元素
  const variableTag = document.createElement('span')
  variableTag.className =
    'inline-flex items-center bg-blue-100 text-blue-800 rounded-sm px-3 py-1 mr-1 mb-1 cursor-pointer transition-all hover:bg-blue-200 var-item'
  let groutId = ''
  let varId = ''
  if (group.isStartNode && option.variable.startsWith('sys.')) {
    groutId = 'sys'
    varId = option?.variable.replace('sys.', '')
  } else {
    groutId = group?.nodeId
    varId = option?.variable
  }
  // variableTag.setAttribute('data-group-variable', group?.nodeId)
  variableTag.setAttribute('data-group-variable', groutId)
  // variableTag.setAttribute('data-variable', option?.variable)
  variableTag.setAttribute('data-variable', varId)
  variableTag.setAttribute('contenteditable', 'false')

  // 设置标签内容
  variableTag.innerHTML = `
    <span class="var-name" title="${group?.title}/${option?.variable}">${group?.title}/${option?.variable}</span>
    <span class="ml-1 text-blue-600 hover:text-blue-800">×</span>
  `

  // 添加点击事件删除标签
  variableTag.addEventListener('click', (e: any) => {
    e.preventDefault()
    e.stopPropagation()
    // 如果点击的是删除按钮
    if (e.target.classList.contains('hover:text-blue-800')) {
      removeVariableTag(variableTag)
    }
  })
  return variableTag
}
// 变量弹框的list
const varList = ref<any[]>([])
type CustomMatchArray = [fullMatch: string, variable?: string, constant?: string] & RegExpMatchArray
const nodesStore = useNodesStore()
onMounted(() => {
  // 获取varList
  varList.value = nodesStore.parentNodesVarList

  // 根据返回的变量块转成对应的格式
  const str = props.varString
  // 正则表达式：匹配所有 {{#xxxx#}} 格式的标记
  // const regex = /\{\{#([^#]+)#\}\}/g;
  // 正则表达式：匹配标记或中间内容
  const regex = /\{\{#([^#]+)#\}\}|([^{]+(?:\{(?!\{#)[^{]*)*)/g
  // 使用 matchAll 方法获取所有匹配结果
  const matches: any = [...str.matchAll(regex)]

  const chunkList: any[] = []
  // 输出匹配结果
  matches.forEach((match: any) => {
    if (match[1]) {
      // 变量
      chunkList.push(match[1].split('.'))
    } else if (match[2]) {
      // 常量str
      chunkList.push(match[2])
    }
  })

  chunkList.forEach((item: any) => {
    if (typeof item == 'string') {
      // 创建文本节点
      const textNode = document.createTextNode(item)
      editorRef.value.appendChild(textNode)
    } else {
      // tag节点
      let group: any = null
      let varTemp = '' // var的取值
      if (item[0] == 'sys') {
        group = varList.value.find((v) => v.isStartNode)
        varTemp = [item[0], [item[1]]].join('.')
      } else {
        group = varList.value.find((v) => v.nodeId == item[0])
        varTemp = item[1]
      }

      let option: any = null
      group &&
        group.vars.forEach((v) => {
          if (v.variable == varTemp) {
            option = v
          }
        })

      const editor = editorRef.value
      if (!editor) return

      // 创建变量标签元素
      if (group) {
        const variableTag = getTags(group, option)
        const selection = window.getSelection()

        // editorRef.value.insertNode(variableTag)
        editorRef.value.appendChild(variableTag)
      }
    }
  })
})

const handleSelectVar = (group, option) => {
  // console.log('group,option:', group, option)
  const editor = editorRef.value
  if (!editor) return

  // 创建变量标签元素
  const variableTag = getTags(group, option)
  // 获取当前光标位置并插入标签
  insertAtCursor(editor, variableTag)

  // 更新原始内容
  updateRawContent()
}

// 在光标位置插入元素
const insertAtCursor = (editor, element) => {
  const selection: any = window.getSelection()
  let className = ''
  try {
    if (selection.anchorNode.nodeType == 3) {
      className = selection.anchorNode.parentNode.getAttribute('class')
    } else if (selection.anchorNode.nodeType == 1) {
      className = selection.anchorNode.getAttribute('class')
    }
  } catch (e) {
    console.log(e)
  }

  if (!className.includes('rich-text-content')) {
    Message.warning('请先选择要输入的位置')
    return false
  }
  if (selection.rangeCount > 0) {
    const range = selection.getRangeAt(0)
    range.deleteContents()
    range.insertNode(element)

    // 设置新的光标位置
    const after = range.cloneRange()
    after.setStartAfter(element)
    after.collapse(true)
    selection.removeAllRanges()
    selection.addRange(after)

    // 触发输入事件
    const event = new Event('input', { bubbles: true })
    editor.dispatchEvent(event)
  }
}
// 移除变量标签
const removeVariableTag = (tag) => {
  const editor = editorRef.value
  if (!editor || !tag) return

  // 获取标签位置
  const range = document.createRange()
  range.selectNodeContents(tag)

  // 删除标签
  tag.remove()

  // 设置光标位置到删除的位置
  const selection: any = window.getSelection()
  selection.removeAllRanges()
  selection.addRange(range)

  // 更新原始内容
  updateRawContent()
}
</script>
<style scoped lang="scss">
.rich-text {
  :deep(.var-name) {
    width: 100px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.var-item {
  user-select: none;
}

.rich-text-content {
  height: auto;
}
</style>
