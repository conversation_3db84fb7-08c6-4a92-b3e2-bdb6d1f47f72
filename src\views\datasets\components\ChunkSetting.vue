<template>
  <div class="segment-mode-cards">
    <div class="setting-card-header">
      <div class="setting-title">分段设置</div>
    </div>
    <!-- 通用分段模式 -->
    <div class="segment-mode-card" :class="{ active: segmentMode === 'normal' }" @click="selectSegmentMode('normal')">
      <div class="card-header">
        <div class="mode-icon">
          <icon-settings class="icon" />
        </div>
        <div class="mode-info">
          <div class="mode-title">通用</div>
          <div class="mode-desc">通用文本分块模式，检索和召回的块是相同的</div>
        </div>
        <a-radio :model-value="segmentMode === 'normal'" />
      </div>
      <!-- 通用模式详情 -->
      <div v-show="segmentMode === 'normal'" class="card-content">
        <div class="form-row">
          <div class="form-item flex-1">
            <div class="form-label">
              分段标识符
              <a-tooltip position="top">
                <template #content>
                  <div class="tooltip-content">
                    分隔符是用于分隔文本的字符。\n\n和\n是常用于分隔段落和行的分隔符。用逗号连接分隔符(\n\n,\n)，当段落超过最大块长度时，会按行进行分割。你也可以使用自定义的特殊分隔符（例如***）。
                  </div>
                </template>
                <icon-question-circle class="hint-icon" />
              </a-tooltip>
            </div>
            <a-input v-model="normalSegmentConfig.separator" placeholder="\n\n" />
          </div>
          <div class="form-item flex-1">
            <div class="form-label">分段最大长度</div>
            <div class="input-with-unit">
              <a-input-number v-model="normalSegmentConfig.maxLength" :min="100" :max="2000" placeholder="1024" />
              <span class="unit">characters</span>
            </div>
          </div>
          <div class="form-item">
            <div class="form-label">
              分段重叠长度
              <a-tooltip position="top">
                <template #content>
                  <div class="tooltip-content">
                    设置分段之间的重叠长度可以保留分段之间的语义关系，提升召回效果。建议设置为最大分段长度的10%-25%
                  </div>
                </template>
                <icon-question-circle class="hint-icon" />
              </a-tooltip>
            </div>
            <div class="input-with-unit">
              <a-input-number v-model="normalSegmentConfig.minLength" :min="10" :max="500" placeholder="50" />
              <span class="unit">characters</span>
            </div>
          </div>
        </div>
        <div class="form-item">
          <div class="form-label">文本预处理规则</div>
          <div class="checkbox-list">
            <a-checkbox v-model="processingRules.removeSpaces">替换连续空白的空格、换行符和制表符</a-checkbox>
            <a-checkbox v-model="processingRules.removeUrls">删除所有 URL 和电子邮件地址</a-checkbox>
          </div>
        </div>
        <div class="form-item">
          <div class="form-label-with-checkbox">
            <a-checkbox v-model="useQaSplitting">使用 Q&A 分段，语言</a-checkbox>
            <a-select
              :model-value="qaLanguage"
              :disabled="!useQaSplitting"
              style="width: 160px"
              size="small"
              @update:model-value="handleQaLanguageChange"
            >
              <a-option value="zh_CN">Chinese Simplified</a-option>
              <a-option value="en_US">English</a-option>
            </a-select>
            <a-tooltip position="top">
              <template #content>
                <div class="tooltip-content">开启后将会消耗额外的 token</div>
              </template>
              <icon-question-circle class="hint-icon" />
            </a-tooltip>
          </div>
          <a-alert v-if="useQaSplitting" type="warning">开启后将会消耗额外的 token</a-alert>
        </div>

        <div class="form-actions">
          <a-button type="outline" @click="previewBlocks('normal')">
            <template #icon><icon-search /></template>
            预览块
          </a-button>
          <a-button type="text">
            <template #icon><icon-refresh /></template>
            重置
          </a-button>
        </div>
      </div>
    </div>
    <!-- 父子分段模式 -->
    <div
      class="segment-mode-card"
      :class="{ active: segmentMode === 'parent-child' }"
      @click="selectSegmentMode('parent-child')"
    >
      <div class="card-header">
        <div class="mode-icon">
          <icon-relation class="icon" />
        </div>
        <div class="mode-info">
          <div class="mode-title">父子分段</div>
          <div class="mode-desc">使用父子模式时，子块用于检索，父块用作上下文</div>
        </div>
        <a-radio :model-value="segmentMode === 'parent-child'" />
      </div>

      <!-- 父子分段模式详情 -->
      <div v-show="segmentMode === 'parent-child'" class="card-content">
        <!-- 父块用作上下文 -->
        <div class="parent-child-section">
          <div class="section-header">
            <div class="section-title">父块用作上下文</div>
          </div>

          <div class="section-content">
            <div class="parent-mode-options">
              <div
                class="mode-option"
                :class="{ selected: parentMode === 'paragraph' }"
                @click="parentMode = 'paragraph'"
              >
                <div class="mode-option-header">
                  <div class="mode-icon">
                    <icon-archive />
                  </div>
                  <div class="mode-info">
                    <div class="mode-name">段落</div>
                    <div class="mode-desc">
                      此模式根据分隔符和最大块长度将文本拆分为段落，使用拆分文本作为检索的父块
                    </div>
                  </div>
                  <a-radio :model-value="parentMode === 'paragraph'" />
                </div>

                <div v-show="parentMode === 'paragraph'" class="mode-option-content">
                  <div class="form-row">
                    <div class="form-item flex-1">
                      <div class="form-label">
                        分段标识符
                        <a-tooltip position="top">
                          <template #content>
                            <div class="tooltip-content">
                              文本分隔符是用于分隔文本的字符。建议用
                              将原始文档划分为较大的父级片段。您也可以自定义特殊分隔符。
                            </div>
                          </template>
                          <icon-question-circle class="hint-icon" />
                        </a-tooltip>
                      </div>
                      <a-input v-model="parentChildConfig.parent.separator" placeholder="\n\n" style="width: 90%" />
                    </div>

                    <div class="form-item flex-1">
                      <div class="form-label">分段最大长度</div>
                      <div class="input-with-unit">
                        <a-input-number
                          v-model="parentChildConfig.parent.maxLength"
                          :min="100"
                          :max="2000"
                          placeholder="1024"
                          style="width: 90%"
                        />
                        <span class="unit">characters</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div
                class="mode-option"
                :class="{ selected: parentMode === 'fulltext' }"
                @click="parentMode = 'fulltext'"
              >
                <div class="mode-option-header">
                  <div class="mode-icon">
                    <icon-file />
                  </div>
                  <div class="mode-info">
                    <div class="mode-name">全文</div>
                    <div class="mode-desc">
                      整个文档用作父块并直接检索。请注意，出于性能原因，超过10000个标记的文本将被自动截断。
                    </div>
                  </div>
                  <a-radio :model-value="parentMode === 'fulltext'" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 子块用于检索 -->
        <div class="parent-child-section">
          <div class="section-header">
            <div class="section-title">子块用于检索</div>
          </div>
          <div class="section-content section-content1">
            <div class="form-item flex-1">
              <div class="form-label">
                分段标识符
                <a-tooltip position="top">
                  <template #content>
                    <div class="tooltip-content">
                      文本分隔符是用于分隔文本的字符、建议使用
                      将父级片段拆分为较小的子级片段。您也可以自定义特殊分隔符。
                    </div>
                  </template>
                  <icon-question-circle class="hint-icon" />
                </a-tooltip>
              </div>
              <a-input v-model="parentChildConfig.child.separator" placeholder="\n" style="width: 80%" />
            </div>
            <div class="form-item flex-1">
              <div class="form-label">分段最大长度</div>
              <div class="input-with-unit">
                <a-input-number
                  v-model="parentChildConfig.child.maxLength"
                  :min="50"
                  :max="1000"
                  style="width: 80%"
                  placeholder="200"
                />
                <span class="unit">characters</span>
              </div>
            </div>
          </div>
          <div class="form-item">
            <div class="form-label">文本预处理规则</div>
            <div class="checkbox-list">
              <a-checkbox v-model="processingRules.removeSpaces">替换连续空白的空格、换行符和制表符</a-checkbox>
              <a-checkbox v-model="processingRules.removeUrls">删除所有 URL 和电子邮件地址</a-checkbox>
            </div>
          </div>
        </div>
        <div class="form-actions">
          <a-button type="outline" @click="previewBlocks('parent-child')">
            <template #icon><icon-search /></template>
            预览块
          </a-button>
          <a-button type="text">
            <template #icon><icon-refresh /></template>
            重置
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

// 定义接收的props
const props = defineProps({
  segmentMode: {
    type: String,
    default: 'normal'
  },
  normalSegmentConfig: {
    type: Object,
    default: () => ({
      separator: '\\n\\n',
      maxLength: 1024,
      minLength: 50
    })
  },
  parentChildConfig: {
    type: Object,
    default: () => ({
      parent: {
        separator: '\\n\\n',
        maxLength: 1024
      },
      child: {
        separator: '\\n',
        maxLength: 512
      }
    })
  },
  parentMode: {
    type: String,
    default: 'paragraph'
  },
  processingRules: {
    type: Object,
    default: () => ({
      removeSpaces: true,
      removeUrls: false
    })
  },
  useQaSplitting: {
    type: Boolean,
    default: false
  },
  qaLanguage: {
    type: String,
    default: 'zh_CN'
  }
})

// 定义向父组件发送的事件
const emit = defineEmits([
  'segment-mode-change',
  'parent-mode-change',
  'normal-segment-config-change',
  'parent-child-config-change',
  'processing-rules-change',
  'qa-splitting-change',
  'qa-language-change',
  'preview-blocks'
])

// 创建本地响应式变量
const normalSegmentConfig = ref({ ...props.normalSegmentConfig })
const parentChildConfig = ref({ ...props.parentChildConfig })
const processingRules = ref({ ...props.processingRules })
const useQaSplitting = ref(props.useQaSplitting)
const parentMode = ref(props.parentMode)

// 初始化标志，确保只在组件初始化时同步一次props
const isInitialized = ref(false)

// 只在组件挂载时同步一次props，之后完全由子组件自己管理状态
onMounted(() => {
  if (!isInitialized.value) {
    normalSegmentConfig.value = { ...props.normalSegmentConfig }
    parentChildConfig.value = { ...props.parentChildConfig }
    processingRules.value = { ...props.processingRules }
    useQaSplitting.value = props.useQaSplitting
    parentMode.value = props.parentMode
    isInitialized.value = true
  }
})

// 监听本地变量变化并发出事件 - 移除防递归逻辑，因为不再有双向绑定
watch(
  normalSegmentConfig,
  (newValue) => {
    if (isInitialized.value) {
      emit('normal-segment-config-change', newValue)
    }
  },
  { deep: true }
)

watch(
  parentChildConfig,
  (newValue) => {
    if (isInitialized.value) {
      emit('parent-child-config-change', newValue)
    }
  },
  { deep: true }
)

watch(
  processingRules,
  (newValue) => {
    if (isInitialized.value) {
      emit('processing-rules-change', newValue)
    }
  },
  { deep: true }
)

watch(useQaSplitting, (newValue) => {
  if (isInitialized.value) {
    emit('qa-splitting-change', newValue)
  }
})

// 分段模式选择
const selectSegmentMode = (mode: string) => {
  emit('segment-mode-change', mode)
}

// 父模式变更
watch(parentMode, (newValue) => {
  if (isInitialized.value) {
    emit('parent-mode-change', newValue)
  }
})

// 处理QA语言变更
const handleQaLanguageChange = (lang: string) => {
  emit('qa-language-change', lang)
}

// 预览块
const previewBlocks = (mode: string) => {
  emit('preview-blocks', mode)
}
</script>
