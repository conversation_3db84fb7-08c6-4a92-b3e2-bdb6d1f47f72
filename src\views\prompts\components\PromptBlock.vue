<template>
  <div class="block-item-wrapper">
    <VueDraggable v-model="promptBlockData" :animation="150" :drag-class="`icon-drag`" class="drag-block">
      <div v-for="item in promptBlockData" :key="item.id" class="block-item">
        <div class="block-item-header">
          <div class="flex items-center">
            <a-button class="block-item-header-btn" style="padding: 0">
              <icon-drag-dot-vertical class="icon-drag" :size="14" />
            </a-button>
            <a-dropdown
              @select="
                (value) => {
                  defaultSelect(value, item)
                }
              "
            >
              <a-button class="block-item-header-btn">
                <span class="pr-1">{{ item.defaultPrompt }}</span>
                <icon-expand :rotate="-45" :size="10" />
              </a-button>
              <template #content>
                <a-doption value="System">System</a-doption>
                <a-doption value="User">User</a-doption>
                <a-doption value="Assistant">Assistant</a-doption>
                <a-doption value="Placeholder">Placeholder</a-doption>
              </template>
            </a-dropdown>
          </div>
          <div class="flex items-center">
            <a-button v-if="item.blockCollapse" class="block-item-header-btn" @click="promptBlockItemShow(item)">
              <icon-up-circle />
            </a-button>
            <a-button v-if="!item.blockCollapse" class="block-item-header-btn" @click="promptBlockItemShow(item)">
              <icon-down-circle />
            </a-button>
            <a-button class="block-item-header-btn" @click="delPromptBlockItem(item.id)">
              <icon-delete />
            </a-button>
          </div>
        </div>
        <div v-show="item.blockCollapse" class="block-item-form">
          <PromptBlockForm :blockItemForm="item.blockItemForm" />
        </div>
      </div>
    </VueDraggable>
    <a-button class="w-full" @click="addPrompt">
      <icon-plus />
      添加提示词块
    </a-button>
  </div>
</template>

<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus'
import { ExtendedPromptDTO } from '@/apis/prompts/type'
import PromptBlockForm from './PromptBlockForm.vue'
import { nanoid } from 'nanoid'

defineOptions({
  name: 'PromptBlock'
})

interface BlockFormType extends ExtendedPromptDTO {
  id?: string
  defaultPrompt?: string
  blockItemForm?: ExtendedPromptDTO
  blockCollapse?: boolean
}
const promptBlockData = ref<BlockFormType[]>([
  {
    id: nanoid(),
    defaultPrompt: 'System',
    blockItemForm: {
      name: '',
      promptKey: '',
      content: '',
      tags: [],
      description: '',
      helpInfo: ''
    },
    blockCollapse: true
  }
])

const defaultSelect = (value: string, record) => {
  record.defaultPrompt = value
}

const addPrompt = () => {
  promptBlockData.value.push({
    id: nanoid(),
    defaultPrompt: 'System',
    blockItemForm: {
      name: '',
      promptKey: '',
      content: '',
      tags: [],
      description: '',
      helpInfo: ''
    },
    blockCollapse: true
  })
}
const promptBlockItemShow = (record) => {
  record.blockCollapse = !record.blockCollapse
}

const delPromptBlockItem = (id) => {
  const blockIndex = promptBlockData.value?.findIndex((ele) => ele.id === id)
  promptBlockData.value.splice(blockIndex, 1)
}

defineExpose({
  promptBlockData
})
</script>

<style scoped lang="scss">
.block-item-wrapper,
.drag-block {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.block-item {
  border: 1px solid #c9c9d6;
  border-radius: 8px;
  &-header {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    color: var(--color-text-3);
    background-color: #fff;
    border-radius: 8px;
    &-btn {
      height: 22px;
      border: none;
      padding: 0 8px;
      color: var(--color-text-3);
    }
  }
  &-form {
    border-top: 1px solid #c9c9d6;
    padding: var(--padding);
    background-color: #fff;
    border-radius: 0 0 8px 8px;
  }
}
</style>
