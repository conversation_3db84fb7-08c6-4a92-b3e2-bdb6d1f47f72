<template>
  <div
    class="relative flex h-8 cursor-pointer items-center rounded-lg px-2 border ring-inset hover:ring-[0.5px] border-util-colors-indigo-indigo-600 bg-state-accent-hover ring-util-colors-indigo-indigo-600 border-workflow-block-parma-bg bg-workflow-block-parma-bg pr-[30px] hover:border-components-input-border-active"
  >
    <!--icon-->
    <ModelIcon class="mr-1.5 !h-5 !w-5" />
    <!--name-->
    <ModelName class="mr-1.5 text-text-primary" />
    <template v-if="false">
      <div class="mr-1 truncate text-[13px] font-medium text-text-primary">
        {{ 111 }}
      </div>
    </template>
    <div class="absolute right-2 top-[3px] h-3.5 w-3.5 text-text-tertiary">
      <icon-down />
    </div>
  </div>
</template>
<script setup lang="ts">
import ModelName from '@/views/app/configuration/components/model-parameter-modal/model/ModelName.vue'
import ModelIcon from '@/views/app/configuration/components/model-parameter-modal/model/ModelIcon.vue'

// const props = withDefaults(
//   defineProps<{
//     currentProvider?: Object
//     currentModel?: Object
//   }>(),
//   {
//     currentProvider: () => {
//     },
//     currentModel: () => {
//     }
//   }
// )
</script>
<style scoped lang="scss"></style>
