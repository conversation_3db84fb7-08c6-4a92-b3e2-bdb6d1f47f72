<template>
  <a-modal :visible="visible" :ok-text="okText" :hide-cancel="hideCancel" @ok="handleOk" @cancel="handleCancel">
    <template #title>
      {{ dialogTitle }}
    </template>
    <!--发送邀请-->
    <div v-if="!hideCancel">
      <a-alert>对方在登录后可以访问你的团队数据。</a-alert>

      <a-typography>
        <a-typography-title :heading="5">邮箱</a-typography-title>
      </a-typography>

      <div class="multi-email p-23px">
        <a-space :wrap="true">
          <a-tag v-for="(email, index) in emailList" :key="email" closable @close="handleDeleteEmail(index)">
            {{ email }}
          </a-tag>
        </a-space>

        <a-input
          v-model="userEmail"
          placeholder="请输入邮箱"
          @blur="handleInputEmail"
          @press-enter="handleInputEmail"
        />
      </div>

      <div class="select-role">
        <a-select v-model="selectedRole">
          <template #label="{ data }">
            <span>邀请为{{ data?.label }}用户</span>
          </template>
          <a-option v-for="role in roleList" :key="role.value" :value="role.value">
            {{ role.label }}
          </a-option>
        </a-select>
      </div>
    </div>

    <!--邀请地址-->
    <div v-else>
      <a-typography>
        <a-typography-title :heading="5">邀请已发送</a-typography-title>
        <a-typography-text type="secondary">邀请已发送，对方登录平台后即可访问你得团队数据。</a-typography-text>
      </a-typography>
      <a-typography>
        <a-typography-title :heading="6">邀请链接</a-typography-title>

        <a-typography-paragraph v-for="(item, index) in invitationList" :key="index" copyable>
          <!--地址后续修改成const变量吧-->
          {{ DOMAIN_NAME + item.url }}
        </a-typography-paragraph>
      </a-typography>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import type { InvitationItem } from '@/apis/user/type.ts'

defineOptions({ name: 'AddUser' })

import { Email } from '@/utils/regexp'
import { ref } from 'vue'
import { inviteUserHttp } from '@/apis/user'
import { DOMAIN_NAME } from '@/views/app/workflow/constant/common'

const emits = defineEmits(['closeAddUserDialog'])

const visible = ref(true)
const dialogTitle = ref('添加团队成员')

const selectedRole = ref('normal')
// 抽离到const里面
const roleList = [
  // {label:'所有者',value:'owner',desc:''},
  { label: '管理员', value: 'admin', desc: '能够建立应用程序和管理团队设置' },
  { label: '编辑', value: 'editor', desc: '能够建立并编辑应用程序，不能管理团队设置' },
  { label: '成员', value: 'normal', desc: '只能使用应用程序，不能建立应用程序' }
  // {label: '知识库管理员', value: 'datasetOperator', desc: '只能管理知识库'},
  // {label: '移出团队', value: 'removeFromTeam', desc: '将取消团队访问'}
]

const invitationList = ref<InvitationItem[]>([])
const okText = ref('确定')
const hideCancel = ref(false)
const handleOk = async () => {
  // visible.value = false

  if (hideCancel.value) {
    emits('closeAddUserDialog', 'ok')
    return false
  }
  const params = {
    emails: emailList.value || [],
    role: selectedRole.value || '',
    language: 'zh-Hans'
  }
  try {
    const res = await inviteUserHttp(params)
    // emits('closeAddUserDialog', 'ok')
    invitationList.value = res.invitation_results || []
    okText.value = '好的'
    hideCancel.value = true
  } catch (error) {
    console.log(error)
  }
}
const handleCancel = () => {
  // visible.value = false
  emits('closeAddUserDialog', 'cancel')
}

const userEmail = ref('')
const emailList = ref<string[]>([])
const handleInputEmail = () => {
  const flag = Email.test(userEmail.value)

  if (flag) {
    emailList.value.push(userEmail.value)
    userEmail.value = ''
  }
}
const handleDeleteEmail = (index) => {
  emailList.value.splice(index, 1)
}
</script>

<style scoped lang="scss">
.multi-email {
  height: 200px;
  text-align: left;
  line-height: 1.21428571em;
  padding: 0.4em 0.5em;
  background: #fff;
  border: 1px solid rgba(34, 36, 38, 0.15);
}

.select-role {
  margin-top: var(--margin);
}
</style>
