import { useVueFlow } from '@vue-flow/core'
import { useWorkflowStore } from '@/stores'
import type { LoopFinishedResponse } from '@/views/app/workflow/types/workflow'

export const useWorkflowNodeLoopFinished = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowNodeLoopFinished = (params: LoopFinishedResponse) => {
    const { data } = params
    const { workflowRunningData, setWorkflowRunningData } = workflowStore
    const { nodes, setNodes, edges, setEdges } = useVueFlow()

    const workflowData = workflowRunningData
    const currentIndex = workflowData.tracing!.findIndex((item) => item.id === data.id)
    if (currentIndex > -1) {
      workflowData.tracing![currentIndex] = {
        ...workflowData.tracing![currentIndex],
        ...workflowData
      }
    }
    setWorkflowRunningData(workflowData)

    if (nodes.value?.length) {
      const newNodes = nodes.value.map((node) => {
        if (node.id === data.node_id) {
          node.data._runningStatus = data.status
        }
        return node
      })
      setNodes(newNodes)
    }

    if (edges.value?.length) {
      const newEdges = edges.value.map((edge) => {
        if (edge.target === data.node_id) {
          edge.data = {
            ...edge.data,
            _targetRunningStatus: data.status
          }
        }
        return edge
      })
      setEdges(newEdges)
    }
  }

  return {
    handleWorkflowNodeLoopFinished
  }
}
