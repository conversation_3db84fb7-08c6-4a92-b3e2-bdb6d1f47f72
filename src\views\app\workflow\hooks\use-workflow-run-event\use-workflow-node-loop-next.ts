import { useVueFlow } from '@vue-flow/core'
import { NodeRunningStatus, type LoopNextResponse } from '@/views/app/workflow/types/workflow'
export const useWorkflowNodeLoopNext = () => {
  const handleWorkflowNodeLoopNext = (params: LoopNextResponse) => {
    const { data } = params
    const { nodes, setNodes } = useVueFlow()

    if (nodes.value?.length) {
      const newNodes = nodes.value.map((node) => {
        if (node.id === data.node_id) {
          node.data._loopIndex = data.index
        }
        if (node.parentNode === data.node_id) {
          node.data._waitingRun = true
          node.data._runningStatus = NodeRunningStatus.Waiting
        }
        return node
      })
      setNodes(newNodes)
    }
  }

  return {
    handleWorkflowNodeLoopNext
  }
}
