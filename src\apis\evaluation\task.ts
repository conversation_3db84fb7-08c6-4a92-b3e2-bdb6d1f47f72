import http from '@/utils/http'
import type * as TsakTypes from './task-types'

// evalTask
/**
 * @name SaveUsingPost
 * @summary 新增评测任务
 * @request POST:/evalTask
 */
export function addTask(params: TsakTypes.EvalTaskSaveDTO) {
  const url = `/evaluation_api/evalTask`

  return http.post<TsakTypes.REvalTask>(url, params)
}

/**
 * @name UpdateUsingPut
 * @summary 修改
 * @request PUT:/evalTask
 */
export function updateTask(params: TsakTypes.EvalTaskUpdateDTO) {
  const url = `/evaluation_api/evalTask`

  return http.put<TsakTypes.REvalTask>(url, params)
}

/**
 * @name DeleteUsingDelete
 * @summary 删除
 * @request DELETE:/evalTask
 */
export function deleteTask(params: number[]) {
  const url = `/evaluation_api/evalTask`

  return http.del<TsakTypes.RBoolean>(url, params)
}

/**
 * @name UpdateAllUsingPut
 * @summary 修改所有字段
 * @request PUT:/evalTask/all
 */
export function updateAllTask(params: TsakTypes.EvalTask) {
  const url = `/evaluation_api/evalTask/all`

  return http.put<TsakTypes.REvalTask>(url, params)
}

/**
 * @name PageUsingPost
 * @summary 分页查询评测任务
 * @request POST:/evalTask/page
 */
export function getTaskPage(params: TsakTypes.PageParamsEvalTaskPageQuery) {
  const url = `/evaluation_api/evalTask/page`

  return http.post<TsakTypes.RIPageEvalTask>(url, params)
}

/**
 * @name QueryUsingPost
 * @summary 批量查询
 * @request POST:/evalTask/query
 */
export function queryTask(params: TsakTypes.EvalTask) {
  const url = `/evaluation_api/evalTask/query`

  return http.post<TsakTypes.RListEvalTask>(url, params)
}

/**
 * @name GetUsingGet
 * @summary 查询评测任务详情
 * @request GET:/evalTask/{id}
 */
export function getTaskDetail(id: string) {
  const url = `/evaluation_api/evalTask/${id}`

  return http.get<TsakTypes.REvalTask>(url)
}

/**
 * @name ExecuteTaskUsingPut
 * @summary 执行评测任务
 * @request PUT:/evalTask/{id}/execute
 */
export function executeTask(id: number, status: number) {
  const url = `/evaluation_api/evalTask/${id}/execute/${status}`
  return http.put<TsakTypes.RBoolean>(url)
}
