# 简介

**Ai Web** 是一个基于 Vue3、Vite、TypeScript、Arco Design 等相关技术栈的管理平台，它使用了最新的前端技术栈，内置丰富的主题配置。

## 特性

- **最新技术栈**：使用 Vue3 / Vite 等前端前沿技术开发，使用高效率的 pnpm 包管理器
- **TypeScript**：应用程序级 JavaScript 的语言
- **主题**：丰富可配置的主题、暗黑模式
- **代码规范**：丰富的规范插件及极高的代码规范

## 安装使用

- 安装依赖

```bash
pnpm install
```

- 运行

```bash
npm run dev
```

- 打包

```bash
npm run build
```

## vs code 插件安装

```bash
1. Prettier - Code formatter
2. Vue - Official
3. Vue 3 Snippets
```

## 注意

```bash
由于升级了vite6.x，node版本建议是: ^18.0.0 || ^20.0.0 || >=22.0.0，目前使用19.18.0
```

**vite 官网地址：** <https://cn.vitejs.dev/>

## 常见问题

**为什么选择 Arco Design 组件库，而不是 Element Plus?**

- [Element Plus 对比 Arco design](https://juejin.cn/post/7294219581894705190)

**为什么全局组件使用前缀 Ai?**

- 全局组件设置了按需引入，使用前缀，方便和局部组件做区分

**为什么组件使用单词大写开头 (PascalCase)命名写法?**

- 本项目`.vue`文件名以及在模板使用中，均采用大写`开头 (PascalCase)`命名方式

- 参考 Vue2 官网-风格指南: <https://v2.cn.vuejs.org/v2/style-guide/>

- 组件命名：`单文件组件的文件名应该要么始终是单词大写开头 (PascalCase)，要么始终是横线连接 (kebab-case)`

- 其他优点：方便搜索（横线连接 (kebab-case)对搜索没那么方便）

**为什么css类名推荐横线连接 (kebab-case)?**

- 参考大部分大网站，都是这个命名规则，禁用： `.myClass`这种

**页面显示异常?**

- `页面必须要保留一个根元素！！！`**

**页面无法缓存?**

请检查页面是否配置了`name`，且名称是否与数据一致

```js
defineOptions({ name: 'Home' })
```

```js
{
  path: '/home/<USER>',
  name: 'Home', // 检查name是否一致
  component: () => import('@/views/home/<USER>')
}
```

## 项目规范

### 开发规范

### 文件规范

- `src/types下为全局默认类型声明文件，业务类型声明文件放到业务文件夹内` 

#### .vue 文件行数规范

一个 .vue 文件行数建议不超过 **`400`** 行，超过建议组件化拆分

#### 变量命名

```vue
<script setup lang="ts">
// 一般情况下，引用类型使用 const 定义，基本类型使用 let 定义
const arr = []
const obj = {}
const fn = () => {
  console.log('123')
}

let num = 10
let str = 'abc'
let flag = false

// vue3 中 ref、reactive 返回的是一个引用类型
const loading = ref(false)
const person = reactive({ name: '张三', age: 20 })
</script>
```

```vue
<script setup lang="ts">
const loading = ref(false) // 加载
const visible = ref(false) // 显示隐藏
const disabled = ref(true) // 是否被禁用
const showAddModal = ref(false) // 新增功能的模态框显示隐藏
const showAddDrawer = ref(false) // 新增功能的抽屉显示隐藏
// 或者 是否显示弹窗
const isShowDialog = ref<boolean>(false)
const isLogin = ref(false) // 是否登录
const isVIP = ref(false) // 是否是VIP用户

// 表单不建议 formData，直接最简（懒得写这么长）
const form = reactive({
  name: '',
  phone: '',
  remark: ''
})

const userInfo = ref({}) // 用户信息
const tableData = ref([]) // 表格数据
const treeData = ref([]) // 树结构数据

// 对象数组 列表数据最好后面加个 List 或者 Data
const companyList = ref([])
const checkedList = ref([])
const selectedList = ref([])
const addressList = ref([])
const userList = [
  { id: '01', name: '张三' },
  { id: '02', name: '李四' }
]
const tableData = []
const optionsList = [
  { label: '哈哈', value: 1 },
  { label: '嘻嘻', value: 2 }
]

// 非对象数组 在字母后面加s
const ids = []
const selectedIds = []
const activedKeys = []
const nums = [3, 5, 6]
const strs = ['aaa', 'bbb', 'ccc']

const getData = () => {
  const arr = []
  nums.forEach((item) => {
    arr.push({ value: item })
  })
}

const getUserList = async () => {
  const res = await Api.getUserPage()
  userList = res.data
}
</script>
```

#### 方法命名

```vue
<script setup lang="ts">
// 编辑
const edit = () => {}
const onEdit = () => {}
const handleEdit = () => {}

// 新增
const add = () => {}
const onAdd = () => {}
const handleAdd = () => {}

// 删除
// const delete = () => {} // 不推荐，delete 是JS关键词
const del = () => {}
const onDelete = () => {}
const handleDelete = () => {}
const remove = () => {}

// 重命名
const rename = () => {}
const onRename = () => {}
const handleRename = () => {}

// 批量删除
const mulDelete = () => {}
const onMulDelete = () => {}
const handleMulDelete = () => {}

// 搜索
const search = () => {}

// 返回
const back = () => {}

// 提交
const submit = () => {}

// 确认
const confirm = () => {}
const ok = () => {}

// 取消
const cancel = () => {}

// 打开 | 关闭
const open = () => {}
const close = () => {}

// 保存
const save = () => {}

// 获取表格列表
const getTableData = () => {}
const getTableList = () => {}
</script>
```

#### 常用前缀

| 前缀       | 前缀 + 命名                  |                         |
| ---------- | ---------------------------- | --------------------------- |
| is         | isTimeout                    | 是否超时                    |
| has        | hasUserInfo                  | 有没有用户信息              |
| handle     | handleLogin                  | 处理登录                    |
| calc       | calcAverageSpeed             | 计算平均速度                |

#### 通用缩写

| 源单词       | 缩写 |
| ----------- | ---- |
| message     | msg  |
| information | info |
| button      | btn  |
| background  | bg   |
| response    | res  |
| request     | req  |
| image       | img  |
| utility     | util |
| property    | prop |
| source      | src  |
| boolean     | bool |
| error       | err  |
| settings    | set  |

#### vue 相关的命名

```vue
<script setup lang="ts">
// 表单建议使用 form 命名(简洁)，不必要使用 formData, 同时使用 reactive
const form = reactive({
  name: '',
  phone: ''
})
</script>
```

```vue
<script setup lang="ts">
// 如果属性比较多
const getInitForm = () => ({
  name: '',
  phone: '',
  email: '',
  sex: 1,
  age: ''
})

const form = reactive(getInitForm())

// 重置form
const resetForm = () => {
  for (const key in form) {
    delete form[key]
  }
  Object.assign(form, getInitForm())
}
</script>
```

```vue
<script setup lang="ts">
import { useAppStore, useUserStore } from '@/stores'
import { useLoading } from '@/hooks'

// stores 或 hooks 的使用命名规则定义
const appStore = useAppStore()
const userStore = useUserStore()

const { loading, setLoading } = useLoading()
</script>
```

#### 写法技巧

尽量使用三元表达式

```js
// 优化前
let isEdit = true
let title = ''
if (isEdit) {
  title = '编辑'
} else {
  title = '新增'
}

// 优化后
let title = isEdit ? '编辑' : '新增'
```

善用 includes 方法

```js
// 优化前
if (type === 1 || type === 2 || type === 3) {
}

// 优化后，此种方式在vue模板也可使用
if ([1, 2, 3].includes(type)) {
}
```

使用箭头函数简化函数

```js
// 优化前
function add(num1, num2) {
  return num1 + num2
}

// 优化后
const add = (num1, num2) => num1 + num2
```

尽量减少 if else if

```vue
<script setup lang="ts">
// 比例进度条颜色 尽量减少 if else if
const getProportionColor = (proportion: number) => {
  if (proportion < 30) return 'danger'
  if (proportion < 60) return 'warning'
  return 'success'
}
</script>
```

```js
// 优化前
const status = 200
const message = ''
if (status === 200) {
  message = '请求成功'
} else if (status === 404) {
  message = '请求出错'
} else if (status === 500) {
  message = '服务器错误'
}

// 优化后
const status = 200
const messageMap = {
  200: '请求成功',
  404: '请求出错',
  500: '服务器错误'
}
const message = messageMap[status] || '服务器错误'
```

如果函数参数超过两个，建议优化

```vue
<script setup lang="ts">
function createUser(name, phone, age) {
  console.log('姓名', name)
  console.log('手机', phone)
  console.log('年龄', age)
}

// 这种方式在使用的时候可读性很差，扩展性差，而且不易于维护
createUser('张三', '178****2828', 20)

function createUser2({ name, phone, age }) {
  console.log('姓名', name)
  console.log('手机', phone)
  console.log('年龄', age)
}

// 以对象传参更直观，更好扩展和维护
createUser2({ name: '张三', phone: '178****2828', age: 20 })
</script>
```

#### 正则使用示例

文件位置：src/utils/regexp.ts

```ts
/** @desc 正则-手机号码 */
export const Phone = /^1[3-9]\d{9}$/

/** @desc 正则-邮箱 */
export const Email = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/

/** @desc 正则-6位数字验证码正则 */
export const Code_6 = /^\d{6}$/

/** @desc 正则-4位数字验证码正则 */
export const Code_4 = /^\d{4}$/

/** @desc 正则-16进颜色值 #333 #8c8c8c */
export const ColorRegex = /^#?([a-fA-F0-9]{6}|[a-fA-F0-9]{3})$/

/** @desc 正则-只能是中文 */
export const OnlyCh = /^[\u4e00-\u9fa5]+$/gi

/** @desc 正则-只能是英文 */
export const OnlyEn = /^[a-zA-Z]*$/

/** @desc 登录注册-密码 6-16位大小写字母、数字的js正则 */
export const Password = /^[a-zA-Z0-9]{6,16}$/
```

使用

```vue
<script lang="ts" setup>
import { reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
// 正则推荐一下导入方式
import { Phone } from '@/utils/regexp'

const form = reactive({
  name: '',
  phone: ''
})

const submit = () => {
  if (!Phone.test(form.phone)) {
    return Message.warning('请输入正确手机号格式')
  }
}
</script>
```

页面模板 CSS 类名采用半角连接符(-)

```vue
<template>
  <div class="detail">
    <h3 class="title">标题</h3>
    <section class="table-box">
      <table></table>
    </section>
  </div>
</template>
```

#### 全局组件--命名规范

组件命名：**单文件组件的文件名应该要么始终是单词大写开头 (PascalCase)，要么始终是横线连接 (kebab-case)**

可参考 Vue2 官网-风格指南: <https://v2.cn.vuejs.org/v2/style-guide/>

```vue
AiTitle.vue
AiThemeBtn.vue
AiSvgIcon.vue
```

#### 局部组件--命名规范

组件命名：**单文件组件的文件名应该要么始终是单词大写开头 (PascalCase)，要么始终是横线连接 (kebab-case)**

可参考 Vue2 官网-风格指南: <https://v2.cn.vuejs.org/v2/style-guide/>

```js
QuickOperation.vue
Welcome.vue
...
```

#### 文件夹命名--命名规范 (采用中划线-)

1、文件名建议只使用小写字母，不使用大写字母

2、名称较长时采用半角连接符(-)分隔

```js
home/index.vue
user/index.vue
user-detail/index.vue
```

#### 弹窗组件 Modal、抽屉组件 Drawer 的一般封装

```vue
<template>
  <a-modal v-model:visible="visible" :title="title" @ok="confirm">
    <!-- 内容 -->
  </a-modal>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue'

const visible = ref(false)
const detailId = ref('')
const isEdit = computed(() => !!detailId.value) // 判断是新增还是编辑模式
const title = computed(() => (isEdit.value ? '编辑' : '新增'))

const add = () => {
  detailId.value = ''
  visible.value = true
}

// 如果这里的参数超过两个，建议优化成对象形式
// const edit = ({ id, taskId }) = {
//   console.log(id, taskId)
// }

const edit = (id: string) => {
  detailId.value = id
  visible.value = true
  // getDetail() 回显操作
}

defineExpose({ add, edit })

const confirm = () => {
  console.log('点击了确认按钮')
}
</script>
```

使用

**`模板里使用自定义组件：使用大写开头驼峰，双击好复制，对于搜索便利`**

```vue
<template>
  <EditModal ref="EditModalRef"></EditModal>
</template>

<script setup lang="ts">
import EditModal from './EditModal.vue'

const EditModalRef = ref<InstanceType<typeof EditModal>>()

// 新增
const onAdd = () => {
  EditModalRef.value?.add()
}

// 编辑
const onEdit = (item: PersonItem) => {
  EditModalRef.value?.edit(item.id)
}
</script>
```

#### usePagination(hooks) 的使用

文件位置：@/hooks/modules/usePagination.ts

**使用方法：**

```js
import { reactive, toRefs } from 'vue'
import type { PaginationProps } from '@arco-design/web-vue'

type Callback = () => void

type Options = {
  defaultPageSize: number
}

export default function usePagination(callback: Callback, options: Options = { defaultPageSize: 10 }) {
  const pagination = reactive({
    showPageSize: true,
    current: 1,
    pageSize: options.defaultPageSize,
    total: 0,
    onChange: (size: number) => {
      pagination.current = size
      callback && callback()
    },
    onPageSizeChange: (size: number) => {
      pagination.current = 1
      pagination.pageSize = size
      callback && callback()
    }
  })

  const changeCurrent = pagination.onChange
  const changePageSize = pagination.onPageSizeChange
  function setTotal(value: number) {
    pagination.total = value
  }

  const { current, pageSize, total } = toRefs(pagination)

  return {
    current,
    pageSize,
    total,
    pagination,
    changeCurrent,
    changePageSize,
    setTotal
  }
}
```

使用方式 1

```vue
<template>
  <!-- ... -->
  <div class="table-box">
    <a-table
      row-key="id"
      :columns="columns"
      :data="tableData"
      :pagination="{ showPageSize: true, total: total, current: current, pageSize: pageSize }"
      @page-change="changeCurrent"
      @page-size-change="changePageSize"
    >
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { usePagination } from '@/hooks'

const { current, pageSize, total, changeCurrent, changePageSize, setTotal } = usePagination(() => {
  getTableData()
})

// 从第一页开始查询
changeCurrent(1)
</script>
```

使用方法 2 (更少代码)

```vue
<template>
  <!-- ... -->
  <div class="table-box">
    <a-table row-key="id" :columns="columns" :data="tableData" :pagination="pagination"> </a-table>
  </div>
</template>

<script setup lang="ts">
import { usePagination } from '@/hooks'

const { pagination, setTotal } = usePagination(() => {
  getTableData()
})

// 从第一页开始查询
pagination.onChange(1)

// 搜索
const search = () => {
  pagination.onChange(1)
}

const search2 = () => {
  pagination.current = 1
  getTableData()
}
</script>
```

注意：

```vue
<script setup lang="ts">
import { usePagination } from '@/hooks'

const { pagination, setTotal } = usePagination(() => {
  getTableData()
})

const form = reactive({
  name: '',
  status: '',
  page: pagination.current, // 此种方式不会响应
  size: pagination.pageSize // 此种方式不会响应
})

const getTableData = async () => {
  const res = await getData(form)
}
</script>
```

改为

```vue
<script setup lang="ts">
import { usePagination } from '@/hooks'

const { pagination, setTotal } = usePagination(() => {
  getTableData()
})

const form = reactive({
  name: '',
  status: ''
})

const getTableData = async () => {
  const res = await getData({ ...form, page: pagination.current, size: pagination.pageSize })
}
</script>
```

或者

```vue
<script setup lang="ts">
import { usePagination } from '@/hooks'

const { pagination, setTotal } = usePagination(() => {
  form.page = pagination.current
  form.size = pagination.pageSize
  getTableData()
})

const form = reactive({
  name: '',
  status: '',
  page: pagination.current,
  size: pagination.pageSize
})

const getTableData = async () => {
  const res = await getData(form)
}
</script>
```

```vue
<template>
  <div>
    <a-pagination v-bind="pagination" />
  </div>
</template>

<script setup lang="ts">
import { usePagination } from '@/hooks'

const { pagination, setTotal } = usePagination(() => {
  getTableData()
})

const form = reactive({
  name: '',
  status: ''
})

const getTableData = async () => {
  const res = await getData({ ...form, page: pagination.current, size: pagination.pageSize })
}
</script>
```

```js
import { useResetReactive } from '@/hooks'

const [form, resetForm] = useResetReactive({
  id: '',
  name: '',
  phone: '',
  status: false
})
```

重置表单数据 resetForm()

`resetForm`方法为什么要加上以下代码

```js
for (const key in form) {
  delete form[key]
}
```

比如一个编辑弹窗，点击编辑，会根据 id 查详情，有时候为了方便，直接把详情的数据赋值到 form 里面，这就会导致重置的时候，有详情的属性冗余，以下举个例子

```js
const form = { name: '' }
const detail = { name: '张三', status: 1 }
Object.assign(form, detail)
console.log(form) // { name: '张三', status: 1 }

// 如果直接重置
Object.assign(form, { name: '' })
console.log(form) // { name: '', status: 1 } 有额外属性冗余，status会不经意的随着保存操作提交到后台
```

#### 组件使用建议

能使用组件尽量使用组件实现页面布局

flex 布局尽量使用 **Row** 组件

```js
<template>
  <a-row justify="space-between" align="center"> </a-row>
</template>
```

按钮间间隔尽量使用 **Space** 组件

```js
<template>
  <a-space :size="10">
    <a-button>返回</a-button>
    <a-button type="primary">提交</a-button>
  </a-space>
</template>
```

状态色文本，尽量使用

```js
<template>
  <a-typography-text>主要文本</a-typography-text>
  <a-typography-text type="secondary">二级文本</a-typography-text>
  <a-typography-text type="primary">主题色文本</a-typography-text>

  <a-typography-text type="primary">已提交</a-typography-text>
  <a-typography-text type="success">审核通过</a-typography-text>
  <a-typography-text type="warning">未提交</a-typography-text>
  <a-typography-text type="danger">不通过</a-typography-text>
</template>
```

Link 组件使用场景

```js
<template>
  <a-table>
    <a-table-column title="操作" :width="150" fixed="right">
      <template #cell="{ record }">
        <a-space>
          <a-link :hoverable="false">编辑</a-link>
          <a-link :hoverable="false">编辑</a-link>
          <a-link :hoverable="false">删除</a-link>
        </a-space>
      </template>
    </a-table-column>
  </a-table>
</template>
```

#### 其他规范

可参考 Vue2 官网-风格指南: <https://v2.cn.vuejs.org/v2/style-guide/> , 其中一些规范也可借鉴

### 提交规范

#### 提交信息

```js
<类型>(<范围>): <描述>

<修改列举>
```

- `feat`: 新功能
- `fix`: 修复bug
- `doc`: 文档变更
- `style`: 代码样式变更（不影响代码含义的变动）
- `refactor`/`rf`: 代码重构
- `perf`: 优化相关
- `test`: 添加或修改测试用例
- `infra`: 构建过程或辅助工具的变动

## Vue 相关

[Vue3 官网](https://cn.vuejs.org/)

[Vue-Router](https://router.vuejs.org/zh/)

[Vite](https://cn.vitejs.dev/)

[Pinia](https://pinia.web3doc.top/)

## 选型对比

> [!Note]
> 在引入第三方模块时，你可以将选型结果描述在下方。将引入的包名作为二级标题，引用标签（`<blockquote>`）内简单描述需求。

## swagger自动化接口代码生成

- 安装依赖：pnpm add -D swagger-typescript-api（本工程已安装）
- 打开swagger接口文档地址，copy完整json到根目录swagger/swagger.json中，若没有改文件，可自行创建swagger.json
- 执行 npm run swagger
- 生成的api + type文件在./swagger/apis下，将其copy至自己建的api文件中即可
