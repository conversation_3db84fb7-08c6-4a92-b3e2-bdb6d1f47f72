<template>
  <div class="step-content step3-content">
    <div class="completion-container">
      <h2 class="completion-title">
        <span class="completion-icon">🎉</span>
        <span v-if="isAddingDocument">文档已上传</span>
        <span v-else>知识库已创建</span>
      </h2>
      <span v-if="isAddingDocument" class="completion-desc">
        文档已上传至知识库：{{ datasetDetail.name || '未命名知识库' }}，你可以在知识库的文档列表中找到它。
      </span>
      <span v-else class="completion-desc">我们自动为该知识库起了个名称，您也可以随时修改</span>

      <div class="dataset-info">
        <div v-if="!isAddingDocument" class="dataset-name">
          <h3>知识库名称</h3>
          <div class="dataset-name-display">
            <div class="name-icon"><icon-folder /></div>
            <div class="name-text">
              {{
                props.datasetForm.name ||
                (props.uploadedFiles.length > 0 ? props.uploadedFiles[0].name.replace(/\.\w+$/, '') : '未命名知识库')
              }}
            </div>
          </div>
        </div>

        <div class="files-status">
          <h3 class="status-title">
            <span v-if="props.processingStatus.completed">嵌入已完成</span>
            <span v-else class="processing-title">
              嵌入处理中
              <icon-loading class="loading-icon" />
            </span>
          </h3>
          <div v-if="props.processingStatus.loading" class="processing-status">
            <a-spin />
            <p>正在处理文档，请稍候...</p>
            <a-progress :percent="props.processingStatus.percent" />
          </div>
          <div v-else class="file-list">
            <div v-for="file in props.uploadedFiles" :key="file.name" class="file-item">
              <div class="file-icon"><icon-file /></div>
              <div class="file-name">{{ file.name }}</div>
              <div class="file-status" :class="{ success: props.processingStatus.completed }">
                <icon-check v-if="props.processingStatus.completed" />
                <icon-loading v-else />
                {{ props.processingStatus.completed ? '完成' : '处理中' }}
              </div>
            </div>
          </div>
        </div>

        <div class="settings-overview">
          <h3>配置信息</h3>
          <div class="settings-list">
            <div class="setting-item">
              <span class="setting-label">分段模式</span>
              <span class="setting-value">{{ props.segmentMode === 'normal' ? '通用' : '父子分段' }}</span>
            </div>
            <div class="setting-item">
              <span class="setting-label">最大分段长度</span>
              <span class="setting-value">
                {{
                  props.segmentMode === 'normal'
                    ? props.normalSegmentConfig.maxLength
                    : props.parentMode === 'paragraph'
                      ? props.parentChildConfig.parent.maxLength
                      : props.parentChildConfig.child.maxLength
                }}
              </span>
            </div>
            <div class="setting-item">
              <span class="setting-label">索引方式</span>
              <span class="setting-value">{{ props.indexingMethod === 'high_quality' ? '高质量' : '经济模式' }}</span>
            </div>
            <div class="setting-item">
              <span class="setting-label">检索设置</span>
              <span class="setting-value">{{ getSearchMethodsText() }}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="action-buttons">
        <a-button class="api-btn">
          <template #icon><icon-code /></template>
          Access the API
        </a-button>
        <a-button type="primary" class="go-btn" @click="goToDataset">
          前往文档
          <template #icon><icon-right /></template>
        </a-button>
      </div>
    </div>

    <!-- <div class="next-steps">
          <div class="next-steps-icon">📖</div>
          <h3>接下来做什么</h3>
          <p>当文档成功索引后，知识库即可提供给应用用户作为上下游服务，你也可以在简单配置后将其作为独立的 ChatGPT 插件发布。</p>
        </div> -->
  </div>
</template>
<script setup lang="ts">
import { useRouter } from 'vue-router'
import { computed } from 'vue'

// Props定义
const props = defineProps({
  // 上传的文件列表
  uploadedFiles: {
    type: Array as () => Array<{
      uid: string
      name: string
      size: number
      response?: {
        id: string
        [key: string]: any
      }
      [key: string]: any
    }>,
    default: () => []
  },
  // 处理状态
  processingStatus: {
    type: Object,
    default: () => ({
      loading: true,
      percent: 0,
      completed: false,
      error: false,
      errorMessage: '',
      datasetId: '',
      batchId: ''
    })
  },
  // 分段设置
  segmentMode: {
    type: String,
    default: 'normal'
  },
  normalSegmentConfig: {
    type: Object,
    default: () => ({
      separator: '\\n\\n',
      maxLength: 1024,
      minLength: 50
    })
  },
  parentChildConfig: {
    type: Object,
    default: () => ({
      parent: {
        separator: '\\n\\n',
        maxLength: 1024
      },
      child: {
        separator: '\\n',
        maxLength: 512
      }
    })
  },
  parentMode: {
    type: String,
    default: 'paragraph'
  },
  // 索引方法
  indexingMethod: {
    type: String,
    default: 'high_quality'
  },
  // 搜索方法
  searchMethod: {
    type: String,
    default: 'hybrid'
  },
  datasetForm: {
    type: Object as () => {
      name: string
      description: string
      fileIds: string[]
      datasetId: string
      [key: string]: any
    },
    default: () => ({
      name: '',
      description: '',
      fileIds: [],
      datasetId: ''
    })
  },
  isAddingDocument: {
    type: Boolean,
    default: false
  },
  datasetDetail: {
    type: Object,
    default: () => ({})
  }
})

// 获取路由
const router = useRouter()

// 获取搜索方法文本
const getSearchMethodsText = () => {
  const methods: string[] = []
  if (props.searchMethod === 'vector' || props.searchMethod === 'hybrid') methods.push('向量检索')
  if (props.searchMethod === 'fulltext' || props.searchMethod === 'hybrid') methods.push('全文检索')
  if (props.searchMethod === 'hybrid') methods.push('混合检索')
  return methods.join('、') || '未设置'
}

// 前往知识库
const goToDataset = () => {
  // 如果有处理完成的datasetId，则跳转到该知识库的文档页面
  if (props.processingStatus.datasetId) {
    router.push(`/datasets/${props.processingStatus.datasetId}/documents`)
  }
  // 如果datasetForm中有datasetId，则使用该ID
  else if (props.datasetForm.datasetId) {
    router.push(`/datasets/${props.datasetForm.datasetId}/documents`)
  }
}

// 向父组件暴露方法
defineExpose({
  goToDataset
})
</script>
<style scoped lang="scss">
.step-content {
  width: 70%;

  // 步骤3样式
  &.step3-content {
    display: flex;
    gap: 24px;

    .completion-container {
      flex: 3;
      background-color: var(--color-bg-2);
      border-radius: 12px;
      padding: 24px;

      .completion-icon {
        font-size: 30px;
        text-align: left;
        margin-bottom: 12px;
      }

      .completion-title {
        font-size: 20px;
        font-weight: 500;
        text-align: left;
        margin-bottom: 8px;
      }

      .completion-desc {
        display: block;
        text-align: left;
        color: var(--color-text-3);
        font-size: 14px;
        margin-bottom: 24px;
      }

      .dataset-info {
        .dataset-name,
        .files-status,
        .settings-overview {
          margin-bottom: 24px;

          h3 {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 12px;

            &.status-title {
              display: flex;
              align-items: center;

              .processing-title {
                display: flex;
                align-items: center;
                color: var(--color-warning);

                .loading-icon {
                  margin-left: 8px;
                  font-size: 14px;
                  animation: spin 1.2s linear infinite;
                }
              }
            }
          }
        }

        .dataset-name {
          .dataset-name-display {
            display: flex;
            align-items: center;
            padding: 10px 12px;
            border-radius: 6px;
            background-color: var(--color-fill-1);
            border: 1px solid var(--color-border-2);

            .name-icon {
              margin-right: 8px;
              color: var(--color-text-3);
            }

            .name-text {
              font-size: 14px;
              color: var(--color-text-1);
            }
          }
        }

        .files-status {
          .file-list {
            .file-item {
              display: flex;
              align-items: center;
              gap: 12px;
              padding: 8px 5px;
              background-color: #155aef0a;
              margin-bottom: 5px;

              .file-icon {
                font-size: 14px;
                color: var(--color-text-3);
              }

              .file-name {
                flex: 1;
                font-size: 14px;
              }

              .file-status {
                display: flex;
                align-items: center;
                gap: 4px;
                font-size: 13px;

                &.success {
                  color: var(--success-6);
                }
              }
            }
          }
        }

        .settings-overview {
          .settings-list {
            .setting-item {
              display: flex;
              margin-bottom: 8px;

              .setting-label {
                flex: 1;
                color: var(--color-text-3);
              }

              .setting-value {
                flex: 2;
              }
            }
          }
        }
      }

      .action-buttons {
        display: flex;
        justify-content: flex-start;
        gap: 16px;
        margin-top: 40px;

        .api-btn,
        .go-btn {
          min-width: 140px;
          height: 36px;
          border-radius: 6px;
        }
      }
    }

    .next-steps {
      flex: 1.2;
      background-color: var(--color-fill-1);
      border-radius: 12px;
      padding: 34px;
      margin-top: 6%;
      height: 300px;

      .next-steps-icon {
        font-size: 24px;
        margin-bottom: 12px;
      }

      h3 {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 16px;
      }

      p {
        color: var(--color-text-2);
        line-height: 1.6;
      }

      .step3-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        margin-top: 24px;

        .prev-btn,
        .submit-btn {
          min-width: 90px;
          height: 36px;
          border-radius: 6px;
        }
      }
    }
  }
}
</style>
