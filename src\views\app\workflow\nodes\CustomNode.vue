<template>
  <div class="custom-node">
    <div class="custom-node-icon" :style="{ backgroundColor: data.color }">
      <AiSvgIcon style="width: 14px; height: 14px" :name="`workflow-${type}`" />
      <!-- <icon :icon="`icon: ${item.icon}`" /> -->
    </div>
    <div class="custom-node-text">
      {{ data.label }}
    </div>
    <Handle type="target" :position="Position.Left" />
    <NodeList class="custom-node-add" :popoverInstance="true" :nodeId="props.id" :nodeProps="props">
      <Handle type="source" class="custom-node-handle" :position="Position.Right">
        <icon-plus :style="{ pointerEvents: 'none' }" />
      </Handle>
    </NodeList>
  </div>
</template>

<script setup lang="ts">
import { Handle, Position } from '@vue-flow/core'
import type { NodeProps } from '@vue-flow/core'
import NodeList from './node-list.vue'

const props = defineProps<NodeProps>()
</script>
<style scoped lang="scss">
.custom-node {
  display: flex;
  align-items: center;
  padding: 4px 14px;
  height: 50px;
  width: 180px;
  background-color: var(--color-bg-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 12px;

  &-icon {
    margin-right: 8px;
    font-size: 14px;
    height: 24px;
    width: 24px;
    border-radius: 8px;
    background-color: var(--color-fill-3);
    text-align: center;
    line-height: 24px;
    color: var(--color-text-1);
  }

  &-text {
    font-size: 18px;
    font-weight: 500;
    color: var(--color-text-2);
  }

  &-handle {
    background: rgb(var(--primary-6));
    height: 10px;
    width: 2px;
    border-radius: 0;
    border: none;
    min-width: 2px;

    .arco-icon {
      display: none;
    }
  }

  &:hover {
    .custom-node-handle {
      background-color: rgb(var(--primary-6));
      border-radius: 50%;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
      cursor: pointer;
    }

    .arco-icon {
      display: inline-block;
      width: 14px;
      height: 14px;
      color: var(--color-white);
    }
  }

  &-add {
    position: absolute;
    right: 0;
    pointer-events: none;
  }
}
</style>
