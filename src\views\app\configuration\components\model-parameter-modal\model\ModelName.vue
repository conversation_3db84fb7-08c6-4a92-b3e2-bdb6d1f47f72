<template>
  <div
    class="system-sm-regular flex items-center gap-0.5 overflow-hidden truncate text-ellipsis text-components-input-text-filled"
  >
    <!--  模型名称-->
    <div class="truncate" :title="renderName(currentModel?.label)">
      {{ renderName(currentModel?.label) }}
    </div>

    <div v-if="showModelType && currentModel?.model_type" class="flex items-center gap-0.5">
      <a-tag size="small">{{ modelTypeFormat(currentModel?.model_type) }}</a-tag>
    </div>
    <div v-if="currentModel?.model_properties?.mode && showMode">
      <a-tag size="small">
        {{ (currentModel?.model_properties?.mode as string).toLocaleUpperCase() }}
      </a-tag>
    </div>

    <div v-if="currentModel?.features && showFeatures">
      <!--TODO-todo: xxx-->
    </div>
    <div v-if="currentModel?.model_properties?.context_size && showContextSize">
      <a-tag size="small">
        {{ sizeFormat(currentModel?.model_properties?.context_size as number) }}
      </a-tag>
    </div>
  </div>
</template>
<script setup lang="ts">
import { modelTypeFormat, renderName, sizeFormat } from '@/views/app/workflow/utils/configuration'
import { useProviderStore } from '@/stores/modules/workflow/provider'

const props = withDefaults(
  defineProps<{
    showMode?: boolean
    showFeatures?: boolean
    showModelType?: boolean
    showContextSize?: boolean
  }>(),
  {
    showMode: true,
    showFeatures: true,
    showModelType: true,
    showContextSize: true
  }
)
const providerStore = useProviderStore()
const currentModel = ref({} as any)
onMounted(() => {
  currentModel.value = providerStore.provider.currentModel || {}
})
watch(
  () => providerStore.provider.currentModel,
  () => {
    currentModel.value = providerStore.provider.currentModel || {}
  }
)
</script>
<style scoped lang="scss"></style>
