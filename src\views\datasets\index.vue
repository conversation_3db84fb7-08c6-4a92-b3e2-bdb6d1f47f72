<template>
  <AiPageLayout class="datasets-page">
    <!-- 顶部选项卡 -->
    <!-- <div class="header-container">
      <div class="tabs-container">
        <a-tabs v-model:active-key="activeTab">
          <a-tab-pane key="datasets" title="知识库" />
          <a-tab-pane key="api" title="API" />
        </a-tabs>
      </div>
    </div> -->

    <!-- 根据选中的标签页显示不同的内容 -->
    <router-view v-if="$route.path !== '/datasets/index'" />
    <component :is="currentTab" v-else />
  </AiPageLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import DatasetsTab from './components/DatasetsTab.vue'
import ApiTab from './components/ApiTab.vue'

const route = useRoute()

// 当前激活的标签页
const activeTab = ref('datasets')

// 根据当前标签选择要显示的组件
const currentTab = computed(() => {
  return activeTab.value === 'datasets' ? DatasetsTab : ApiTab
})
</script>

<style scoped lang="scss">
.datasets-page {
  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    margin-bottom: 20px;

    @media (max-width: 992px) {
      flex-direction: column;
      align-items: flex-start;
    }

    .tabs-container {
      flex-shrink: 0;
    }
  }

  :deep(.arco-tabs-header-nav) {
    padding-left: 0;
  }
}
</style>
