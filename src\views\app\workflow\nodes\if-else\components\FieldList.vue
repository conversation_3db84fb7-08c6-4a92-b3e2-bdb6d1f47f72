<template>
  <div class="space-y-1">
    <Ifnode :nodeId="nodeId" :nodeInfo="nodeInfo" />
    <Elsenode />
  </div>
</template>

<script setup lang="ts">
import Ifnode from '@/views/app/workflow/nodes/if-else/components/Ifnode.vue'
import Elsenode from '@/views/app/workflow/nodes/if-else/components/Elsenode.vue'
interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
}
const props = defineProps<{
  list: FieldType[]
  isChatMode?: false
  nodeInfo?: any
  nodeId: string
}>()
// const props = defineProps({
//   list: FieldType[],
//   isChatMode: false
// })
const defaultList = [
  {
    label: '',
    required: false,
    readonly: true,
    type: 'string',
    variable: 'sys.user_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'array[file]',
    variable: 'sys.files'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.app_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_run_id'
  }
]
const isChatModeDefaultList = [
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.query'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.dialogue_count'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.conversation_id'
  }
]
const processedList = computed(() => {
  if (props.isChatMode) {
  }
  return [
    ...props.list.map((item) => {
      const processed = {
        ...item,
        readonly: false
      }
      return processed
    }),
    ...defaultList,
    ...(props.isChatMode ? isChatModeDefaultList : [])
  ]
})
onMounted(() => {
  console.log(
    props.list.map((e) => {
      console.log(e)
    })
  )
})
</script>
<style scoped lang="scss"></style>
