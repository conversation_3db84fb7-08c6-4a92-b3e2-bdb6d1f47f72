import dayjs from 'dayjs'

// 精确校验参数类型
interface TypeFunction {
  [key: string]: (val: any) => boolean
}
export const Type: TypeFunction = (function () {
  const typeObj = {}
  const defaultTypeList = [
    'String',
    'Number',
    'Boolean',
    'Object',
    'Array',
    'Function',
    'File',
    'Null',
    'Undefined',
    'Map',
    'Error',
    'Date'
  ]
  for (const key of defaultTypeList) {
    typeObj[`is${key}`] = function (val: any): boolean {
      return Object.prototype.toString.call(val) === `[object ${key}]`
    }
  }
  return typeObj
})()

/**
 * 深拷贝
 * @param {object|array} 深拷贝引用类型数据
 * @returns
 */
import cloneDeep from 'lodash-es/cloneDeep'
export const deepCopy = cloneDeep

/**
 * 树型数组扁平化
 * @param {array} arr 树型数组
 * @param {string} childrenKey 子节点的 key 值
 */
export function flatMapTree(arr: any[], childrenKey: string = 'children'): any[] {
  if (!Array.isArray(arr)) return []
  return arr.flatMap((item) => [item, ...flatMapTree(item[childrenKey])])
}

/**
 * Vue 组件 v-model绑定
 * @param props
 * @param key
 * @param emit
 */
import { computed, getCurrentInstance, type WritableComputedRef } from 'vue'

type Recordable<T = any> = Record<string, T>
export function useVModel(
  props: Recordable,
  key = 'modelValue',
  emit?: ((event: string, ...args: any[]) => void) | undefined
): WritableComputedRef<unknown> {
  // 获取组件实例
  const vm = getCurrentInstance()

  const _emit = emit || vm?.emit
  // 事件名
  const event = `update:${key}`

  return computed({
    get() {
      return props[key]
    },
    set(val) {
      _emit && _emit(event, val)
    }
  })
}

/**
 * 生成由 a-z|A-Z|0-9|+|-|=生成的随机字符串
 * @param {number} num 位数
 * @returns {string}
 */
export function generateRandomString(num: number = 20): string {
  const characters: string = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+-='
  let randomString: string = ''

  for (let i = 0; i < num; i++) {
    const randomIndex: number = Math.floor(Math.random() * characters.length)
    randomString += characters.charAt(randomIndex)
  }

  return randomString
}

/**
 * 复制内容到粘贴板
 * @param {string} text 需要复制的文本
 */
export function copyText(text: string) {
  // 创建一个临时textarea元素
  const tempTextArea = document.createElement('textarea')
  // 设置textarea值为您想要复制的内容
  tempTextArea.value = text
  // 将textarea元素添加到文档中
  document.body.appendChild(tempTextArea)
  // 选择textarea中的文本
  tempTextArea.select()
  // 执行复制命令
  const isCopy = document.execCommand('copy')
  // 删除临时textarea元素
  document.body.removeChild(tempTextArea)
  return isCopy
}

/**
 * 获取远程图片地址
 * @param url
 * @returns string
 */
export const getImageUrl = (url: string): string => {
  const { VITE_APP_DEV_PROXY, VITE_APP_ENV } = import.meta.env
  const llmUrl = url.indexOf('arcana-llm-service/') == 0 ? '' : 'arcana-llm-service/'
  return VITE_APP_ENV === 'production' ? `${location.origin}/${llmUrl}${url}` : `${VITE_APP_DEV_PROXY}/${llmUrl}${url}`
}

/**
 * 随机生成code码；
 * @param length
 * @returns
 */
export function randomString(length: number) {
  const chars = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_'
  let result = ''
  for (let i = length; i > 0; --i) result += chars[Math.floor(Math.random() * chars.length)]
  return result
}

/**
 * base64图片下载
 */
export const downloadBase64Image = (base64String, options: { fileName?: string }) => {
  return new Promise((resolve, reject) => {
    try {
      const dataURLtoBlob = (dataUrl: string) => {
        const arr = dataUrl.split(',')
        const mime = arr[0].split(':')[1]
        const bstr = atob(arr[1])
        let n = bstr.length
        const u8arr = new Uint8Array(n)
        while (n--) {
          u8arr[n] = bstr.charCodeAt(n)
        }
        return new Blob([u8arr], {
          type: mime
        })
      }
      const fileType = base64String.split(';')[0].split('/')[1]
      const blobImage = dataURLtoBlob(base64String)
      const fileOfBlob = new File([blobImage], options.fileName || `${new Date()}.${fileType}`)
      const aDom = document.createElement('a')
      aDom.download = fileOfBlob.name
      const href = URL.createObjectURL(fileOfBlob)
      aDom.href = href
      document.body.appendChild(aDom)
      aDom.click() // 触发 a 标签的点击
      document.body.removeChild(aDom)
      URL.revokeObjectURL(href)
      resolve('success')
    } catch (error) {
      reject(error)
    }
  })
}

// 空值回显
export const formatEmptyText = (value) => {
  if (value !== undefined && value !== null && value !== '') {
    return value
  } else {
    return '--'
  }
}

/**
 * 获取字节最终单位，
 * @param val 当前数值
 * @param unit 初始单位简写 ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'YB', 'BB']
 * @returns {
 * divide: 可用除数, unit: 简写单位,
 * }
 */
export const getByteUnit = (val: number = 0, unit: string = 'B') => {
  const memorUtils = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'YB', 'BB']
  const getUnit = (value, num) => {
    const lng = num >= memorUtils.length ? memorUtils.length : num
    const divide = 1024 ** lng
    if (value / divide > 1024 && num < memorUtils.length) {
      return getUnit(value, num + 1)
    }
    if (value / divide > 1024 && num < memorUtils.length) {
      return {
        divide,
        unit: memorUtils[memorUtils.length - 1]
      }
    }
    if (!value || value / divide < 1024) {
      return {
        divide,
        unit: memorUtils[num]
      }
    }
  }
  return getUnit(val, memorUtils.indexOf(unit))
}

/**
 * 正则验证 - 包含数字、字母、下划线
 */
export const variable_regularization = RegExp(/^[a-zA-Z][a-zA-Z0-9_]*$/)
export const regularVerification_01 = (rule, value) => {
  if (value && !variable_regularization.test(value)) {
    return Promise.reject('只能包含英文名、数字或下划线，数字和_不能在首位')
  } else {
    return Promise.resolve()
  }
}

export const uuidTools = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0,
      v = c ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

/**
 * 禁止
 * @param current
 * @returns
 */
export const disabledAfterDate = (current) => dayjs(current).isAfter(dayjs())

export const dragControllerDiv = (
  parentClass = 'box',
  leftClass = 'left',
  rightClass = 'right',
  resizeClass = 'resize'
) => {
  const resize: any = document.getElementsByClassName(resizeClass)
  const right: any = document.getElementsByClassName(rightClass)
  const box = document.getElementsByClassName(parentClass)
  const left: any = document.getElementsByClassName(leftClass)
  for (let i = 0; i < resize.length; i++) {
    // 鼠标按下事件
    if (resize[i]) {
      resize[i].onmousedown = function (e: { clientX: any }) {
        //颜色改变提醒
        resize[i].style.background = getRootColor('-primary-color')
        const startX = e.clientX
        resize[i].left = resize[i].offsetLeft
        // 鼠标拖动事件
        document.onmousemove = function (e) {
          const endX = e.clientX
          let moveLen = resize[i].left + (endX - startX) // （endx-startx）=移动的距离。resize[i].left+移动的距离=左边区域最后的宽度
          const maxT = box[i].clientWidth - resize[i].offsetWidth // 容器宽度 - 左边区域的宽度 = 右边区域的宽度

          if (moveLen < 200) moveLen = 200 // 左边区域的最小宽度为32px
          if (moveLen > maxT - 400) moveLen = maxT - 400 //右边区域最小宽度为150px

          resize[i].style.left = moveLen // 设置左侧区域的宽度

          for (let j = 0; j < left.length; j++) {
            left[j].style.width = moveLen + 'px'
            right[j].style.width = box[i].clientWidth - moveLen - 10 + 'px'
          }
        }
        // 鼠标松开事件
        document.onmouseup = function () {
          //颜色恢复
          resize[i].style.background = '#f8f9fd'
          document.onmousemove = null
          document.onmouseup = null
          resize[i].releaseCapture && resize[i].releaseCapture() //当你不在需要继续获得鼠标消息就要应该调用ReleaseCapture()释放掉
        }
        resize[i].setCapture && resize[i].setCapture() //该函数在属于当前线程的指定窗口里设置鼠标捕获
        return false
      }
    }
  }
}

export function createDownloadLink(file_content, name, type) {
  const data = file_content
  let file_type, file_suffix
  switch (type) {
    case 'jsonl': {
      file_type = 'application/json'
      file_suffix = 'jsonl'
      break
    }
    case 'json': {
      file_type = 'application/json'
      file_suffix = 'json'
      break
    }
    case 'text': {
      file_type = 'text/plain'
      file_suffix = 'txt'
    }
  }
  const blob = new Blob([data], { type: file_type })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `${name}.${file_suffix}`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
}

export function getRootColor(color: string): string {
  const root = document.documentElement
  return getComputedStyle(root).getPropertyValue(color)?.trim() || ''
}
/**
 * 将小数转换为百分比格式, 小数位都为0则不显示小数位, eg: 100.00 -> 100%, 0.9234 -> 92.34%
 * @param value 需要转换的值
 * @param decimal 保留的小数位数
 * @param placeholder 空值占位符
 * @returns 格式化后的百分比字符串
 */
export function formatPercent(
  value: number | null | undefined,
  decimal: number = 2,
  placeholder: string = '-'
): string {
  if (value === null || value === undefined) {
    return placeholder
  }
  const formatVal = (value * 100) % 1 === 0 ? (value * 100).toFixed(0) : (value * 100).toFixed(decimal)
  return `${formatVal}%`
}
