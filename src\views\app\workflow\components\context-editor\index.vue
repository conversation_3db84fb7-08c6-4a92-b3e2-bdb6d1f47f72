<template>
  <div ref="toolbarTextarea" class="toolbar-textarea" :class="{ 'focus-state': isFocus }">
    <div class="toolbar">
      <div class="toolbar-left">
        <slot name="toolbarLeft" />
      </div>
      <div class="toolbar-right">
        <slot name="toolbarRight">
          <div class="class-toolbar">
            <span class="text-length">{{ content.length }}</span>
            <span class="divider">|</span>
            <span v-if="props.showJinja" class="jinja-switch">
              jinja
              <a-switch
                class="ml-1"
                :model-value="edition_type === EditionType.jinja2"
                size="small"
                @change="switchEditionType"
              />
            </span>
            <span class="inset-var-btn" @mousedown.stop="handleInsertButtonMouseDown" @click.stop="insetVar">{x}</span>
            <icon-delete v-if="props.showRemove" class="toolbar-icon" @click.stop="removeMessage" />
            <icon-copy class="toolbar-icon" @click.stop="copyClick" />
            <icon-fullscreen class="toolbar-icon" @click.stop="maximizeClick" />
          </div>
        </slot>
      </div>
    </div>
    <div ref="textareaInputBox" class="textarea-input-box">
      <!-- 富文本编辑器 - 支持变量标签显示 -->
      <div
        v-show="edition_type === EditionType.basic"
        ref="richEditor"
        class="rich-editor"
        contenteditable
        :data-placeholder="props.placeholder"
        @input="handleRichInput"
        @keydown="handleKeydown"
        @focus="handleFocus"
        @blur="handleBlur"
        @paste="handlePaste"
        @mouseup="handleMouseUp"
        @keyup="handleKeyUp"
      />

      <template v-if="props.showJinja">
        <div v-show="edition_type === EditionType.jinja2" ref="editorRef" class="code-mirror-box" />
      </template>
    </div>
  </div>
  <div class="error-info">
    <slot name="error">
      <span v-if="props.warn" class="error-text">{{ props.warn }}</span>
    </slot>
  </div>

  <!-- 变量选择器弹窗 -->
  <Teleport to="body">
    <div v-if="showTip" ref="selContext2" class="tips-dropdown" :style="tipsDropdownPosition" @click.stop>
      <VariableList
        :vars="availableVars"
        :var-type="VarType.any"
        :filter-var="filterVar"
        @select="selVar"
        @close="cancelSelVar"
      />
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, onUnmounted, reactive, watch } from 'vue'
import { useVModel } from '@vueuse/core'
import type { Node } from '@vue-flow/core'
import { Message } from '@arco-design/web-vue'
import { IconDelete, IconCopy, IconFullscreen } from '@arco-design/web-vue/es/icon'
import { EditionType, VarType } from '../../types/workflow'
import { VariableList } from '../variable-selector'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import {
  getBeforeNodesInSameBranchIncludeParent,
  toNodeAvailableVars,
  filterVar as filterVarByType
} from '../../utils/variable'

// CodeMirror 相关导入
import { defaultKeymap } from '@codemirror/commands'
import { python } from '@codemirror/lang-python'
import { EditorState } from '@codemirror/state'
import { EditorView, keymap } from '@codemirror/view'
import { basicSetup } from 'codemirror'

interface Props {
  value: string
  editionType?: EditionType
  filterType?: string | Array<string> | undefined
  node?: Node
  customOutputs?: any[]
  showRemove?: boolean
  showJinja?: boolean
  placeholder?: string
  warn?: string
  mode: string
  nodeId: string
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  editionType: EditionType.basic,
  filterType: undefined,
  showRemove: false,
  showJinja: false,
  placeholder: '请输入',
  warn: '',
  customOutputs: undefined
})

const emit = defineEmits([
  'update:value',
  'update:editionType',
  'editionTypeChange',
  'removeClick',
  'maximizeClick',
  'change',
  'blur',
  'input',
  'jinjaInsetVar'
])

const content = useVModel(props, 'value', emit)
const edition_type = useVModel(props, 'editionType', emit)

// 响应式变量
const editorRef = ref()
const showTip = ref(false)
const selContext2 = ref()
const tipsDropdownPosition = reactive({
  left: '0px',
  top: '0px'
})

const isFocus = ref(false)

const codeMirrorView = ref()
const currentState = ref()
const isTipSelect = ref(false)
const richEditor = ref()

// 获取真实的变量数据
const nodesStore = useNodesStore()
const availableVars = ref<any[]>([])

// 变量分组映射，用于存储变量路径到分组标题的映射
const variableGroupMap = ref<Map<string, string>>(new Map())

// 获取可用的变量
function getAvailableVars() {
  try {
    const beforeNodes = getBeforeNodesInSameBranchIncludeParent(props.nodeId)
    const parentNode = nodesStore.parentNode

    const outputVars = toNodeAvailableVars(parentNode, beforeNodes, false, filterVarByType(VarType.any), [], [])

    // 构建变量分组映射
    buildVariableGroupMap(outputVars)

    return outputVars
  } catch (error) {
    console.error('Error getting available vars:', error)
    return []
  }
}

// 构建变量分组映射
function buildVariableGroupMap(vars: any[]) {
  variableGroupMap.value.clear()

  vars.forEach((group) => {
    if (group.vars && Array.isArray(group.vars)) {
      group.vars.forEach((varItem: any) => {
        // 构建变量的完整路径
        let variablePath = ''
        if (group.nodeId === 'sys' || group.nodeId === 'env' || group.nodeId === 'conversation') {
          variablePath = varItem.variable
        } else {
          variablePath = `${group.nodeId}.${varItem.variable}`
        }

        // 存储路径到分组标题的映射
        const groupTitle = group.title || group.nodeId
        variableGroupMap.value.set(variablePath, groupTitle)
      })
    }
  })
}

// 变量过滤函数
function filterVar(v: any) {
  return filterVarByType(VarType.any)(v)
}

// 初始化CodeMirror
function initCodemirror() {
  if (codeMirrorView.value) {
    codeMirrorView.value.destroy()
    codeMirrorView.value = undefined
  }

  if (!editorRef.value) {
    return
  }

  // 清空容器内容，防止重复内容
  editorRef.value.innerHTML = ''

  const customKeyMap = keymap.of([
    ...defaultKeymap,
    {
      key: 'Ctrl-/',
      run: () => {
        emit('jinjaInsetVar')
        return true
      }
    }
  ])

  nextTick(() => {
    const state = EditorState.create({
      doc: content.value || '', // 确保有默认值
      extensions: [
        basicSetup,
        python(),
        customKeyMap,
        // 添加自动换行配置
        EditorView.lineWrapping,
        EditorView.updateListener.of((viewUpdate) => {
          if (viewUpdate.docChanged && !isUpdatingFromCodeMirror.value) {
            currentState.value = viewUpdate.state

            // 设置标志避免循环更新
            isUpdatingFromCodeMirror.value = true

            const newContent = viewUpdate.state.doc.toString()

            // 只有当内容真正不同时才更新
            if (newContent !== content.value) {
              content.value = newContent
              emit('change', newContent)
            }

            // 检查是否输入了触发字符
            const state = codeMirrorView.value.state
            const cursorPos = state.selection.main.head
            const beforeText = state.doc.sliceString(Math.max(0, cursorPos - 1), cursorPos)

            if (['/', '{'].includes(beforeText)) {
              // 获取光标位置
              const coords = codeMirrorView.value.coordsAtPos(cursorPos)
              if (coords) {
                showFloatingList({
                  left: coords.left,
                  top: coords.bottom + 5
                })
              }
            } else {
              nextTick(() => {
                if (!['/', '{'].includes(beforeText)) {
                  showTip.value = false
                }
              })
            }

            // 重置标志
            setTimeout(() => {
              isUpdatingFromCodeMirror.value = false
            }, 50)
          }
        }),
        EditorView.domEventHandlers({
          blur: () => {
            emit('blur')
          }
        })
      ]
    })

    codeMirrorView.value = new EditorView({
      state,
      parent: editorRef.value
    })
  })
}

onMounted(() => {
  if (props.showJinja) {
    initCodemirror()
  }
})

// 监听editionType变化，重新初始化CodeMirror
watch(
  () => edition_type.value,
  (newType) => {
    if (newType === EditionType.jinja2 && props.showJinja) {
      nextTick(() => {
        initCodemirror()
      })
    } else if (newType === EditionType.basic) {
      if (codeMirrorView.value) {
        // 获取 CodeMirror 的当前内容
        const jinjaContent = codeMirrorView.value.state.doc.toString()

        // 确保内容已经同步到 content.value
        if (jinjaContent !== content.value) {
          content.value = jinjaContent
        }

        codeMirrorView.value.destroy()
        codeMirrorView.value = undefined
      }

      nextTick(() => {
        updateRichEditorContent()
      })
    }
  }
)

// 组件挂载时添加全局点击事件监听和获取变量数据
onMounted(() => {
  document.addEventListener('click', handleGlobalClick, true)
  // 获取可用变量
  availableVars.value = getAvailableVars()
})

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick, true)
})

const isUpdatingFromRichEditor = ref(false)
const isUpdatingFromCodeMirror = ref(false)

watch(
  () => content.value,
  (newValue) => {
    if (edition_type.value === EditionType.basic && !isUpdatingFromRichEditor.value) {
      nextTick(() => {
        updateRichEditorContent()
      })
    } else if (edition_type.value === EditionType.jinja2 && !isUpdatingFromCodeMirror.value && codeMirrorView.value) {
      const currentDoc = codeMirrorView.value.state.doc.toString()

      if (currentDoc !== newValue) {
        isUpdatingFromCodeMirror.value = true
        codeMirrorView.value.dispatch({
          changes: {
            from: 0,
            to: codeMirrorView.value.state.doc.length,
            insert: newValue
          }
        })
        setTimeout(() => {
          isUpdatingFromCodeMirror.value = false
        }, 50)
      }
    }
  }
)

// 更新富文本编辑器内容
function updateRichEditorContent() {
  if (!richEditor.value) return

  const htmlContent = convertTextToHtml(content.value)
  if (richEditor.value.innerHTML !== htmlContent) {
    richEditor.value.innerHTML = htmlContent
  }
}

function convertTextToHtml(text: string): string {
  if (!text) return ''

  let html = text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')

  html = html.replace(/\{\{#([^#]+)#\}\}/g, (_, variable) => {
    let groupName = variableGroupMap.value.get(variable)
    let variableName = variable

    if (!groupName) {
      const parts = variable.split('.')
      if (parts.length >= 2) {
        if (parts[0] === 'sys') {
          groupName = '系统'
          variableName = parts.slice(1).join('.')
        }
        // 如果是环境变量（env.xxx）
        else if (parts[0] === 'env') {
          groupName = '环境'
          variableName = parts.slice(1).join('.')
        }
        // 如果是对话变量（conversation.xxx）
        else if (parts[0] === 'conversation') {
          groupName = '对话'
          variableName = parts.slice(1).join('.')
        }
        // 普通节点变量
        else {
          groupName = parts[0] // 节点ID作为分组名
          variableName = parts.slice(1).join('.')
        }
      } else {
        groupName = '未知分组'
        variableName = variable
      }
    } else {
      // 从映射中获取到了分组标题，解析变量名
      const parts = variable.split('.')
      if (parts.length >= 2) {
        if (parts[0] === 'sys' || parts[0] === 'env' || parts[0] === 'conversation') {
          variableName = parts.slice(1).join('.')
        } else {
          variableName = parts.slice(1).join('.')
        }
      }
    }

    return `<span class="variable-tag" contenteditable="false" data-variable="${variable}" data-group-name="${groupName}" data-variable-name="${variableName}">
      <span class="variable-group" title="${groupName}">${groupName}</span>
      <span class="variable-separator">/</span>
      <span class="variable-icon">{x}</span>
      <span class="variable-name" title="${variableName}">${variableName}</span>
    </span>`
  })

  // 处理换行
  html = html.replace(/\n/g, '<br>')

  return html
}

function convertHtmlToText(html: string): string {
  if (!html) return ''

  // 创建临时div来解析HTML
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = html

  // 将变量标签转换回文本格式
  const variableTags = tempDiv.querySelectorAll('.variable-tag')
  variableTags.forEach((tag) => {
    const variable = tag.getAttribute('data-variable')
    if (variable) {
      tag.replaceWith(`{{#${variable}#}}`)
    }
  })

  // 获取纯文本内容
  let text = tempDiv.textContent || tempDiv.innerText || ''

  // 处理换行
  text = tempDiv.innerHTML
    .replace(/<br\s*\/?>/gi, '\n')
    .replace(/<[^>]*>/g, '')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")

  return text
}

// 处理富文本编辑器输入
function handleRichInput(e: Event) {
  const target = e.target as HTMLElement
  const htmlContent = target.innerHTML
  const textContent = convertHtmlToText(htmlContent)

  // 设置标志避免循环更新
  isUpdatingFromRichEditor.value = true

  // 更新content值
  content.value = textContent

  // 重置标志
  nextTick(() => {
    isUpdatingFromRichEditor.value = false
  })

  // 检查是否输入了触发字符
  const inputData = (e as any).data
  if (inputData === '/' || inputData === '{') {
    // 延迟保存光标位置，确保输入字符已经被处理
    setTimeout(() => {
      currentTextCursorPosition = getCurrentTextPosition()

      const selection = window.getSelection()
      if (selection && selection.rangeCount > 0) {
        const range = selection.getRangeAt(0)
        const rect = range.getBoundingClientRect()

        showFloatingList({
          left: rect.left,
          top: rect.bottom + 5
        })
      }
    }, 10)
  } else if (showTip.value && inputData) {
    cancelSelVar()
  }

  emit('input', e)
  emit('change', textContent)
}

// 处理键盘按下事件
function handleKeydown(e: KeyboardEvent) {
  // 处理回车键
  if (e.key === 'Enter') {
    e.preventDefault()

    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const br = document.createElement('br')
      range.deleteContents()
      range.insertNode(br)
      range.setStartAfter(br)
      range.setEndAfter(br)
      selection.removeAllRanges()
      selection.addRange(range)
      handleRichInput(e as any)
    }
  }

  // 处理删除键
  if (e.key === 'Backspace' || e.key === 'Delete') {
    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      const startContainer = range.startContainer

      // 检查是否要删除变量标签
      if (startContainer.nodeType === Node.TEXT_NODE) {
        const parentElement = startContainer.parentElement
        if (parentElement && parentElement.classList.contains('variable-tag')) {
          e.preventDefault()
          parentElement.remove()
          handleRichInput(e as any)
          return
        }
      }
    }
  }
}

// 处理粘贴事件
function handlePaste(e: ClipboardEvent) {
  e.preventDefault()

  const clipboardData = e.clipboardData
  if (clipboardData) {
    const text = clipboardData.getData('text/plain')

    const selection = window.getSelection()
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0)
      range.deleteContents()

      // 插入纯文本
      const textNode = document.createTextNode(text)
      range.insertNode(textNode)
      range.setStartAfter(textNode)
      range.setEndAfter(textNode)
      selection.removeAllRanges()
      selection.addRange(range)
      handleRichInput(e as any)
    }
  }
}

// 初始化富文本编辑器
function initRichEditor() {
  if (richEditor.value) {
    updateRichEditorContent()
  }
}

// 组件挂载后初始化
onMounted(() => {
  if (props.showJinja) {
    initCodemirror()
  }
  initRichEditor()
})

// 显示浮动列表 - 智能位置计算
function showFloatingList(cursorRect: { left: number; top: number }) {
  // 弹窗的预估尺寸
  const popupWidth = 300 // 变量选择器的宽度
  const popupHeight = 232 // 变量选择器的最大高度
  const offset = 5 // 与光标的间距

  // 获取视窗尺寸
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight
  const scrollTop = window.pageYOffset || document.documentElement.scrollTop
  const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft

  // 计算可用空间
  const spaceBelow = viewportHeight - (cursorRect.top - scrollTop)
  const spaceAbove = cursorRect.top - scrollTop
  const spaceRight = viewportWidth - (cursorRect.left - scrollLeft)
  const spaceLeft = cursorRect.left - scrollLeft

  let finalLeft = cursorRect.left
  let finalTop = cursorRect.top

  // 水平位置计算
  if (spaceRight >= popupWidth) {
    // 右侧空间足够，保持原位置
    finalLeft = cursorRect.left
  } else if (spaceLeft >= popupWidth) {
    // 右侧空间不够，但左侧空间足够，向左显示
    finalLeft = cursorRect.left - popupWidth
  } else {
    // 两侧空间都不够，居中显示或贴边显示
    if (viewportWidth >= popupWidth) {
      finalLeft = Math.max(scrollLeft + 10, Math.min(cursorRect.left, scrollLeft + viewportWidth - popupWidth - 10))
    } else {
      finalLeft = scrollLeft + 10
    }
  }

  // 垂直位置计算
  if (spaceBelow >= popupHeight + offset) {
    // 下方空间足够，显示在光标下方
    finalTop = cursorRect.top + offset
  } else if (spaceAbove >= popupHeight + offset) {
    // 下方空间不够，但上方空间足够，显示在光标上方
    finalTop = cursorRect.top - popupHeight - offset
  } else {
    // 上下空间都不够，选择空间较大的一侧
    if (spaceBelow > spaceAbove) {
      // 下方空间较大
      finalTop = cursorRect.top + offset
    } else {
      // 上方空间较大
      finalTop = Math.max(scrollTop + 10, cursorRect.top - popupHeight - offset)
    }
  }

  // 确保不超出视窗边界
  finalLeft = Math.max(scrollLeft + 10, Math.min(finalLeft, scrollLeft + viewportWidth - popupWidth - 10))
  finalTop = Math.max(scrollTop + 10, Math.min(finalTop, scrollTop + viewportHeight - popupHeight - 10))

  tipsDropdownPosition.left = finalLeft + 'px'
  tipsDropdownPosition.top = finalTop + 'px'
  showTip.value = true
}

// 取消变量选择
function cancelSelVar() {
  showTip.value = false
}

// 处理全局点击事件，点击空白处关闭弹窗
function handleGlobalClick(event: MouseEvent) {
  if (!showTip.value) return

  const target = event.target as HTMLElement

  // 检查点击的元素是否在弹窗内部
  const dropdown = document.querySelector('.tips-dropdown')
  if (dropdown && dropdown.contains(target)) {
    return // 点击在弹窗内部，不关闭
  }

  // 检查点击的元素是否是编辑器或插入按钮
  if (richEditor.value && richEditor.value.contains(target)) {
    return // 点击在富文本编辑器内部，不关闭
  }

  if (codeMirrorView.value && codeMirrorView.value.dom.contains(target)) {
    return // 点击在 CodeMirror 编辑器内部，不关闭
  }

  const insertButton = document.querySelector('.inset-var-btn')
  if (insertButton && insertButton.contains(target)) {
    return // 点击在插入按钮上，不关闭
  }

  // 点击在其他地方，关闭弹窗
  cancelSelVar()
}

// 处理失焦事件
function handleBlur() {
  isFocus.value = false
  setTimeout(() => {
    if (isTipSelect.value) return false
    cancelSelVar()
  }, 500)
  emit('blur')
}

// 处理聚焦事件
function handleFocus() {
  isFocus.value = true
}

// 处理鼠标抬起事件 - 保存光标位置
function handleMouseUp() {
  // 延迟保存，确保光标位置已更新
  setTimeout(() => {
    currentTextCursorPosition = getCurrentTextPosition()
  }, 10)
}

// 处理键盘抬起事件 - 保存光标位置
function handleKeyUp(e: KeyboardEvent) {
  // 对于方向键、Home、End等会改变光标位置的键，保存位置
  const positionChangingKeys = ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown', 'Home', 'End', 'PageUp', 'PageDown']
  if (positionChangingKeys.includes(e.key)) {
    setTimeout(() => {
      currentTextCursorPosition = getCurrentTextPosition()
    }, 10)
  }
}

// 切换编辑器类型
function switchEditionType(checked: boolean) {
  showTip.value = false
  edition_type.value = checked ? EditionType.jinja2 : EditionType.basic
  emit('editionTypeChange', checked)
}

// 全局变量存储当前光标位置（文本位置）
let currentTextCursorPosition: number = 0

// 获取当前光标在文本中的位置
function getCurrentTextPosition(): number {
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0 || !richEditor.value) {
    return content.value.length // 默认到末尾
  }

  const range = selection.getRangeAt(0)

  // 创建一个临时范围来获取光标前的内容
  const tempRange = document.createRange()
  tempRange.selectNodeContents(richEditor.value)
  tempRange.setEnd(range.startContainer, range.startOffset)

  // 获取光标前的HTML内容
  const beforeHtml = tempRange.cloneContents()
  const tempDiv = document.createElement('div')
  tempDiv.appendChild(beforeHtml)

  // 转换为文本格式
  const beforeText = convertHtmlToText(tempDiv.innerHTML)

  return beforeText.length
}

// 设置光标到指定的文本位置
function setTextCursorPosition(position: number) {
  if (!richEditor.value) return

  // 更新富文本编辑器内容
  updateRichEditorContent()

  // 等待DOM更新
  nextTick(() => {
    const selection = window.getSelection()
    if (!selection) return

    try {
      // 遍历所有节点（文本和元素），找到对应位置
      let currentPos = 0
      const walker = document.createTreeWalker(richEditor.value, NodeFilter.SHOW_TEXT | NodeFilter.SHOW_ELEMENT, null)

      let node = walker.nextNode()
      while (node) {
        if (node.nodeType === Node.TEXT_NODE) {
          const textLength = node.textContent?.length || 0
          if (currentPos + textLength >= position) {
            // 找到目标文本节点
            const offset = position - currentPos
            const range = document.createRange()
            range.setStart(node, Math.min(offset, textLength))
            range.setEnd(node, Math.min(offset, textLength))
            selection.removeAllRanges()
            selection.addRange(range)
            return
          }
          currentPos += textLength
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element
          if (element.classList.contains('variable-tag')) {
            // 变量标签，计算其文本长度
            const variable = element.getAttribute('data-variable')
            if (variable) {
              const variableLength = `{{#${variable}#}}`.length
              if (currentPos + variableLength >= position) {
                // 光标应该在变量标签后面
                const range = document.createRange()
                range.setStartAfter(element)
                range.setEndAfter(element)
                selection.removeAllRanges()
                selection.addRange(range)
                return
              }
              currentPos += variableLength
            }
          }
        }
        node = walker.nextNode()
      }

      // 如果没找到，设置到末尾
      const lastNode = richEditor.value.lastChild
      if (lastNode) {
        const range = document.createRange()
        if (lastNode.nodeType === Node.TEXT_NODE) {
          range.setStart(lastNode, lastNode.textContent?.length || 0)
          range.setEnd(lastNode, lastNode.textContent?.length || 0)
        } else {
          range.setStartAfter(lastNode)
          range.setEndAfter(lastNode)
        }
        selection.removeAllRanges()
        selection.addRange(range)
      }
    } catch (error) {
      // 静默处理错误
    }
  })
}

// 处理插入按钮的 mousedown 事件 - 在失去焦点前保存光标位置
function handleInsertButtonMouseDown() {
  if (edition_type.value === EditionType.basic) {
    if (!richEditor.value) {
      return
    }
    // 在按钮点击导致编辑器失去焦点之前，保存当前光标位置
    currentTextCursorPosition = getCurrentTextPosition()
  } else if (edition_type.value === EditionType.jinja2) {
    if (!codeMirrorView.value) {
      return
    }
    // 保存 CodeMirror 的光标位置
    const state = codeMirrorView.value.state
    const cursorPos = state.selection.main.head
    // 将位置存储在全局变量中，供后续使用
    currentTextCursorPosition = cursorPos
  }
}

// 插入变量 - 支持基础模式和 Jinja 模式
function insetVar() {
  // 设置标志
  isTipSelect.value = true

  let popupPosition = { left: 0, top: 0 }

  if (edition_type.value === EditionType.basic) {
    if (!richEditor.value) {
      return
    }
    // 基础模式：使用富文本编辑器位置
    const editorRect = richEditor.value.getBoundingClientRect()
    popupPosition = {
      left: editorRect.left + 10,
      top: editorRect.bottom + 5
    }
  } else if (edition_type.value === EditionType.jinja2) {
    if (!codeMirrorView.value) {
      return
    }
    // Jinja 模式：使用 CodeMirror 光标位置
    try {
      const coords = codeMirrorView.value.coordsAtPos(currentTextCursorPosition)
      if (coords) {
        popupPosition = {
          left: coords.left,
          top: coords.bottom + 5
        }
      } else {
        // 降级到编辑器位置
        const editorRect = codeMirrorView.value.dom.getBoundingClientRect()
        popupPosition = {
          left: editorRect.left + 10,
          top: editorRect.bottom + 5
        }
      }
    } catch (error) {
      const editorRect = codeMirrorView.value.dom.getBoundingClientRect()
      popupPosition = {
        left: editorRect.left + 10,
        top: editorRect.bottom + 5
      }
    }
  }

  // 显示变量选择器
  showFloatingList(popupPosition)
}

// 选择变量 - 适配 VariableList 的事件格式
function selVar(group: any, varItem: any) {
  // 先关闭变量选择器
  cancelSelVar()
  isTipSelect.value = false

  // 构建变量选择器数组
  const variableSelector = []
  if (group.nodeId === 'sys' || group.nodeId === 'env' || group.nodeId === 'conversation') {
    // 系统变量
    variableSelector.push(...varItem.variable.split('.'))
  } else {
    // 普通节点变量
    variableSelector.push(group.nodeId, ...varItem.variable.split('.'))
  }

  // 创建兼容的 option 对象，包含分组信息
  const option = {
    selector: variableSelector,
    id: `${group.nodeId}.${varItem.variable}`,
    title: varItem.variable,
    type: varItem.type,
    groupTitle: group.title || group.nodeId, // 使用 VariableList 中的 group.title
    variableName: varItem.variable, // 变量名称
    groupNodeId: group.nodeId // 保存节点ID用于后续识别
  }

  if (edition_type.value === EditionType.basic) {
    // 确保富文本编辑器获得焦点
    if (richEditor.value) {
      richEditor.value.focus()

      // 等待一帧后执行插入，确保焦点设置完成
      nextTick(() => {
        insertVariableInRichEditor(option)
      })
    }
  } else if (edition_type.value === EditionType.jinja2) {
    insertVariableInCodeMirror(option)
  }
}

// 在 CodeMirror 中插入变量
function insertVariableInCodeMirror(option: any) {
  if (!codeMirrorView.value) {
    return
  }

  const state = codeMirrorView.value.state
  const cursorPos = state.selection.main.head
  const doc = state.doc

  // 生成变量文本
  const variableText = `{{#${option.selector.join('.')}#}}`

  // 查找并删除触发字符 '/' 或 '{'
  let insertPos = cursorPos
  const beforeText = doc.sliceString(Math.max(0, cursorPos - 5), cursorPos)
  const slashIndex = beforeText.lastIndexOf('/')
  const braceIndex = beforeText.lastIndexOf('{')

  // 找到最近的触发字符
  const triggerIndex = Math.max(slashIndex, braceIndex)
  if (triggerIndex !== -1) {
    const actualTriggerPos = Math.max(0, cursorPos - 5) + triggerIndex
    insertPos = actualTriggerPos
  }

  // 构建变更
  const changes = {
    from: insertPos,
    to: cursorPos,
    insert: variableText + ' '
  }

  // 设置标志避免循环更新
  isUpdatingFromCodeMirror.value = true

  // 应用变更
  codeMirrorView.value.dispatch({
    changes: changes,
    selection: {
      anchor: insertPos + variableText.length + 1,
      head: insertPos + variableText.length + 1
    }
  })

  // 重置标志
  setTimeout(() => {
    isUpdatingFromCodeMirror.value = false
  }, 50)
}

// 在富文本编辑器中插入变量标签 - 基于文本位置的简单方法
function insertVariableInRichEditor(option: any) {
  // 获取当前文本内容（确保是最新的）
  const currentText = content.value

  // 生成变量文本
  const variableText = `{{#${option.selector.join('.')}#}}`

  // 验证光标位置是否有效
  let insertPosition = Math.min(currentTextCursorPosition, currentText.length)

  // 检查是否需要删除触发字符 '/'
  const searchStart = Math.max(0, insertPosition - 5)
  const beforeText = currentText.substring(searchStart, insertPosition)
  const slashIndex = beforeText.lastIndexOf('/')

  if (slashIndex !== -1) {
    // 找到了 '/' 字符，删除它
    const actualSlashPosition = searchStart + slashIndex
    insertPosition = actualSlashPosition
  }

  // 构建新的文本内容
  const beforeInsert = currentText.substring(0, insertPosition)
  const afterInsert = currentText.substring(currentTextCursorPosition)
  const newText = beforeInsert + variableText + ' ' + afterInsert

  // 计算新的光标位置（在插入的变量和空格后面）
  const newCursorPosition = insertPosition + variableText.length + 1 // +1 for space

  // 更新内容
  isUpdatingFromRichEditor.value = true
  content.value = newText

  // 更新当前光标位置，以便下次插入使用正确的位置
  currentTextCursorPosition = newCursorPosition

  // 更新富文本编辑器并设置光标位置
  nextTick(() => {
    // 等待DOM更新完成
    setTimeout(() => {
      setTextCursorPosition(newCursorPosition)
      isUpdatingFromRichEditor.value = false
    }, 100) // 增加等待时间，确保DOM完全更新
  })

  emit('change', newText)
}

// 删除消息
function removeMessage() {
  emit('removeClick')
}

// 复制内容
function copyClick() {
  try {
    navigator.clipboard
      .writeText(content.value)
      .then(() => {
        Message.success('复制成功')
      })
      .catch(() => {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = content.value
        document.body.appendChild(textArea)
        textArea.select()
        const success = document.execCommand('copy')
        document.body.removeChild(textArea)
        if (success) {
          Message.success('复制成功')
        } else {
          Message.error('复制失败')
        }
      })
  } catch (error) {
    Message.error('复制失败')
  }
}

// 最大化点击
function maximizeClick() {
  emit('maximizeClick')
}
</script>

<style scoped lang="scss">
.toolbar-textarea {
  display: flex;
  flex-direction: column;
  width: 100%;
  border: 1px solid var(--color-border-2);
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.3s ease;
  background: var(--color-bg-1);

  // &.focus-state {
  //   border-color: var(--color-primary-6);
  //   box-shadow: 0 0 0 2px var(--color-primary-light-2);
  // }

  .toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: var(--color-fill-2);
    border-bottom: 1px solid var(--color-border-2);
    min-height: 40px;

    .toolbar-left {
      display: flex;
      align-items: center;
    }

    .toolbar-right {
      display: flex;
      align-items: center;

      .class-toolbar {
        display: flex;
        align-items: center;
        gap: 8px;
        color: var(--color-text-3);

        .text-length {
          font-size: 12px;
          color: var(--color-text-3);
        }

        .divider {
          font-size: 12px;
          color: var(--color-text-4);
          margin: 0 4px;
        }

        .jinja-switch {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;
          color: var(--color-text-2);

          .ml-1 {
            margin-left: 4px;
          }
        }

        .inset-var-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 20px;
          height: 20px;
          font-size: 12px;
          font-weight: 500;
          background: var(--color-primary-light-1);
          color: var(--color-primary-6);
          border: 1px solid var(--color-primary-light-3);
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background: var(--color-primary-light-2);
            border-color: var(--color-primary-light-4);
          }
        }

        .toolbar-icon {
          font-size: 18px;
          color: var(--color-text-3);
          cursor: pointer;
          padding: 2px;
          border-radius: 4px;
          transition: all 0.2s;

          &:hover {
            color: var(--color-text-1);
            background: var(--color-fill-3);
          }
        }
      }
    }
  }

  .textarea-input-box {
    padding: 8px;
    flex: 1;

    .rich-editor {
      width: 100%;
      min-height: 60px;
      padding: 8px 12px;
      border: 1px solid var(--color-border-2);
      border-radius: 4px;
      background: var(--color-bg-1);
      font-family: inherit;
      font-size: 14px;
      line-height: 1.5;
      color: var(--color-text-1);
      outline: none;
      resize: vertical;
      overflow-y: auto;

      &:focus {
        border-color: var(--color-primary-6);
        box-shadow: 0 0 0 2px var(--color-primary-light-2);
      }

      &:empty::before {
        content: attr(data-placeholder);
        color: var(--color-text-3);
        pointer-events: none;
      }

      // 换行间距样式 - 使用更可靠的方法
      br {
        display: block;
        height: 5px;
        line-height: 5px;
        content: '';
      }

      // 使用伪元素为换行添加间距
      br::before {
        content: '';
        display: block;
        height: 5px;
      }

      // 为文本节点添加行间距
      & > * {
        display: block;
        margin-bottom: 5px;

        // &:last-child {
        //   margin-bottom: 0;
        // }
      }

      // 处理纯文本节点的间距
      &[contenteditable='true'] {
        line-height: 1.8; // 增加行高来实现行间距效果
      }

      // 变量标签样式
      :deep(.variable-tag) {
        display: inline-flex !important;
        align-items: center !important;
        gap: 2px !important;
        padding: 0 8px !important;
        background: var(--color-fill-2) !important;
        color: var(--color-primary-6, #165dff) !important;
        border: 1px solid var(--color-primary-light-3, #bedaff) !important;
        border-radius: 6px !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        cursor: default !important;
        user-select: none !important;
        white-space: nowrap !important;
        vertical-align: middle !important;
        margin: 0 2px;
        &:hover {
          background: var(--color-fill-3) !important;
          border-color: var(--color-primary-light-4, #a8ccff) !important;
        }
        .variable-icon {
          color: var(--color-primary-6, #165dff);
          margin-right: 2px;
          flex-shrink: 0;
        }

        .variable-group {
          max-width: 60px !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          white-space: nowrap !important;
          flex-shrink: 0 !important;
          color: var(--color-text-2, #4e5969) !important;
        }

        .variable-separator {
          color: var(--color-text-3, #86909c) !important;
          margin: 0 !important;
          flex-shrink: 0 !important;
        }

        .variable-name {
          max-width: 60px !important;
          overflow: hidden !important;
          text-overflow: ellipsis !important;
          white-space: nowrap !important;
          flex-shrink: 0 !important;
          color: var(--color-primary-6, #165dff) !important;
          font-weight: 600 !important;
        }
      }
    }

    .textarea-input {
      width: 100%;

      :deep(.arco-textarea) {
        border: none;
        box-shadow: none;
        background: transparent;
        resize: vertical;
        font-family: inherit;

        &:focus {
          border: none;
          box-shadow: none;
        }
      }
    }

    .code-mirror-box {
      width: 100%;
      min-height: 100px;
      border: 1px solid var(--color-border-2);
      border-radius: 4px;
      overflow: hidden;

      :deep(.cm-editor) {
        border-radius: 4px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        width: 100%;
        max-width: 100%;
      }

      :deep(.cm-focused) {
        outline: none;
        border-color: var(--color-primary-6);
      }

      :deep(.cm-content) {
        padding: 8px;
        width: 100%;
        max-width: 100%;
        box-sizing: border-box;
      }

      :deep(.cm-line) {
        line-height: 1.5;
        word-wrap: break-word;
        word-break: break-all;
      }

      :deep(.cm-wrap) {
        width: 100%;
        max-width: 100%;
      }

      :deep(.cm-scroller) {
        overflow-x: hidden;
        width: 100%;
        max-width: 100%;
      }
    }
  }
}

.error-info {
  margin-top: 4px;

  .error-text {
    font-size: 12px;
    color: var(--color-danger-6);
  }
}

// 变量选择器弹窗样式
.tips-dropdown {
  position: fixed;
  background: var(--color-bg-1);
  border: 1px solid var(--color-border-2);
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  padding: 4px 0;
  overflow-y: auto;
  z-index: 9999;
}
</style>

<!-- 全局样式，确保变量标签在任何地方都能正确显示 -->
<style lang="scss"></style>
