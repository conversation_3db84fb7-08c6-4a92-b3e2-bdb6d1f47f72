<template>
  <div class="space-y-1">
    <div
      v-for="item in processedList"
      class="text-primary flex h-8 cursor-pointer items-center justify-between rounded-lg border px-2.5 shadow-xs hover:shadow-md"
    >
      <div class="flex w-0 grow items-center space-x-1">
        <AiSvgIcon name="workflow-ai-variable" />
        <div class="system-xs-regular max-w-[130px] shrink-0 truncate font-medium text-text-secondary">
          {{ item.variable }}{{ item.label }}
        </div>
        <!-- <div class="shrink-0 text-xs font-medium text-text-quaternary">·</div>
        <div title="code" class="max-w-[130px] truncate text-[13px] font-medium text-text-tertiary">code</div> -->
      </div>
      <div class="ml-2 flex shrink-0 items-center">
        <div v-if="item.readonly" class="mr-2 text-xs font-normal text-text-tertiary">
          <span>{{ item.type }}</span>
        </div>
        <div v-else class="mr-2 text-xs font-normal text-text-tertiary">
          <span v-if="item.required">必填</span>
        </div>
        <svg
          v-if="!item.readonly"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          fill="currentColor"
          class="remixicon h-3.5 w-3.5 text-text-tertiary"
        >
          <path
            d="M7.78428 14L8.2047 10H4V8H8.41491L8.94043 3H10.9514L10.4259 8H14.4149L14.9404 3H16.9514L16.4259 8H20V10H16.2157L15.7953 14H20V16H15.5851L15.0596 21H13.0486L13.5741 16H9.58509L9.05957 21H7.04855L7.57407 16H4V14H7.78428ZM9.7953 14H13.7843L14.2047 10H10.2157L9.7953 14Z"
          />
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
}
const props = defineProps<{
  list: FieldType[]
  isChatMode?: false
}>()
// const props = defineProps({
//   list: FieldType[],
//   isChatMode: false
// })
const defaultList = [
  {
    label: '',
    required: false,
    readonly: true,
    type: 'string',
    variable: 'sys.user_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'array[file]',
    variable: 'sys.files'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.app_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_run_id'
  }
]
const isChatModeDefaultList = [
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.query'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.dialogue_count'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.conversation_id'
  }
]
const processedList = computed(() => {
  if (props.isChatMode) {
  }
  return [
    ...props.list.map((item) => {
      const processed = {
        ...item,
        readonly: false
      }
      return processed
    }),
    ...defaultList,
    ...(props.isChatMode ? isChatModeDefaultList : [])
  ]
})
onMounted(() => {
  console.log(
    props.list.map((e) => {
      console.log(e)
    })
  )
})
</script>
<style scoped lang="scss"></style>
