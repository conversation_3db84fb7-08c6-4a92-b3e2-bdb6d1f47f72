import type { InstalledApp } from '@/apis'
import type { FileResponse, HistoryWorkflowData, NodeTracing } from '@/views/app/workflow/types/workflow'
import { defineStore } from 'pinia'

export type RunFile = {
  type: string
  url?: string
  upload_file_id?: string
  related_id?: string
}

const storeSetup = () => {
  const installedApps = ref<InstalledApp[]>([])

  const setInstalledApps = (v: InstalledApp[]) => {
    installedApps.value = v
  }

  return {
    installedApps,
    setInstalledApps
  }
}

export const useAppShare = defineStore('appShare', storeSetup, { persist: false })
