<template>
  <div
    :class="['ai-editor-container', isFocus && ' ai-editor-container-foucs']"
    :style="{ width: '100%', height: height + 'px' }"
  >
    <div
      ref="editorRef"
      class="ai-editor"
      contenteditable="true"
      :data-placeholder="placeholder"
      @input="handleInput"
      @keydown="handleKeydown"
      @keyup="handleKeyup"
      @paste="handlePaste"
      @click="handleClick"
      @focus="handleFocus"
      @blur="handleBlur"
    />
    <div
      v-if="showCustomComponent"
      class="custom-component-popup"
      :style="{ top: popupPosition.top + 'px', left: popupPosition.left + 'px' }"
    >
      <slot name="custom-component" />
      <div class="popup-close" @click="hideCustomComponent">×</div>
    </div>
    <div class="word-count">{{ textCount }} 字</div>
    <div class="resize-handle" @mousedown="startResize" />
  </div>
</template>

<script setup lang="ts">
defineOptions({
  name: 'AiEditor'
})

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  height: {
    type: Number,
    default: 200
  },
  triggerChar: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'trigger-custom-component'])

// 编辑器DOM引用
const editorRef = ref<HTMLElement | null>(null)
// 文本计数
const textCount = ref(0)
// 自定义组件状态
const showCustomComponent = ref(false)
const popupPosition = reactive({
  top: 0,
  left: 0
})
// 缩放相关状态
const height = ref(props.height)
const isResizing = ref(false)
const resizeStartPos = reactive({
  x: 0,
  y: 0
})
const initialSize = reactive({
  height: 0
})
const isFocus = ref(false)

// 监听modelValue变化，更新编辑器内容
watch(
  () => props.modelValue,
  (newValue) => {
    if (editorRef.value && editorRef.value.innerHTML !== newValue) {
      editorRef.value.innerHTML = newValue || ''
      updateTextCount()
      updatePlaceholderVisibility()
    }
  },
  { immediate: true }
)

// 初始化编辑器
onMounted(() => {
  if (editorRef.value) {
    // 只有当有初始值时才设置内容并进行包装
    if (props.modelValue) {
      // 统一使用函数处理初始内容格式化
      formatInitialContent(props.modelValue)
      updateTextCount()
    }
    // 初始化时判断是否显示placeholder
    updatePlaceholderVisibility()
  }
  // 添加全局事件监听
  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
})

// 清理事件监听
onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
})

// 格式化初始内容
const formatInitialContent = (content: string) => {
  if (!editorRef.value) return
  // 如果内容不是以p标签开始，进行包装
  if (!content.trim().startsWith('<p>')) {
    const paragraphs = content.split('\n').map((line) => `<p>${line || '<br>'}</p>`)
    editorRef.value.innerHTML = paragraphs.join('')
  } else {
    editorRef.value.innerHTML = content
  }
}

// 处理输入事件
const handleInput = () => {
  if (!editorRef.value) return
  // 清除节点缓存，因为DOM结构可能变化
  clearNodeCache()
  // 检查是否需要处理首次输入
  const isFirstInput =
    editorRef.value.childNodes.length === 0 ||
    (editorRef.value.childNodes.length === 1 && editorRef.value.childNodes[0].nodeType === Node.TEXT_NODE)
  if (isFirstInput && editorRef.value.textContent?.trim() !== '') {
    // 首次输入且有文本内容，创建一个p标签
    const text = editorRef.value.textContent || ''
    const p = document.createElement('p')
    p.textContent = text
    // 清空编辑器后添加p标签
    editorRef.value.innerHTML = ''
    editorRef.value.appendChild(p)
    // 将光标放到文本末尾
    const selection = window.getSelection()
    if (selection) {
      const range = document.createRange()
      if (p.firstChild) {
        range.setStart(p.firstChild, p.firstChild.textContent?.length || 0)
      } else {
        range.selectNodeContents(p)
      }
      range.collapse(true)
      selection.removeAllRanges()
      selection.addRange(range)
    }
  } else {
    // 非首次输入，确保内容都被p标签包裹
    ensureParagraphWrapping()
  }
  // 判断是否需要显示placeholder
  updatePlaceholderVisibility()
  // 更新文本计数
  updateTextCount()
  // 更新v-model
  emit('update:modelValue', editorRef.value.innerHTML)
}

// 判断是否显示placeholder
const updatePlaceholderVisibility = () => {
  if (!editorRef.value) return
  const content = editorRef.value.innerHTML
  const isEmpty = !content || content === '' || content === '<p><br></p>'
  // 处理自定义属性以支持CSS选择器匹配
  if (isEmpty) {
    editorRef.value.setAttribute('is-empty', 'true')
  } else {
    editorRef.value.removeAttribute('is-empty')
  }
}

// 处理按键按下事件
const handleKeydown = (e: KeyboardEvent) => {
  // 处理左花括号自动闭合
  if (e.key === '{') {
    e.preventDefault()
    insertPairedCharacters('{}', 1)
    return
  }
  // 处理回车键，确保新行被p标签包裹
  if (e.key === 'Enter') {
    // 仅当不是组合输入法状态时处理回车
    if (!e.isComposing) {
      e.preventDefault()
      insertNewParagraph()
    }
    return
  }
  // 检查是否输入触发字符
  if (e.key === props.triggerChar && props.triggerChar !== '') {
    showCustomComponentPopup()
    return
  }
}

// 插入成对字符（如括号）并将光标定位在中间
const insertPairedCharacters = (chars: string, cursorPosition: number) => {
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) return
  const range = selection.getRangeAt(0)
  try {
    // 插入字符
    const textNode = document.createTextNode(chars)
    range.insertNode(textNode)
    // 创建新范围，将光标定位在指定位置
    const newRange = document.createRange()
    if (textNode.parentNode) {
      newRange.setStart(textNode, cursorPosition)
      newRange.setEnd(textNode, cursorPosition)
      // 应用新范围
      selection.removeAllRanges()
      selection.addRange(newRange)
      // 触发input事件以更新内容
      handleInput()
    } else {
      // 如果节点被规范化或其他DOM操作影响，尝试查找插入的字符
      findAndPositionCursor(range.startContainer.parentNode, chars, cursorPosition)
    }
  } catch {}
}

// 显示自定义组件弹出层
const showCustomComponentPopup = () => {
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0 || !editorRef.value) return
  const range = selection.getRangeAt(0)
  const rect = range.getBoundingClientRect()
  const editorRect = editorRef.value.getBoundingClientRect()
  // 计算弹出位置
  popupPosition.top = Math.max(0, rect.bottom - editorRect.top)
  popupPosition.left = Math.max(0, rect.left - editorRect.left)
  // 延迟显示以便先完成字符的输入
  setTimeout(() => {
    showCustomComponent.value = true
    emit('trigger-custom-component')
    // 下一帧检查位置并调整
    nextTick(() => {
      adjustPopupPosition(editorRect)
    })
  }, 0)
}

// 调整弹出层位置，确保在视口内
const adjustPopupPosition = (editorRect: DOMRect) => {
  const popupElement = document.querySelector('.custom-component-popup') as HTMLElement
  if (!popupElement) return
  const popupRect = popupElement.getBoundingClientRect()
  // 如果弹出框超出了编辑器底部，向上调整位置
  if (popupRect.bottom > editorRect.bottom) {
    popupPosition.top = Math.max(0, popupPosition.top - (popupRect.bottom - editorRect.bottom) - 10)
  }
  // 如果弹出框超出了编辑器右侧，向左调整位置
  if (popupRect.right > editorRect.right) {
    popupPosition.left = Math.max(0, popupPosition.left - (popupRect.right - editorRect.right) - 10)
  }
}

// 处理按键松开事件
const handleKeyup = () => {
  // 可以在这里添加额外的按键处理逻辑
}

// 处理粘贴事件，确保粘贴的内容格式正确
const handlePaste = (e: ClipboardEvent) => {
  e.preventDefault()
  if (!e.clipboardData) return
  const text = e.clipboardData.getData('text/plain')
  if (!text) return
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) return
  const range = selection.getRangeAt(0)
  const currentP = findParentParagraph(range.startContainer)
  if (!currentP || !editorRef.value) return
  try {
    // 根据文本行数决定处理方式
    const lines = text.split('\n')

    if (lines.length === 1) {
      // 单行文本：直接插入当前位置
      handleSingleLinePaste(lines[0], range, selection)
    } else {
      // 多行文本：分割成多个段落
      handleMultiLinePaste(lines, currentP, range, selection)
    }
  } catch {
    fallbackPaste(text, range, selection)
  }
}

// 处理单行文本粘贴
const handleSingleLinePaste = (text: string, range: Range, selection: Selection) => {
  const textNode = document.createTextNode(text)
  range.deleteContents()
  range.insertNode(textNode)
  // 将光标放在插入的文本后面
  range.setStartAfter(textNode)
  range.collapse(true)
  selection.removeAllRanges()
  selection.addRange(range)
  handleInput()
}

// 处理多行文本粘贴
const handleMultiLinePaste = (lines: string[], currentP: HTMLParagraphElement, range: Range, selection: Selection) => {
  // 获取光标前后的内容
  const beforeRange = document.createRange()
  beforeRange.setStartBefore(currentP.firstChild || currentP)
  beforeRange.setEnd(range.startContainer, range.startOffset)
  const beforeContent = beforeRange.cloneContents()
  const afterRange = document.createRange()
  afterRange.setStart(range.startContainer, range.startOffset)
  afterRange.setEndAfter(currentP.lastChild || currentP)
  const afterContent = afterRange.cloneContents()
  // 更新当前段落：前半部分 + 第一行
  currentP.innerHTML = ''
  if (beforeContent.childNodes.length > 0) {
    currentP.appendChild(beforeContent)
  }
  currentP.appendChild(document.createTextNode(lines[0]))
  // 插入后续行
  let lastP = currentP
  let lastTextNode = null
  // 处理剩余行
  for (let i = 1; i < lines.length; i++) {
    const p = document.createElement('p')
    const isLastLine = i === lines.length - 1
    // 处理当前行内容
    if (lines[i].trim()) {
      const lineNode = document.createTextNode(lines[i])
      p.appendChild(lineNode)
      lastTextNode = lineNode
    }
    // 最后一行需要添加原有光标后的内容
    if (isLastLine && afterContent.childNodes.length > 0) {
      p.appendChild(afterContent)
      // 如果没有设置过lastTextNode，查找最后一个文本节点
      if (!lastTextNode) {
        const walker = document.createTreeWalker(p, NodeFilter.SHOW_TEXT)
        let node
        while ((node = walker.nextNode())) {
          lastTextNode = node
        }
      }
    }
    // 如果段落为空，添加<br>
    if (p.childNodes.length === 0) {
      p.innerHTML = '<br>'
    }
    // 插入新段落
    if (lastP.nextSibling) {
      lastP.parentNode?.insertBefore(p, lastP.nextSibling)
    } else {
      lastP.parentNode?.appendChild(p)
    }
    lastP = p
  }
  // 设置光标位置到末尾
  positionCursorAtEnd(lastP, lastTextNode, selection)
  // 更新编辑器状态
  handleInput()
}

// 设置光标位置到末尾
const positionCursorAtEnd = (paragraph: HTMLElement, lastTextNode: Node | null, selection: Selection) => {
  const newRange = document.createRange()
  if (lastTextNode && lastTextNode.nodeType === Node.TEXT_NODE) {
    // 光标放在文本节点末尾
    newRange.setStart(lastTextNode, lastTextNode.textContent?.length || 0)
  } else if (paragraph.lastChild && paragraph.lastChild.nodeType === Node.TEXT_NODE) {
    // 光标放在段落最后一个文本节点末尾
    newRange.setStart(paragraph.lastChild, paragraph.lastChild.textContent?.length || 0)
  } else {
    // 其他情况：光标放在段落末尾
    newRange.selectNodeContents(paragraph)
    newRange.collapse(false)
  }
  selection.removeAllRanges()
  selection.addRange(newRange)
}

// 粘贴失败时的回退方法
const fallbackPaste = (text: string, range: Range, selection: Selection) => {
  try {
    // 简单粘贴：将多行文本合并为单行
    const singleLine = text.replace(/\n/g, ' ')
    const textNode = document.createTextNode(singleLine)
    range.deleteContents()
    range.insertNode(textNode)
    // 将光标放在文本末尾
    range.setStartAfter(textNode)
    range.collapse(true)
    selection.removeAllRanges()
    selection.addRange(range)
    handleInput()
  } catch {}
}

// 处理点击事件
const handleClick = () => {
  // 隐藏自定义组件
  hideCustomComponent()
}

// 隐藏自定义组件
const hideCustomComponent = () => {
  showCustomComponent.value = false
}

// 确保所有内容都被p标签包裹
const ensureParagraphWrapping = () => {
  if (!editorRef.value) return
  // 无内容时不处理
  if (!editorRef.value.textContent?.trim() && editorRef.value.childNodes.length === 0) return
  const childNodes = Array.from(editorRef.value.childNodes)
  if (childNodes.length === 0) return
  // 处理非p标签节点
  let hasChanges = false
  childNodes.forEach((node) => {
    // 文本节点且有内容，包装成p标签
    if (node.nodeType === Node.TEXT_NODE && node.textContent?.trim()) {
      const p = document.createElement('p')
      p.appendChild(node.cloneNode())
      editorRef.value!.replaceChild(p, node)
      hasChanges = true
    }
    // 非p标签元素节点（跳过span）
    else if (
      node.nodeType === Node.ELEMENT_NODE &&
      (node as Element).tagName.toLowerCase() !== 'p' &&
      (node as Element).tagName.toLowerCase() !== 'span'
    ) {
      const p = document.createElement('p')
      p.appendChild(node.cloneNode(true))
      editorRef.value!.replaceChild(p, node)
      hasChanges = true
    }
  })
  // 只有在有变更时才处理空p标签，减少不必要的DOM操作
  if (hasChanges) {
    // 移除空p标签，保留带有<br>的空段落
    const allParagraphs = Array.from(editorRef.value.querySelectorAll('p'))
    if (allParagraphs.length > 1) {
      allParagraphs.forEach((p) => {
        if (!p.textContent?.trim() && p.innerHTML.trim() !== '<br>') {
          p.remove()
        }
      })
    }
  }
}

// 查找文本并定位光标
const findAndPositionCursor = (node: Node | null, text: string, position: number = 1) => {
  if (!node) return false
  const selection = window.getSelection()
  if (!selection) return false
  // 如果是文本节点且包含目标文本
  if (node.nodeType === Node.TEXT_NODE && node.textContent && node.textContent.includes(text)) {
    const content = node.textContent
    const index = content.indexOf(text)
    if (index !== -1) {
      const range = document.createRange()
      range.setStart(node, index + position)
      range.collapse(true)
      selection.removeAllRanges()
      selection.addRange(range)
      return true
    }
  }
  // 递归搜索子节点
  if (node.hasChildNodes()) {
    for (let i = 0; i < node.childNodes.length; i++) {
      if (findAndPositionCursor(node.childNodes[i], text, position)) {
        return true
      }
    }
  }
  return false
}

// 插入新段落
const insertNewParagraph = () => {
  const selection = window.getSelection()
  if (!selection || selection.rangeCount === 0) return
  const range = selection.getRangeAt(0)
  // 处理编辑器为空的情况
  if (!editorRef.value || editorRef.value.childNodes.length === 0) {
    createFirstParagraph()
    return
  }
  // 找到当前所在的段落
  const currentP = findParentParagraph(range.startContainer)
  // 如果找不到段落，可能是直接在编辑器中输入的文本
  if (!currentP) {
    // 包装当前选区内容到p标签中
    ensureParagraphWrapping()
    // 重新获取段落，如果还找不到，创建新段落
    const newCurrentP = findParentParagraph(range.startContainer)
    if (!newCurrentP) {
      createFirstParagraph()
      return
    }
    // 使用新找到的段落继续处理
    insertNewParagraphWithCurrentP(newCurrentP, range, selection)
  } else {
    // 使用已找到的段落继续处理
    insertNewParagraphWithCurrentP(currentP, range, selection)
  }
}

// 创建第一个段落 - 提取为独立函数
const createFirstParagraph = () => {
  if (!editorRef.value) return
  const selection = window.getSelection()
  if (!selection) return
  // 创建一个新段落
  const newP = document.createElement('p')
  newP.innerHTML = '<br>'
  editorRef.value.appendChild(newP)
  // 将光标放到新段落
  const newRange = document.createRange()
  newRange.selectNodeContents(newP)
  newRange.collapse(true)
  selection.removeAllRanges()
  selection.addRange(newRange)
  // 更新内容
  handleInput()
}

const insertNewParagraphWithCurrentP = (currentP: HTMLParagraphElement, range: Range, selection: Selection) => {
  if (!editorRef.value) return
  try {
    // 创建新段落
    const newP = document.createElement('p')
    newP.innerHTML = '<br>'
    // 分割当前段落内容
    const [beforeContent, afterContent] = splitParagraphContent(currentP, range)
    // 更新当前段落和新段落
    updateParagraphsContent(currentP, newP, beforeContent, afterContent)
    // 插入新段落到当前段落后
    insertParagraphAfter(currentP, newP)
    // 设置光标位置
    setCursorToNewParagraph(newP, selection)
    // 触发内容更新
    handleInput()
  } catch {
    // 失败时使用简单方法创建段落
    createSimpleParagraphAfter(currentP, selection)
  }
}

// 分割段落内容
const splitParagraphContent = (paragraph: HTMLParagraphElement, range: Range): [DocumentFragment, DocumentFragment] => {
  // 获取光标前内容
  const beforeRange = document.createRange()
  beforeRange.setStartBefore(paragraph.firstChild || paragraph)
  beforeRange.setEnd(range.startContainer, range.startOffset)
  const beforeContent = beforeRange.cloneContents()
  // 获取光标后内容
  const afterRange = document.createRange()
  afterRange.setStart(range.startContainer, range.startOffset)
  afterRange.setEndAfter(paragraph.lastChild || paragraph)
  const afterContent = afterRange.cloneContents()
  return [beforeContent, afterContent]
}

// 更新段落内容
const updateParagraphsContent = (
  currentP: HTMLParagraphElement,
  newP: HTMLParagraphElement,
  beforeContent: DocumentFragment,
  afterContent: DocumentFragment
) => {
  // 检查内容状态
  const hasContentBefore = beforeContent.textContent?.trim() !== ''
  const hasContentAfter = afterContent.textContent?.trim() !== ''
  // 如果光标后有内容，将其移到新段落
  if (hasContentAfter) {
    newP.innerHTML = ''
    newP.appendChild(afterContent)
  }
  // 更新当前段落，只保留光标前的内容
  if (hasContentBefore) {
    currentP.innerHTML = ''
    currentP.appendChild(beforeContent)
  } else {
    currentP.innerHTML = '<br>'
  }
}

// 插入段落到另一段落后面
const insertParagraphAfter = (refParagraph: HTMLParagraphElement, newParagraph: HTMLParagraphElement) => {
  if (refParagraph.nextSibling) {
    refParagraph.parentNode?.insertBefore(newParagraph, refParagraph.nextSibling)
  } else {
    refParagraph.parentNode?.appendChild(newParagraph)
  }
}

// 设置光标到新段落
const setCursorToNewParagraph = (paragraph: HTMLParagraphElement, selection: Selection) => {
  const newRange = document.createRange()
  if (paragraph.firstChild && paragraph.firstChild.nodeType === Node.TEXT_NODE) {
    // 文本节点，光标放在开始位置
    newRange.setStart(paragraph.firstChild, 0)
  } else {
    // 其他情况，光标放在段落内容开始
    newRange.selectNodeContents(paragraph)
    newRange.collapse(true)
  }
  selection.removeAllRanges()
  selection.addRange(newRange)
}

// 创建简单段落作为回退方法
const createSimpleParagraphAfter = (refParagraph: HTMLParagraphElement, selection: Selection) => {
  try {
    const simpleP = document.createElement('p')
    simpleP.innerHTML = '<br>'
    insertParagraphAfter(refParagraph, simpleP)
    const simpleRange = document.createRange()
    simpleRange.selectNodeContents(simpleP)
    simpleRange.collapse(true)
    selection.removeAllRanges()
    selection.addRange(simpleRange)

    handleInput()
  } catch {}
}

// 查找父段落元素 - 添加缓存以提高性能
let nodeParentCache = new WeakMap<Node, HTMLParagraphElement | null>()
const findParentParagraph = (node: Node): HTMLParagraphElement | null => {
  // 检查缓存
  if (nodeParentCache.has(node)) {
    return nodeParentCache.get(node) || null
  }
  let current: Node | null = node
  while (current && current !== editorRef.value) {
    if (current.nodeType === Node.ELEMENT_NODE && (current as Element).tagName.toLowerCase() === 'p') {
      // 保存到缓存
      nodeParentCache.set(node, current as HTMLParagraphElement)
      return current as HTMLParagraphElement
    }
    current = current.parentNode
  }
  // 保存null结果到缓存
  nodeParentCache.set(node, null)
  return null
}

// 清除节点缓存
const clearNodeCache = () => {
  // WeakMap 没有 clear 方法，创建一个新的 WeakMap 实例替代
  nodeParentCache = new WeakMap<Node, HTMLParagraphElement | null>()
}

// 更新文本计数
const updateTextCount = () => {
  if (!editorRef.value) return
  // 获取纯文本内容并计算字数
  const text = editorRef.value.textContent || ''
  textCount.value = text.length
}

// 开始缩放
const startResize = (e: MouseEvent) => {
  e.preventDefault()
  isResizing.value = true
  resizeStartPos.x = e.clientX
  resizeStartPos.y = e.clientY
  initialSize.height = height.value
}

// 处理鼠标移动（用于缩放）
const handleMouseMove = (e: MouseEvent) => {
  if (!isResizing.value) return
  const deltaY = e.clientY - resizeStartPos.y
  height.value = Math.max(100, initialSize.height + deltaY)
}

// 处理鼠标松开（结束缩放）
const handleMouseUp = () => {
  isResizing.value = false
}
const handleFocus = () => {
  isFocus.value = true
  // 更新placeholder状态
  updatePlaceholderVisibility()
}

const handleBlur = () => {
  isFocus.value = false
  // 更新placeholder状态
  updatePlaceholderVisibility()
}
</script>

<style scoped lang="scss">
.ai-editor-container {
  position: relative;
  border: 1px solid var(--color-border-3);
  border-radius: 4px;
  overflow: hidden;

  .ai-editor {
    width: 100%;
    height: calc(100% - 30px);
    padding: 4px 12px;
    overflow-y: auto;
    outline: none;
    font-size: 14px;
    min-height: 100px;
    &:empty::before,
    &[is-empty='true']::before {
      content: attr(data-placeholder);
      color: var(--color-text-3);
      pointer-events: none;
      font-size: 14px;
      position: absolute;
      left: 12px;
      top: 3.5px;
    }
    p {
      min-height: 21px;
    }
  }

  .word-count {
    position: absolute;
    bottom: 5px;
    right: 10px;
    font-size: 12px;
    color: #909399;
  }

  .resize-handle {
    position: absolute;
    left: 50%;
    bottom: 0;
    width: 40px;
    margin-left: -20px;
    border-radius: 2px;
    height: 2px;
    cursor: ns-resize;
    background: #409eff;
  }

  .custom-component-popup {
    position: absolute;
    background-color: white;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    padding: 10px;
    z-index: 100;
    min-width: 200px;

    .popup-close {
      position: absolute;
      top: 5px;
      right: 5px;
      width: 16px;
      height: 16px;
      line-height: 16px;
      text-align: center;
      cursor: pointer;
      color: #909399;

      &:hover {
        color: #409eff;
      }
    }
  }
}
.ai-editor-container-foucs {
  border-color: rgb(var(--primary-6));
}
</style>
