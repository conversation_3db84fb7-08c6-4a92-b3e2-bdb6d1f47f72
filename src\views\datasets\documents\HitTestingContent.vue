<template>
  <div class="hit-testing-container">
    <div class="hit-testing-content">
      <!-- 左侧：测试区域 -->
      <div class="left-panel">
        <div class="hit-testing-header">
          <h2>召回测试</h2>
          <div class="description">根据给定的查询文本测试知识库的召回效果。</div>
        </div>

        <div class="query-input-container">
          <div class="input-wrapper">
            <div class="label">源文本</div>
            <a-textarea
              v-model="queryText"
              placeholder="请输入文本，建议使用简短的陈述句。"
              :auto-size="{ minRows: 10, maxRows: 30 }"
            />
            <div class="char-count">{{ queryText.length }} / 200</div>
          </div>
          <div class="search-settings-button" @click="openSearchSettings">
            <icon-bulb v-if="searchSettings.searchMethod === 'semantic_search'" />
            <icon-search v-else-if="searchSettings.searchMethod === 'full_text_search'" />
            <icon-apps v-else-if="searchSettings.searchMethod === 'hybrid_search'" />
            {{ getSearchMethodText() }}
          </div>
          <a-button type="primary" :loading="searching" size="small" @click="searchQuery">测试</a-button>
        </div>

        <!-- 记录区域 -->
        <div v-if="queryHistory.length > 0" class="history-container">
          <div class="history-header">
            <h3>记录</h3>
          </div>

          <div class="history-list">
            <div class="history-table">
              <div class="table-header">
                <div class="column-data">数据源</div>
                <div class="column-query">文本</div>
                <div class="column-time">时间</div>
              </div>
              <div v-for="(item, index) in queryHistory" :key="index" class="table-row" @click="loadHistoryQuery(item)">
                <div class="column-data">@Retrieval Test</div>
                <div class="column-query">{{ item.content }}</div>
                <div class="column-time">{{ formatDate(item.timestamp) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：结果展示区域 -->
      <div class="right-panel">
        <div v-if="hasSearched" class="result-section">
          <div class="result-header">
            <div class="result-title">
              {{ results.length }} 个召回段落
              <a-tag size="small" style="margin-left: 8px">{{ getSearchMethodText() }}</a-tag>
              <span v-if="searchTime" class="search-time">{{ (searchTime / 1000).toFixed(2) }}s</span>
            </div>
          </div>

          <div v-if="results.length > 0" class="result-list">
            <div
              v-for="(result, index) in results"
              :key="index"
              class="result-item"
              @click.stop="openSegmentDetail(result)"
            >
              <div class="result-info">
                <div class="chunk-id">
                  {{ result.chunk_id }}
                  <span class="chars">{{ result.char_count }} 字符</span>
                </div>
                <div class="score">SCORE {{ result.score.toFixed(2) }}</div>
              </div>
              <div class="result-content">
                {{ result.content }}
              </div>
              <div class="result-tags">
                <a-tag v-for="(tag, tagIndex) in result.tags" :key="tagIndex" size="small" class="tag-item">
                  #{{ tag }}
                </a-tag>
              </div>
              <div class="result-actions" @click.stop>
                <div class="file-name">
                  <icon-file-pdf />
                  {{ result.file_name }}
                </div>
                <a-button type="text" size="mini" class="open-btn" @click.stop="openFile(result)">
                  打开
                  <icon-arrow-right />
                </a-button>
              </div>
            </div>
          </div>

          <div v-else class="no-results">
            <icon-exclamation-circle />
            <p>没有找到匹配的结果</p>
          </div>
        </div>

        <div v-else class="empty-result">
          <div class="empty-icon">
            <icon-search style="font-size: 60px; opacity: 0.6" />
          </div>
          <div class="empty-text">召回测试结果将展示在这里</div>
        </div>
      </div>
    </div>

    <!-- 检索设置侧边栏 -->
    <a-drawer v-model:visible="searchSettingsVisible" width="620px" title="检索设置" unmount-on-close>
      <div class="form-section">
        <retrieval-setting
          :search-method="retrievalSettings.searchMethod"
          :vector-settings="retrievalSettings.vectorSettings"
          :full-text-settings="retrievalSettings.fullTextSettings"
          :hybrid-settings="retrievalSettings.hybridSettings"
          :rerank-models="retrievalSettings.rerankModels"
          @search-method-change="handleSearchMethodChange"
          @vector-settings-change="handleVectorSettingsChange"
          @full-text-settings-change="handleFullTextSettingsChange"
          @hybrid-settings-change="handleHybridSettingsChange"
        />
      </div>
    </a-drawer>

    <!-- 段落详情弹窗 -->
    <a-modal
      v-model:visible="segmentDetailVisible"
      title="段落详情"
      :footer="false"
      title-align="start"
      :mask-closable="true"
      width="700px"
    >
      <div v-if="currentSegment" class="segment-detail-container">
        <div class="segment-header">
          <div class="segment-id-group">
            <span class="segment-id">{{ currentSegment.chunk_id }}</span>
            <span class="segment-file">
              <icon-file-pdf />
              {{ currentSegment.file_name }}
            </span>
          </div>
          <div class="segment-score">SCORE {{ currentSegment.score.toFixed(2) }}</div>
        </div>

        <div class="segment-content">
          {{ currentSegment.content }}
        </div>

        <div class="segment-tags-section">
          <h4 class="tags-title">关键词</h4>
          <div class="segment-tags">
            <a-tag v-for="(tag, tagIndex) in currentSegment.tags" :key="tagIndex" size="small" class="tag-item">
              #{{ tag }}
            </a-tag>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { getDatasetQueries, hitTesting, getRerankModels, getDefaultRerankModel } from '@/apis/datasets'
import RetrievalSetting from '@/views/datasets/components/RetrievalSetting.vue'

// 定义RerankModel接口
interface RerankModel {
  model: string
  label: {
    zh_Hans: string
    en_US: string
  }
  model_type: string
  model_properties: {
    context_size: number
  }
  status: string
  features: any
  fetch_from: string
  deprecated: boolean
  load_balancing_enabled: boolean
  provider: string
}

// 声明一个用于获取rerank模型提供商的函数
const getRerankProviderFromModel = (modelName: string) => {
  // 根据模型名称返回对应的provider
  if (modelName === 'gte-rerank') {
    return 'langgenius/tongyi/tongyi'
  }

  // 从模型名称中提取提供商信息，格式通常是 "provider/model_name"
  if (modelName && modelName.includes('/')) {
    return modelName.split('/')[0]
  }

  // 如果无法确定，返回默认值
  return 'default'
}

// 定义props
const props = defineProps({
  datasetId: {
    type: String,
    required: true
  }
})

// 查询相关变量
const queryText = ref('')
const searching = ref(false)
const hasSearched = ref(false)
const results = ref<any[]>([])
const searchTime = ref(0)
const queryHistory = ref<any[]>([])

// 搜索设置相关变量
const searchSettingsVisible = ref(false)
const searchSettings = reactive({
  searchMethod: 'semantic_search', // 'semantic_search' | 'full_text_search' | 'hybrid_search'
  rerankEnabled: true,
  rerankModel: 'gte-rerank',
  topK: 3,
  scoreEnabled: true,
  scoreThreshold: 0.5,
  rerankMode: '',
  provider: ''
})

// 添加RetrievalSetting组件需要的详细设置
const retrievalSettings = reactive({
  searchMethod: 'vector', // 'vector', 'fulltext', 'hybrid' - 映射到API需要的格式
  vectorSettings: {
    useRerank: true,
    rerankModel: 'gte-rerank',
    topK: 3,
    useScoreThreshold: true,
    scoreThreshold: 0.5
  },
  fullTextSettings: {
    useRerank: true,
    rerankModel: 'gte-rerank',
    topK: 3,
    useScoreThreshold: true,
    scoreThreshold: 0.5
  },
  hybridSettings: {
    mode: 'rerank' as 'rerank' | 'weight',
    vectorWeight: 0.5,
    rerankModel: 'gte-rerank',
    topK: 3,
    useScoreThreshold: true,
    scoreThreshold: 0.5
  },
  rerankModels: [] as RerankModel[]
})

// 段落详情相关变量
const segmentDetailVisible = ref(false)
const currentSegment = ref<any>(null)

// 生命周期钩子
onMounted(() => {
  fetchQueryHistory()
  fetchRerankModels()
})

// 获取查询历史记录
const fetchQueryHistory = async () => {
  try {
    const response = await getDatasetQueries(props.datasetId, {
      page: 1,
      limit: 10
    })

    queryHistory.value = response.data.map((item: any) => ({
      id: item.id,
      content: item.content,
      timestamp: item.created_at,
      resultCount: item.results?.length || 0
    }))
  } catch (error) {
    console.error('获取查询历史失败:', error)
  }
}

// 获取Rerank模型列表
const fetchRerankModels = async () => {
  try {
    console.log('开始获取Rerank模型列表...')

    // 添加超时控制
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('API调用超时')), 10000) // 10秒超时
    })

    // 获取模型列表
    const modelsResponse = (await Promise.race([getRerankModels(), timeoutPromise])) as any

    // 处理模型数据
    if (modelsResponse?.data && Array.isArray(modelsResponse.data)) {
      const allModels: RerankModel[] = []

      // 遍历所有提供商及其模型
      modelsResponse.data.forEach((provider: any) => {
        if (provider?.models && Array.isArray(provider.models)) {
          provider.models.forEach((model: any) => {
            console.log(provider, '-------------')
            if (model?.model) {
              // 创建新的RerankModel对象
              const completeModel: RerankModel = {
                model: model.model || '',
                label: model.label || { zh_Hans: '', en_US: '' },
                model_type: model.model_type || '',
                model_properties: model.model_properties || { context_size: 0 },
                status: model.status || 'active',
                features: null,
                fetch_from: 'model',
                deprecated: false,
                load_balancing_enabled: false,
                provider: provider.provider
              }
              allModels.push(completeModel)
            }
          })
        }
      })

      // 更新模型列表
      retrievalSettings.rerankModels = allModels
      console.log('成功获取Rerank模型列表:', allModels.length, '个模型')
      console.log('模型详情:', allModels)
    }

    // 获取默认模型
    try {
      const defaultModelResponse = (await Promise.race([getDefaultRerankModel(), timeoutPromise])) as any
      if (defaultModelResponse?.data && (defaultModelResponse.data as any).model) {
        // 更新所有使用Rerank的设置
        const defaultModel = (defaultModelResponse.data as any).model
        retrievalSettings.vectorSettings.rerankModel = defaultModel
        retrievalSettings.fullTextSettings.rerankModel = defaultModel
        retrievalSettings.hybridSettings.rerankModel = defaultModel
        searchSettings.rerankModel = defaultModel
        searchSettings.rerankMode = defaultModelResponse.data?.model_type
        console.log('设置默认Rerank模型:', defaultModel)
      }
    } catch (defaultModelError) {
      console.warn('获取默认Rerank模型失败，使用预设默认值:', defaultModelError)
      // 如果有模型列表，使用第一个模型作为默认值
      if (retrievalSettings.rerankModels.length > 0) {
        const firstModel = retrievalSettings.rerankModels[0].model
        retrievalSettings.vectorSettings.rerankModel = firstModel
        retrievalSettings.fullTextSettings.rerankModel = firstModel
        retrievalSettings.hybridSettings.rerankModel = firstModel
        searchSettings.rerankModel = firstModel
      }
    }
  } catch (error) {
    console.error('获取Rerank模型列表失败:', error)
    // 设置一个默认模型确保页面可以正常使用
    const defaultModel = 'gte-rerank'
    retrievalSettings.vectorSettings.rerankModel = defaultModel
    retrievalSettings.fullTextSettings.rerankModel = defaultModel
    retrievalSettings.hybridSettings.rerankModel = defaultModel
    searchSettings.rerankModel = defaultModel

    // 提供一个默认的模型选项
    retrievalSettings.rerankModels = [
      {
        model: 'gte-rerank',
        label: { zh_Hans: 'GTE-Rerank (默认)', en_US: 'GTE-Rerank (Default)' },
        model_type: 'rerank',
        model_properties: { context_size: 512 },
        status: 'active',
        features: null,
        fetch_from: 'model',
        deprecated: false,
        load_balancing_enabled: false,
        provider: 'gte-rerank'
      }
    ]
  }
}

// 打开搜索设置侧边栏
const openSearchSettings = () => {
  searchSettingsVisible.value = true
}

// 取消搜索设置
const cancelSearchSettings = () => {
  searchSettingsVisible.value = false
}

// 保存搜索设置
const saveSearchSettings = () => {
  // 根据当前选择的检索方法，同步最新设置
  switch (retrievalSettings.searchMethod) {
    case 'vector':
      searchSettings.searchMethod = 'semantic_search'
      searchSettings.rerankEnabled = retrievalSettings.vectorSettings.useRerank
      searchSettings.rerankModel = retrievalSettings.vectorSettings.rerankModel
      searchSettings.topK = retrievalSettings.vectorSettings.topK
      searchSettings.scoreEnabled = retrievalSettings.vectorSettings.useScoreThreshold
      searchSettings.scoreThreshold = retrievalSettings.vectorSettings.scoreThreshold
      break
    case 'fulltext':
      searchSettings.searchMethod = 'full_text_search'
      searchSettings.rerankEnabled = retrievalSettings.fullTextSettings.useRerank
      searchSettings.rerankModel = retrievalSettings.fullTextSettings.rerankModel
      searchSettings.topK = retrievalSettings.fullTextSettings.topK
      searchSettings.scoreEnabled = retrievalSettings.fullTextSettings.useScoreThreshold
      searchSettings.scoreThreshold = retrievalSettings.fullTextSettings.scoreThreshold
      break
    case 'hybrid':
      searchSettings.searchMethod = 'hybrid_search'
      searchSettings.rerankEnabled = retrievalSettings.hybridSettings.mode === 'rerank'
      searchSettings.rerankModel = retrievalSettings.hybridSettings.rerankModel
      searchSettings.topK = retrievalSettings.hybridSettings.topK
      searchSettings.scoreEnabled = retrievalSettings.hybridSettings.useScoreThreshold
      searchSettings.scoreThreshold = retrievalSettings.hybridSettings.scoreThreshold
      break
  }

  searchSettingsVisible.value = false
  Message.success('检索设置已保存')
}

// 添加处理RetrievalSetting组件事件的函数
const handleSearchMethodChange = (method: string) => {
  retrievalSettings.searchMethod = method

  // 转换为API需要的格式
  switch (method) {
    case 'vector':
      searchSettings.searchMethod = 'semantic_search'
      break
    case 'fulltext':
      searchSettings.searchMethod = 'full_text_search'
      break
    case 'hybrid':
      searchSettings.searchMethod = 'hybrid_search'
      break
  }
}

const handleVectorSettingsChange = (settings: any) => {
  console.log(settings)
  retrievalSettings.vectorSettings = settings
  // 更新searchSettings
  searchSettings.rerankEnabled = settings.useRerank
  searchSettings.rerankModel = settings.rerankModel
  searchSettings.topK = settings.topK
  searchSettings.scoreEnabled = settings.useScoreThreshold
  searchSettings.scoreThreshold = settings.scoreThreshold
}

const handleFullTextSettingsChange = (settings: any) => {
  retrievalSettings.fullTextSettings = settings

  // 如果当前是全文检索模式，更新searchSettings
  if (retrievalSettings.searchMethod === 'fulltext') {
    searchSettings.rerankEnabled = settings.useRerank
    searchSettings.rerankModel = settings.rerankModel
    searchSettings.topK = settings.topK
    searchSettings.scoreEnabled = settings.useScoreThreshold
    searchSettings.scoreThreshold = settings.scoreThreshold
  }
}

const handleHybridSettingsChange = (settings: any) => {
  retrievalSettings.hybridSettings = settings

  // 如果当前是混合检索模式，更新searchSettings
  if (retrievalSettings.searchMethod === 'hybrid') {
    searchSettings.rerankEnabled = settings.mode === 'rerank'
    searchSettings.rerankModel = settings.rerankModel
    searchSettings.topK = settings.topK
    searchSettings.scoreEnabled = settings.useScoreThreshold
    searchSettings.scoreThreshold = settings.scoreThreshold
  }
}

// 获取当前搜索方法的显示文本
const getSearchMethodText = () => {
  switch (searchSettings.searchMethod) {
    case 'semantic_search':
      return '向量检索'
    case 'full_text_search':
      return '全文检索'
    case 'hybrid_search':
      return '混合检索'
    default:
      return '向量检索'
  }
}

// 执行查询
const searchQuery = async () => {
  if (!queryText.value.trim()) {
    Message.warning('请输入测试问题')
    return
  }

  searching.value = true
  const startTime = Date.now()
  try {
    // 调用实际的API
    const response = await hitTesting(props.datasetId, {
      query: queryText.value,
      retrieval_model: {
        search_method: searchSettings.searchMethod as 'semantic_search' | 'full_text_search' | 'hybrid_search',
        reranking_enable: searchSettings.rerankEnabled,
        reranking_mode: searchSettings.rerankEnabled ? searchSettings.rerankMode : null,
        reranking_model: searchSettings.rerankEnabled
          ? {
              // reranking_provider_name: getRerankProviderFromModel(searchSettings.rerankModel),
              reranking_provider_name: searchSettings.provider,

              reranking_model_name: searchSettings.rerankModel
            }
          : null,
        weights:
          searchSettings.searchMethod === 'hybrid_search'
            ? {
                semantic_search: retrievalSettings.hybridSettings.vectorWeight,
                full_text_search: 1 - retrievalSettings.hybridSettings.vectorWeight
              }
            : null,
        top_k: searchSettings.topK,
        score_threshold_enabled: searchSettings.scoreEnabled,
        score_threshold: searchSettings.scoreEnabled ? searchSettings.scoreThreshold : 0
      }
    })

    // 处理API返回数据
    results.value = response.records.map((record) => ({
      chunk_id: record.segment.id,
      content: record.segment.content,
      score: record.score,
      char_count: record.segment.word_count,
      file_type: record.segment.document.data_source_type,
      file_name: record.segment.document.name,
      tags: record.segment.keywords || [],
      document_id: record.segment.document_id
    }))

    searchTime.value = Date.now() - startTime
    hasSearched.value = true

    // 添加到历史记录
    queryHistory.value.unshift({
      content: queryText.value,
      timestamp: Date.now() / 1000,
      resultCount: results.value.length
    })

    Message.success('测试完成')
  } catch (error) {
    console.error('执行测试查询失败:', error)
    Message.error('执行测试查询失败')
  } finally {
    searching.value = false
  }
}

// 打开文件
const openFile = (segment: any) => {
  // 如果有documentId，可以跳转到文档详情页
  if (segment.document_id) {
    Message.info(`打开文件: ${segment.file_name}`)
    // 这里可以添加路由跳转逻辑
    // router.push(`/datasets/${props.datasetId}/documents/${segment.document_id}`);
  } else {
    Message.info(`打开文件: ${segment.file_name}`)
  }
}

// 从历史记录加载查询
const loadHistoryQuery = (item: any) => {
  queryText.value = item.content
  searchQuery()
}

// 格式化日期
const formatDate = (timestamp: number) => {
  const date = new Date(timestamp * 1000)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}

// 打开段落详情
const openSegmentDetail = (segment: any) => {
  currentSegment.value = segment
  segmentDetailVisible.value = true
}
</script>

<style scoped lang="scss">
.hit-testing-container {
  max-width: 100%;
  padding: 0;

  .hit-testing-content {
    display: flex;
    gap: 20px;
    height: calc(100vh - 120px);
  }

  // 左侧面板
  .left-panel {
    flex: 0 0 48%;
    display: flex;
    flex-direction: column;
    max-width: 48%;

    .hit-testing-header {
      margin-bottom: 24px;

      h2 {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 8px;
      }

      .description {
        color: var(--color-text-3);
        font-size: 14px;
      }
    }

    .query-input-container {
      position: relative;
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 24px;
      // border: 1px solid var(--color-border-2);
      // border-radius: 8px;
      padding: 16px 0 0 0;
      background-color: #fff;

      .input-wrapper {
        position: relative;

        .label {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 8px;
        }

        .char-count {
          position: absolute;
          bottom: 8px;
          right: 10px;
          font-size: 12px;
          color: var(--color-text-3);
        }
      }

      .search-settings-button {
        position: absolute;
        top: 16px;
        right: 16px;
        display: flex;
        align-items: center;
        gap: 4px;
        cursor: pointer;
        color: var(--color-text-3);
        font-size: 14px;
        padding: 5px 10px;
        border-radius: 4px;

        &:hover {
          color: var(--color-primary);
        }

        .arco-icon {
          margin-right: 4px;
        }
      }

      .arco-btn {
        align-self: flex-end;
        width: 120px;
      }
    }

    .history-container {
      flex: 1;
      // border: 1px solid var(--color-border-2);
      // border-radius: 8px;
      background-color: #fff;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      margin-top: auto;

      .history-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        // padding: 12px 16px;
        // border-bottom: 1px solid var(--color-border-2);

        h3 {
          font-size: 14px;
          font-weight: 500;
          margin: 0;
          padding-bottom: 10px;
        }
      }

      .history-list {
        overflow-y: auto;
        flex: 1;
      }

      .history-table {
        width: 100%;

        .table-header,
        .table-row {
          display: flex;
          padding: 10px 14px;

          .column-data {
            width: 20%;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 13px;
          }

          .column-query {
            width: 50%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 13px;
          }

          .column-time {
            width: 30%;
            text-align: right;
            font-size: 13px;
          }
        }

        .table-header {
          background-color: var(--color-fill-1);
          font-weight: 500;
          color: var(--color-text-2);
          font-size: 12px;
        }

        .table-row {
          cursor: pointer;
          border-bottom: 1px solid var(--color-border-1);

          &:hover {
            background-color: var(--color-fill-1);
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }
  }

  // 右侧面板
  .right-panel {
    flex: 0 0 48%;
    background-color: #f9f9fa;
    border-radius: 8px;
    padding: 20px;
    overflow-y: auto;

    .empty-result {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--color-text-3);

      .empty-icon {
        margin-bottom: 16px;

        img {
          width: 80px;
          height: 80px;
          opacity: 0.6;
        }
      }

      .empty-text {
        font-size: 14px;
      }
    }

    .result-section {
      .result-header {
        margin-bottom: 20px;

        .result-title {
          font-size: 16px;
          font-weight: 500;
          display: flex;
          align-items: center;

          .search-time {
            margin-left: auto;
            font-size: 13px;
            color: var(--color-text-3);
            font-weight: normal;
          }
        }
      }

      .result-list {
        .result-item {
          background-color: #fff;
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 16px;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);

          .result-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .chunk-id {
              font-size: 13px;
              font-weight: 500;

              .chars {
                font-weight: normal;
                color: var(--color-text-3);
                margin-left: 8px;
              }
            }

            .score {
              background-color: #f2f2f2;
              padding: 2px 8px;
              border-radius: 4px;
              font-size: 12px;
              color: var(--color-text-2);
            }
          }

          .result-content {
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 12px;
            white-space: pre-line;
          }

          .result-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 6px;
            margin-bottom: 12px;

            .tag-item {
              margin: 0;
              background-color: #f5f5f5;
              border: none;
              color: var(--color-text-2);
            }
          }

          .result-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;

            .file-name {
              font-size: 13px;
              color: var(--color-text-3);
              display: flex;
              align-items: center;
              gap: 4px;

              .arco-icon {
                color: #f54e4e;
              }
            }

            .open-btn {
              color: var(--color-primary);
              padding: 0;
            }
          }
        }
      }

      .no-results {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 48px 0;
        color: var(--color-text-3);

        .arco-icon {
          font-size: 32px;
          margin-bottom: 16px;
        }
      }
    }
  }
}

// 段落详情弹窗样式
.segment-detail-container {
  .segment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .segment-id-group {
      display: flex;
      flex-direction: row;
      gap: 4px;

      .segment-id {
        font-size: 16px;
        font-weight: 500;
      }

      .segment-file {
        font-size: 13px;
        color: var(--color-text-3);
        display: flex;
        align-items: center;
        gap: 4px;

        .arco-icon {
          color: #f54e4e;
        }
      }
    }

    .segment-score {
      background-color: #f2f2f2;
      padding: 4px 10px;
      border-radius: 4px;
      font-size: 12px;
      color: var(--color-text-2);
    }
  }

  .segment-content {
    padding: 16px;
    background-color: var(--color-fill-1);
    border-radius: 8px;
    margin-bottom: 16px;
    line-height: 1.6;
    white-space: pre-line;
    min-height: 300px;
    overflow-y: auto;
  }

  .segment-tags-section {
    .tags-title {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 12px;
    }

    .segment-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .tag-item {
        margin: 0;
        background-color: #f5f5f5;
        border: none;
        color: var(--color-text-2);
      }
    }
  }
}

.form-section {
  margin-bottom: 32px;
  background-color: #fff;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

  &:last-child {
    margin-bottom: 0;
  }
}
.form-item {
  margin-bottom: 24px;

  &:last-child {
    margin-bottom: 0;
  }
}

.form-label {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;

  &.required::after {
    content: '*';
    color: var(--color-danger);
    margin-left: 4px;
  }
}

.retrieval-settings-note {
  margin-bottom: 16px;
  color: var(--color-text-3);
  font-size: 14px;
}

.full-width {
  width: 100%;
}

.select-prefix {
  display: flex;
  align-items: center;
  margin-right: 8px;
}

.avatar-circle {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #f97316;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;

  &.team {
    background-color: #3b82f6;
  }

  &.team-part {
    background-color: #8b5cf6;
  }
}

.permission-option {
  display: flex;
  align-items: center;

  .option-icon {
    margin-right: 8px;
  }
}

.index-mode-options {
  display: flex;
  gap: 16px;

  .mode-option {
    flex: 1;
    border: 1px solid var(--color-border-2);
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s;

    &.active {
      border-color: var(--color-primary);
      background-color: var(--color-primary-light-1);
    }

    .mode-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .mode-icon {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        &.quality {
          background-color: var(--color-success-light-1);
          color: var(--color-success);
        }

        &.economy {
          background-color: var(--color-warning-light-1);
          color: var(--color-warning);
        }
      }

      .mode-title {
        font-weight: 500;
        display: flex;
        align-items: center;
        gap: 8px;
      }
    }

    .mode-description {
      font-size: 13px;
      color: var(--color-text-3);
      line-height: 1.5;
    }
  }
}

.recommend-tag {
  color: var(--color-success);
  background-color: var(--color-success-light-1);
  border: none;
}

.retrieval-methods {
  .retrieval-method-item {
    display: flex;
    margin-bottom: 24px;
    padding: 16px;
    background-color: var(--color-fill-1);
    border-radius: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    .method-icon {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      font-size: 20px;

      &.vector {
        background-color: var(--color-primary-light-1);
        color: var(--color-primary);
      }

      &.fulltext {
        background-color: var(--color-warning-light-1);
        color: var(--color-warning);
      }

      &.hybrid {
        background-color: var(--color-success-light-1);
        color: var(--color-success);
      }
    }

    .method-content {
      flex: 1;

      .method-title {
        font-weight: 500;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .method-description {
        font-size: 13px;
        color: var(--color-text-3);
        line-height: 1.5;
        margin-bottom: 16px;
      }

      .method-settings {
        .setting-row {
          display: flex;
          align-items: center;
          margin-bottom: 8px;

          .setting-label {
            font-weight: 500;
            margin-right: 4px;
          }

          .help-icon {
            color: var(--color-text-3);
            font-size: 14px;
            cursor: pointer;
          }
        }

        .setting-select {
          margin-bottom: 16px;
        }

        .setting-slider {
          display: flex;
          align-items: center;
          gap: 16px;
          margin-bottom: 16px;

          .arco-slider {
            flex: 1;
          }
        }
      }
    }
  }
}

// 添加固定底部按钮区域的样式
.drawer-footer-fixed {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 24px;
  background-color: #fff;
  box-shadow: 0 -1px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-top: 1px solid var(--color-border-2);
  z-index: 10;
}
</style>
<style lang="scss">
// 检索设置容器
.retrieval-settings-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;
  .retrieval-option {
    border: 1px solid var(--color-border-2);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;
    &.active {
      border-color: var(--color-primary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    .option-header {
      display: flex;
      align-items: center;
      padding: 16px;
      background-color: var(--color-fill-1);
      cursor: pointer;
      .option-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;
        .icon {
          font-size: 18px;
          color: var(--color-text-1);
        }
      }
      .option-info {
        flex: 1;
        .option-title {
          font-weight: 500;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          gap: 6px;
        }
        .option-desc {
          font-size: 12px;
          color: var(--color-text-3);
        }
      }
    }
    .option-content {
      padding: 16px;
      background-color: #fff;
      border-top: 1px solid var(--color-border-2);
      .content-section {
        .content-detail {
          font-size: 13px;
          color: var(--color-text-2);
          margin-bottom: 16px;
          line-height: 1.6;
        }
        .hybrid-modes {
          display: flex;
          flex-direction: row;
          gap: 12px;
          margin-bottom: 20px;
          .hybrid-mode-option {
            flex: 1;
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid var(--color-border-2);
            border-radius: 6px;
            cursor: pointer;
            &.selected {
              border-color: var(--color-primary);
              background-color: var(--color-primary-light-1);
            }
            .mode-icon {
              font-size: 18px;
              margin-right: 12px;
              color: var(--color-text-2);
            }
            .mode-info {
              flex: 1;
              .mode-name {
                font-weight: 500;
                margin-bottom: 4px;
              }
              .mode-desc {
                font-size: 12px;
                color: var(--color-text-3);
                line-height: 1.5;
              }
            }
          }
        }
        .weight-settings {
          padding: 12px;
          background-color: var(--color-fill-1);
          border-radius: 6px;
          .weight-setting-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            .weight-label {
              width: 120px;
              font-size: 13px;
            }
            .arco-slider {
              flex: 1;
              margin: 0 12px;
            }
            .weight-value {
              width: 50px;
              font-size: 12px;
            }
          }
          .weight-note {
            font-size: 12px;
            color: var(--color-text-3);
            text-align: right;
            padding-right: 30px;
          }
        }
        .rerank-settings {
          .rerank-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            .hint-icon {
              font-size: 14px;
              color: var(--color-text-3);
              margin-left: 4px;
              cursor: pointer;
            }
          }
          .select-label {
            font-size: 13px;
            margin-bottom: 8px;
          }
          .arco-select {
            width: 100%;
            margin-bottom: 16px;
          }
          .params-config {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-top: 12px;
            .param-item {
              display: flex;
              align-items: center;
              gap: 8px;
              .param-label {
                font-size: 13px;
                min-width: 80px;
              }
              .param-label1 {
                min-width: 100px;
                display: flex;
                align-items: center;
              }
              .hint-icon {
                font-size: 14px;
                color: var(--color-text-3);
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }
}
</style>
