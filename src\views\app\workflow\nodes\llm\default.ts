import { PromptRole } from '@/views/app/workflow/types/workflow'

const nodeDefault: any = {
  defaultValue: {
    model: {
      provider: '',
      name: '',
      mode: 'chat',
      completion_params: {
        temperature: 0.7
      }
    },
    prompt_template: [
      {
        role: PromptRole.system,
        text: ''
      }
    ],
    context: {
      enabled: false,
      variable_selector: []
    },
    vision: {
      enabled: false
    }
  }
}
export default nodeDefault
