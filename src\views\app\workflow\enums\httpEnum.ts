export default [
  {
    dictCode: 'REQUEST_TYPE',
    dictName: '请求类型',
    elements: [
      {
        value: 'GET',
        name: 'GET',
        code: 'GET'
      },
      {
        value: 'POST',
        name: 'POST',
        code: 'POST'
      },
      {
        value: 'PUT',
        name: 'PUT',
        code: 'PUT'
      },
      {
        value: 'DELETE',
        name: 'DELETE',
        code: 'DELETE'
      }
    ],
    rules: []
  },
  {
    dictCode: 'CONTENT_TYPE',
    dictName: '请求信息类型',
    elements: [
      {
        // json
        value: 'application/json;charset=UTF-8',
        name: 'JSO<PERSON>',
        code: 'JSON'
      },
      {
        // form-data qs
        value: 'application/x-www-form-urlencoded;charset=UTF-8',
        name: '共享空间',
        code: 'FORM_URLENCODED'
      },
      {
        // form-data  upload
        value: 'multipart/form-data;charset=UTF-8',
        name: '共享空间',
        code: 'FORM_DATA'
      }
    ],
    rules: []
  },
  {
    dictCode: 'RESULT_TYPE',
    dictName: '响应结果类型',
    elements: [
      {
        value: 0,
        name: '成功',
        code: 'SUCCESS'
      },
      {
        value: -1,
        name: '错误',
        code: 'ERROR'
      },
      {
        value: 401,
        name: '超时',
        code: 'TIMEOUT'
      }
    ],
    rules: []
  }
]
