import { getIncomers } from '@vue-flow/core'
import type { Node, Edge } from '@vue-flow/core'
import { uniqBy } from 'lodash-es'
import {
  SUPPORT_OUTPUT_VARS_NODE,
  type Var,
  type StartNodeType,
  type CodeNodeType,
  type ParameterExtractorNodeType,
  type IterationNodeType,
  type DocExtractorNodeType,
  type ListFilterNodeType,
  type AgentNodeType,
  type ToolNodeType,
  type LoopNodeType,
  type VariableAssignerNodeType,
  type StructuredOutput,
  Type,
  type Field
} from '@/views/app/workflow/types/variable'
import type { ValueSelector } from '@/views/app/workflow/nodes/http/types.ts'
import {
  BlockEnum,
  InputVarType,
  VarType,
  type ConversationVariable,
  type EnvironmentVariable,
  type NodeOutPutVar
} from '@/views/app/workflow/types/workflow'
import {
  LLM_OUTPUT_STRUCT,
  KNOWLEDGE_RETRIEVAL_OUTPUT_STRUCT,
  TEMPLATE_TRANSFORM_OUTPUT_STRUCT,
  QUESTION_CLASSIFIER_OUTPUT_STRUCT,
  HTTP_REQUEST_OUTPUT_STRUCT,
  TOOL_OUTPUT_STRUCT,
  PARAMETER_EXTRACTOR_COMMON_STRUCT,
  OUTPUT_FILE_SUB_VARIABLES
} from '@/views/app/workflow/constant/common'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import {
  getMaxVarNameLength,
  VAR_ITEM_TEMPLATE,
  VAR_ITEM_TEMPLATE_IN_WORKFLOW
} from '@/views/app/workflow/constant/configuration'

const inputVarTypeToVarType = (type: InputVarType): VarType => {
  return (
    (
      {
        [InputVarType.number]: VarType.number,
        [InputVarType.singleFile]: VarType.file,
        [InputVarType.multiFiles]: VarType.arrayFile
      } as any
    )[type] || VarType.string
  )
}
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const getBeforeNodesInSameBranch = (nodeId: string, newNodes?: any[], newEdges?: any[]) => {
  // const nodes: Node[] = localStorage.getItem('ksNodes') ? JSON.parse(localStorage.getItem('ksNodes') as string) : []
  // const edges: Edge[] = localStorage.getItem('ksEdges') ? JSON.parse(localStorage.getItem('ksEdges') as string) : []
  const nodesStore = useNodesStore()
  const nodes: Node[] = nodesStore.nodes
  const edges: Edge[] = nodesStore.edges

  const currentNode: any = nodes.find((node) => node.id === nodeId)
  //
  const list: Node[] = []
  if (!currentNode) return list

  if (currentNode.parentId) {
    const parentNode = nodes.find((node) => node.id === currentNode.parentId)
    if (parentNode) {
      const parentList = getBeforeNodesInSameBranch(parentNode.id)

      list.push(...parentList)
    }
  }

  const traverse = (root: Node, callback: (node: Node) => void) => {
    if (root) {
      const incomers = getIncomers(root, nodes, edges)
      if (incomers.length) {
        incomers.forEach((node) => {
          if (!list.find((n) => node.id === n.id)) {
            callback(node)
            traverse(node, callback)
          }
        })
      }
    }
  }
  traverse(currentNode, (node: Node) => {
    list.push(node)
  })

  const length = list.length
  if (length) {
    return uniqBy(list, 'id')
      .reverse()
      .filter((item: Node) => {
        return SUPPORT_OUTPUT_VARS_NODE.includes(item.data.type)
      })
  }

  return []
}

export const getBeforeNodesInSameBranchIncludeParent = (nodeId: string, newNodes?: Node[], newEdges?: Edge[]) => {
  const nodes: any[] = getBeforeNodesInSameBranch(nodeId, newNodes, newEdges)
  // const allNodes: Node[] = localStorage.getItem('ksNodes') ? JSON.parse(localStorage.getItem('ksNodes') as string) : []
  const nodesStore = useNodesStore()
  const allNodes: Node[] = nodesStore.nodes
  const node: any = allNodes.find((n) => n.id === nodeId)
  const parentNodeId = node?.parentId
  const parentNode = allNodes.find((n) => n.id === parentNodeId)
  if (parentNode) nodes.push(parentNode)

  return nodes
}
export const filterVar = (varType: VarType) => {
  return (v: Var) => {
    if (varType === VarType.any) return true
    if (v.type === VarType.any) return true
    return v.type === varType
  }
}
export const isSystemVar = (valueSelector: ValueSelector) => {
  return valueSelector[0] === 'sys' || valueSelector[1] === 'sys'
}
export const isENV = (valueSelector: ValueSelector) => {
  return valueSelector[0] === 'env'
}

export const isConversationVar = (valueSelector: ValueSelector) => {
  return valueSelector[0] === 'conversation'
}
const getIterationItemType = ({
  valueSelector,
  beforeNodesOutputVars
}: {
  valueSelector: ValueSelector
  beforeNodesOutputVars: NodeOutPutVar[]
}): VarType => {
  const outputVarNodeId = valueSelector[0]
  const isSystem = isSystemVar(valueSelector)

  const targetVar = isSystem
    ? beforeNodesOutputVars.find((v) => v.isStartNode)
    : beforeNodesOutputVars.find((v) => v.nodeId === outputVarNodeId)
  if (!targetVar) return VarType.string

  let arrayType: VarType = VarType.string

  let curr: any = targetVar.vars
  if (isSystem) {
    arrayType = curr.find((v: any) => v.variable === valueSelector.join('.'))?.type
  } else {
    valueSelector.slice(1).forEach((key, i) => {
      const isLast = i === valueSelector.length - 2
      curr = curr?.find((v: any) => v.variable === key)
      if (isLast) {
        arrayType = curr?.type
      } else {
        if (curr?.type === VarType.object || curr?.type === VarType.file) curr = curr.children
      }
    })
  }

  switch (arrayType as VarType) {
    case VarType.arrayString:
      return VarType.string
    case VarType.arrayNumber:
      return VarType.number
    case VarType.arrayObject:
      return VarType.object
    case VarType.array:
      return VarType.any
    case VarType.arrayFile:
      return VarType.file
    default:
      return VarType.string
  }
}
const getLoopItemType = ({
  valueSelector,
  beforeNodesOutputVars
}: {
  valueSelector: ValueSelector
  beforeNodesOutputVars: NodeOutPutVar[]
}): VarType => {
  const outputVarNodeId = valueSelector[0]
  const isSystem = isSystemVar(valueSelector)

  const targetVar = isSystem
    ? beforeNodesOutputVars.find((v) => v.isStartNode)
    : beforeNodesOutputVars.find((v) => v.nodeId === outputVarNodeId)
  if (!targetVar) return VarType.string

  let arrayType: VarType = VarType.string

  let curr: any = targetVar.vars
  if (isSystem) {
    arrayType = curr.find((v: any) => v.variable === valueSelector.join('.'))?.type
  } else {
    valueSelector.slice(1).forEach((key, i) => {
      const isLast = i === valueSelector.length - 2
      curr = curr?.find((v: any) => v.variable === key)
      if (isLast) {
        arrayType = curr?.type
      } else {
        if (curr?.type === VarType.object || curr?.type === VarType.file) curr = curr.children
      }
    })
  }

  switch (arrayType as VarType) {
    case VarType.arrayString:
      return VarType.string
    case VarType.arrayNumber:
      return VarType.number
    case VarType.arrayObject:
      return VarType.object
    case VarType.array:
      return VarType.any
    case VarType.arrayFile:
      return VarType.file
    default:
      return VarType.string
  }
}

export const getVarType = ({
  parentNode,
  valueSelector,
  isIterationItem,
  isLoopItem,
  availableNodes,
  isChatMode,
  isConstant,
  environmentVariables = [],
  conversationVariables = []
}: {
  valueSelector: ValueSelector
  parentNode?: Node | null
  isIterationItem?: boolean
  isLoopItem?: boolean
  availableNodes: any[]
  isChatMode: boolean
  isConstant?: boolean
  environmentVariables?: EnvironmentVariable[]
  conversationVariables?: ConversationVariable[]
}): VarType => {
  if (isConstant) return VarType.string
  const beforeNodesOutputVars = toNodeOutputVars(
    availableNodes,
    isChatMode,
    undefined,
    environmentVariables,
    conversationVariables
  )

  const isIterationInnerVar = parentNode?.data.type === BlockEnum.Iteration
  if (isIterationItem) {
    return getIterationItemType({
      valueSelector,
      beforeNodesOutputVars
    })
  }
  if (isIterationInnerVar) {
    if (valueSelector[1] === 'item') {
      const itemType = getIterationItemType({
        valueSelector: (parentNode?.data as any).iterator_selector || [],
        beforeNodesOutputVars
      })
      return itemType
    }
    if (valueSelector[1] === 'index') return VarType.number
  }

  const isLoopInnerVar = parentNode?.data.type === BlockEnum.Loop
  if (isLoopItem) {
    return getLoopItemType({
      valueSelector,
      beforeNodesOutputVars
    })
  }
  if (isLoopInnerVar) {
    if (valueSelector[1] === 'item') {
      const itemType = getLoopItemType({
        valueSelector: (parentNode?.data as any).iterator_selector || [],
        beforeNodesOutputVars
      })
      return itemType
    }
    if (valueSelector[1] === 'index') return VarType.number
  }

  const isSystem = isSystemVar(valueSelector)
  const isEnv = isENV(valueSelector)
  const isChatVar = isConversationVar(valueSelector)
  const startNode = availableNodes.find((node: any) => {
    return node?.data.type === BlockEnum.Start
  })

  const targetVarNodeId = isSystem ? startNode?.id : valueSelector[0]
  const targetVar = beforeNodesOutputVars.find((v) => v.nodeId === targetVarNodeId)

  if (!targetVar) return VarType.string

  let type: VarType = VarType.string
  let curr: any = targetVar.vars

  if (isSystem || isEnv || isChatVar) {
    return curr.find((v: any) => v.variable === (valueSelector as ValueSelector).join('.'))?.type
  } else {
    const targetVar = curr.find((v: any) => v.variable === valueSelector[1])
    if (!targetVar) return VarType.string

    const isStructuredOutputVar = !!targetVar.children?.schema?.properties
    if (isStructuredOutputVar) {
      if (valueSelector.length === 2) {
        // root
        return VarType.object
      }
      let currProperties = targetVar.children.schema
      ;(valueSelector as ValueSelector).slice(2).forEach((key, i) => {
        const isLast = i === valueSelector.length - 3
        if (!currProperties) return

        currProperties = currProperties.properties[key]
        if (isLast) type = structTypeToVarType(currProperties?.type)
      })
      return type
    }

    ;(valueSelector as ValueSelector).slice(1).forEach((key, i) => {
      const isLast = i === valueSelector.length - 2
      if (Array.isArray(curr)) curr = curr?.find((v: any) => v.variable === key)

      if (isLast) {
        type = curr?.type
      } else {
        if (curr?.type === VarType.object || curr?.type === VarType.file) curr = curr.children
      }
    })
    return type
  }
}
export const toNodeAvailableVars = (
  parentNode,
  beforeNodes,
  isChatMode,
  filterVar,
  environmentVariables,
  conversationVariables
) => {
  const beforeNodesOutputVars = toNodeOutputVars(
    beforeNodes,
    isChatMode,
    filterVar,
    environmentVariables,
    conversationVariables
  )
  const isInIteration = parentNode?.data?.type === BlockEnum.Iteration

  if (isInIteration) {
    const iterationNode: any = parentNode
    const itemType = getVarType({
      parentNode: iterationNode,
      isIterationItem: true,
      valueSelector: iterationNode?.data.iterator_selector || [],
      availableNodes: beforeNodes,
      isChatMode,
      environmentVariables,
      conversationVariables
    })
    const itemChildren =
      itemType === VarType.file
        ? {
            children: OUTPUT_FILE_SUB_VARIABLES.map((key) => {
              return {
                variable: key,
                type: key === 'size' ? VarType.number : VarType.string
              }
            })
          }
        : {}
    const iterationVar: any = {
      nodeId: iterationNode?.id,
      title: '当前迭代',
      vars: [
        {
          variable: 'item',
          type: itemType,
          ...itemChildren
        },
        {
          variable: 'index',
          type: VarType.number
        }
      ]
    }
    const iterationIndex = beforeNodesOutputVars.findIndex((v) => v.nodeId === iterationNode?.id)
    if (iterationIndex > -1) beforeNodesOutputVars.splice(iterationIndex, 1)
    beforeNodesOutputVars.unshift(iterationVar)
  }
  return beforeNodesOutputVars
}
export const toNodeOutputVars = (
  nodes: any[],
  isChatMode: boolean,
  filterVar = (_payload: Var, _selector: ValueSelector) => true,
  environmentVariables: EnvironmentVariable[] = [],
  conversationVariables: ConversationVariable[] = []
) => {
  const ENV_NODE = {
    id: 'env',
    data: {
      title: 'ENVIRONMENT',
      type: 'env',
      envList: environmentVariables
    }
  }
  // CHAT_VAR_NODE data format
  const CHAT_VAR_NODE = {
    id: 'conversation',
    data: {
      title: 'CONVERSATION',
      type: 'conversation',
      chatVarList: conversationVariables
    }
  }
  const res = [
    ...nodes.filter((node) => SUPPORT_OUTPUT_VARS_NODE.includes(node?.data?.type)),
    ...(environmentVariables.length > 0 ? [ENV_NODE] : []),
    ...(isChatMode && conversationVariables.length > 0 ? [CHAT_VAR_NODE] : [])
  ]
    .map((node) => {
      return {
        ...formatItem(node, isChatMode, filterVar),
        isStartNode: node.data.type === BlockEnum.Start
      }
    })
    .filter((item) => item.vars.length > 0)
  return res
}

const structTypeToVarType = (type: Type, isArray?: boolean): VarType => {
  if (isArray) {
    return (
      (
        {
          [Type.string]: VarType.arrayString,
          [Type.number]: VarType.arrayNumber,
          [Type.object]: VarType.arrayObject
        } as any
      )[type] || VarType.string
    )
  }
  return (
    (
      {
        [Type.string]: VarType.string,
        [Type.number]: VarType.number,
        [Type.boolean]: VarType.boolean,
        [Type.object]: VarType.object,
        [Type.array]: VarType.array
      } as any
    )[type] || VarType.string
  )
}

export const produce = (state, producer) => {
  producer(state)
  return state
}

const findExceptVarInStructuredProperties = (
  properties: Record<string, Field>,
  filterVar: (payload: Var, selector: ValueSelector) => boolean
): Record<string, Field> => {
  const res = produce(properties, (draft) => {
    Object.keys(properties).forEach((key) => {
      const item = properties[key]
      const isObj = item.type === Type.object
      const isArray = item.type === Type.array
      const arrayType = item.items?.type

      if (
        !isObj &&
        !filterVar(
          {
            variable: key,
            type: structTypeToVarType(isArray ? arrayType! : item.type, isArray)
          },
          [key]
        )
      ) {
        delete properties[key]
        return
      }
      if (item.type === Type.object && item.properties)
        item.properties = findExceptVarInStructuredProperties(item.properties, filterVar)
    })
    return draft
  })
  return res
}

const findExceptVarInStructuredOutput = (
  structuredOutput: StructuredOutput,
  filterVar: (payload: Var, selector: ValueSelector) => boolean
): StructuredOutput => {
  const res = produce(structuredOutput, (draft) => {
    const properties = draft.schema.properties
    Object.keys(properties).forEach((key) => {
      const item = properties[key]
      const isObj = item.type === Type.object
      const isArray = item.type === Type.array
      const arrayType = item.items?.type
      if (
        !isObj &&
        !filterVar(
          {
            variable: key,
            type: structTypeToVarType(isArray ? arrayType! : item.type, isArray)
          },
          [key]
        )
      ) {
        delete properties[key]
        return
      }
      if (item.type === Type.object && item.properties)
        item.properties = findExceptVarInStructuredProperties(item.properties, filterVar)
    })
    return draft
  })
  return res
}

const findExceptVarInObject = (
  obj: any,
  filterVar: (payload: Var, selector: ValueSelector) => boolean,
  value_selector: ValueSelector,
  isFile?: boolean
): Var => {
  const { children } = obj
  const isStructuredOutput = !!(children as StructuredOutput)?.schema?.properties

  const res: Var = {
    variable: obj.variable,
    type: isFile ? VarType.file : VarType.object,
    children: isStructuredOutput
      ? findExceptVarInStructuredOutput(children, filterVar)
      : children.filter((item: Var) => {
          const { children } = item
          const currSelector = [...value_selector, item.variable]
          if (!children) return filterVar(item, currSelector)
          const obj = findExceptVarInObject(item, filterVar, currSelector, false) // File doesn't contains file children
          return obj.children && (obj.children as Var[])?.length > 0
        })
  }
  return res
}

const formatItem = (
  item: any,
  isChatMode: boolean,
  filterVar: (payload: Var, selector: ValueSelector) => boolean
): NodeOutPutVar => {
  const { id, data } = item

  // const res: NodeOutPutVar = {
  const res: any = {
    nodeId: id,
    title: data.title,
    vars: []
  }
  switch (data.type) {
    case BlockEnum.Start: {
      const { variables } = data as StartNodeType
      res.vars = variables.map((v) => {
        return {
          variable: v.variable,
          type: inputVarTypeToVarType(v.type),
          isParagraph: v.type === InputVarType.paragraph,
          isSelect: v.type === InputVarType.select,
          options: v.options,
          required: v.required
        }
      })
      if (isChatMode) {
        res.vars.push({
          variable: 'sys.query',
          type: VarType.string
        })
        res.vars.push({
          variable: 'sys.dialogue_count',
          type: VarType.number
        })
        res.vars.push({
          variable: 'sys.conversation_id',
          type: VarType.string
        })
      }
      res.vars.push({
        variable: 'sys.user_id',
        type: VarType.string
      })
      res.vars.push({
        variable: 'sys.files',
        type: VarType.arrayFile
      })
      res.vars.push({
        variable: 'sys.app_id',
        type: VarType.string
      })
      res.vars.push({
        variable: 'sys.workflow_id',
        type: VarType.string
      })
      res.vars.push({
        variable: 'sys.workflow_run_id',
        type: VarType.string
      })

      break
    }

    case BlockEnum.LLM: {
      res.vars = [...LLM_OUTPUT_STRUCT]
      if (
        data.structured_output_enabled &&
        data.structured_output?.schema?.properties &&
        Object.keys(data.structured_output.schema.properties).length > 0
      ) {
        res.vars.push({
          variable: 'structured_output',
          type: VarType.object,
          children: data.structured_output
        })
      }

      break
    }
    case BlockEnum.KnowledgeRetrieval: {
      res.vars = KNOWLEDGE_RETRIEVAL_OUTPUT_STRUCT
      break
    }

    case BlockEnum.Code: {
      const { outputs } = data as CodeNodeType
      res.vars = outputs
        ? Object.keys(outputs).map((key) => {
            return {
              variable: key,
              type: outputs[key].type
            }
          })
        : []
      break
    }

    case BlockEnum.TemplateTransform: {
      res.vars = TEMPLATE_TRANSFORM_OUTPUT_STRUCT
      break
    }

    case BlockEnum.QuestionClassifier: {
      res.vars = QUESTION_CLASSIFIER_OUTPUT_STRUCT
      break
    }

    case BlockEnum.HttpRequest: {
      res.vars = HTTP_REQUEST_OUTPUT_STRUCT
      break
    }

    case BlockEnum.VariableAssigner: {
      const { output_type, advanced_settings } = data as VariableAssignerNodeType
      const isGroup = !!advanced_settings?.group_enabled
      if (!isGroup) {
        res.vars = [
          {
            variable: 'output',
            type: output_type
          }
        ]
      } else {
        res.vars = advanced_settings?.groups.map((group) => {
          return {
            variable: group.group_name,
            type: VarType.object,
            children: [
              {
                variable: 'output',
                type: group.output_type
              }
            ]
          }
        })
      }
      break
    }

    case BlockEnum.VariableAggregator: {
      const { output_type, advanced_settings } = data as VariableAssignerNodeType
      const isGroup = !!advanced_settings?.group_enabled
      if (!isGroup) {
        res.vars = [
          {
            variable: 'output',
            type: output_type
          }
        ]
      } else {
        res.vars = advanced_settings?.groups.map((group) => {
          return {
            variable: group.group_name,
            type: VarType.object,
            children: [
              {
                variable: 'output',
                type: group.output_type
              }
            ]
          }
        })
      }
      break
    }

    case BlockEnum.Tool: {
      const { output_schema } = data as ToolNodeType
      if (!output_schema) {
        res.vars = TOOL_OUTPUT_STRUCT
      } else {
        const outputSchema: any[] = []
        Object.keys(output_schema.properties).forEach((outputKey) => {
          const output = output_schema.properties[outputKey]
          const dataType = output.type
          outputSchema.push({
            variable: outputKey,
            type:
              dataType === 'array'
                ? `array[${output.items?.type.slice(0, 1).toLocaleLowerCase()}${output.items?.type.slice(1)}]`
                : `${output.type.slice(0, 1).toLocaleLowerCase()}${output.type.slice(1)}`,
            description: output.description,
            children:
              output.type === 'object'
                ? {
                    schema: {
                      type: 'object',
                      properties: output.properties
                    }
                  }
                : undefined
          })
        })
        res.vars = [...TOOL_OUTPUT_STRUCT, ...outputSchema]
      }
      break
    }

    case BlockEnum.ParameterExtractor: {
      res.vars = [
        ...((data as ParameterExtractorNodeType).parameters || []).map((p) => {
          return {
            variable: p.name,
            type: p.type as unknown as VarType
          }
        }),
        ...PARAMETER_EXTRACTOR_COMMON_STRUCT
      ]
      break
    }

    case BlockEnum.Iteration: {
      res.vars = [
        {
          variable: 'output',
          type: (data as IterationNodeType).output_type || VarType.arrayString
        }
      ]
      break
    }

    case BlockEnum.Loop: {
      const { loop_variables } = data as LoopNodeType
      res.isLoop = true
      res.vars =
        loop_variables?.map((v) => {
          return {
            variable: v.label,
            type: v.var_type,
            isLoopVariable: true,
            nodeId: res.nodeId
          }
        }) || []

      break
    }

    case BlockEnum.DocExtractor: {
      res.vars = [
        {
          variable: 'text',
          type: (data as DocExtractorNodeType).is_array_file ? VarType.arrayString : VarType.string
        }
      ]
      break
    }

    case BlockEnum.ListFilter: {
      if (!(data as ListFilterNodeType).var_type) break

      res.vars = [
        {
          variable: 'result',
          type: (data as ListFilterNodeType).var_type
        },
        {
          variable: 'first_record',
          type: (data as ListFilterNodeType).item_var_type
        },
        {
          variable: 'last_record',
          type: (data as ListFilterNodeType).item_var_type
        }
      ]
      break
    }

    case BlockEnum.Agent: {
      const payload = data as AgentNodeType
      const outputs: Var[] = []
      Object.keys(payload.output_schema?.properties || {}).forEach((outputKey) => {
        const output = payload.output_schema.properties[outputKey]
        outputs.push({
          variable: outputKey,
          type:
            output.type === 'array'
              ? (`Array[${output.items?.type.slice(0, 1).toLocaleUpperCase()}${output.items?.type.slice(1)}]` as VarType)
              : (`${output.type.slice(0, 1).toLocaleUpperCase()}${output.type.slice(1)}` as VarType)
        })
      })
      res.vars = [...outputs, ...TOOL_OUTPUT_STRUCT]
      break
    }

    case 'env': {
      res.vars = data.envList.map((env: EnvironmentVariable) => {
        return {
          variable: `env.${env.name}`,
          type: env.value_type
        }
      }) as Var[]
      break
    }

    case 'conversation': {
      res.vars = data.chatVarList.map((chatVar: ConversationVariable) => {
        return {
          variable: `conversation.${chatVar.name}`,
          type: chatVar.value_type,
          des: chatVar.description
        }
      }) as Var[]
      break
    }
  }

  const { error_strategy } = data

  if (error_strategy) {
    res.vars = [
      ...res.vars,
      {
        variable: 'error_message',
        type: VarType.string,
        isException: true
      },
      {
        variable: 'error_type',
        type: VarType.string,
        isException: true
      }
    ]
  }

  const selector = [id]
  res.vars = res.vars
    .filter((v) => {
      const isCurrentMatched = filterVar(
        v,
        (() => {
          const variableArr = v.variable.split('.')
          const [first] = variableArr
          if (first === 'sys' || first === 'env' || first === 'conversation') return variableArr

          return [...selector, ...variableArr]
        })()
      )
      if (isCurrentMatched) return true

      const isFile = v.type === VarType.file
      const children = (() => {
        if (isFile) {
          return OUTPUT_FILE_SUB_VARIABLES.map((key) => {
            return {
              variable: key,
              type: key === 'size' ? VarType.number : VarType.string
            }
          })
        }
        return v.children
      })()
      if (!children) return false

      const obj = findExceptVarInObject(isFile ? { ...v, children } : v, filterVar, selector, isFile)
      return (
        obj?.children &&
        ((obj?.children as Var[]).length > 0 ||
          Object.keys((obj?.children as StructuredOutput)?.schema?.properties || {}).length > 0)
      )
    })
    .map((v) => {
      const isFile = v.type === VarType.file

      const { children } = (() => {
        if (isFile) {
          return {
            children: OUTPUT_FILE_SUB_VARIABLES.map((key) => {
              return {
                variable: key,
                type: key === 'size' ? VarType.number : VarType.string
              }
            })
          }
        }
        return v
      })()

      if (!children) return v

      return findExceptVarInObject(isFile ? { ...v, children } : v, filterVar, selector, isFile)
    })
  return res
}

export const getNewVarInWorkflow = (key: string, type = InputVarType.textInput) => {
  const { ...rest } = VAR_ITEM_TEMPLATE_IN_WORKFLOW
  if (type !== InputVarType.textInput) {
    return {
      ...rest,
      type,
      variable: key,
      label: key.slice(0, getMaxVarNameLength(key))
    }
  }
  return {
    ...VAR_ITEM_TEMPLATE_IN_WORKFLOW,
    type,
    variable: key,
    label: key.slice(0, getMaxVarNameLength(key))
  }
}

export const getNewVar = (key: string, type: string) => {
  const { ...rest } = VAR_ITEM_TEMPLATE
  if (type !== 'string') {
    return {
      ...rest,
      type: type || 'string',
      key,
      name: key.slice(0, getMaxVarNameLength(key))
    }
  }
  return {
    ...VAR_ITEM_TEMPLATE,
    type: type || 'string',
    key,
    name: key.slice(0, getMaxVarNameLength(key))
  }
}
