<template>
  <div class="input-var">
    <Field :fieldTitle="'输入变量'" :tooltip="''">
      <template #operation>
        <a-button type="text" @click="handleAdd">
          <icon-plus />
        </a-button>
      </template>

      <a-space direction="vertical" fill>
        <div v-for="(varItem, index) in nodeInfo.variables" :key="varItem.index" class="var-item">
          <a-row :gutter="10" class="var-item-key-value" align="center">
            <a-col :span="6">
              <a-input v-model="varItem.variable" />
            </a-col>
            <a-col :span="18">
              <!--v1:select组件。-->
              <!--v2:自定div-->
              <!--<SelectVar
                :nodeInfo="nodeInfo"
                :varItem="varItem"
                @change="(val) => handleSelectedVar(varItem,val)"
              />-->
              <VariableSelector
                v-model:value-selector="varItem.value_selector"
                :nodeId="nodeId"
                @change="handleSelectedVar"
              />
            </a-col>
          </a-row>
          <div class="delete-btn">
            <a-button type="text" @click="handleDelete(index)">
              <icon-delete :size="18" class="delete-icon" />
            </a-button>
          </div>
        </div>
      </a-space>
    </Field>
  </div>
</template>
<script setup lang="ts">
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'
import VariableSelector from '@/views/app/workflow/components/variable-selector/VariableSelector.vue'

const props = defineProps(['nodeInfo', 'nodeId'])
// 添加
const handleAdd = () => {
  props.nodeInfo.variables.push({
    variable: '',
    value_selector: []
  })
}
// 删除
const handleDelete = (index: number) => {
  props.nodeInfo.variables.splice(index, 1)
}
onMounted(() => {
  // TODO-todo: 没有获取到id。
  // const beforeList = getBeforeNodesInSameBranch('1747724606665')
  // console.log('beforeList:', beforeList)
})

// 变量更新
const handleSelectedVar = (varItem, val) => {
  // varItem['value_selector'] = val
}
</script>
<style scoped lang="scss">
.var-item {
  display: flex;
  align-items: center;

  .var-item-key-value {
    flex: 1;
  }

  .delete-btn {
    width: 32px;
    margin-left: 12px;

    .arco-button {
      padding: 0 6px;
    }
  }
}

.arco-select-option {
  //width: 300px;
  width: 100%;

  :deep(.arco-select-option-content) {
    flex: 1;
  }
}

.arco-select-dropdown .arco-select-option-content {
  width: 100%;
  flex: 1;
}

.arco-select-option-content {
  width: 100%;
}
</style>
