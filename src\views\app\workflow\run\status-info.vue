<template>
  <div>
    <div :status="props.status" className="relative">
      <div className="system-xs-medium-uppercase h-6 py-1 text-text-tertiary">元数据</div>
      <div className="py-1">
        <div className="flex">
          <div className="system-xs-regular w-[104px] shrink-0 truncate px-2 py-1.5 text-text-tertiary">状态</div>
          <div className="system-xs-regular grow px-2 py-1.5 text-text-secondary">
            <div v-if="status === 'running'" className="my-1 h-2 w-16 rounded-sm bg-text-quaternary" />
            <span v-if="status === 'succeeded'">SUCCESS</span>
            <span v-if="status === 'partial-succeeded'">PARTIAL SUCCESS</span>
            <span v-if="status === 'exception'">EXCEPTION</span>
            <span v-if="status === 'failed'">FAIL</span>
            <span v-if="status === 'stopped'">STOP</span>
          </div>
        </div>
        <div className="flex">
          <div className="system-xs-regular w-[104px] shrink-0 truncate px-2 py-1.5 text-text-tertiary">执行人</div>
          <div className="system-xs-regular grow px-2 py-1.5 text-text-secondary">
            <div v-if="status === 'running'" className="my-1 h-2 w-[88px] rounded-sm bg-text-quaternary" />
            <span v-else>{{ executor || 'N/A' }}</span>
          </div>
        </div>
        <div className="flex">
          <div className="system-xs-regular w-[104px] shrink-0 truncate px-2 py-1.5 text-text-tertiary">开始时间</div>
          <div className="system-xs-regular grow px-2 py-1.5 text-text-secondary">
            <div v-if="status === 'running'" className="my-1 h-2 w-[72px] rounded-sm bg-text-quaternary" />
            <span v-else>{{ startTime ? startTime : '-' }}</span>
          </div>
        </div>
        <div className="flex">
          <div className="system-xs-regular w-[104px] shrink-0 truncate px-2 py-1.5 text-text-tertiary">运行时间</div>
          <div className="system-xs-regular grow px-2 py-1.5 text-text-secondary">
            <div v-if="status === 'running'" className="my-1 h-2 w-[72px] rounded-sm bg-text-quaternary" />
            <span v-else>{{ time ? `${time.toFixed(3)}s` : '-' }}</span>
          </div>
        </div>
        <div className="flex">
          <div className="system-xs-regular w-[104px] shrink-0 truncate px-2 py-1.5 text-text-tertiary">
            总 token 数
          </div>
          <div className="system-xs-regular grow px-2 py-1.5 text-text-secondary">
            <div v-if="status === 'running'" className="my-1 h-2 w-[48px] rounded-sm bg-text-quaternary" />
            <span v-else>{{ `${tokens || 0} Tokens` }}</span>
          </div>
        </div>
        <div className="flex">
          <div className="system-xs-regular w-[104px] shrink-0 truncate px-2 py-1.5 text-text-tertiary">运行步数</div>
          <div className="system-xs-regular grow px-2 py-1.5 text-text-secondary">
            <div v-if="status === 'running'" className="my-1 h-2 w-[24px] rounded-sm bg-text-quaternary" />
            <span v-else>{{ steps }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    status: string
    executor?: string
    startTime?: number
    time?: number
    tokens?: number
    steps?: number
    showSteps?: boolean
  }>(),
  {}
)
</script>

<style scoped lang="scss"></style>
