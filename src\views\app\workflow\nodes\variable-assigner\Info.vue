<template>
  <div class="field-info">
    <div class="flex items-center justify-between mb-1">
      <span>输入字段</span>
      <a-button style="width: 24px; height: 24px" type="text" @click="onAdd">
        <template #icon>
          <icon-plus />
        </template>
      </a-button>
    </div>
    <Fieldlist :list="fieldList" />
    <AddFieldModal />
    <AddFieldModal ref="AddFieldModalRef" @save-success="onSave" />
  </div>
</template>

<script setup lang="ts">
import Fieldlist from './components/FieldList.vue'
import AddFieldModal from './components/AddFieldModal.vue'

interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
}

const AddFieldModalRef = ref<InstanceType<typeof AddFieldModal>>()
// 新增
const onAdd = () => {
  AddFieldModalRef.value?.onAdd()
}
const props = defineProps({
  popupContainer: String
})
const fieldList = ref<FieldType[]>([])

const onSave = (e) => {
  const field = {
    label: e.label,
    max_length: e.max_length,
    options: e.options,
    required: e.required && e.required.length > 0 ? e.required[0] : false,
    type: e.type,
    variable: e.variable
  }
  fieldList.value.push(field)
}
</script>
<style scoped lang="scss">
.field-info {
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-1);
}
</style>
