<template>
  <div>
    <!--TODO-todo: 需要使用编辑器实现功能。。。并可以插入变量块。可以trigger触发。-->
    <div class="flex justify-between items-center mb-4">
      <div />
      <div>
        <a-button size="small" class="arco-btn-only-icon" @click="handleAdd">
          <icon-plus />
        </a-button>
      </div>
    </div>
    <a-table :columns="columns" :data="data" :bordered="false" :pagination="false">
      <template #key="{ record }">
        <!--<a-input v-model="record.key" @change="handleChangeInput"></a-input>-->
        <RichText
          :nodeInfo="nodeInfo"
          :varString="record.key"
          @handleInputVar="(val) => handleInputVarKey(val, record)"
        />
      </template>
      <template #value="{ record, rowIndex }">
        <!--record,column,rowIndex-->
        <div class="value-container">
          <div class="flex justify-between items-center">
            <!--v1:select-->
            <!--v2:input或者textarea-->
            <!--<a-input v-model="record.value" :ref="`inputRef${rowIndex}`" @change="handleChangeInput"
                     @focus="handleFocus(record, rowIndex, $event)">

            </a-input>-->
            <RichText
              :nodeInfo="nodeInfo"
              :varString="record.value"
              class="flex-1"
              @handleInputVar="(val) => handleInputVar(val, record)"
            />
            <template v-if="data.length > 1">
              <icon-delete class="delete-icon" @click="deleteItem(rowIndex)" />
              <!--<icon-plus-circle @click="insertItem(rowIndex)" class="insert-icon" />-->
            </template>
          </div>
        </div>
      </template>
    </a-table>
  </div>
</template>
<script setup lang="ts">
import { uniqueId } from 'lodash-es'
import RichText from '@/views/app/workflow/nodes/http/components/rich-text/index.vue'
const props = defineProps(['defaultList', 'data', 'nodeInfo', 'type'])
// const emits = defineEmits(['addItem'])
const columns = [
  {
    title: '键',
    dataIndex: 'key',
    slotName: 'key',
    width: 100
  },
  {
    title: '值',
    dataIndex: 'value',
    slotName: 'value',
    width: 100
  }
]

const UNIQUE_ID_PREFIX = 'key-value-'
const handleFocus = async (record: any, rowIndex: number, event) => {
  if (rowIndex === props.data.length - 1) {
    props.data.push({
      id: uniqueId(UNIQUE_ID_PREFIX),
      key: '',
      value: ''
    })
  }
}

const handleAdd = async () => {
  props.data.push({
    id: uniqueId(UNIQUE_ID_PREFIX),
    key: '',
    value: ''
  })
}
const deleteItem = (rowIndex: number) => {
  props.data.splice(rowIndex, 1)
}
const handleInputVarKey = (val, item) => {
  item.key = val
  handleChangeInput()
}

const handleInputVar = (val, item) => {
  item.value = val
  handleChangeInput()
}
const insertItem = (rowIndex: number) => {}
const handleChangeVar = (item, val) => {
  item.value = val.variable
}

const handleChangeInput = () => {
  // headers 和 params 需要处理成string后在提交。
  if (props.type === 'headers' || props.type === 'params') {
    let arr: any = []
    props.data.forEach((item) => {
      if (item.key != '' && item.value != '') {
        let str = `${item.key}:${item.value}`
        arr.push(str)
      }
    })
    props.nodeInfo[props.type] = arr.join('\n')
  }
}
watch(
  () => props.data,
  () => {},
  { deep: true, immediate: true }
)
</script>

<style scoped lang="scss">
.value-container {
  .delete-icon {
    visibility: hidden;
    margin-left: 12px;
  }

  &:hover {
    .delete-icon {
      visibility: visible;
    }
  }
}
</style>
