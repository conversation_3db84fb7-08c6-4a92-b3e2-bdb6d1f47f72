export enum APP_ASCRIPTION {
  我的 = 'MY_SELF',
  分享 = 'SHARE'
}

export enum APP_MORE_OPERATION {
  编排 = 'layout',
  停用 = 'stop',
  启用 = 'start',
  编辑信息 = 'edit',
  总览 = 'overview',
  对外接口 = 'access',
  会话日志 = 'logs',
  标注 = 'tagging',
  分享 = 'share',
  复制 = 'copy',
  导出 = 'export',
  删除 = 'remove'
}

export enum ShakedownTestModel {
  单模型调试 = 'single',
  多模型调试 = 'more',
  参数设置 = 'params-edit'
}

export enum UploadFileAllowedFileTypes {
  文档格式 = 'document',
  图片格式 = 'image'
}

export enum FeaturesType {
  对话开场白 = 'prologue',
  下一步问题建议 = 'afterAnswer',
  文件上传 = 'file_upload',
  引用和归属 = 'retRes',
  内容审查 = 'contentAdvisor',
  标注回复 = 'annotationReply'
}

export enum FieldType {
  文本 = 'text-input',
  段落 = 'paragraph',
  下拉选项 = 'select',
  数字 = 'number',
  文件 = 'file-list'
}
export enum APP_ORCHESTRATION {
  对话流 = 'advanced-chat',
  工作流 = 'workflow',
  智能会话 = 'agent-chat',
  未知 = 'completion'
}
export interface AppSite {
  access_token: string
  app_base_url: string
  code: string
  copyright: any
  custom_disclaimer: any
  customize_domain: any
  customize_token_strategy: string
  default_language: string
  description: string
  icon: string
  icon_background: string
  privacy_policy: any
  prompt_public: boolean
  title: string
}
