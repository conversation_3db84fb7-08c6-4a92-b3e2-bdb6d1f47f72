<template>
  <div>
    <a-popover ref="popovers" v-model:popup-visible="popoverInstance" position="right" trigger="click">
      <a-popover :content-style="{ padding: '8px', borderRadius: '8px' }">
        <div class="node-plus" @click.stop="PopStateEvent">
          <slot />
        </div>
        <template #content>
          <div class="node-plus-content">
            <p>
              <span>点击</span>
              添加节点
            </p>
            <p v-if="parentType != 'edge'">
              <span>拖动</span>
              连接节点
            </p>
          </div>
        </template>
      </a-popover>
      <template #content>
        <div class="node-list" :style="{ width: divWidth }">
          <a-tabs v-model="tabvalue" @tab-click="tabClick">
            <a-tab-pane key="1" title="节点">
              <a-tooltip v-for="item in nodeList" :key="item.type" position="right" :content="item.title">
                <div class="node-list-item" @click="() => selectTool(item)">
                  <div class="node-list-item-icon" :style="{ backgroundColor: nodeColor[item.type] }">
                    <AiSvgIcon style="width: 14px; height: 14px" :name="`workflow-${item.type}`" />
                    <!-- <icon :icon="`icon: ${item.icon}`" /> -->
                  </div>
                  <div class="node-list-item-text">
                    {{ item.title }}
                  </div>
                </div>
              </a-tooltip>
            </a-tab-pane>
            <a-tab-pane key="2" title="工具">
              <a-space direction="vertical" size="large">
                <a-radio-group v-model="toolType" type="button" size="mini">
                  <a-radio value="1">全部</a-radio>
                  <!-- <a-radio value="Shanghai">Shanghai</a-radio>
                      <a-radio value="Guangzhou">Guangzhou</a-radio> -->
                  <a-radio value="4">工作流</a-radio>
                </a-radio-group>
              </a-space>
              <a-collapse :default-active-key="['1']" expand-icon-position="left" :bordered="false">
                <a-collapse-item v-for="item in toolworkflowList" :key="item.id" :header="item.name">
                  <div
                    v-for="(item1, index1) in item.tools"
                    :key="index1"
                    class="node-list-item"
                    @click="() => selectToolWork(item, item1)"
                  >
                    {{ item1.label.zh_Hans }}
                  </div>
                </a-collapse-item>
              </a-collapse>
              <!-- <div v-for="item in toolworkflowList" class="node-list-item" :key="item.id">
                      {{ item.name }}
                    </div> -->
            </a-tab-pane>
          </a-tabs>
        </div>
      </template>
    </a-popover>
  </div>
</template>

<script setup lang="ts">
import { useVueFlow } from '@vue-flow/core'
import nodeUtils from '../utils/node-utils'
import { nanoid } from 'nanoid'
import { inject, nextTick, ref, watch, computed } from 'vue'
import { nodeColor } from '@/views/app/workflow/types/workflow'
import useViewportHook from '../hooks/useViewportHook'
import { useRoute } from 'vue-router'
import { NodeType } from '../types/node'
import { toolsworkflow } from '@/apis'
import { useAppStore } from '@/stores'
import nodeDefault from '@/views/app/workflow/nodes/llm/default'
import nodeDefaultHttp from '@/views/app/workflow/nodes/http/default'
import nodeDefaultCode from '@/views/app/workflow/nodes/code/default'
import nodeDefaultInteration from '@/views/app/workflow/nodes/iteration/default'
import nodeDefaultParameter from '@/views/app/workflow/nodes/parameter-extractor/default'
import { getModelDefaultHttp } from '@/apis/model-mgmt'
import { BlockEnum } from '@/views/app/workflow/nodes/http/types'
import { useNodesStore } from '@/stores/modules/workflow/nodes'

const { addNodeToflow } = nodeUtils()

interface Props {
  nodeId: string
  nodeProps: Record<string, any>
  sourceHandle?: string
  isIterationNode?: boolean
  iterationNodeId?: string
  parentType?: string
}

const props = withDefaults(defineProps<Props>(), {
  nodeId: '',
  nodeProps: () => ({}),
  sourceHandle: void 0,
  isIterationNode: false,
  iterationNodeId: void 0,
  parentType: void 0
})

const toolType = ref('1')
const tabvalue = ref('1')
const toolworkflowList = ref([])
const divWidth = ref('150px')

// 从store获取app信息
const appStore = useAppStore()
const appInfo = computed(() => appStore.currentAppInfo)

const allNodeList = [
  {
    title: 'LLM',
    type: 'llm',
    desc: '',
    ...nodeDefault.defaultValue
  },
  {
    title: '知识检索',
    type: 'knowledge-retrieval',
    desc: '',
    query_variable_selector: [],
    dataset_ids: [],
    retrieval_mode: 'single'
  },
  {
    title: '结束',
    type: 'end',
    desc: '',
    outputs: []
  },
  {
    title: '直接回复',
    type: 'answer',
    desc: '',
    answer: '',
    variables: []
  },
  {
    title: '迭代',
    type: 'iteration',
    desc: '',
    ...nodeDefaultInteration.defaultValue
  },
  {
    title: '条件分支',
    type: 'if-else',
    cases: [
      {
        id: 'true',
        case_id: 'true',
        logical_operator: 'and',
        conditions: []
      }
    ]
  },
  {
    title: '代码执行',
    type: 'code',
    desc: '',
    ...nodeDefaultCode.defaultValue
  },
  {
    title: '文档提取',
    type: 'document-extractor',
    desc: ''
  },
  {
    title: '变量赋值',
    type: 'assigner',
    desc: ''
  },
  {
    title: '变量聚合',
    type: 'variable-aggregator',
    desc: '',
    variables: [],
    output_type: 'any'
  },
  {
    title: '参数提取',
    type: 'parameter-extractor',
    desc: '',
    ...nodeDefaultParameter.defaultValue
  },
  {
    title: 'http请求',
    type: 'http-request',
    desc: '',
    ...nodeDefaultHttp.defaultValue
  }
]

// 根据app模式过滤节点列表
const nodeList = computed(() => {
  const mode = appInfo.value?.mode

  return allNodeList.filter((node) => {
    // 根据mode字段判断节点显示
    if (mode === 'advanced-chat') {
      // advanced-chat模式：不显示结束节点，显示answer节点
      return node.type !== 'end'
    } else if (mode === 'workflow') {
      // workflow模式：显示结束节点，不显示answer节点
      return node.type !== 'answer'
    } else {
      return true
    }
  })
})

const { addNodes, getNodes, getEdges } = useVueFlow()
const nodesStore = useNodesStore()
const popoverInstance = ref(false)
const state = inject('popoverInstance')
watch(ref(state), (val) => {
  if (val) return
  popoverInstance.value = false
})
const PopStateEvent = () => {
  popoverInstance.value = !popoverInstance.value
  AddtoolWorkFlow()
}

const AddtoolWorkFlow = () => {
  toolsworkflow()
    .then((res) => {
      // console.log(res);
      toolworkflowList.value = [...res]
    })
    .catch((error) => {
      console.error('Failed to fetch tool workflow data:', error)
      // 可选：通知用户或触发重试机制
    })
}
const tabClick = (value) => {
  if (value === '2') {
    divWidth.value = '300px'
  } else {
    divWidth.value = '150px'
  }
}
const handleClose = () => {
  popoverInstance.value = false
}
const route = useRoute()
const appId = route.params.appId as string
const { insertNodeClick } = useViewportHook(undefined, appId)

const selectTool = async (toolItem) => {
  if (toolItem.type === BlockEnum.LLM || toolItem.type === BlockEnum.ParameterExtractor) {
    // LLM的场景下：需要查询默认的llm，然后赋值。
    const { data: res } = await getModelDefaultHttp('llm')
    toolItem.model.provider = res.provider.provider
    toolItem.model.name = res.model
  }

  const { newNodeProps } = await addNodeToflow(props.nodeProps, toolItem, props.sourceHandle)
  console.log(newNodeProps)

  // 如果是迭代类型，动态添加node
  nextTick(() => {
    if (newNodeProps.type === 'iteration') {
      addNodes({
        id: `node_${nanoid()}`,
        type: 'iteration-start',
        position: { x: 40, y: 80 },
        data: { title: '', desc: '', type: 'iteration-start', isInIteration: true },
        parentNode: newNodeProps.id,
        extent: 'parent',
        expandParent: true
      })
    }
  })
  nodesStore.setNodes(getNodes.value)
  nodesStore.setEdges(getEdges.value)
  handleClose()
}
const selectToolWork = async (item, item1) => {
  let toolvalue = {
    desc: '',
    is_team_authorization: true,
    paramSchemas: item1.parameters,
    params: {},
    provider_id: item.id,
    provider_name: item.name,
    provider_type: item.type,
    retry_config: {
      max_retries: 3,
      retry_enabled: false,
      retry_interval: 1000
    },
    selected: true,
    title: item1.label.zh_Hans,
    tool_configurations: {},
    tool_description: item1.description.zh_Hans,
    tool_label: item1.label.zh_Hans,
    tool_name: item1.name,
    tool_parameters: {},
    type: 'tool'
  }

  toolvalue.params = item1.parameters.reduce((acc, obj) => {
    acc[obj.name] = '' // 将 name 作为键，对象作为值
    return acc
  }, {})

  const { newNodeProps } = await addNodeToflow(props.nodeProps, toolvalue, props.sourceHandle)
  handleClose()
}

defineExpose({ handleClose })
</script>
<style scoped lang="scss">
.node-plus {
  &:hover {
    transform: scale(1.2, 1.2);
  }
}

.node-plus-content {
  p {
    font-size: 12px;

    span {
      font-weight: bold;
      color: var(--color-text-1);
    }
  }
}

.node-list {
  display: flex;
  flex-direction: column;
  // width: 150px;
  height: auto;
  overflow-y: auto;

  .node-list-item {
    display: flex;
    align-items: center;
    padding: 4px 8px;
    margin-bottom: 4px;
    cursor: pointer;

    &:hover {
      border-radius: 4px;
      background-color: var(--color-fill-2);
    }

    &-icon {
      margin-right: 8px;
      height: 19px;
      width: 19px;
      border-radius: 6px;
      background-color: var(--color-fill-3);
      text-align: center;
      line-height: 19px;
      color: var(--color-text-1);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    &-text {
      font-size: 14px;
      color: var(--color-text-2);
    }
  }
}
</style>
