import http from '@/utils/http'

// 获取评估器列表
export const getEvaluatorList = (params) => {
  return http.get<{ list: []; total: number }>('/mock/evaluator/list', { params })
}

// 获取评估器详情
export const getEvaluatorDetail = (id: string) => {
  return http.get<any>(`/mock/evaluator/${id}`)
}

// 创建评估器
export const createEvaluator = (data: any) => {
  return http.post('/mock/evaluator/create', data)
}

// 更新评估器
export const updateEvaluator = (id: string, data: any) => {
  return http.put(`/mock/evaluator/${id}`, data)
}

// 删除评估器
export const deleteEvaluator = (id: string) => {
  return http.del(`/mock/evaluator/${id}`)
}

// 复制评估器
export const copyEvaluator = (id: string) => {
  return http.post(`/mock/evaluator/${id}/copy`)
}
