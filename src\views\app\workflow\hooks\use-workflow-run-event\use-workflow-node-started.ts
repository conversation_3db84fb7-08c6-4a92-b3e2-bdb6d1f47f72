import { useVueFlow } from '@vue-flow/core'
import { useWorkflowStore } from '@/stores'
import { NodeRunningStatus, type NodeStartedResponse } from '@/views/app/workflow/types/workflow'

export const useWorkflowNodeStarted = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowNodeStarted = (params: NodeStartedResponse) => {
    const { data } = params
    const { workflowRunningData, setWorkflowRunningData } = workflowStore
    const { nodes, setNodes, edges, setEdges } = useVueFlow()

    const workflowData = workflowRunningData
    workflowData?.tracing.push({
      ...data,
      status: NodeRunningStatus.Running
    })

    setWorkflowRunningData(workflowData)

    if (nodes.value?.length) {
      const newNodes = nodes.value.map((node) => {
        if (node.id === data.node_id) {
          node.data._runningStatus = NodeRunningStatus.Running
          node.data._waitingRun = false
        }
        return node
      })

      setNodes(newNodes)
    }

    if (edges.value?.length) {
      const newEdges = edges.value.map((edge) => {
        if (edge.target === data.node_id) {
          const incomeNode = nodes.value?.find((node) => node.id === edge.source)!
          if (
            (!incomeNode.data._runningBranchId && edge.sourceHandle === 'source') ||
            (incomeNode.data._runningBranchId && edge.sourceHandle === incomeNode.data._runningBranchId)
          ) {
            edge.data = {
              ...edge.data,
              _sourceRunningStatus: incomeNode.data._runningStatus,
              _targetRunningStatus: NodeRunningStatus.Running,
              _waitingRun: false
            }
          }
        }
        return edge
      })
      setEdges(newEdges)
    }
  }

  return {
    handleWorkflowNodeStarted
  }
}
