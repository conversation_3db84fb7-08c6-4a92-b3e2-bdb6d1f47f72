import type { TableInstance } from '@arco-design/web-vue'

export interface TableProps {
  columns?: TableInstance['$props']['columns']
  data?: TableInstance['$props']['data']
  bordered?: TableInstance['$props']['bordered']
  hoverable?: TableInstance['$props']['hoverable']
  stripe?: TableInstance['$props']['stripe']
  size?: TableInstance['$props']['size']
  tableLayoutFixed?: TableInstance['$props']['tableLayoutFixed']
  loading?: TableInstance['$props']['loading']
  rowSelection?: TableInstance['$props']['rowSelection']
  expandable?: TableInstance['$props']['expandable']
  scroll?: TableInstance['$props']['scroll']
  pagination?: TableInstance['$props']['pagination']
  pagePosition?: TableInstance['$props']['pagePosition']
  indentSize?: TableInstance['$props']['indentSize']
  rowKey?: TableInstance['$props']['rowKey']
  showHeader?: TableInstance['$props']['showHeader']
  virtualListProps?: TableInstance['$props']['virtualListProps']
  spanMethod?: TableInstance['$props']['spanMethod']
  spanAll?: TableInstance['$props']['spanAll']
  loadMore?: TableInstance['$props']['loadMore']
  filterIconAlignLeft?: TableInstance['$props']['filterIconAlignLeft']
  hideExpandButtonOnEmpty?: TableInstance['$props']['hideExpandButtonOnEmpty']
  rowClass?: TableInstance['$props']['rowClass']
  draggable?: TableInstance['$props']['draggable']
  rowNumber?: TableInstance['$props']['rowNumber']
  columnResizable?: TableInstance['$props']['columnResizable']
  summary?: TableInstance['$props']['summary']
  summaryText?: TableInstance['$props']['summaryText']
  summarySpanMethod?: TableInstance['$props']['summarySpanMethod']
  selectedKeys?: TableInstance['$props']['selectedKeys']
  defaultSelectedKeys?: TableInstance['$props']['defaultSelectedKeys']
  expandedKeys?: TableInstance['$props']['expandedKeys']
  defaultExpandedKeys?: TableInstance['$props']['defaultExpandedKeys']
  defaultExpandAllRows?: TableInstance['$props']['defaultExpandAllRows']
  stickyHeader?: TableInstance['$props']['stickyHeader']
  scrollbar?: TableInstance['$props']['scrollbar']
  showEmptyTree?: TableInstance['$props']['showEmptyTree']
}
