import { defineStore } from 'pinia'
import { computed, reactive, ref } from 'vue'
import { resetRouter } from '@/router'
import {
  type AccountLoginReq,
  type UserInfo,
  getUserInfo as getUserInfoApi,
  accountLogin as accountLoginApi
} from '@/apis'
import { clearToken, getToken, setToken } from '@/utils/auth'
import { resetHasRouteFlag } from '@/router/guard'

const storeSetup = () => {
  const userInfo = reactive<UserInfo>({
    id: '',
    name: '',
    avatar: '',
    avatar_url: null,
    email: '',
    is_password_set: false,
    interface_language: 'zh-Hans',
    interface_theme: '',
    timezone: '',
    last_login_at: '',
    last_login_ip: '',
    created_at: ''
  })
  const nickname = computed(() => userInfo.name)
  const username = computed(() => userInfo.email)
  const avatar = computed(() => userInfo.avatar)

  const token = ref(getToken() || '')
  const pwdExpiredShow = ref<boolean>(true)
  const roles = ref<string[]>([]) // 当前用户角色
  const permissions = ref<string[]>([]) // 当前角色权限标识集合

  // 重置token
  const resetToken = () => {
    token.value = ''
    clearToken()
    resetHasRouteFlag()
  }

  // 登录
  const accountLogin = async (req: AccountLoginReq) => {
    const res = (await accountLoginApi({ ...req })) as any
    setToken(res.data.access_token)
    token.value = res.data.access_token
  }
  // 退出登录回调
  const logoutCallBack = async () => {
    roles.value = []
    permissions.value = []
    pwdExpiredShow.value = true
    resetToken()
    resetRouter()
  }

  // 退出登录
  const logout = async () => {
    try {
      await logoutCallBack()
      return true
    } catch {
      return false
    }
  }

  // 获取用户信息
  const getInfo = async () => {
    const res = await getUserInfoApi()
    Object.assign(userInfo, res)
    // if (res.data.roles && res.data.roles.length) {
    //   roles.value = res.data.roles
    //   permissions.value = res.data.permissions
    // }
  }

  return {
    userInfo,
    nickname,
    username,
    avatar,
    token,
    roles,
    permissions,
    pwdExpiredShow,
    accountLogin,
    logout,
    logoutCallBack,
    getInfo,
    resetToken
  }
}

export const useUserStore = defineStore('user', storeSetup, {
  persist: { paths: ['token', 'roles', 'permissions', 'pwdExpiredShow'], storage: localStorage }
})
