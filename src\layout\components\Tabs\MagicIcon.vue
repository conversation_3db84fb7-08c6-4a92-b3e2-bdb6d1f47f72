<template>
  <span class="ai-more-icon-wrap">
    <span class="ai-more-icon">
      <i class="block block-top" />
      <i class="block block-bottom" />
    </span>
  </span>
</template>

<script setup lang="ts"></script>

<style scoped lang="scss">
.ai-more-icon-wrap {
  position: relative;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;

  &::before {
    content: '';
    width: 26px;
    height: 26px;
    position: absolute;
    left: -6px;
    top: -6px;
    cursor: pointer;
  }

  .ai-more-icon {
    display: inline-block;
    color: var(--color-text-2);
    cursor: pointer;
    transition: transform 0.3s ease-out;

    .block {
      position: relative;
      display: block;
      width: 14px;
      height: 6px;

      // &.block-top:before {
      //   transition: transform 0.3s ease-out 0.3s;
      // }
      &.block-bottom {
        margin-top: 2px;
      }
    }

    .block:before {
      position: absolute;
      left: 0;
      width: 6px;
      height: 6px;
      content: '';
      background: var(--color-text-3);
    }

    .block:after {
      position: absolute;
      left: 8px;
      width: 6px;
      height: 6px;
      content: '';
      background: var(--color-text-3);
    }
  }
}

.ai-more-icon-wrap:hover .ai-more-icon .block:first-child::before,
.arco-dropdown-open .ai-more-icon .block:first-child::before {
  transform: rotate(45deg);
  background: rgb(var(--primary-3));
}

.ai-more-icon-wrap:hover .ai-more-icon .block:before,
.arco-dropdown-open .ai-more-icon .block:before {
  background: rgb(var(--primary-6));
}

.ai-more-icon-wrap:hover .ai-more-icon .block:after,
.arco-dropdown-open .ai-more-icon .block:after {
  background: rgb(var(--primary-6));
}

.ai-more-icon-wrap:hover .ai-more-icon,
.arco-dropdown-open .ai-more-icon {
  transform: rotate(90deg);
}
</style>
