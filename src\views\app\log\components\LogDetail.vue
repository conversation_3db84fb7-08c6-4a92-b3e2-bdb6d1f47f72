<template>
  <div class="log-detail">
    <a-descriptions :column="2" bordered>
      <a-descriptions-item label="对话ID">
        {{ log.conversation_id }}
      </a-descriptions-item>
      <a-descriptions-item label="消息ID">
        {{ log.message_id }}
      </a-descriptions-item>
      <a-descriptions-item label="用户">
        {{ log.from_end_user_session_id || log.from_account_name || log.user_id || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="状态">
        <a-tag :color="getStatusColor(log.status)">
          {{ getStatusText(log.status) }}
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="耗时">
        {{ log.elapsed_time ? `${log.elapsed_time}ms` : '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="Token数">
        {{ log.total_tokens || '-' }}
      </a-descriptions-item>
      <a-descriptions-item label="创建时间" :span="2">
        {{ log.created_at ? dateFormat(new Date(log.created_at), 'yyyy-MM-dd HH:mm:ss') : '-' }}
      </a-descriptions-item>
    </a-descriptions>

    <div class="detail-section">
      <h4>输入内容</h4>
      <a-card class="content-card">
        <pre class="content-text">{{ formatContent(log.input) }}</pre>
      </a-card>
    </div>

    <div class="detail-section">
      <h4>输出内容</h4>
      <a-card class="content-card">
        <pre class="content-text">{{ formatContent(log.output) }}</pre>
      </a-card>
    </div>

    <div v-if="log.error" class="detail-section">
      <h4>错误信息</h4>
      <a-card class="content-card error-card">
        <pre class="content-text">{{ log.error }}</pre>
      </a-card>
    </div>

    <div v-if="log.feedback" class="detail-section">
      <h4>用户反馈</h4>
      <a-card class="content-card">
        <div class="feedback-content">
          <a-tag :color="log.feedback.rating === 'like' ? 'green' : 'red'">
            {{ log.feedback.rating === 'like' ? '👍 满意' : '👎 不满意' }}
          </a-tag>
          <div v-if="log.feedback.content" class="feedback-text">
            {{ log.feedback.content }}
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Tag } from '@arco-design/web-vue'
import type { LogEntry, LogStatus } from '@/apis/apps'
import { dateFormat } from '@/utils'

defineOptions({ name: 'LogDetail' })

const props = defineProps<{
  log: LogEntry
  appMode?: string
}>()

const emit = defineEmits<{
  close: []
}>()

// 状态颜色映射
const getStatusColor = (status: LogStatus): string => {
  const colorMap = {
    success: 'green',
    error: 'red',
    stopped: 'orange',
    running: 'blue'
  }
  return colorMap[status] || 'gray'
}

// 状态文本映射
const getStatusText = (status: LogStatus): string => {
  const textMap = {
    success: '成功',
    error: '错误',
    stopped: '停止',
    running: '运行中'
  }
  return textMap[status] || status
}

// 格式化内容
const formatContent = (content: any): string => {
  if (!content) return '-'

  if (typeof content === 'string') {
    return content
  }

  try {
    return JSON.stringify(content, null, 2)
  } catch (error) {
    return String(content)
  }
}
</script>

<style scoped lang="scss">
.log-detail {
  .detail-section {
    margin-top: 20px;

    h4 {
      margin-bottom: 8px;
      color: var(--color-text-1);
      font-weight: 600;
    }

    .content-card {
      .content-text {
        margin: 0;
        padding: 0;
        white-space: pre-wrap;
        word-break: break-word;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.5;
        color: var(--color-text-2);
        max-height: 300px;
        overflow-y: auto;
      }

      &.error-card {
        border-color: var(--color-danger-light-4);
        background-color: var(--color-danger-light-1);

        .content-text {
          color: var(--color-danger-6);
        }
      }
    }

    .feedback-content {
      .feedback-text {
        margin-top: 8px;
        color: var(--color-text-2);
        line-height: 1.5;
      }
    }
  }
}

:deep(.arco-descriptions-item-label) {
  font-weight: 600;
  color: var(--color-text-1);
}

:deep(.arco-descriptions-item-value) {
  color: var(--color-text-2);
}
</style>
