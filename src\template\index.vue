<template>
  <AiPageLayout>
    <AiTable
      row-key="id"
      :data="dataList || []"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%' }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #toolbar-left>
        <a-input-search v-model="queryForm.name" placeholder="搜索名称/描述" allow-clear @search="search" />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>
      <template #toolbar-right>
        <a-button type="primary" @click="onAdd">
          <template #icon><icon-plus /></template>
          <template #default>新增</template>
        </a-button>
        <a-button @click="onExport">
          <template #icon><icon-download /></template>
          <template #default>导出</template>
        </a-button>
      </template>
      <template #type="{ record }">
        <span>{{ record?.type === 1 ? '对话类' : '工作流类' }}</span>
      </template>
      <template #status="{ record }">
        <a-tag v-if="record.status === 1" color="orange">草稿</a-tag>
        <a-tag v-else color="green">已发布</a-tag>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link title="详情" @click="onDetail(record)">详情</a-link>
          <a-link v-if="record.status === 1" title="修改" @click="onUpdate(record)">修改</a-link>
          <a-link v-if="record.status === 1" title="发布">发布</a-link>
          <a-link v-if="record.status === 1" status="danger" title="删除" @click="onDelete(record)">删除</a-link>
        </a-space>
      </template>
    </AiTable>

    <AppAddModal
      v-if="modalVisible"
      :modalVisible="modalVisible"
      :appId="appId"
      @hide-modal="close"
      @save-success="search"
    />
    <AppDetailDrawer v-if="detailVisible" :detailVisible="detailVisible" :appId="appId" @hide-drawer="close" />
  </AiPageLayout>
</template>

<script setup lang="ts">
import type { TableInstance } from '@arco-design/web-vue'
import AppAddModal from './AppAddModal.vue'
import AppDetailDrawer from './AppDetailDrawer.vue'
import { deleteUsingDelete, pageUsingPost } from '@/apis/template'
import { useTable } from '@/hooks'
import { EvalDataset, RIPageEvalDataset } from '@/apis/template/type'

defineOptions({ name: 'TemplateApp' })

const modalVisible = ref(false)
const appId = ref<string | null>()
const detailVisible = ref(false)
const queryForm = reactive<{
  name?: string
}>({})

const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete
} = useTable<RIPageEvalDataset>((page) => pageUsingPost({ ...page, model: queryForm }), { immediate: true })

const columns: TableInstance['columns'] = [
  { title: '名称', dataIndex: 'name', ellipsis: true, tooltip: true, minWidth: 100 },
  { title: '类型', dataIndex: 'type', slotName: 'type', width: 100 },
  { title: '版本', dataIndex: 'version', width: 100 },
  { title: '状态', dataIndex: 'status', slotName: 'status', width: 100 },
  { title: '描述', dataIndex: 'description', ellipsis: true, tooltip: true, minWidth: 100 },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 200,
    align: 'left'
  }
]

// 重置
const reset = () => {
  queryForm.name = ''
  search()
}

// 删除
const onDelete = (record) => {
  return handleDelete(() => deleteUsingDelete([record.id]), {
    content: `是否确定删除应用「${record.name}」？`,
    showModal: true
  })
}

// 导出
const onExport = () => {
  console.log('导出功能')
}

// 新增
const onAdd = () => {
  modalVisible.value = true
}

// 修改
const onUpdate = (record) => {
  appId.value = record.id
  modalVisible.value = true
}

// 详情
const onDetail = (record) => {
  detailVisible.value = true
  appId.value = record.id
}

const close = () => {
  appId.value = null
  modalVisible.value = false
  detailVisible.value = false
}
</script>

<style scoped lang="scss"></style>
