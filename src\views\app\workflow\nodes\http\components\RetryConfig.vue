<template>
  <div class="retry-config">
    <div class="retry-item flex justify-between items-center">
      <div class="retry-label">失败时重试</div>
      <div class="retry-value">
        <a-switch
          :default-checked="nodeInfo?.retry_config?.retry_enabled"
          size="medium"
          type="round"
          @change="handleChangeEnable"
        />
      </div>
    </div>

    <!--启用的时候，才会显示下面的配置-->
    <template v-if="nodeInfo?.retry_config?.retry_enabled">
      <div class="retry-item flex justify-between items-center">
        <div class="retry-label">最大重试次数</div>
        <div class="retry-value">
          <a-space>
            <a-slider v-model="nodeInfo.retry_config.max_retries" :style="{ width: '100px' }" :min="1" :max="10" />
            <a-input-number
              v-model="nodeInfo.retry_config.max_retries"
              placeholder="请输入"
              hide-button
              style="width: 100px"
            >
              <template #suffix>次</template>
            </a-input-number>
          </a-space>
        </div>
      </div>

      <div class="retry-item flex justify-between items-center">
        <div class="retry-label">重试间隔</div>
        <div class="retry-value">
          <a-space>
            <a-slider
              v-model="nodeInfo.retry_config.retry_interval"
              :style="{ width: '100px' }"
              :min="100"
              :max="5000"
            />
            <a-input-number
              v-model="nodeInfo.retry_config.retry_interval"
              placeholder="请输入"
              hide-button
              style="width: 100px"
            >
              <template #suffix>毫秒</template>
            </a-input-number>
          </a-space>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
const props = defineProps(['nodeInfo'])
// if (!props.nodeInfo.retry_config)
//   props.nodeInfo.retry_config = {
//     retry_enabled: false,
//     max_retries: 3,
//     retry_interval: 1000
//   }

/**
 * switch默认是没有选中的。
 * 甚至没有retry_config。需要切换的时候，才会有。
 * 节点的默认值，也没有retry_config这个字段。
 * @param val
 */
const handleChangeEnable = (val) => {
  if (!props.nodeInfo.retry_config) {
    props.nodeInfo.retry_config = {
      retry_enabled: val,
      max_retries: 3,
      retry_interval: 1000
    }
  } else {
    props.nodeInfo.retry_config.retry_enabled = val
  }
}
</script>

<style scoped lang="scss">
.retry-item {
  margin: 12px 0;

  .retry-label {
    font-weight: 600;
  }
}
</style>
