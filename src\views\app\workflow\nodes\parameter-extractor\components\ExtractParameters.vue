<template>
  <div class="extract-parameters">
    <div class="header">
      <div class="title">提取参数</div>
      <div class="right-operate">
        <a-popover position="bottom" trigger="click">
          <a-button size="mini" class="mt-4" @click="addtoolbox">从工具箱导入</a-button>
          <template #content>
            <a-space direction="vertical" size="large">
              <a-radio-group v-model="selectType" type="button" size="mini">
                <a-radio value="1">全部</a-radio>
                <a-radio value="4">工作流</a-radio>
              </a-radio-group>
            </a-space>
            <a-collapse :default-active-key="['1']" expand-icon-position="left" :bordered="false">
              <a-collapse-item v-for="item in toolworkflowList" :key="item.id" :header="item.name">
                <div
                  v-for="(item1, index1) in item.tools"
                  :key="index1"
                  class="node-list-item"
                  @click="selectToolWork(item1)"
                >
                  {{ item1.label.zh_Hans }}
                </div>
              </a-collapse-item>
            </a-collapse>
          </template>
        </a-popover>

        <span style="margin: 0 5px">|</span>
        <icon-plus style="cursor: pointer" @click="addtoolitem" />
      </div>
    </div>
    <div v-for="(item, index) in props.nodeInfo.parameters" :key="index" class="item-tool">
      <div>
        <div class="flex h-[18px] items-center space-x-2">
          <span class="code-sm-semibold text-text-secondary">{{ item.name }}</span>
          <span class="system-xs-regular text-text-tertiary">{{ item.type }}</span>
          <span class="system-xs-regular text-util-colors-orange-dark-orange-dark-600" style="color: #e62e05">
            {{ item.required ? 'Required' : '' }}
          </span>
        </div>
        <div class="mt-0.5 text-xs font-normal leading-[18px] text-text-tertiary" style="padding: 0 10px">
          {{ item.description }}
        </div>
      </div>
      <div class="right-btn">
        <icon-edit @click="editTool(item, index)" />
        <icon-delete style="color: red" @click="deleteTool(item, index)" />
      </div>
    </div>

    <a-modal v-model:visible="visible" @cancel="handleCancel">
      <template #title>
        <div class="model-title">添加提取参数</div>
      </template>
      <div>
        <a-form ref="formRef" :model="modelitem" layout="vertical">
          <a-form-item label="名称" name="name" :rules="[{ required: true, message: '请输入名称' }]">
            <a-input v-model="modelitem.name" placeholder="请输入参数名称" />
          </a-form-item>
          <a-form-item label="类型" name="type" :rules="[{ required: true, message: '请选择类型' }]">
            <a-select v-model="modelitem.type" :style="{ width: '320px' }" placeholder="Please select ...">
              <a-option>String</a-option>
              <a-option>Number</a-option>
              <a-option>Array[String]</a-option>
              <a-option>Array[Number]</a-option>
              <a-option>Array[Object]</a-option>
            </a-select>
          </a-form-item>
          <a-form-item label="描述" name="description" :rules="[{ required: true, message: '请输入描述' }]">
            <a-textarea v-model="modelitem.description" placeholder="Please enter something" allow-clear />
          </a-form-item>
          <a-form-item label="是否必填" name="required" :rules="[{ required: true, message: '请选择是否必填' }]">
            <a-switch v-model="modelitem.required" type="round" />
          </a-form-item>
        </a-form>
      </div>
      <template #footer>
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleSubmit">确定</a-button>
      </template>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { toolsworkflow } from '@/apis'

const props = defineProps<{
  nodeInfo?: any
}>()
const formRef = ref()
const toolworkflowList = ref([])
const selectType = ref('1')
const visible = ref(false)
const modelitem = ref({
  name: '',
  type: '',
  description: '',
  required: true
})
const editItem = ref({})
const editIndex = ref(0)
const modalType = ref('add')
const addtoolbox = async () => {
  toolsworkflow()
    .then((res) => {
      toolworkflowList.value = [...res]
    })
    .catch((error) => {
      console.error('Failed to fetch tool workflow data:', error)
    })
}

const selectToolWork = (item) => {
  item.parameters.forEach((el) => {
    props.nodeInfo.parameters.push({
      description: '',
      name: el.name,
      options: el.options,
      required: el.required,
      type: el.type
    })
  })
}

const handleSubmit = () => {
  formRef.value
    .validate()
    .then(() => {
      if (modalType.value == 'add') {
        props.nodeInfo.parameters.push({ ...modelitem.value })
      } else {
        props.nodeInfo.parameters[editIndex.value] = { ...modelitem.value }
      }

      formRef.value.resetFields()
      visible.value = false // 隐藏模态框
    })
    .catch((error) => {
      console.error('表单验证失败:', error)
    })
}
const handleCancel = () => {
  visible.value = false
}
const editTool = (item, index) => {
  console.log(item)
  editItem.value = item
  editIndex.value = index
  modalType.value = 'edit'
  visible.value = true
  modelitem.value = {
    name: item.name,
    type: item.type,
    description: item.description,
    required: item.required
  }
}
const addtoolitem = () => {
  visible.value = true
  modalType.value = 'add'
  modelitem.value = {
    name: '',
    type: '',
    description: '',
    required: true
  }
}
const deleteTool = (item, index) => {
  props.nodeInfo.parameters.splice(index, 1)
}
</script>

<style scoped lang="scss">
.extract-parameters {
  .header {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .right-operate {
      .a-collapse {
        .node-list-item {
          display: flex;
          align-items: center;
          padding: 4px 8px;
          margin-bottom: 4px;
          cursor: pointer;

          &:hover {
            border-radius: 4px;
            background-color: var(--color-fill-2);
          }

          &-icon {
            margin-right: 8px;
            height: 19px;
            width: 19px;
            border-radius: 6px;
            background-color: var(--color-fill-3);
            text-align: center;
            line-height: 19px;
            color: var(--color-text-1);
            display: flex;
            align-items: center;
            justify-content: center;
          }

          &-text {
            font-size: 14px;
            color: var(--color-text-2);
          }
        }
      }
    }
  }
  .item-tool {
    position: relative;
    // height: 30px;
    margin: 10px 0;
    background: #f2f4f7;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;

    .right-btn {
      display: none; /* 默认隐藏 */
      gap: 8px;
      cursor: pointer;
    }

    &:hover .right-btn {
      display: flex; /* 悬停时显示按钮组 */
    }
  }
  .model-title {
    font-size: 18px;
    font-weight: bold;
  }
}
</style>
