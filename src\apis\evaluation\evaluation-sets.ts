import http from '@/utils/http'
import type * as evaluationTypes from './evaluation-sets-type'

// evalDataset
/**
 * @name SaveUsingPost
 * @summary 新增
 * @request POST:/evalDataset
 */
export function addEvaluationSets(params: evaluationTypes.EvalDatasetSaveDTO) {
  const url = `/evaluation_api/evalDataset`

  return http.post<evaluationTypes.REvalDataset>(url, params)
}

/**
 * @name UpdateUsingPut
 * @summary 修改
 * @request PUT:/evalDataset
 */
export function updateEvaluationSets(params: evaluationTypes.EvalDatasetUpdateDTO) {
  const url = `/evaluation_api/evalDataset`

  return http.put<evaluationTypes.REvalDataset>(url, params)
}

/**
 * @name DeleteUsingDelete
 * @summary 删除
 * @request DELETE:/evalDataset
 */
export function deleteEvaluationSets(params: number[]) {
  const url = `/evaluation_api/evalDataset`

  return http.del<evaluationTypes.RBoolean>(url, params)
}

/**
 * @name UpdateAllUsingPut
 * @summary 修改所有字段
 * @request PUT:/evalDataset/all
 */
export function updateAllEvaluationSets(params: evaluationTypes.EvalDataset) {
  const url = `/evaluation_api/evalDataset/all`

  return http.put<evaluationTypes.REvalDataset>(url, params)
}

/**
 * @name PageUsingPost
 * @summary 分页列表查询
 * @request POST:/evalDataset/page
 */
export function getEvaluationSetsPage(params: evaluationTypes.PageParamsEvalDatasetPageQuery) {
  const url = `/evaluation_api/evalDataset/page`

  return http.post<evaluationTypes.RIPageEvalDataset>(url, params)
}

/**
 * @name QueryUsingPost
 * @summary 批量查询
 * @request POST:/evalDataset/query
 */
export function queryEvaluationSets(params: evaluationTypes.EvalDataset) {
  const url = `/evaluation_api/evalDataset/query`

  return http.post<evaluationTypes.RListEvalDataset>(url, params)
}

/**
 * @name GetUsingGet
 * @summary 单体查询
 * @request GET:/evalDataset/{id}
 */
export function getEvaluationSetsDetail(id: string) {
  const url = `/evaluation_api/evalDataset/${id}`

  return http.get<evaluationTypes.REvalDataset>(url)
}
