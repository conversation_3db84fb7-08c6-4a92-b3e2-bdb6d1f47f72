export class ChartEdge {
  id: string = ''
  source: string = ''
  sourceHandle: string = ''
  target: string = ''
  targetHandle: string = ''
  type = 'button'
  zIndex: number = 0
  selected: boolean = false
  selectable: boolean = true
  data = {
    selected: false,
    hover: false
  }

  constructor(config) {
    this.id = config.id || `${+new Date()}`
    this.source = config.source
    this.sourceHandle = config.sourceHandle
    this.target = config.target
    this.targetHandle = config.targetHandle
    this.selected = this.data.selected = false
    if (config.zIndex) {
      this.zIndex = config.zIndex
    }
    if (typeof config.selectable === 'boolean') {
      this.selectable = config.selectable
    }
  }
}
