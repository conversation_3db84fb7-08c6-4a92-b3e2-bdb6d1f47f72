<%
const { route, utils, config } = it;
const { _, pascalCase, require } = utils;
const { query, payload, pathParams, headers } = route.request;

const routeDocs = includeFile("./route-docs", { config, route, utils });
const routeNamespace = pascalCase(route.routeName.usage);

// 更精确地检查类型，判断是否需要添加 DataContracts 前缀
const formatType = (typeStr) => {
  if (!typeStr) return typeStr;
  
  // 已经包含 DataContracts 前缀的直接返回
  if (typeStr.includes('DataContracts.')) {
    return typeStr;
  }
  
  // 基本类型列表（不需要 DataContracts 前缀）
  const basicTypes = [
    'string', 'String',
    'number', 'Number',
    'boolean', 'Boolean',
    'any', 'unknown',
    'null', 'undefined', 'void', 'never',
    'object', 'Object',
    'Record', 'Array',
    'Date', 'Promise',
    'Error'
  ];
  
  // 完全匹配基本类型
  if (basicTypes.includes(typeStr)) {
    return typeStr;
  }
  
  // 基本类型的数组表示（如 number[]、Array<string> 等）
  const arrayTypeRegex1 = new RegExp(`^(${basicTypes.join('|')})\\[\\]$`); // 如 number[]
  const arrayTypeRegex2 = new RegExp(`^Array<(${basicTypes.join('|')})>$`); // 如 Array<string>
  if (arrayTypeRegex1.test(typeStr) || arrayTypeRegex2.test(typeStr)) {
    return typeStr;
  }
  
  // 复杂类型模式（不需要 DataContracts 前缀）
  // 1. 对象字面量如 { prop: string }
  // 2. 数组字面量如 [string, number]
  // 3. 联合类型如 string | number
  // 4. 交叉类型如 Type1 & Type2
  // 5. 泛型如 Promise<T>
  // 6. 函数类型如 () => void
  const isComplexType = 
    typeStr.startsWith('{') || // 对象字面量
    typeStr.startsWith('[') || // 数组字面量
    typeStr.includes('|') ||   // 联合类型
    typeStr.includes('&') ||   // 交叉类型
    typeStr.includes('<') ||   // 泛型
    typeStr.includes('=>') ||  // 函数类型
    typeStr.includes('(') ||   // 函数类型
    typeStr.startsWith('Record<') || // Record 类型
    typeStr.startsWith('Partial<') || // Partial 类型
    typeStr.startsWith('Omit<') ||   // Omit 类型
    typeStr.startsWith('Pick<');     // Pick 类型
    
  if (isComplexType) {
    return typeStr;
  }
  
  // 内置全局类型（不需要 DataContracts 前缀）
  const builtInTypes = [
    'HTMLElement', 'Element', 'Node',
    'Window', 'Document', 'Event',
    'Map', 'Set', 'WeakMap', 'WeakSet',
    'Int8Array', 'Uint8Array', 'Uint8ClampedArray',
    'Int16Array', 'Uint16Array',
    'Int32Array', 'Uint32Array',
    'Float32Array', 'Float64Array',
    'BigInt64Array', 'BigUint64Array',
    'ArrayBuffer', 'DataView',
    'FormData', 'File', 'Blob',
    'URL', 'URLSearchParams',
    'RegExp', 'Function'
  ];
  
  if (builtInTypes.includes(typeStr)) {
    return typeStr;
  }
  
  // 非上述任何类型，则认为是自定义类型，添加 DataContracts 前缀
  return `DataContracts.${typeStr}`;
};

// 格式化响应类型
const responseType = formatType(route.response.type);

%>

/**
<%~ routeDocs.description %>

<%~ routeDocs.lines %>

*/
export namespace <%~ routeNamespace %> {
  export type RequestParams = <%~ (pathParams && formatType(pathParams.type)) || '{}' %>;
  export type RequestQuery = <%~ (query && formatType(query.type)) || '{}' %>;
  export type RequestBody = <%~ (payload && formatType(payload.type)) || 'never' %>;
  export type RequestHeaders = <%~ (headers && formatType(headers.type)) || '{}' %>;
  export type ResponseBody = <%~ responseType %>;
}
