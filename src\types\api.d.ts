type ApiRes<T> = T

/** 分页响应数据格式 */
interface PageRes<T> {
  list: T
  total: number
}

/** 分页请求数据格式 */
interface PageQuery {
  page: number
  size: number
}
/** 接口返回数据格式-唯一 */
interface ApiResponese<T = any> {
  [x: string]: any
  code: number
  data: T
  msg: string
  timestamp: string
  errorMsg: string
  isSuccess: boolean
}

/** 分页数据格式-唯一 */
interface ApiQuery {
  size: number
  current: number
  sort?: number
  order?: number
  model?: any
  extra?: any
}
