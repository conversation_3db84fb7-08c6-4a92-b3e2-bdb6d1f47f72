<template>
  <div class="retrieval-config-panel">
    <!-- 检索模式选择 -->
    <div class="config-section">
      <div class="section-title">检索模式</div>
      <div class="section-content">
        <a-radio-group v-model="localConfig.retrieval_mode" @change="handleRetrievalModeChange">
          <a-radio :value="RetrievalMode.多路召回">多路召回</a-radio>
          <a-radio :value="RetrievalMode.N选1召回">N选1召回</a-radio>
        </a-radio-group>
      </div>
    </div>

    <!-- 多路召回配置 -->
    <div v-if="localConfig.retrieval_mode === RetrievalMode.多路召回" class="config-section">
      <div class="section-title">多路召回设置</div>
      <div class="section-content">
        <div class="config-row">
          <label>Top K:</label>
          <a-input-number
            v-model="localConfig.multiple_retrieval_config.top_k"
            :min="1"
            :max="20"
            placeholder="返回结果数量"
            @change="emitChange"
          />
        </div>

        <div class="config-row">
          <label>相似度阈值:</label>
          <a-input-number
            v-model="localConfig.multiple_retrieval_config.score_threshold"
            :min="0"
            :max="1"
            :step="0.1"
            placeholder="0.0-1.0"
            @change="emitChange"
          />
        </div>

        <div class="config-row">
          <label>启用重排序:</label>
          <a-switch v-model="localConfig.multiple_retrieval_config.reranking_enable" @change="handleRerankingToggle" />
        </div>

        <!-- 重排序模型配置 -->
        <div v-if="localConfig.multiple_retrieval_config.reranking_enable" class="reranking-config">
          <div class="config-row">
            <label>重排序提供商:</label>
            <a-select
              v-model="localConfig.multiple_retrieval_config.reranking_model.provider"
              placeholder="选择提供商"
              :options="rerankingProviders"
              @change="handleRerankingProviderChange"
            />
          </div>

          <div class="config-row">
            <label>重排序模型:</label>
            <a-select
              v-model="localConfig.multiple_retrieval_config.reranking_model.model"
              placeholder="选择模型"
              :options="rerankingModels"
              :disabled="!localConfig.multiple_retrieval_config.reranking_model.provider"
              @change="emitChange"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- N选1召回配置 -->
    <div v-if="localConfig.retrieval_mode === RetrievalMode.N选1召回" class="config-section">
      <div class="section-title">N选1召回设置</div>
      <div class="section-content">
        <ModelSelector v-model="localConfig.single_retrieval_config.model" @change="handleSingleRetrievalModelChange" />
      </div>
    </div>

    <!-- 移除关联知识库信息展示 -->
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import ModelSelector from './ModelSelector.vue'
import { RetrievalMode } from '@/views/app/workflow/types/node'

const props = withDefaults(
  defineProps<{
    modelValue?: any
    selectedDatasets?: any[]
  }>(),
  {
    modelValue: () => ({
      retrieval_mode: RetrievalMode.多路召回,
      multiple_retrieval_config: {
        top_k: 3,
        score_threshold: 0.5,
        reranking_enable: false,
        reranking_model: {
          provider: '',
          model: ''
        }
      },
      single_retrieval_config: {
        model: {
          provider: '',
          name: '',
          mode: 'chat',
          completion_params: {}
        }
      }
    }),
    selectedDatasets: () => []
  }
)

const emit = defineEmits<{
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}>()

const localConfig = ref({
  retrieval_mode: RetrievalMode.多路召回,
  multiple_retrieval_config: {
    top_k: 3,
    score_threshold: 0.5,
    reranking_enable: false,
    reranking_model: {
      provider: '',
      model: ''
    }
  },
  single_retrieval_config: {
    model: {
      provider: '',
      name: '',
      mode: 'chat',
      completion_params: {}
    }
  }
})

const rerankingProviders = ref([
  { label: 'Cohere', value: 'cohere' },
  { label: 'Jina', value: 'jina' },
  { label: 'BGE', value: 'bge' }
])

const rerankingModels = ref([])

onMounted(() => {
  if (props.modelValue) {
    Object.assign(localConfig.value, props.modelValue)
  }
})

const handleRetrievalModeChange = () => {
  emitChange()
}

const handleRerankingToggle = () => {
  if (!localConfig.value.multiple_retrieval_config.reranking_enable) {
    localConfig.value.multiple_retrieval_config.reranking_model = {
      provider: '',
      model: ''
    }
  }
  emitChange()
}

const handleRerankingProviderChange = (providerId: string) => {
  // 模拟不同提供商的模型数据
  const modelMap = {
    cohere: [
      { label: 'rerank-english-v2.0', value: 'rerank-english-v2.0' },
      { label: 'rerank-multilingual-v2.0', value: 'rerank-multilingual-v2.0' }
    ],
    jina: [
      { label: 'jina-reranker-v1-base-en', value: 'jina-reranker-v1-base-en' },
      { label: 'jina-reranker-v1-turbo-en', value: 'jina-reranker-v1-turbo-en' }
    ],
    bge: [
      { label: 'bge-reranker-base', value: 'bge-reranker-base' },
      { label: 'bge-reranker-large', value: 'bge-reranker-large' }
    ]
  }

  rerankingModels.value = modelMap[providerId] || []

  // 清空已选择的模型
  localConfig.value.multiple_retrieval_config.reranking_model.model = ''
  emitChange()
}

const handleSingleRetrievalModelChange = (model: any) => {
  localConfig.value.single_retrieval_config.model = model
  emitChange()
}

const emitChange = () => {
  emit('update:modelValue', localConfig.value)
  emit('change', localConfig.value)
}

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      Object.assign(localConfig.value, newValue)
    }
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.retrieval-config-panel {
  max-height: 60vh;
  overflow-y: auto;

  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--color-text-1);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--color-border-2);
    }

    .section-content {
      .config-row {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        label {
          min-width: 120px;
          font-size: 13px;
          color: var(--color-text-2);
          margin-right: 12px;
        }

        .arco-input-number,
        .arco-select,
        .arco-switch {
          max-width: 200px;
        }
      }

      .reranking-config {
        margin-top: 16px;
        padding: 12px;
        background-color: var(--color-bg-2);
        border-radius: 6px;
        border: 1px solid var(--color-border-2);
      }
    }
  }
}
</style>
