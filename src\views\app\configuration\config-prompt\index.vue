<template>
  <div
    class="relative rounded-xl bg-gradient-to-r from-components-input-border-active-prompt-1 to-components-input-border-active-prompt-2 p-0.5 shadow-sm"
  >
    <div class="rounded-xl bg-background-section-burn">
      <div class="flex h-11 items-center justify-between pl-3 pr-2.5">
        <div class="flex items-center space-x-1">
          <div class="h2 system-sm-semibold-uppercase text-text-secondary">
            提示词
            <a-tooltip
              content="提示词用于对 AI 的回复做出一系列指令和约束。可插入表单变量，例如 {{input}}。这段提示词不会被最终用户所看到。"
            >
              <icon-question-circle />
            </a-tooltip>
          </div>
        </div>

        <div class="flex items-center">
          <a-button size="small">生成</a-button>
        </div>
      </div>

      <div class="rounded-xl bg-background-default px-4 pt-2 text-sm text-text-secondary">
        <a-textarea
          v-model="promptVal"
          placeholder="请输入内容"
          class="min-h-[228px]"
          show-word-limit
          auto-size
          @change="handleChange"
        />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    promptTemplate?: string
  }>(),
  {
    promptTemplate: ''
  }
)
const emits = defineEmits(['updatePrompt'])
const promptVal = ref(props.promptTemplate || '')
const handleChange = () => {
  emits('updatePrompt', promptVal.value)
}
watch(
  () => props.promptTemplate,
  () => {
    promptVal.value = props.promptTemplate
  })
</script>
<style scoped lang="scss">
.bg-gradient-to-r {
  background-image: linear-gradient(to right, #0ba5ec, #155aef);
}

.bg-background-section-burn {
  background: #f2f4f7;
}

.bg-background-default {
  background: #ffffff;
}

.from-components-input-border-active-prompt-1 {
  --tw-gradient-from: var(--color-components-input-border-active-prompt-1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-components-panel-gradient-2 {
  --tw-gradient-from: var(--color-components-panel-gradient-2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

// custom
.arco-textarea-wrapper {
  border: none;
}
</style>
