<template>
  <div v-for="(param, paramIndex) in outputData" :key="paramIndex" class="param-item">
    <a-space direction="vertical" :size="'mini'">
      <a-space align="center">
        <a-typography-text class="params-name">{{ param.name || param.label || param.title }}</a-typography-text>
        <a-typography-text type="secondary" class="params-type">{{ param.type }}</a-typography-text>
      </a-space>
      <a-typography-text type="secondary" class="params-desc">
        {{ param.desc }}
      </a-typography-text>
    </a-space>
  </div>
</template>

<script setup lang="ts">
interface OutputItem {
  name: string
  type: string
  desc: string
  label?: string
  title?: string
} // 数据由父组件传：因为有动态变量的场景。 outputData nodeInfo

const props = withDefaults(
  defineProps<{
    nodeInfo: any
    outputData: OutputItem[]
  }>(),
  {
    nodeInfo: () => {},
    outputData: () => []
  }
)
</script>

<style scoped lang="scss">
.param-item {
  margin-top: var(--margin);

  .params-name {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
