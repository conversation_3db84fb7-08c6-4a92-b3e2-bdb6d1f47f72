<template>
  <div style="height: 100%">
    <div style="padding-bottom: 10px">工作区</div>
    <a-scrollbar outer-style="height: calc(100% - 31px)" style="height: 100%; overflow-y: auto">
      <div class="applist cursor-pointer">
        <div
          v-for="(item, index) in appListData"
          :key="item.id"
          :title="item?.app?.name"
          :style="{ background: appId == item.id ? 'var(--color-fill-2)' : '' }"
          class="applist-item bg-state-base-active"
          @click="onclick(item, index)"
        >
          <a-avatar class="applist-item-avatar" shape="square">
            <span>
              <AiSvgIcon :name="`workflow-ai-${item?.app?.mode}`" />
            </span>
          </a-avatar>
          <div class="applist-item-content overflow-hidden text-ellipsis whitespace-nowrap">
            {{ item?.app?.name }}
          </div>
        </div>
      </div>
    </a-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { getInstalleApps } from '@/apis'
import { useAppShare } from '@/stores'
const appListData = ref()
const router = useRouter()
const route = useRoute()
const appId = ref(route.query.appId as string)

const { setInstalledApps } = useAppShare()

const getAppListFn = async () => {
  const res = await getInstalleApps()
  appListData.value = res.installed_apps
  setInstalledApps(res.installed_apps)
}
const onclick = (item, index) => {
  appId.value = item.id
  router.replace({ path: '/workspace/ai/explore', query: { appId: item.id } })
}
onMounted(() => {
  getAppListFn()
})
</script>

<style scoped lang="scss">
.applist {
  &-item {
    display: flex;
    align-items: center;
    height: 32px;
    padding: 0 5px;
    margin-bottom: 10px;
    border-radius: 4px;

    &-avatar {
      height: 24px;
      width: 24px;
      margin-right: 10px;
      border-radius: 4px;
      background: linear-gradient(rgb(var(--primary-6)) 0%, rgb(var(--primary-4)) 100%);
    }

    &-content {
      flex: 1;
    }

    &:hover {
      background-color: var(--color-fill-2);
    }
  }
}
</style>
