<template>
  <a-modal
    v-model:visible="visible"
    title="重命名对话"
    title-align="start"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 500 ? 500 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <AiForm ref="formRef" v-model="form" layout="vertical" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import type { TreeItem } from '../index.vue'
import { AiForm, type ColumnItem } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'

const emit = defineEmits<{
  (e: 'save-success', keyword: string): TreeItem[]
}>()

const { width } = useWindowSize()
const visible = ref(false)
const formRef = ref<InstanceType<typeof AiForm>>()

const [form, resetForm] = useResetReactive({
  name: '' as string,
  description: '' as string
})

const columns = reactive<ColumnItem[]>([
  {
    label: '对话名称',
    field: 'name',
    type: 'input',
    span: 24,
    required: true,
    props: {
      maxLength: 100
    }
  }
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    Message.success('重命名成功')
    emit('save-success', '1')
    return true
  } catch (error) {
    return false
  }
}
const open = (item) => {
  reset()
  form.name = item.name
  visible.value = true
}

defineExpose({ open })
</script>
