<template>
  <a-modal :visible="visible" :mask-closable="false" @ok="handleOk" @cancel="handleCancel">
    <!--TODO-todo:1. select内容超长；2.接口在内部调用了；3. 使用v-model控制弹框的显示和隐藏；-->
    <template #title>
      {{ dialogTitle }}
    </template>

    <div>
      <a-form :model="form" :layout="'vertical'">
        <template v-for="config in systemModelConfigList" :key="config.value">
          <a-form-item :field="config.value" :label="config.label">
            <a-select v-model="form[config.value]" placeholder="请选择" allow-search value-key="model">
              <!--group-->
              <a-optgroup
                v-for="(group, index) in modelObj[config.value + 'List']"
                :key="index"
                :label="renderI18nName(group.label)"
              >
                <template #label>
                  <a-typography-title :heading="6">{{ renderI18nName(group.label) }}</a-typography-title>
                </template>
                <!--option：绑定的是item对象-->
                <a-option
                  v-for="model in group.models"
                  :key="model.model"
                  :label="model.model"
                  :value="model"
                  :class="{ active: model.model === form[config.value].model }"
                >
                  <a-space fill>
                    <!--模型供应商的logo-->
                    <a-avatar :size="24">
                      <img :src="DOMAIN_NAME + renderI18nName(group.icon_small)" alt="" />
                    </a-avatar>
                    <!--模型名称-->
                    {{ model.model }}
                    <a-tag>{{ modelTypeFormat(model.model_type) }}</a-tag>
                    <!--模型大小-->
                    <a-tag v-if="model.model_properties.context_size">
                      {{ sizeFormat(model.model_properties.context_size as number) }}
                    </a-tag>
                    <!--是否选中-->
                    <icon-check v-if="model.model === form[config.value].model" class="option-check" />
                  </a-space>
                </a-option>
              </a-optgroup>
            </a-select>
          </a-form-item>
        </template>

        <!--单个模型设置-->
        <!--<a-form-item field="post" label="文本转语音模型">
          <a-select v-model="form.tts" placeholder="请选择" allow-search value-key="model">
            <a-optgroup
              v-for="(group,index) in modelObj.ttsList"
              :key="index"
              :label="renderI18nName(group.label)"
            >
              <a-option
                v-for="model in group.models"
                :key="model.model"
                :label="model.model"
                :value="model"
                :class="{ 'active': model.model === form.tts.model}"
              >
                <a-space>
                  <a-avatar :size="24">
                    <img :src="DOMAIN_NAME + renderI18nName(group.icon_small)" alt="">
                  </a-avatar>
                  {{ model.model }}
                  <a-tag>{{ modelTypeFormat(model.model_type) }}</a-tag>
                  <a-tag v-if="model.model_properties.context_size">{{
                      sizeFormat(model.model_properties.context_size as number)
                    }}
                  </a-tag>
                  <icon-check v-if="model.model === form.tts.model" class="option-check" />
                </a-space>
              </a-option>
            </a-optgroup>
          </a-select>
        </a-form-item>-->
      </a-form>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { getModelDefaultHttp, getModelTypesHttp, setModelHttp } from '@/apis/model-mgmt'
import { modelTypeFormat, sizeFormat, renderI18nName } from '@/views/app/workflow/utils/model'
import { omit } from 'lodash-es'
import { Message } from '@arco-design/web-vue'
import type { DefaultModelResponse, Model } from '@/apis/model-mgmt/type.ts'
import { DOMAIN_NAME } from '@/views/app/workflow/constant/common'

const emits = defineEmits(['closeConfigDialog'])
const visible = ref(true)
const dialogTitle = ref('系统模型配置')

const systemModelConfigList = [
  { label: '系统推理模型', value: 'llm', desc: '' },
  { label: 'Embedding 模型', value: 'textEmbedding', desc: '' },
  { label: 'Rerank 模型', value: 'rerank', desc: '' },
  { label: '语音转文本模型', value: 'speech2text', desc: '' },
  { label: '文本转语音模型', value: 'tts', desc: '' }
]
const form = reactive({
  llm: {} as DefaultModelResponse,
  textEmbedding: {} as DefaultModelResponse,
  rerank: {} as DefaultModelResponse,
  speech2text: {} as DefaultModelResponse,
  tts: {} as DefaultModelResponse
})
const modelObj = reactive({
  llmList: [] as Model[],
  textEmbeddingList: [] as Model[],
  rerankList: [] as Model[],
  speech2textList: [] as Model[],
  ttsList: [] as Model[]
})
const getModelTypes = async (modelType: string) => {
  const res = await getModelTypesHttp(modelType)
  const result = res.data || []
  result.forEach((item) => {
    item.models.forEach((model) => {
      model.provider = {
        ...omit(item, 'models')
      }
    })
  })
  if (modelType === 'llm') {
    modelObj.llmList = res.data || []
  }
  if (modelType === 'text-embedding') {
    modelObj.textEmbeddingList = res.data || []
  }
  if (modelType === 'rerank') {
    modelObj.rerankList = res.data || []
  }
  if (modelType === 'speech2text') {
    modelObj.speech2textList = res.data || []
  }
  if (modelType === 'tts') {
    modelObj.ttsList = res.data || []
  }
}
getModelTypes('llm')
getModelTypes('text-embedding')
getModelTypes('rerank')
getModelTypes('speech2text')
getModelTypes('tts')

const getModelDefault = async (modelType: string) => {
  const res = await getModelDefaultHttp(modelType)
  if (modelType === 'llm') {
    form.llm = res.data || []
  }
  if (modelType === 'text-embedding') {
    form.textEmbedding = res.data || []
  }
  if (modelType === 'rerank') {
    form.rerank = res.data || []
  }
  if (modelType === 'speech2text') {
    form.speech2text = res.data || []
  }
  if (modelType === 'tts') {
    form.tts = res.data || []
  }
}

getModelDefault('llm')
getModelDefault('text-embedding')
getModelDefault('rerank')
getModelDefault('speech2text')
getModelDefault('tts')

const handleOk = async () => {
  console.log('form', form)
  /**
   * TODO-todo: 没有遍历，直接重复写了。
   */
  const model_settings: DefaultModelResponse[] = []
  /*const params = {
    model_settings: [
      // {
      //   model_type:'llm',
      //   provider: form.llm?.provider?.provider || '',
      //   model:form.llm?.model || '',
      // },
      // {
      //   model_type:'text-embedding',
      //   provider: form.textEmbedding?.provider?.provider || '',
      //   model:form.textEmbedding?.model || '',
      // },
      // {
      //   model_type:'rerank',
      //   provider: form.rerank?.provider?.provider || '',
      //   model:form.rerank?.model || '',
      // },
      // {
      //   model_type:'speech2text',
      //   provider: form.speech2text?.provider?.provider || '',
      //   model:form.speech2text?.model || '',
      // },
      // {
      //   model_type:'tts',
      //   provider: form.tts?.provider?.provider || '',
      //   model:form.tts?.model || '',
      // },
    ]
  }*/
  const keys = Object.keys(form)
  keys.forEach((key: string) => {
    model_settings.push({
      model_type: key === 'textEmbedding' ? 'text-embedding' : key,
      provider: form[key]?.provider?.provider || undefined,
      model: form[key]?.model || undefined
    })
  })
  const params = {
    model_settings
  }
  try {
    const res = await setModelHttp(params)
    Message.success('修改成功')
    emits('closeConfigDialog', 'cancel')
  } catch (err) {
    console.log(err)
  }
}
const handleCancel = () => {
  emits('closeConfigDialog', 'cancel')
}
</script>

<style scoped lang="scss">
.arco-select-option {
  &.active {
    background-color: var(--color-fill-2);
  }

  .option-check {
    color: $color-primary;
  }
}
</style>
