<template>
  <div class="tracing-panel">
    <a-collapse :default-active-key="[]">
      <a-collapse-item v-for="(step, index) in props.list" :key="index">
        <template #header>
          <div class="custom-header">
            <div class="step-icon" :style="{ backgroundColor: nodeColor[step.node_type] }">
              <AiSvgIcon style="width: 14px; height: 14px" :name="`workflow-${step.node_type}`" />
            </div>
            <span class="step-title">{{ step.title }}</span>
          </div>
        </template>
        <template #extra>
          <div class="step-extra">
            <span class="step-duration">{{ step.elapsed_time + 's' }}</span>
            <span class="step-status">
              <icon-exclamation-circle-fill v-if="step.status === 'failed'" class="warning" />
              <icon-check-circle-fill v-else class="success" />
            </span>
          </div>
        </template>
        <template #header-icon>
          <div class="step-icon" :style="{ backgroundColor: nodeColor[step.type] }">
            <AiSvgIcon style="width: 14px; height: 14px" :name="step.type" />
          </div>
        </template>
        <a-button v-if="step.retries" long class="retry-info" href="#">
          <div class="retry-count">
            <icon-refresh />
            {{ step.retries }} 重试次数
          </div>
          <div>
            <icon-right />
          </div>
        </a-button>
        <a-alert
          v-if="step.type === 'http-request' && step.error"
          :show-icon="false"
          type="error"
          class="error-message"
        >
          {{ step.error }}
        </a-alert>
        <Code v-if="step.inputs" class="mb-4 mt-4" :title="'输入'" :inputs="JSON.stringify(step.inputs || {})" />
        <Code v-if="step.outputs" :title="'输出'" :inputs="JSON.stringify(step.outputs || {})" />
        <!-- <div  class="step-io-container">
          <div class="io-header">输入</div>
          <div class="io-content">
            <a-textarea :model-value="formatJson(step.inputs)" auto-size />
          </div>
        </div>
        <div  class="step-io-container">
          <div class="io-header">输出</div>
          <div class="io-content">
            <a-textarea :model-value="formatJson(step.outputs)" auto-size />
          </div>
        </div> -->
        <div v-if="step.request" class="step-io-container">
          <div class="io-header">数据处理</div>
          <div class="io-content">
            <a-textarea :model-value="formatJson(step.request)" auto-size />
          </div>
        </div>
      </a-collapse-item>
    </a-collapse>
  </div>
</template>

<script setup lang="ts">
import Code from './code.vue'
import { nodeColor, type NodeTracing } from '@/views/app/workflow/types/workflow'

const props = defineProps<{
  list: NodeTracing[]
}>()
// 模拟跟踪数据
const tracingSteps = ref([
  {
    title: '开始',
    type: 'start',
    duration: '223.241 ms',
    status: 'success',
    expanded: false,
    input: {
      uuid: '001',
      url: '测试123',
      split_level: 11,
      prompt: '11',
      sys_name: '11',
      'sys.files': [],
      'sys.user_id': '043b8064-6ebb-4215-a5f0-6125eadaa1f7'
    },
    output: {
      uuid: '001',
      url: '测试123',
      split_level: 11,
      prompt: '11',
      sys_name: '11',
      'sys.files': [],
      'sys.user_id': '043b8064-6ebb-4215-a5f0-6125eadaa1f7'
    }
  },
  {
    title: '对用户输入提示词赋默认值',
    type: 'code',
    duration: '158.251 ms',
    status: 'success',
    expanded: false,
    input: {
      input: '11'
    },
    output: {
      result: '11'
    }
  },
  {
    title: '获取分块',
    type: 'http-request',
    duration: '936.342 ms',
    status: 'warning',
    expanded: false,
    retries: 3,
    error: 'Reached maximum retries (0) for URL http://*************:8000/api/v1/doc/chunk',
    request: {
      request:
        'GET /api/v1/doc/chunk?file_id=001&split_level=11&file_url=%E6%B5%8B%E8%AF%95123 HTTP/1.1\r\nHost: *************:8000\r\n\r\n'
    }
  }
])

// 格式化JSON显示
const formatJson = (json: any) => {
  return JSON.stringify(json, null, 2)
}
</script>

<style scoped lang="scss">
.tracing-panel {
  font-family:
    system-ui,
    -apple-system,
    BlinkMacSystemFont,
    'Segoe UI',
    Roboto,
    sans-serif;
  width: 100%;
}

.step-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: var(--color-text-1);
  background-color: var(--color-fill-3);
  margin-right: 10px;
}

.step-extra {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-duration {
  color: var(--color-text-3);
  font-size: 12px;
}

.step-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.success {
  color: #10b981;
}

.warning {
  color: #f59e0b;
}

.error {
  color: #ef4444;
}

.error-message {
  // background-color: #fef2f2;
  // color: #ef4444;
  // padding: 12px 16px;
  // border-radius: 4px;
  // margin: 12px 0;
  // border: 1px solid #fecaca;
}

.retry-info {
  padding: 8px 5px;
  color: var(--color-text-3);
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;
}

.step-io-container {
  margin: 12px 0;
  border: 1px solid var(--color-border-2);
  border-radius: 4px;
  overflow: hidden;
}

.io-header {
  padding: 8px 12px;
  font-weight: 500;
  background-color: var(--color-fill-1);
  border-bottom: 1px solid var(--color-border-2);
}

.io-content {
  padding: 0;
  background-color: var(--color-bg-1);
}

:deep(.arco-textarea) {
  width: 100%;
  font-family: Monaco, monospace;
  font-size: 12px;
  line-height: 1.6;
  background-color: var(--color-bg-1);
}

pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

:deep(.arco-collapse) {
  border: none;
}

:deep(.arco-collapse-item) {
  border: 1px solid var(--color-border-2);
  border-radius: 4px;
  margin-bottom: 8px;
  overflow: hidden;
}

:deep(.arco-collapse-item-header) {
  background-color: var(--color-fill-1);
}

:deep(.arco-collapse-item-content) {
  padding: 0;
}

:deep(.arco-collapse-item-content-box) {
  background-color: var(--color-bg-1);
  padding: 12px;
}

:deep(.arco-textarea-wrapper) {
  border: none;
}

.custom-header {
  display: flex;
  align-items: center;
}

.step-title {
  font-weight: 500;
  color: var(--color-text-1);
}
</style>
