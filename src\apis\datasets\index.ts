import type * as T from './type'
import http from '@/utils/http'
import { getToken } from '@/utils/auth'

export type * from './type'

const BASE_URL = 'console/api'

/** @desc 获取知识库列表 */
export function getDatasetList(params: T.DatasetListParams) {
  return http.get<T.DatasetListResponse>(`${BASE_URL}/datasets`, params)
}

/** @desc 获取知识库详情 */
export function getDatasetDetail(id: string) {
  return http.get<T.Dataset>(`${BASE_URL}/datasets/${id}`)
}

/** @desc 创建空知识库 */
export function createEmptyDataset(params: { name: string; description?: string; tag_ids?: string[] }) {
  return http.post<T.Dataset>(`${BASE_URL}/datasets`, params)
}

/** @desc 创建知识库 */
export function createDataset(params: any) {
  return http.post<T.Dataset>(`${BASE_URL}/datasets`, params)
}

/** @desc 更新知识库 */
export function updateDataset(id: string, params: any) {
  return http.patch<T.Dataset>(`${BASE_URL}/datasets/${id}`, params)
}

/** @desc 检查知识库是否被使用 */
export function checkDatasetUsage(id: string) {
  return http.get<{ is_using: boolean }>(`${BASE_URL}/datasets/${id}/use-check`)
}

/** @desc 删除知识库 */
export function deleteDataset(id: string) {
  return http.del(`${BASE_URL}/datasets/${id}`)
}

/** @desc 获取知识库标签列表 */
export function getDatasetTags() {
  return http.get<T.DatasetTag[]>(`${BASE_URL}/tags`, { type: 'knowledge' })
}

/** @desc 创建知识库标签 */
export function createDatasetTag(params: { name: string }) {
  return http.post<T.DatasetTag>(`${BASE_URL}/tags`, {
    ...params,
    type: 'knowledge'
  })
}

/** @desc 更新知识库标签 */
export function updateDatasetTag(id: string, params: { name: string }) {
  return http.patch<T.DatasetTag>(`${BASE_URL}/tags/${id}`, params)
}

/** @desc 删除知识库标签 */
export function deleteDatasetTag(id: string) {
  return http.del(`${BASE_URL}/tags/${id}`)
}

/** @desc 文档重命名 */
export function renameDocument(datasetId: string, documentId: string, params: { name: string }) {
  return http.post(`${BASE_URL}/datasets/${datasetId}/documents/${documentId}/rename`, params)
}

/** @desc 禁用文档 */
export function disableDocuments(datasetId: string, documentId: string) {
  return http.patch(
    `${BASE_URL}/datasets/${datasetId}/documents/status/disable/batch`,
    {},
    {
      params: { document_id: documentId }
    }
  )
}

/** @desc 启用文档 */
export function enableDocuments(datasetId: string, documentId: string) {
  return http.patch(
    `${BASE_URL}/datasets/${datasetId}/documents/status/enable/batch`,
    {},
    {
      params: { document_id: documentId }
    }
  )
}

/** @desc 归档文档 */
export function archiveDocuments(datasetId: string, documentId: string) {
  return http.patch(
    `${BASE_URL}/datasets/${datasetId}/documents/status/archive/batch`,
    {},
    {
      params: { document_id: documentId }
    }
  )
}

/** @desc 撤销归档文档 */
export function unArchiveDocuments(datasetId: string, documentId: string) {
  return http.patch(
    `${BASE_URL}/datasets/${datasetId}/documents/status/un_archive/batch`,
    {},
    {
      params: { document_id: documentId }
    }
  )
}

/** @desc 删除文档 */
export function deleteDocument(datasetId: string, documentId: string) {
  return http.del(`${BASE_URL}/datasets/${datasetId}/documents?document_id=${documentId}`)
}

/** @desc 添加知识库标签绑定 */
export function createTagBinding(params: { tag_ids: string[]; target_id: string; type: string }) {
  return http.post(`${BASE_URL}/tag-bindings/create`, params)
}

/** @desc 移除知识库标签绑定 */
export function deleteTagBinding(params: { tag_id: string; target_id: string; type: string }) {
  return http.post(`${BASE_URL}/tag-bindings/delete`, params)
}

/** @desc 上传文本文件 */
export function uploadFile(file: File, onProgress?: (percent: number) => void) {
  const formData = new FormData()
  formData.append('file', file)
  const headers = {
    Authorization: `Bearer ${getToken()}`, // 使用 Bearer Token
    'Content-Type': 'multipart/form-data' // 设置Content-Type
  }

  return http.post<T.FileUploadResponse>(`${BASE_URL}/files/upload?source=datasets`, formData, {
    headers: headers,
    onUploadProgress: (progressEvent: any) => {
      if (onProgress && progressEvent.total) {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(percent)
      }
    }
  })
}

/** @desc 预览文档分段（索引预估） */
export function previewDocumentSegments(params: T.IndexingEstimateParams) {
  return http.post<T.IndexingEstimateResponse>(`${BASE_URL}/datasets/indexing-estimate`, params)
}

/** @desc 获取Embedding模型列表 */
export function getEmbeddingModels() {
  return http.get<T.EmbeddingModelsResponse>(`${BASE_URL}/workspaces/current/models/model-types/text-embedding`)
}

/** @desc 获取知识库文件内容（预览） */
export function getFileContent(fileId: string) {
  return http.get<{ content: string }>(`${BASE_URL}/files/${fileId}/content`)
}

/** @desc 获取文件预览 */
export function getFilePreview(fileId: string) {
  return http.get<{ content: string }>(`${BASE_URL}/files/${fileId}/preview`)
}

/** @desc 获取知识库中的文档列表 */
export function getDatasetDocuments(
  datasetId: string,
  params?: {
    page?: number
    limit?: number
    keyword?: string
  }
) {
  return http.get<T.DatasetDocumentsResponse>(`${BASE_URL}/datasets/${datasetId}/documents`, params)
}

/** @desc 获取知识库元数据 */
export function getDatasetMetadata(datasetId: string) {
  return http.get<{ doc_metadata: any[]; built_in_field_enabled: boolean }>(
    `${BASE_URL}/datasets/${datasetId}/metadata`
  )
}

/** @desc 获取知识库错误文档 */
export function getDatasetErrorDocs(datasetId: string) {
  return http.get<{ data: any[]; total: number }>(`${BASE_URL}/datasets/${datasetId}/error-docs`)
}

/** @desc 获取知识库相关应用 */
export function getDatasetRelatedApps(datasetId: string) {
  return http.get<{ data: any[]; total: number }>(`${BASE_URL}/datasets/${datasetId}/related-apps`)
}

/** @desc 获取知识库召回测试历史记录 */
export function getDatasetQueries(
  datasetId: string,
  params: {
    page?: number
    limit?: number
  }
) {
  return http.get<{
    data: Array<{
      id: string
      content: string
      source: string
      created_at: number
    }>
    has_more: boolean
    limit: number
    total: number
    page: number
  }>(`${BASE_URL}/datasets/${datasetId}/queries`, params)
}

/** @desc 获取内置字段 */
export function getDatasetBuiltInFields() {
  return http.get<{ fields: { name: string; type: string }[] }>(`${BASE_URL}/datasets/metadata/built-in`)
}

/** @desc 获取Rerank模型列表 */
export function getRerankModels() {
  return http.get<{
    data: Array<{
      tenant_id: string
      provider: string
      label: {
        zh_Hans: string
        en_US: string
      }
      models: Array<{
        model: string
        label: {
          zh_Hans: string
          en_US: string
        }
        model_type: string
        model_properties: {
          context_size: number
        }
        status: string
      }>
    }>
  }>(`${BASE_URL}/workspaces/current/models/model-types/rerank`)
}

/** @desc 获取默认Rerank模型 */
export function getDefaultRerankModel() {
  return http.get<{
    data: {
      model: string
      model_type: string
      provider: {
        provider: string
        label: {
          zh_Hans: string
          en_US: string
        }
        supported_model_types: string[]
      }
    }
  }>(`${BASE_URL}/workspaces/current/default-model?model_type=rerank`)
}

/** @desc 保存并处理知识库:新建知识库 */
export function initDataset(params: T.DatasetInitParams) {
  return http.post<T.DatasetInitResponse>(`${BASE_URL}/datasets/init`, params)
}

/** @desc 向已有知识库添加文件 */
export function addDocumentsToDataset(datasetId: string, params: T.DatasetInitParams) {
  return http.post<{
    documents: T.DocumentResponse[]
    batch: string
  }>(`${BASE_URL}/datasets/${datasetId}/documents`, params)
}

/** @desc 获取知识库处理规则 */
export function getDatasetProcessRule(documentId: string) {
  return http.get<T.DatasetProcessRuleResponse>(`${BASE_URL}/datasets/process-rule`, { document_id: documentId })
}

/** @desc 检查知识库索引状态 */
export function checkDatasetIndexingStatus(datasetId: string, batchId: string) {
  return http.get<{ is_indexing: boolean }>(`${BASE_URL}/datasets/${datasetId}/indexing/status?batch=${batchId}`)
}

/** @desc 获取知识库索引状态 */
export function getDatasetIndexingStatus(datasetId: string, batchId: string) {
  return http.get<T.DatasetIndexingStatusResponse>(`${BASE_URL}/datasets/${datasetId}/batch/${batchId}/indexing-status`)
}

/** @desc 获取批处理状态（兼容性别名）*/
export function getBatchProcessingStatus(datasetId: string, batchId: string) {
  return getDatasetIndexingStatus(datasetId, batchId)
}

/** @desc 获取文档分段列表 */
export function getDocumentSegments(
  datasetId: string,
  documentId: string,
  params?: {
    page?: number
    limit?: number
    keyword?: string
    enabled?: 'all' | 'true' | 'false'
  }
) {
  return http.get<{
    data: any[]
    limit: number
    total: number
    total_pages: number
    page: number
  }>(`${BASE_URL}/datasets/${datasetId}/documents/${documentId}/segments`, params)
}

/** @desc 获取文档详情和处理规则 */
export function getDocumentDetail(
  datasetId: string,
  documentId: string,
  params?: {
    metadata?: 'without' | 'with' | 'only'
  }
) {
  return http.get<any>(`${BASE_URL}/datasets/${datasetId}/documents/${documentId}`, params)
}

/** @desc 禁用文档块 */
export function disableDocumentSegment(datasetId: string, documentId: string, segmentId: string) {
  return http.patch<{ result: string }>(
    `${BASE_URL}/datasets/${datasetId}/documents/${documentId}/segment/disable?segment_id=${segmentId}`
  )
}

/** @desc 启用文档块 */
export function enableDocumentSegment(datasetId: string, documentId: string, segmentId: string) {
  return http.patch<{ result: string }>(
    `${BASE_URL}/datasets/${datasetId}/documents/${documentId}/segment/enable?segment_id=${segmentId}`
  )
}

/** @desc 编辑文档块 */
export function updateDocumentSegment(
  datasetId: string,
  documentId: string,
  segmentId: string,
  params: {
    content: string
    keywords?: string[]
  }
) {
  return http.patch<{
    data: any
    doc_form: string
  }>(`${BASE_URL}/datasets/${datasetId}/documents/${documentId}/segments/${segmentId}`, params)
}

/** @desc 删除文档块 */
export function deleteDocumentSegment(datasetId: string, documentId: string, segmentId: string) {
  return http.del<{ result: string }>(
    `${BASE_URL}/datasets/${datasetId}/documents/${documentId}/segments?segment_id=${segmentId}`
  )
}

/** @desc 新增文档块 */
export function createDocumentSegment(
  datasetId: string,
  documentId: string,
  params: {
    content: string
    keywords?: string[]
  }
) {
  return http.post<{
    data: any
    doc_form: string
  }>(`${BASE_URL}/datasets/${datasetId}/documents/${documentId}/segment`, params)
}

/** @desc 批量启用文档块 */
export function batchEnableDocumentSegments(datasetId: string, documentId: string, segmentIds: string[]) {
  const params = new URLSearchParams()
  segmentIds.forEach((id) => params.append('segment_id', id))

  return http.patch<{ result: string }>(
    `${BASE_URL}/datasets/${datasetId}/documents/${documentId}/segment/enable?${params.toString()}`
  )
}

/** @desc 批量禁用文档块 */
export function batchDisableDocumentSegments(datasetId: string, documentId: string, segmentIds: string[]) {
  const params = new URLSearchParams()
  segmentIds.forEach((id) => params.append('segment_id', id))

  return http.patch<{ result: string }>(
    `${BASE_URL}/datasets/${datasetId}/documents/${documentId}/segment/disable?${params.toString()}`
  )
}

/** @desc 批量删除文档块 */
export function batchDeleteDocumentSegments(datasetId: string, documentId: string, segmentIds: string[]) {
  const params = new URLSearchParams()
  segmentIds.forEach((id) => params.append('segment_id', id))

  return http.del<{ result: string }>(
    `${BASE_URL}/datasets/${datasetId}/documents/${documentId}/segments?${params.toString()}`
  )
}

/** @desc 创建知识库元数据 */
export function createDatasetMetadata(datasetId: string, params: { type: 'string' | 'number' | 'time'; name: string }) {
  return http.post<{ id: string; type: string; name: string }>(`${BASE_URL}/datasets/${datasetId}/metadata`, params)
}

/** @desc 更新知识库元数据 */
export function updateDatasetMetadata(datasetId: string, metadataId: string, params: { name: string }) {
  return http.patch<{ id: string; type: string; name: string }>(
    `${BASE_URL}/datasets/${datasetId}/metadata/${metadataId}`,
    params
  )
}

/** @desc 删除知识库元数据 */
export function deleteDatasetMetadata(datasetId: string, metadataId: string) {
  return http.del(`${BASE_URL}/datasets/${datasetId}/metadata/${metadataId}`)
}

/** @desc 启用内置元数据 */
export function enableBuiltInMetadata(datasetId: string) {
  return http.post(`${BASE_URL}/datasets/${datasetId}/metadata/built-in/enable`)
}

/** @desc 禁用内置元数据 */
export function disableBuiltInMetadata(datasetId: string) {
  return http.post(`${BASE_URL}/datasets/${datasetId}/metadata/built-in/disable`)
}

/** @desc 批量保存文档元数据 */
export function updateDocumentsMetadata(
  datasetId: string,
  params: {
    operation_data: Array<{
      document_id: string
      metadata_list: Array<{
        key: string
        value: string | number
      }>
    }>
  }
) {
  return http.post(`${BASE_URL}/datasets/${datasetId}/documents/metadata`, params)
}

/** @desc 召回测试 - 测试知识库文档召回效果 */
export function hitTesting(
  datasetId: string,
  params: {
    query: string
    retrieval_model: {
      search_method: 'semantic_search' | 'full_text_search' | 'hybrid_search'
      reranking_enable: boolean
      reranking_mode?: string | null
      reranking_model?: {
        reranking_provider_name: string
        reranking_model_name: string
      } | null
      weights?: any | null
      top_k: number
      score_threshold_enabled: boolean
      score_threshold: number
    }
  }
) {
  return http.post<{
    query: {
      content: string
    }
    records: Array<{
      segment: {
        id: string
        position: number
        document_id: string
        content: string
        word_count: number
        keywords: string[]
        document: {
          id: string
          name: string
          data_source_type: string
        }
      }
      score: number
    }>
  }>(`${BASE_URL}/datasets/${datasetId}/hit-testing`, params)
}

/** @desc 获取团队成员列表 */
export function getWorkspaceMembers() {
  return http.get<{
    accounts: Array<{
      id: string
      name: string
      avatar: string | null
      avatar_url: string | null
      email: string
      last_login_at: number | null
      last_active_at: number
      created_at: number
      role: 'owner' | 'admin' | 'editor' | 'normal'
      status: 'active' | 'pending'
    }>
  }>(`${BASE_URL}/workspaces/current/members`)
}

/** @desc 获取知识库设置 */
export function getDatasetSettings(datasetId: string) {
  return http.get<{
    id: string
    name: string
    description: string
    provider: string
    permission: string
    data_source_type: string
    indexing_technique: string
    app_count: number
    document_count: number
    word_count: number
    created_by: string
    created_at: number
    updated_by: string
    updated_at: number
    embedding_model: string
    embedding_model_provider: string
    embedding_available: any
    retrieval_model_dict: {
      search_method: 'semantic_search' | 'full_text_search' | 'hybrid_search'
      reranking_enable: boolean
      reranking_mode: string | null
      reranking_model: {
        reranking_provider_name: string
        reranking_model_name: string
      } | null
      weights: any | null
      top_k: number
      score_threshold_enabled: boolean
      score_threshold: number
    }
    tags: any[]
    doc_form: string
    external_knowledge_info: {
      external_knowledge_id: string | null
      external_knowledge_api_id: string | null
      external_knowledge_api_name: string | null
      external_knowledge_api_endpoint: string | null
    }
    external_retrieval_model: {
      top_k: number
      score_threshold: number
      score_threshold_enabled: boolean
    }
    doc_metadata: Array<{
      id: string
      name: string
      type: string
    }>
    built_in_field_enabled: boolean
    partial_member_list: string[]
  }>(`${BASE_URL}/datasets/${datasetId}/settings`)
}

/** @desc 更新知识库设置 */
export function updateDatasetSettings(
  datasetId: string,
  params: {
    name?: string
    description?: string
    permission?: 'only_me' | 'all_team_members' | 'partial_members'
    indexing_technique?: 'high_quality' | 'economy'
    retrieval_model?: {
      search_method: 'semantic_search' | 'full_text_search' | 'hybrid_search'
      reranking_enable: boolean
      reranking_mode?: string | null
      reranking_model?: {
        reranking_provider_name: string
        reranking_model_name: string
      } | null
      weights?: any | null
      top_k: number
      score_threshold_enabled: boolean
      score_threshold: number
    }
    embedding_model?: string
    embedding_model_provider?: string
    partial_member_list?: Array<{
      user_id: string
      role: string
    }>
  }
) {
  return http.patch<any>(`${BASE_URL}/datasets/${datasetId}`, params)
}

/** @desc 获取重排序提供商列表 */
export function getRerankingProviders() {
  return http.get<{
    data: Array<{
      id: string
      name: string
      label: string
      description?: string
    }>
  }>(`${BASE_URL}/workspaces/current/model-providers/rerank`)
}

/** @desc 获取指定提供商的重排序模型列表 */
export function getRerankingModels(providerId: string) {
  return http.get<{
    data: Array<{
      id: string
      name: string
      label: string
      model_type: string
      status: string
    }>
  }>(`${BASE_URL}/workspaces/current/model-providers/${providerId}/models/rerank`)
}

/** @desc 获取模型提供商列表 */
export function getModelProviders() {
  return http.get<{
    data: Array<{
      id: string
      name: string
      label: string
      description?: string
      supported_model_types: string[]
    }>
  }>(`${BASE_URL}/workspaces/current/model-providers`)
}

/** @desc 获取指定提供商的模型列表 */
export function getModels(providerId: string, modelType: string = 'llm') {
  return http.get<{
    data: Array<{
      id: string
      name: string
      label: string
      model_type: string
      status: string
      features?: string[]
    }>
  }>(`${BASE_URL}/workspaces/current/model-providers/${providerId}/models/${modelType}`)
}
