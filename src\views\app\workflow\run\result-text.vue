<template>
  <div class="bg-background-section-burn flex">
    <div v-if="isRunning && !outputs" class="pl-[26px] pt-4">
      <AiSvgIcon name="common-loading-bold" :size="30" class="loading-icon" spin />
    </div>
    <div v-if="!isRunning && error" class="flex-1 w-full">
      <StatusContainer status="failed">
        {{ props.error }}
      </StatusContainer>
    </div>
    <div
      v-if="!isRunning && !outputs && !error && !allFiles?.length"
      class="flex flex-col items-center px-4 py-2 text-[13px] leading-[18px] text-gray-500 w-full"
    >
      <div class="mr-2">本次运行仅输出 JSON 格式，</div>
      <div>
        请转到
        <span class="cursor-pointer text-primary-600">详细信息面板</span>
        查看它。
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import StatusContainer from './status-container.vue'
defineOptions({
  name: 'ResultText'
})

const props = withDefaults(
  defineProps<{
    isRunning?: boolean
    outputs?: any
    error?: string
    onClick?: () => void
    allFiles?: any[]
  }>(),
  {}
)
</script>

<style scoped lang="scss">
.bg-background-section-burn {
  background-color: #f2f4f7;
}
</style>
