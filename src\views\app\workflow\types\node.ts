export enum InputVarType {
  textInput = 'text-input',
  paragraph = 'paragraph',
  select = 'select',
  number = 'number',
  url = 'url',
  files = 'files',
  json = 'json', // obj, array
  contexts = 'contexts', // knowledge retrieval
  iterator = 'iterator', // iteration input
  singleFile = 'file',
  multiFiles = 'file-list',
  loop = 'loop' // loop input
}
export enum NodeType {
  开始 = 'start',
  大模型 = 'llm',
  知识检索 = 'knowledge-retrieval',
  直接回复 = 'answer',
  问题分类 = 'question-classifier',
  条件分支 = 'if-else',
  循环 = 'loop',
  循环开始 = 'loop-start',
  迭代 = 'iteration',
  迭代开始 = 'iteration-start',
  变量聚合 = 'variable-aggregator',
  代码执行 = 'code',
  模板转换 = 'template-transform',
  参数提取 = 'parameter-extractor',
  HTTP请求 = 'http-request',
  TextToSQL = 'text-to-sql-query',
  文档提取器 = 'document-extractor',
  工具 = 'tool',
  结束 = 'end',
  并行节点 = 'parallel'
}
export enum AuthorizationType {
  无 = 'no-auth',
  'API-Key' = 'api-key'
}
export enum ErrorHandleMode {
  错误终止 = 'terminated',
  忽略错误并继续 = 'continue-on-error',
  移除错误输出 = 'remove-abnormal-output'
}
export enum ReasoningMode {
  'Function/Tool Calling' = 'function_call',
  'Prompt' = 'prompt'
}
export interface NodePosition {
  x: number
  y: number
}
export enum FieldType {
  文本 = 'text-input',
  段落 = 'paragraph',
  下拉选项 = 'select',
  数字 = 'number',
  文件 = 'file-list'
}

export enum HTTPMethod {
  GET = 'get',
  POST = 'post',
  HEAD = 'head',
  PATCH = 'patch',
  PUT = 'put',
  DELETE = 'delete'
}

export interface VariableItem {
  default: any
  label: string
  required?: boolean
  variable: string
  field_type?: FieldType
  operator?: any
  value?: any
}

export interface StringVariableItem extends VariableItem {
  default: string
  max_length?: number
}

export interface ParagraphVariableItem extends VariableItem {
  default: string
  max_length?: number
}

export interface SelectVariableItem extends VariableItem {
  default: string
  options: string[]
}

export interface NumberVariableItem extends VariableItem {
  default: number
}
export type VariableSelector = Array<string>
export interface VariableConfig {
  value_selector: VariableSelector
  variable: string
}
export enum Features {
  toolCall = 'tool-call',
  multiToolCall = 'multi-tool-call',
  agentThought = 'agent-thought',
  vision = 'vision'
}
export interface ModelForm {
  id?: string
  completion_params: {
    stop?: []
    max_tokens?: number // 最大标记
    temperature?: number // 温度
    top_p?: number // Top P
    top_k?: number // Top K
    seed?: number // 随机种子
    repetition_penalty?: number // Repetition penalty
    enable_search?: boolean // enable_search
    response_format?: 'JSON' | 'XML' // 回复格式
    [key: string]: any
  }
  mode: string
  name: string
  provider: string
  features?: Features[]
}

export interface PromptTemplate {
  id: string
  role: string
  text: string
}
export enum VisionDetail {
  高 = 'high',
  低 = 'low'
}

export enum RetrievalMode {
  N选1召回 = 'single',
  多路召回 = 'multiple'
}

export enum LogicalOperator {
  AND = 'and',
  OR = 'or'
}

export enum MetadataFilteringModeEnum {
  disabled = 'disabled',
  automatic = 'automatic',
  manual = 'manual'
}

export enum CodeLanguage {
  Python3 = 'python3',
  JavaScript = 'javascript'
}

export enum OutputType {
  String = 'string',
  Number = 'number',
  'Array[Number]' = 'array[number]',
  'Array[String]' = 'array[string]',
  'Array[Object]' = 'array[object]',
  Object = 'object',
  Any = 'any'
}
export interface Condition {
  comparison_operator: string
  id: string
  value: string
  variable_selector: VariableSelector
}
export interface CodeOutputs {
  [key: string]: OutputConfig
}
export interface OutputConfig {
  children: any
  type: OutputType | undefined
}
export interface ParameterConfig {
  description: string
  name: string
  required: boolean
  type: Exclude<OutputType, OutputType.Object>
}

export enum AgentStrategy {
  functionCall = 'function_call',
  react = 'react'
}
export enum AuthorizationConfigType {
  基础 = 'basic',
  Bearer = 'bearer',
  自定义 = 'custom'
}
export enum BodyType {
  none = 'none',
  'form-data' = 'form-data',
  'x-www-form-urlencoded' = 'x-www-form-urlencoded',
  'raw text' = 'raw-text',
  JSON = 'json'
}
export enum Method {
  GET = 'get',
  POST = 'post',
  HEAD = 'head',
  PATCH = 'patch',
  PUT = 'put',
  DELETE = 'delete'
}
export interface AuthorizationSetting {
  type: AuthorizationType
  config: null | {
    type: AuthorizationConfigType
    header?: string
    api_key: string
  }
}

export interface NodeData {
  title: string
  desc: string
  type: NodeType | null
  selected: boolean
  hover: boolean
  variables?: Array<
    StringVariableItem | ParagraphVariableItem | SelectVariableItem | NumberVariableItem | VariableConfig
  >
  context?: {
    enabled: boolean
    variable_selector: VariableSelector
  }
  model?: ModelForm
  prompt_template?: Array<PromptTemplate>
  version?: {
    enabled: boolean
  }
  vision?: {
    configs: {
      detail: VisionDetail
      variable_selector: Array<string>
    }
  }

  dataset_ids?: Array<string>
  retrieval_mode?: RetrievalMode
  multiple_retrieval_config?: {
    reranking_model: {
      model: string
      provider: string
    }
    score_threshold: number
    top_k: number
  }
  single_retrieval_config?: {
    model: ModelForm
  }
  query_variable_selector?: VariableSelector
  topics?: Array<any>
  conditions?: Array<Condition>
  logical_operator?: LogicalOperator
  iterator_selector?: Array<string>
  output_selector?: Array<string>
  startNodeType?: string
  start_node_id?: string
  width?: number
  height?: number
  code?: string
  code_language?: CodeLanguage
  outputs?: CodeOutputs | Array<{ variable: string; value_selector: Array<string> }>
  template?: string
  output_type?: string
  instruction?: string
  parameters?: Array<ParameterConfig>
  query?: Array<string>
  reasoning_mode?: AgentStrategy
  authorization?: AuthorizationSetting
  body?: {
    type: BodyType
    data: null | string
  }
  headers?: string
  method?: Method
  params?: string
  timeout?: {
    connect: number
    max_connect_timeout: number
    max_read_timeout: number
    max_write_timeout: number
    read: number
    write: number
  }
  url?: string
  answer?: string
}
