<template>
  <div class="select-object">
    <a-form :model="localFormData" layout="vertical" size="medium">
      <!-- 评测任务名称 -->
      <a-form-item field="taskName" label="评测任务名称" required>
        <a-input v-model="localFormData.taskName" placeholder="为您的评测任务起个名字吧" />
      </a-form-item>

      <!-- 评测对象 -->
      <a-form-item field="appData[appId]" label="评测对象" required>
        <div class="evaluation-object-container">
          <div class="evaluation-object-container-left flex-col">
            <div class="evaluation-object-container-title">智能助手</div>
            <div
              v-for="item in appListData"
              :key="item.id"
              :class="[
                'evaluation-object-container-item',
                'flex',
                `${item.id === selectAppId ? 'evaluation-object-container-item-active' : ''}`
              ]"
              @click="selectApp(item)"
            >
              <AiSvgIcon name="evaluation-assistant" style="width: 46px; height: 46px" />
              <div class="flex-1 flex flex-col pl-2 gap-1">
                <span>{{ item.name }}</span>
                <span class="evaluation-object-container-item-desc">{{ item.mode }}</span>
              </div>
            </div>
          </div>
          <div class="evaluation-object-container-right">
            <div class="evaluation-object-container-title">版本</div>
            <div v-if="selectAppItem?.id" class="evaluation-object-container-item flex !pl-0 items-center">
              <a-radio v-model="localFormData.appData.appId" :value="selectAppItem?.id || ''">
                <span class="pr-4">{{ selectAppItem?.id || '' }}</span>
                <a-tag color="orangered">1.0</a-tag>
              </a-radio>
              <!-- <div class="flex-1 flex flex-col pl-4 gap-1">
                <div class="flex items-center">
                  <span class="pr-4">8787987</span>
                  <a-tag color="orangered">1.0</a-tag>
                </div>
                <span class="evaluation-object-container-item-desc text-sm">
                  <a-space>
                    <icon-user />
                    <span>平台管理员</span>
                    <span>创建时间：111111</span>
                  </a-space>
                </span>
              </div> -->
            </div>
          </div>
        </div>
      </a-form-item>
      <a-form-item field="intention" label="执行信息-意图" required>
        <a-input v-model="localFormData.intention" placeholder="分析测试需求，生成测试案例" />
      </a-form-item>
      <!-- 测试分析方法 -->
      <a-form-item field="testMethod" required>
        <template #label>
          <a-space class="items-baseline">
            测试分析方法
            <span style="font-size: 12px; color: var(--color-text-3)">请输入测试分析方法</span>
          </a-space>
        </template>
        <a-textarea
          v-model="localFormData.testMethod"
          placeholder="请输入测试分析方法"
          :auto-size="{ minRows: 4, maxRows: 6 }"
        />
        <!-- <div>
          <a-checkbox-group
            v-model="localFormData.analysisMethod"
            :options="[
              { label: 1, value: '1' },
              { label: 2, value: '2' }
            ]"
          >
            <template #label>
              <div class="flex flex-col">
                <span>测试大纲分析</span>
                <span style="font-size: 12px; color: var(--color-text-3)">创建测试大纲和测试框架</span>
              </div>
            </template>
          </a-checkbox-group>
        </div> -->
      </a-form-item>
      <!-- 质量要求 -->
      <a-form-item field="qualityRequirement" label="质量要求" required>
        <a-textarea
          v-model="localFormData.qualityRequirement"
          placeholder="请输入质量要求"
          :auto-size="{ minRows: 4, maxRows: 6 }"
        />
      </a-form-item>
      <!-- 输出格式 -->
      <a-form-item field="outputFormat" label="输出格式" required>
        <a-radio-group v-model="localFormData.outputFormat">
          <a-radio value="xmind">xmind</a-radio>
          <a-radio value="excel">excel</a-radio>
        </a-radio-group>
      </a-form-item>
    </a-form>

    <!-- 底部按钮 -->
    <div class="action-buttons">
      <a-button type="primary" :disabled="!isFormValid" @click="handleNext">下一步</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { EvalTaskSaveDTO } from '@/apis/evaluation/task-types'
import { getAppList } from '@/apis/apps'

const props = defineProps({
  formData: {
    type: Object as () => EvalTaskSaveDTO,
    required: true
  }
})

const emit = defineEmits(['next', 'saveForm'])

const appListData = ref<any>([])
const selectAppItem = ref<any>({})
const selectAppId = ref<string>(undefined)

const localFormData = ref<EvalTaskSaveDTO>({
  taskName: '', // 任务名称
  appData: {}, // 评测对象
  intention: '', // 意图
  testMethod: '', // 分析方法
  outputFormat: 'xmind', // 输出格式
  qualityRequirement: '' // 质量要求
})

watchEffect(() => {
  if (props.formData) {
    localFormData.value = { ...localFormData.value, ...props.formData }
  }
})

const params = reactive<any>({
  page: '1',
  limit: '30',
  name: '',
  is_created_by_me: false
})

const getAppListFn = async () => {
  const res = await getAppList(params)
  appListData.value = res?.data || []
}

const selectApp = (record) => {
  selectAppItem.value = appListData.value?.find((item) => item.id === record.id)
  selectAppId.value = record.id
  localFormData.value.appData.appVersion = '1.0'
  localFormData.value.appData.appName = record.name
}
// 表单验证
const isFormValid = computed(() => {
  return (
    !!localFormData.value.taskName &&
    !!localFormData.value.intention &&
    !!localFormData.value.testMethod &&
    !!localFormData.value.outputFormat &&
    !!localFormData.value.qualityRequirement
  )
})

const handleNext = () => {
  if (isFormValid.value) {
    emit('saveForm', localFormData.value)
    emit('next')
  }
}

onMounted(async () => {
  await getAppListFn()
  if (props?.formData?.appData?.appId) {
    const record = appListData.value?.find((item) => item.id === props?.formData?.appData?.appId)
    selectApp(record)
  }
})
</script>

<style scoped lang="scss">
.select-object {
  width: 1000px;
  height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  .arco-form {
    flex: 1;
  }
}

/* 表单项样式 */
:deep(.arco-form-item-label-col) {
  label {
    color: rgba(0, 0, 0, 0.85);
  }
}

/* 评测对象容器 */
.evaluation-object-container {
  border: 1px solid #d0d1dd;
  border-radius: 8px;
  width: 100%;
  height: 252px;
  display: flex;
  &-left,
  &-right {
    width: 50%;
    padding: var(--padding);
    overflow-y: auto;
  }
  &-right {
    border-left: 1px solid #d0d1dd;
    flex: 1;
  }
  &-title {
    color: var(--color-text-3);
  }
  &-item {
    height: 58px;
    width: 100%;
    border-radius: 8px;
    margin-top: 10px;
    padding: 6px 10px;
    cursor: pointer;
    &-desc {
      color: var(--color-text-3);
    }
    &:hover {
      background-color: #f1f2f7;
    }
    &-active {
      background-color: #f1f2f7;
    }
  }
}

/* 质量要求文本框 */
:deep(.arco-textarea-wrapper) {
  border-color: #dbdbdb;
  border-radius: 8px;

  &:hover,
  &:focus-within {
    border-color: #5048f8;
  }
}

.action-buttons {
  text-align: right;
}
</style>
