<template>
  <a-form ref="formRef" :model="form" layout="vertical">
    <template v-if="promptConfig?.prompt_variables?.length">
      <a-form-item v-for="item in promptConfig.prompt_variables" :key="item.key" :label="item.name">
        <a-input
          v-if="item.type === 'string'"
          v-model="form[item.key]"
          :default-value="inputs[item.key]"
          :placeholder="`${item.name}${!item.required ? `可选` : ''}`"
          :max-length="item.max_length || 48"
          @change="
            (value) => {
              handleInputsChange({ ...inputs, [item.key]: value })
            }
          "
        />

        <a-select
          v-if="item.type === 'select'"
          v-model="form[item.key]"
          :default-value="inputs[item.key]"
          @change="
            (value) => {
              handleInputsChange({ ...inputs, [item.key]: value })
            }
          "
        >
          <a-option v-for="ele in item.options || []" :key="ele" :value="ele">{{ ele }}</a-option>
        </a-select>

        <a-textarea
          v-if="item.type === 'paragraph'"
          v-model="form[item.key]"
          :default-value="inputs[item.key]"
          :placeholder="`${item.name}${!item.required ? `可选` : ''}`"
          @change="
            (value) => {
              handleInputsChange({ ...inputs, [item.key]: value })
            }
          "
        />
        <a-input-number
          v-if="item.type === 'number'"
          v-model="form[item.key]"
          :default-value="inputs[item.key]"
          :placeholder="`${item.name}${!item.required ? `可选` : ''}`"
          @change="
            (value) => {
              handleInputsChange({ ...inputs, [item.key]: value })
            }
          "
        />
        <AiFileUpload
          v-if="item.type === 'files'"
          v-model="form[item.key]"
          :inputsKey="item.key"
          :method="item?.config?.allowed_file_upload_methods?.[0] || ''"
          @uploadFile="onUploadFile"
        />
        <AiFileUpload
          v-if="item.type === 'file'"
          v-model="form[item.key]"
          :multiple="false"
          :inputsKey="item.key"
          :method="item?.config?.allowed_file_upload_methods?.[0] || ''"
          @uploadFile="onUploadFile"
        />

        <AiFileUpload
          v-if="item.type === 'file-list'"
          v-model="form[item.key]"
          :inputsKey="item.key"
          :method="item?.config?.allowed_file_upload_methods?.[0] || ''"
          @uploadFile="onUploadFile"
        />
      </a-form-item>
    </template>
  </a-form>
</template>

<script setup lang="ts">
import { uploadFile } from '@/apis/workflow/share'
import type { PromptConfig } from '@/views/app/workflow/types/debug'
import type { VisionFile, VisionSettings } from '@/views/app/workflow/types/app'
import type { FormInstance } from '@arco-design/web-vue'

export type IRunOnceProps = {
  promptConfig: PromptConfig | null
  inputs: Record<string, any>
  visionConfig: VisionSettings
}

defineOptions({
  name: 'RunOnce'
})

const emits = defineEmits(['onSend', 'onInputsChange', 'onVisionFilesChange'])

const form = ref({})
const formRef = ref<FormInstance>()
const reset = () => {
  try {
    formRef.value?.resetFields()
    form.value = {}
  } catch (e) {}
}
const { promptConfig, inputs, visionConfig } = defineProps<IRunOnceProps>()

onMounted(() => {
  // console.log(promptConfig, '=========')
})

const onUploadFile = async (
  file: File,
  onProgress?: (percent: number) => void,
  key?: string,
  transfer_method?: string,
  callback?: (res) => void
) => {
  const response = await uploadFile(file, (percent) => {
    onProgress && onProgress(percent)
  })

  const newInputs = {
    ...inputs
  }
  if (key) {
    if (key == 'file') {
      newInputs[key] = {
        type: 'document',
        transfer_method: transfer_method,
        url: '',
        upload_file_id: response.id || ''
      }
    } else {
      newInputs[key] = [
        {
          type: 'document',
          transfer_method: transfer_method,
          url: '',
          upload_file_id: response.id || ''
        }
      ]
    }
  }
  if (callback) {
    callback(response)
  }
  emits('onInputsChange', newInputs)
}

const handleInputsChange = (newInputs: Record<string, any>) => {
  emits('onInputsChange', newInputs)
}
defineExpose({ reset })
</script>

<style scoped lang="scss"></style>
