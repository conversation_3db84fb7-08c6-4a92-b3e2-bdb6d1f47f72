<!--
  HTTP认证配置模态框
  配置API认证信息
-->
<template>
  <a-modal
    :visible="visible"
    title="认证配置"
    :width="500"
    @ok="handleSave"
    @cancel="handleCancel"
    @update:visible="$emit('update:visible', $event)"
  >
    <div class="auth-modal">
      <!--<a-form>-->
      <!--  <a-form-item ></a-form-item>-->
      <!--</a-form>-->
      <Field fieldTitle="鉴权类型">
        <a-radio-group v-model="authConfig.type" @change="handleAuthTypeChange2">
          <a-radio :value="AuthorizationType.none">无</a-radio>
          <a-radio :value="AuthorizationType.apiKey">API-Key</a-radio>
        </a-radio-group>
      </Field>
      <Field v-if="localAuth.type === 'api-key'" title="API 鉴权类型">
        <a-radio-group v-model="localAuth.config.type">
          <a-radio :value="APIType.basic">基础</a-radio>
          <a-radio :value="APIType.bearer">Bearer</a-radio>
          <a-radio :value="APIType.custom">自定义</a-radio>
        </a-radio-group>
      </Field>

      <!-- 认证类型选择 -->
      <div class="form-group">
        <label class="form-label">认证类型</label>
        <a-select v-model="localAuth.type" @change="handleAuthTypeChange">
          <a-option v-for="type in authTypes" :key="type.value" :value="type.value">
            {{ type.label }}
          </a-option>
        </a-select>
      </div>

      <!-- API Key认证 -->
      <div v-if="localAuth.type === 'api-key'" class="auth-config">
        <div class="form-group">
          <label class="form-label">API Key类型</label>
          <a-select v-model="apiKeyConfig.type">
            <a-option value="basic">Basic</a-option>
            <a-option value="bearer">Bearer</a-option>
            <a-option value="custom">自定义</a-option>
          </a-select>
        </div>

        <div class="form-group">
          <label class="form-label">API Key</label>
          <a-input v-model="apiKeyConfig.api_key" type="password" placeholder="请输入API Key" show-password />
        </div>

        <div v-if="apiKeyConfig.type === 'custom'" class="form-group">
          <label class="form-label">请求头名称</label>
          <a-input v-model="apiKeyConfig.header" placeholder="X-API-Key" />
        </div>

        <!-- 预览 -->
        <div class="auth-preview">
          <div class="preview-title">请求头预览</div>
          <div class="preview-content">
            <code>{{ authHeaderPreview }}</code>
          </div>
        </div>
      </div>

      <!-- Bearer Token认证 -->
      <div v-else-if="localAuth.type === 'bearer'" class="auth-config">
        <div class="form-group">
          <label class="form-label">Bearer Token</label>
          <a-input v-model="bearerToken" type="password" placeholder="请输入Bearer Token" show-password />
        </div>

        <!-- 预览 -->
        <div class="auth-preview">
          <div class="preview-title">请求头预览</div>
          <div class="preview-content">
            <code>Authorization: Bearer {{ bearerToken || '***' }}</code>
          </div>
        </div>
      </div>

      <!-- Basic认证 -->
      <div v-else-if="localAuth.type === 'basic'" class="auth-config">
        <div class="form-group">
          <label class="form-label">用户名</label>
          <a-input v-model="basicAuth.username" placeholder="请输入用户名" />
        </div>

        <div class="form-group">
          <label class="form-label">密码</label>
          <a-input v-model="basicAuth.password" type="password" placeholder="请输入密码" show-password />
        </div>

        <!-- 预览 -->
        <div class="auth-preview">
          <div class="preview-title">请求头预览</div>
          <div class="preview-content">
            <code>Authorization: Basic {{ basicAuthPreview }}</code>
          </div>
        </div>
      </div>

      <!-- 无认证 -->
      <div v-else-if="localAuth.type === 'no-auth'" class="auth-config">
        <div class="no-auth-tip">
          <icon-info-circle />
          <span>不使用任何认证方式</span>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
/**
 * HTTP认证配置模态框逻辑
 */
import { ref, computed, watch } from 'vue'
import { IconInfoCircle } from '@arco-design/web-vue/es/icon'
import { AuthorizationType, APIType } from '@/views/app/workflow/constant/common'
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'
// 认证类型选项
const authTypes = [
  { label: '无认证', value: 'no-auth' },
  { label: 'API Key', value: 'api-key' },
  { label: 'Bearer Token', value: 'bearer' },
  { label: 'Basic认证', value: 'basic' }
]

// 组件属性
interface Props {
  visible: boolean
  authorization: any
}

const props = defineProps<Props>()

// 事件定义
const emit = defineEmits<{
  'update:visible': [visible: boolean]
  update: [authorization: any]
}>()

// 响应式数据
const localAuth = ref<any>({ ...props.authorization })
const apiKeyConfig = ref<any>({
  type: 'basic',
  api_key: '',
  header: 'X-API-Key'
})
const bearerToken = ref('')
const basicAuth = ref({
  username: '',
  password: ''
})

// 计算属性
const authHeaderPreview = computed(() => {
  if (!apiKeyConfig.value.api_key) return '***'

  switch (apiKeyConfig.value.type) {
    case 'basic':
      return `Authorization: Basic ${btoa(apiKeyConfig.value.api_key + ':')}`
    case 'bearer':
      return `Authorization: Bearer ${apiKeyConfig.value.api_key}`
    case 'custom':
      return `${apiKeyConfig.value.header || 'X-API-Key'}: ${apiKeyConfig.value.api_key}`
    default:
      return '***'
  }
})

const basicAuthPreview = computed(() => {
  if (!basicAuth.value.username || !basicAuth.value.password) return '***'
  return btoa(`${basicAuth.value.username}:${basicAuth.value.password}`)
})

// 方法
const handleAuthTypeChange = () => {
  // 重置配置
  apiKeyConfig.value = {
    type: 'basic',
    api_key: '',
    header: 'X-API-Key'
  }
  bearerToken.value = ''
  basicAuth.value = {
    username: '',
    password: ''
  }
}

const handleSave = () => {
  let config: any | null = null

  switch (localAuth.value.type) {
    case 'api-key':
      config = { ...apiKeyConfig.value }
      break
    case 'bearer':
      config = {
        type: 'bearer',
        api_key: bearerToken.value
      }
      break
    case 'basic':
      config = {
        type: 'basic',
        api_key: btoa(`${basicAuth.value.username}:${basicAuth.value.password}`)
      }
      break
    default:
      config = null
  }

  const result: any = {
    type: localAuth.value.type,
    config
  }

  emit('update', result)
  emit('update:visible', false)
}

const handleCancel = () => {
  emit('update:visible', false)
}

// 初始化数据
const initData = () => {
  localAuth.value = { ...props.authorization }

  if (props.authorization.config) {
    const config = props.authorization.config

    switch (props.authorization.type) {
      case 'api-key':
        apiKeyConfig.value = { ...config }
        break
      case 'bearer':
        bearerToken.value = config.api_key || ''
        break
      case 'basic':
        try {
          const decoded = atob(config.api_key || '')
          const [username, password] = decoded.split(':')
          basicAuth.value = { username: username || '', password: password || '' }
        } catch {
          basicAuth.value = { username: '', password: '' }
        }
        break
    }
  }
}

// 监听props变化
watch(
  () => props.authorization,
  () => {
    initData()
  },
  { deep: true, immediate: true }
)

const authConfig = ref({
  type: AuthorizationType.none
})
const handleAuthTypeChange2 = () => {
  if (1) {
  }
}
</script>

<style scoped lang="scss">
.auth-modal {
  .form-group {
    margin-bottom: 16px;

    .form-label {
      display: block;
      font-size: 12px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 6px;
    }
  }

  .auth-config {
    margin-top: 16px;
  }

  .auth-preview {
    margin-top: 16px;
    padding: 12px;
    background: #f9fafb;
    border-radius: 6px;
    border: 1px solid #e5e7eb;

    .preview-title {
      font-size: 12px;
      font-weight: 500;
      color: #374151;
      margin-bottom: 8px;
    }

    .preview-content {
      code {
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 11px;
        color: #6b7280;
        background: white;
        padding: 4px 8px;
        border-radius: 4px;
        border: 1px solid #d1d5db;
        display: block;
        word-break: break-all;
      }
    }
  }

  .no-auth-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    border-radius: 6px;
    color: #0369a1;
    font-size: 12px;

    .arco-icon {
      width: 16px;
      height: 16px;
    }
  }
}
</style>
