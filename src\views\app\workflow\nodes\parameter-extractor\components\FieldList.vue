<template>
  <div class="space-y-1">
    <!--模型也拿出去吧。。。。-->
    <Field :fieldTitle="'模型'">
      <!-- trigger="click"-->
      <a-popover
        position="left"
        trigger="click"
        :content-style="{ width: '320px' }"
        @popup-visible-change="handleChangeVisible"
      >
        <a-select
          :model-value="nodeInfo.model.name"
          :popup-visible="false"
          @update:model-value="
            (val: string) => $emit('update:nodeInfo', { ...nodeInfo, model: { ...nodeInfo.model, name: val } })
          "
        />
        <template #content>
          <Field fieldTitle="模型">
            <a-select
              v-model="selectedModel"
              placeholder="请选择"
              allow-search
              value-key="model"
              @change="handleChangeModel"
            >
              <a-optgroup v-for="(group, index) in modelObj.llmList" :key="index" :label="renderI18nName(group.label)">
                <a-option v-for="model in group.models" :key="model.model" :label="model.model" :value="model">
                  <a-space>
                    <a-avatar :size="24">
                      <img
                        :src="
                          DOMAIN_NAME +
                          (typeof group.icon_small === 'object' ? renderI18nName(group.icon_small) : group.icon_small)
                        "
                        alt=""
                      />
                    </a-avatar>
                    {{ model.model }}
                    <a-tag>{{ modelTypeFormat(model.model_type) }}</a-tag>
                    <a-tag v-if="model.model_properties.context_size">
                      {{ sizeFormat(model.model_properties.context_size as number) }}
                    </a-tag>
                  </a-space>
                </a-option>
              </a-optgroup>
            </a-select>
          </Field>
          <Field fieldTitle="参数">
            <LlmParams :paramsList="paramsList" :nodeInfo="nodeInfo" />
          </Field>
        </template>
      </a-popover>
    </Field>
    <Field fieldTitle="输入变量">
      <VariableSelector v-model:value-selector="nodeInfo.query" :node-id="nodeId" />
    </Field>

    <a-divider style="margin: 20px 0" />
    <!--视觉-->
    <ConfigVision :nodeInfo="nodeInfo" />
    <a-divider style="margin: 20px 0" />
    <ExtractParameters :nodeInfo="nodeInfo" />
    <RichText
      :nodeInfo="nodeInfo"
      :varString="nodeInfo.instruction"
      @handleInputVar="(val) => handleInputVar(nodeInfo, val)"
    />
    <a-divider style="margin: 20px 0" />
    <Field fieldTitle="高级设置" />
    <!--输出变量-->
    <a-divider style="margin: 20px 0" />
    <Field v-if="nodeInfo.parameters.length > 0" fieldTitle="输出变量">
      <OutputVar :nodeInfo="nodeInfo" />
    </Field>

    <!-- <Field fieldTitle="下一步" >
        </Field> -->
  </div>
</template>

<script setup lang="ts">
import { modelTypeFormat, renderI18nName, sizeFormat } from '@/views/app/workflow/utils/model'
import { omit } from 'lodash-es'
import { DOMAIN_NAME } from '@/views/app/workflow/constant/common'
import { getModelTypesHttp, getParamsRulesHttp } from '@/apis/model-mgmt'
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'
import ExtractParameters from './ExtractParameters.vue'
import VariableSelector from '@/views/app/workflow/components/variable-selector/VariableSelector.vue'
import LlmParams from '@/views/app/workflow/nodes/llm/components/LlmParams.vue'
import RichText from '@/views/app/workflow/nodes/http/components/rich-text/index.vue'
import ConfigVision from '@/views/app/workflow/nodes/http/components/ConfigVision.vue'
import OutputVar from './outputVar.vue'

// 定义Model接口
interface Model {
  model: string
  model_type: string
  model_properties: {
    context_size?: number
    [key: string]: any
  }
  provider: any
}

// 定义ModelProvider接口
interface ModelProvider {
  label: {
    zh_Hans: string
    en_US: string
  }
  icon_small: string
  provider: string
  models: Model[]
}

interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
}

const props = defineProps<{
  list: FieldType[]
  isChatMode?: false
  nodeInfo?: any
  nodeId?: string
}>()

const emit = defineEmits<{
  (e: 'update:nodeInfo', value: any): void
}>()

const nodeInfo = computed(() => props.nodeInfo || {})
const selectedModel = ref<any>({})
const modelObj = reactive({
  llmList: [] as ModelProvider[]
})
const paramsList = ref([])

const getModelTypes = async (modelType: string) => {
  const res = await getModelTypesHttp(modelType)
  const result = res.data || []
  result.forEach((item: any) => {
    item.models.forEach((model: any) => {
      model.provider = {
        ...omit(item, 'models')
      }
    })
  })
  if (modelType === 'llm') {
    modelObj.llmList = res.data || []
  }
}
getModelTypes('llm')

const getParamsList = async () => {
  const res = await getParamsRulesHttp(nodeInfo.value.model.provider, nodeInfo.value.model.name)
  console.log('模型的参数配置res：', res)
  paramsList.value = res.data || []
}
const handleChangeVisible = () => {
  const nodeModel = nodeInfo.value.model
  const provider = modelObj.llmList.filter((item: ModelProvider) => {
    return item.provider === nodeModel.provider
  })
  if (provider.length > 0) {
    selectedModel.value = provider[0].models.find((v: any) => {
      return v.model === nodeModel.name
    })
  }
  getParamsList()
}

const handleChangeModel = (item: any) => {
  const updatedNodeInfo = {
    ...nodeInfo.value,
    model: {
      ...nodeInfo.value.model,
      name: item.model,
      provider: item.provider.provider
    }
  }
  emit('update:nodeInfo', updatedNodeInfo)
  getParamsList()
}

// 富文本输入更新
const handleInputVar = (item: any, val: string) => {
  const updatedItem = { ...item, instruction: val }
  emit('update:nodeInfo', updatedItem)
}
onMounted(() => {
  console.log(
    props.list.map((e) => {
      console.log(e)
    })
  )
})
</script>
<style scoped lang="scss"></style>
