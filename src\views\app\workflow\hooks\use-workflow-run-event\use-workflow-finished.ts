import { useWorkflowStore } from '@/stores'
import { getFilesInLogs } from '../../utils/file-utils'
import type { WorkflowFinishedResponse } from '@/views/app/workflow/types/workflow'

export const useWorkflowFinished = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowFinished = (params: WorkflowFinishedResponse) => {
    const { data } = params
    const { workflowRunningData, setWorkflowRunningData } = workflowStore

    const isStringOutput =
      data.outputs &&
      Object.keys(data.outputs).length === 1 &&
      typeof data.outputs[Object.keys(data.outputs)[0]] === 'string'
    const workflowData = workflowRunningData

    workflowData.result = {
      ...workflowData.result,
      ...data,
      files: getFilesInLogs(data.outputs)
    } as any
    if (isStringOutput) {
      workflowData.resultTabActive = true
      workflowData.resultText = data.outputs[Object.keys(data.outputs)[0]]
    }

    setWorkflowRunningData(workflowData)
  }

  return {
    handleWorkflowFinished
  }
}
