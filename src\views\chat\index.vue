<template>
  <a-layout class="layout layout-default" :class="{ mobile: isMobile }">
    <div
      v-if="isDesktop"
      class="asider"
      :class="{ 'app-menu-dark': appStore.menuDark }"
      :style="appStore.menuDark ? appStore.themeCSSVar : undefined"
    >
      <a-layout-sider
        class="chat-list"
        collapsible
        breakpoint="xl"
        hide-trigger
        :width="260"
        :collapsed="appStore.menuCollapse"
        @collapse="handleCollapse"
      >
        <a-space>
          <Logo :collapsed="appStore.menuCollapse" />
        </a-space>
        <div class="search">
          <a-input v-if="!appStore.menuCollapse" v-model="searchKey" placeholder="搜索" allow-clear>
            <template #prefix><icon-search /></template>
          </a-input>
          <a-tooltip content="新建对话">
            <a-button type="primary">
              <template #icon><icon-plus /></template>
            </a-button>
          </a-tooltip>
        </div>
        <a-scrollbar outer-class="menu-scroll-view" style="height: 100%; overflow: auto">
          <div v-if="!appStore.menuCollapse" class="tree-wrapper">
            <div class="tree">
              <a-tree
                :data="treeData as unknown as TreeNodeData[]"
                :field-names="{ key: 'id' }"
                block-node
                :selected-keys="selectedKeys"
                @select="select"
              >
                <template #title="node">
                  <a-typography-paragraph
                    :ellipsis="{
                      rows: 1,
                      showTooltip: true,
                      css: true
                    }"
                  >
                    {{ node.name }}
                  </a-typography-paragraph>
                </template>
                <template #extra="node">
                  <a-trigger
                    trigger="click"
                    align-point
                    animation-name="slide-dynamic-origin"
                    auto-fit-transform-origin
                    position="bl"
                    scroll-to-close
                  >
                    <icon-more class="action" />
                    <template #content>
                      <RightMenu :data="node" @on-menu-item-click="onMenuItemClick" />
                    </template>
                  </a-trigger>
                </template>
              </a-tree>
            </div>
          </div>
        </a-scrollbar>
      </a-layout-sider>
    </div>
    <a-layout class="layout-right">
      <a-layout-header style="padding-left: 20px">
        <a-button size="mini" class="ai_hover_btn menu-fold-btn" @click="onCollapse">
          <template #icon>
            <icon-menu-fold v-if="!appStore.menuCollapse" :size="18" :stroke-width="3" />
            <icon-menu-unfold v-else :size="18" :stroke-width="3" />
          </template>
        </a-button>
      </a-layout-header>
      <Chat />
      <ChatUpdateModal ref="ChatUpdateModalRef" @save-success="search" />
    </a-layout>
  </a-layout>
</template>

<script lang="ts" setup>
import { defineComponent, ref } from 'vue'
import type { TreeNodeData } from '@arco-design/web-vue'
import { Message, Modal } from '@arco-design/web-vue'
import { mapTree } from 'xe-utils'
import Logo from './components/Logo.vue'

import RightMenu from './components/RightMenu.vue'
import ChatUpdateModal from './components/ChatUpdateModal.vue'
import Chat from './components/Chat.vue'
import { useAppStore } from '@/stores'

import { useDevice } from '@/hooks'

const { isDesktop } = useDevice()
const appStore = useAppStore()

const onCollapse = () => {
  appStore.menuCollapse = !appStore.menuCollapse
}
const { isMobile } = useDevice()
const handleCollapse = (isCollapsed: boolean) => {
  appStore.menuCollapse = isCollapsed
}
const ChatUpdateModalRef = ref<InstanceType<typeof ChatUpdateModal>>()
export interface TreeItem {
  name: string
  code: string
  id: number
  children?: TreeItem[]
  popupVisible?: boolean
}
const loading = ref(false)
const searchKey = ref('')
const selectedKeys = ref()
const dataList = ref<TreeItem[]>([])

const search = (keyword: string) => {
  const loop = (data: TreeItem[]) => {
    const result = [] as TreeItem[]
    data.forEach((item: TreeItem) => {
      if (item.name?.toLowerCase().includes(keyword) || item.code?.toLowerCase().includes(keyword)) {
        result.push({ ...item })
      }
    })
    return result
  }
  return loop(dataList.value)
}
const treeData = computed(() => {
  if (!searchKey.value) return dataList.value
  return search(searchKey.value.toLowerCase())
})
const select = (keys: Array<any>) => {
  if (selectedKeys.value && selectedKeys.value[0] === keys[0]) {
    return
  }
  selectedKeys.value = keys
}
// 查询树列表
const getTreeData = async () => {
  try {
    loading.value = true
    const data = [
      { name: 'JavaScript代码安全漏洞分析与修复', code: '001', id: 1 },
      { name: 'JavaScript代码安全漏洞分析与修复', code: '002', id: 2 }
    ]
    dataList.value = mapTree(data, (i) => ({
      ...i,
      popupVisible: false
    }))
    await nextTick(() => {
      select([dataList.value[0]?.id])
    })
  } finally {
    loading.value = false
  }
}
interface RoleResp {
  name: string
  code: string
  id: number
  children?: TreeItem[]
}
// 点击菜单项
const onMenuItemClick = (mode: string, node: RoleResp) => {
  if (mode === 'update') {
    ChatUpdateModalRef.value?.open(node)
  } else if (mode === 'delete') {
    Modal.warning({
      title: '提示',
      content: `是否确定删除？`,
      hideCancel: false,
      okButtonProps: { status: 'danger' },
      onBeforeOk: async () => {
        try {
          Message.success('删除成功')
        } catch (error) {
          return false
        }
      }
    })
  }
}

onMounted(() => {
  getTreeData()
})
</script>

<style scoped lang="scss">
.layout-default {
  flex-direction: row;
  &-right {
    overflow: hidden;
  }
}
:deep(.arco-menu.arco-menu-vertical.arco-menu-collapsed) {
  .arco-menu-icon {
    margin-right: 0;
    padding: 10px 0;
  }

  .arco-menu-has-icon {
    padding: 0;
    justify-content: center;
  }

  .arco-menu-title {
    display: none;
  }
}

:deep(.arco-layout-sider-children) {
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.asider {
  z-index: 1000;
  display: flex;
  flex-direction: column;
  border-right: 1px solid var(--color-border-2);
  box-sizing: border-box;
  color: var(--color-text-1);
  background-color: rgb(var(--primary-1) 0.9);

  .menu-scroll-view {
    flex: 1;
    overflow: hidden;
  }
  .chat-list {
    padding: 12px 12px 60px 12px;
    flex: 1;
    overflow: hidden;
    background-color: inherit;
    .search {
      display: flex;
      justify-content: flex-start;
      margin: 20px 0 10px;
      .arco-input-wrapper {
        margin-right: 8px;
      }
      .arco-btn {
        padding: 0 15px;
      }
    }
  }
}
.menu-fold-btn {
  background-color: var(--color-secondary-hover) !important;
  flex-shrink: 0;
}
.layout {
  height: 100%;
  overflow: hidden;
  background: var(var(--primary-1));
  border: 1px solid var(--color-border);
}
.layout :deep(.arco-layout-sider) .logo {
  height: 32px;
  margin: 12px 8px;
  background: rgba(255, 255, 255, 0.2);
}
.layout :deep(.arco-layout-sider-light) .logo {
  background: var(--color-fill-2);
}
.layout :deep(.arco-layout-header) {
  height: 64px;
  line-height: 64px;
  background: var(--color-bg-3);
}
.layout-right {
  background: var(--color-bg-3);
}

:deep(.arco-tree-node) {
  line-height: normal;
  border-radius: var(--border-radius-medium);
  margin: 5px 0;
  .action {
    opacity: 0;
    margin-right: 8px;
    padding: 4px;
    transition: all 0.25s;
    border-radius: 8px;

    &:hover {
      background-color: var(--color-bg-1);
    }
  }
  &:hover {
    background-color: var(--color-secondary-hover);
    .action {
      opacity: 1;
    }
  }

  .arco-tree-node-switcher {
    width: 0;
    margin-right: 0;
  }

  .arco-tree-node-title {
    &:hover {
      background-color: transparent;
    }
  }

  .arco-tree-node-title-text {
    width: 100%;
    white-space: normal;
    overflow-wrap: anywhere;
  }
}

:deep(.arco-tree-node-selected) {
  font-weight: bold;
  background-color: rgba(var(--primary-6), 0.1);
  &:hover {
    background-color: rgba(var(--primary-6), 0.1);
  }
  .arco-typography {
    color: rgb(var(--primary-6));
  }
  .action {
    opacity: 1;
  }
}
.tree-wrapper {
  flex: 1;
  overflow: hidden;
  position: relative;
  height: 100%;
  .tree {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: auto;
  }
}
</style>
