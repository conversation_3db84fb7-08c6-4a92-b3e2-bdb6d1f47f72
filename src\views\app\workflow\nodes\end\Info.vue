<template>
  <div class="field-info">
    <div class="flex items-center justify-between mb-1">
      <span>输出变量</span>
      <a-button style="width: 24px; height: 24px" type="text" @click="onAdd">
        <template #icon>
          <icon-plus />
        </template>
      </a-button>
    </div>
    <Fieldlist ref="FieldlistRef" :nodeId="nodeId" :list="fieldList" />
  </div>
</template>

<script setup lang="ts">
import Fieldlist from './components/FieldList.vue'
const props = withDefaults(
  defineProps<{
    node: any
    nodeId: string
  }>(),
  {
    node: {},
    nodeId: ''
  }
)
console.log(props.node)
interface FieldType {
  value_selector: string[]
  variable: string
  selector: string
}
const FieldlistRef = ref<InstanceType<typeof Fieldlist>>()

const fieldList = ref<FieldType[]>(props.node.outputs)
// 新增
const onAdd = () => {
  fieldList.value.push({
    value_selector: [],
    variable: '',
    selector: ''
  })
}
</script>
<style scoped lang="scss">
.field-info {
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-1);
}
</style>
