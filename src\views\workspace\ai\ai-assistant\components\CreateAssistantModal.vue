<template>
  <a-modal
    :visible="modelValue"
    title="创建智能体"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @update:visible="$emit('update:modelValue', $event)"
    @close="handleCancel"
  >
    <div class="template-bg p-4 rounded-lg mb-4">
      <div class="flex items-center mb-2">
        <div class="template-icon flex items-center justify-center mr-3">
          <icon-plus class="text-gray-500" />
        </div>
        <span class="text-base font-medium">选择应用模版</span>
      </div>
      <p class="text-sm text-gray-600 ml-10">使用该模版创建应用到对应的空间</p>
    </div>
    <AiForm ref="formRef" v-model="form" :columns="columns" />
    <template #footer>
      <div class="flex justify-end">
        <a-button class="mr-2" @click="handleCancel">取消</a-button>
        <a-button type="primary" :loading="submitting" @click="handleSubmit">创建</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, h } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { createAssistant } from '@/apis/ai-assistant'
import { type ColumnItem } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'

// 定义props和emit
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue', 'cancel', 'submit'])

// 窗口大小
const { width } = useWindowSize()

// 表单引用
const formRef = ref()

// 提交状态
const submitting = ref(false)

// 类型选项
const typeOptions = [
  { label: '测试用例生成助手', value: 'test-case' },
  { label: '智能问答助手', value: 'qa' },
  { label: '代码审查助手', value: 'code-review' },
  { label: '文章写作助手', value: 'article' }
]

// 表单数据
const [form, resetForm] = useResetReactive({
  name: '测试用例生成助手（副本）',
  type: 'test-case',
  version: 'V1.2',
  analysis: {
    scenarios: '自动生成测试用例，提取接口信息，测试流程设计，测试覆盖率分析',
    keywords: ['测试用例', '接口测试', '单元测试', '覆盖率分析'],
    testResult: '测试用例生成准确率达到90%，可满足基本测试需求'
  }
})

// 版本选项
const versionOptions = [
  { label: 'V1.2', value: 'V1.2' },
  { label: 'V1.8', value: 'V1.8' },
  { label: 'V2.4', value: 'V2.4' }
]

// 表单列定义
const columns = reactive<ColumnItem[]>([
  {
    label: '应用名称',
    field: 'name',
    type: 'input',
    span: 24,
    required: true,
    props: {
      placeholder: '请输入应用生成名称（副本）',
      maxLength: 50
    }
  },
  {
    label: '应用类型',
    field: 'type',
    type: 'select',
    span: 24,
    required: true,
    props: {
      placeholder: '请选择',
      options: typeOptions
    }
  },
  {
    label: '智能体版本',
    field: 'version',
    type: 'select',
    span: 24,
    required: true,
    props: {
      placeholder: '请选择',
      options: versionOptions
    }
  },
  {
    label: '应用分析',
    field: 'analysis',
    type: 'group-title',
    span: 24,
    formItemSlots: {
      extra: () =>
        h('div', { class: 'analysis-container bg-gray-50 p-4 rounded-md' }, [
          h('div', { class: 'analysis-header text-base font-medium mb-2' }, '应用分析'),
          h('div', { class: 'analysis-item mb-3' }, [
            h('div', { class: 'text-gray-600 mb-1' }, '适用场景：'),
            h('div', null, form.analysis?.scenarios || '无')
          ]),
          h('div', { class: 'analysis-item mb-3' }, [
            h('div', { class: 'text-gray-600 mb-1' }, '提取关键词：'),
            h('div', null, [
              ...(form.analysis?.keywords || []).map((keyword, index) =>
                h('a-tag', { key: index, class: 'mr-2 mb-2' }, keyword)
              )
            ])
          ]),
          h('div', { class: 'analysis-item' }, [
            h('div', { class: 'text-gray-600 mb-1' }, '测试结果：'),
            h('div', null, form.analysis?.testResult || '无')
          ])
        ])
    }
  }
])

// 取消处理
const handleCancel = () => {
  emit('update:modelValue', false)
  emit('cancel')
  resetForm()
}

// 提交处理
const handleSubmit = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return

    submitting.value = true
    await createAssistant(form)
    Message.success('创建成功')
    emit('update:modelValue', false)
    emit('submit', { ...form })
    resetForm()
  } catch (error) {
    console.error('创建助手失败', error)
    Message.error('创建助手失败，请稍后重试')
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.analysis-container {
  border: 1px solid #f0f0f0;
}

.template-bg {
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.76), rgba(255, 255, 255, 0.8));
  border: 1px solid #d9d9d9;
  border-radius: 8px;
}

.template-icon {
  width: 33px;
  height: 33px;
  border-radius: 50%;
  background-color: #f2f3f5;
}

:deep(.arco-modal-header) {
  border-bottom: none;
  padding: 16px 20px;
}

:deep(.arco-modal-body) {
  padding: 0 20px 20px;
}

:deep(.arco-modal-footer) {
  border-top: none;
  padding: 0 20px 20px;
}

:deep(.arco-form-item-label-col) {
  font-weight: normal;
}
</style>
