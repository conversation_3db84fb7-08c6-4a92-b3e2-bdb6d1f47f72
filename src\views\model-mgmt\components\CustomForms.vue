<template>
  <a-form ref="formRef" :model="localForm" :layout="'vertical'">
    <template v-for="(formItem, formIndex) in formSchemas" :key="formIndex">
      <a-form-item
        v-if="formItem.show_on.length == 0 || formItem.show_on.every((v) => form[v.variable] == v.value)"
        :formItem="formItem"
        :label="renderI18nName(formItem.label)"
        :field="formItem.variable"
        :required="formItem.required"
        :rules="[{ required: formItem.required, message: `${renderI18nName(formItem.label)}是必填项` }]"
        :validate-trigger="['change', 'input']"
      >
        <template v-if="formItem.type == FormTypeEnum.textInput">
          <a-input
            v-model="localForm[formItem.variable]"
            :placeholder="renderI18nName(formItem.placeholder)"
            autocomplete="new-password"
          />
        </template>
        <template v-if="formItem.type == FormTypeEnum.textNumber">
          <a-input-number v-model="localForm[formItem.variable]" :placeholder="renderI18nName(formItem.placeholder)" />
        </template>
        <template v-if="formItem.type == FormTypeEnum.secretInput">
          <a-input-password
            v-model="localForm[formItem.variable]"
            :placeholder="renderI18nName(formItem.placeholder)"
            :invisible-button="false"
            autocomplete="new-password"
          />
        </template>
        <template v-else-if="formItem.type == FormTypeEnum.select">
          <a-select
            v-model="localForm[formItem.variable]"
            :placeholder="renderI18nName(formItem.placeholder)"
            allow-clear
          >
            <a-option v-for="option in formItem.options" :key="option.value" :value="option.value">
              {{ renderI18nName(option.label) }}
            </a-option>
          </a-select>
        </template>
        <template v-else-if="formItem.type == FormTypeEnum.radio">
          <a-radio-group v-model="localForm[formItem.variable]">
            <a-radio v-for="radio in formItem.options" :key="radio.value" :value="radio.value">
              {{ renderI18nName(radio.label) }}
            </a-radio>
          </a-radio-group>
        </template>
      </a-form-item>
    </template>
  </a-form>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'

defineOptions({ name: 'CustomForms' })
import { renderI18nName } from '@/views/app/workflow/utils/model'
import { FormTypeEnum } from '@/apis/model-mgmt/type'

const props = defineProps<{
  // providerItem: Object,
  formSchemas: any // form结构
  // pageMode: string, // add| edit
  form: Object // 表单form的默认值
}>()
const formRef = ref()
const localForm = reactive({ ...props.form })

// 监听 props.form 的变化，同步到 localForm
watch(
  () => props.form,
  (newVal) => {
    Object.assign(localForm, newVal)
  },
  { deep: true }
)

const validForm = async () => {
  const valid = await formRef.value.validate()
  return valid
}

defineExpose({
  localForm,
  formRef,
  validForm
})
</script>

<style scoped lang="scss"></style>
