import http from '@/utils/http'
import type { DefaultFormValues } from '@/apis/model-mgmt/type.ts'

const BASE_URL = 'console/api'
// 获取模型List（已配置和未配置的）
// http://jettodify.jettech.com/console/api/workspaces/current/model-providers  get 参数为空
/**
 * 获取模型List（已配置和未配置的）
 * http://jettodify.jettech.com/console/api/workspaces/current/model-providers  get 参数为空
 * @param params
 */
export const getModelProvidersListHttp = () => {
  return http.get<any>(`${BASE_URL}/workspaces/current/model-providers`)
}

// 查询model下的细分模型
// http://jettodify.jettech.com/console/api/workspaces/current/model-providers/langgenius/tongyi/tongyi/models get 路径参数
export const getSubProvidersListHttp = (provider: any) => {
  return http.get<any>(`${BASE_URL}/workspaces/current/model-providers/${provider}/models`)
}

// 配置embedding：
// http://jettodify.jettech.com/console/api/workspaces/current/models/model-types/text-embedding get kong
// 配置rerank模型
// http://jettodify.jettech.com/console/api/workspaces/current/models/model-types/rerank get kong
// 配置语音转文本：
// http://jettodify.jettech.com/console/api/workspaces/current/models/model-types/speech2text
// 配置文本转语音：
// http://jettodify.jettech.com/console/api/workspaces/current/models/model-types/tts

export const getModelTypesHttp = (modelType: string) => {
  return http.get<any>(`${BASE_URL}/workspaces/current/models/model-types/${modelType}`)
}

// 默认配置：
// http://jettodify.jettech.com/console/api/workspaces/current/default-model?model_type=llm  get
export const getModelDefaultHttp = (modelType: string) => {
  return http.get<any>(
    `${BASE_URL}/workspaces/current/default-model?model_type=${modelType}`
    // params
  )
}

/**
 * 修改系统模型配置
 * 入参：
 * {
 *  "model_settings": [{
 *    "model_type": "llm",
 *    "provider": "langgenius/tongyi/tongyi",
 *    "model": "qwen2.5-72b-instruct"
 *  }, {
 *    "model_type": "text-embedding",
 *    "provider": "langgenius/tongyi/tongyi",
 *    "model": "text-embedding-v3"
 *  }, {
 *    "model_type": "rerank",
 *    "provider": "langgenius/tongyi/tongyi",
 *    "model": "gte-rerank"
 *  }, {
 *    "model_type": "speech2text"
 *  }, {
 *    "model_type": "tts",
 *    "provider": "langgenius/tongyi/tongyi",
 *    "model": "tts-1"
 *  }]
 * }
 */
// http://jettodify.jettech.com/console/api/workspaces/current/default-model
export const setModelHttp = (params: any) => {
  return http.post<any>(`${BASE_URL}/workspaces/current/default-model`, params)
}

// 启用模型
// http://jettodify.jettech.com/console/api/workspaces/current/model-providers/langgenius/tongyi/tongyi/models/enable
// patch
// {"model":"deepseek-r1-distill-qwen-14b","model_type":"llm"}
// 禁用模型：
// http://jettodify.jettech.com/console/api/workspaces/current/model-providers/langgenius/tongyi/tongyi/models/disable
// patch
// {"model":"deepseek-r1-distill-qwen-14b","model_type":"llm"}

export const changeModelStatusHttp = (params: any, provider: string, status: string) => {
  return http.patch<any>(`${BASE_URL}/workspaces/current/model-providers/${provider}/models/${status}`, params)
}

// 添加模型：
// http://jettodify.jettech.com/console/api/workspaces/current/model-providers/langgenius/tongyi/tongyi/models
// post {"model":"4","model_type":"llm","credentials":{"context_size":"4096","max_tokens":"4096","function_calling_type":"no_call","dashscope_api_key":"5"},"load_balancing":{"enabled":false,"configs":[]}}
export const addModelHttp = (provider, params: any) => {
  return http.post<any>(`${BASE_URL}/workspaces/current/model-providers/${provider}/models`, params)
}

// 编辑时候，查看模型的值
// http://jettodify.jettech.com/console/api/workspaces/current/model-providers/langgenius/xinference/xinference/models/credentials?model=Qwen2.5-VL-32B-Instruct&model_type=llm
export const getModelDefaultValueHttp = (provider: string, model: any, model_type: string) => {
  return http.get<DefaultFormValues>(
    `${BASE_URL}/workspaces/current/model-providers/${provider}/models/credentials?model=${model}&model_type=${model_type}`
  )
}

// 查询api-key的默认值
// http://jettodify.jettech.com:8080/console/api/workspaces/current/model-providers/langgenius/tongyi/tongyi/credentials
export const getApiKeyDefaultHttp = (provider: string) => {
  return http.get(`${BASE_URL}/workspaces/current/model-providers/${provider}/credentials`)
}

// 保存api-key
// http://jettodify.jettech.com:8080/console/api/workspaces/current/model-providers/langgenius/tongyi/tongyi
// post
// {"config_from":"predefined-model","credentials":{"dashscope_api_key":"[__HIDDEN__]"},"load_balancing":{"enabled":false,"configs":[]}}
export const saveApiKeyHttp = (provider: string, params: any) => {
  return http.post(`${BASE_URL}/workspaces/current/model-providers/${provider}`, params)
}

// 删除apikey
// http://jettodify.jettech.com:8080/console/api/workspaces/current/model-providers/langgenius/wenxin/wenxin
// DELETE，空参
export const deleteApiKeyHttp = (provider: string) => {
  return http.del(`${BASE_URL}/workspaces/current/model-providers/${provider}`)
}

/**
 * llm的请求，后续迁移走
 */

export const getParamsRulesHttp = (provider: string, model: string) => {
  return http.get(`${BASE_URL}/workspaces/current/model-providers/${provider}/models/parameter-rules?model=${model}`)
}
