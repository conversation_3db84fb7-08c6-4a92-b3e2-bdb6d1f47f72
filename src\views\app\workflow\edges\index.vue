<template>
  <BaseEdge
    :id="props.id"
    :style="style"
    :path="path[0]"
    :marker-end="props.markerEnd"
    :class="{ hovered: edgeData.hover }"
    class="custom-line"
  />
  <EdgeLabelRenderer width="100%" class="edge-label-box">
    <div
      :style="{
        pointerEvents: 'all',
        position: 'absolute',
        transform: `translate(-50%, -50%) translate(${path[1]}px,${path[2]}px)`,
        zIndex: edge.zIndex
      }"
      class="add-node-btn-box"
    >
      <!-- <NodeList class="custom-node-add" :popoverInstance="true" :nodeId="props.id" :nodeProps="props">
        <icon-plus :style="{ pointerEvents: 'none' }" />
      </NodeList> -->
    </div>
  </EdgeLabelRenderer>
</template>

<script setup lang="ts">
import { BaseEdge, EdgeLabelRenderer, type EdgeProps, getBezierPath, useEdge } from '@vue-flow/core'
// import NodeList from '../nodes/node-list.vue'

interface Props extends EdgeProps {
  // appId: string;
  // disabled: boolean;
  // mode: 'workflow';
}

const props = withDefaults(defineProps<Props>(), {
  style: void 0,
  disabled: false
})

const { edge } = useEdge()
const showPopover = ref(false)

const edgeData = computed(() => edge.data)

const path = computed(() => getBezierPath(props))
const style = computed(() => {
  return props.style || { zIndex: edge.zIndex || 0 }
})
</script>

<style lang="less">
.vue-flow__edge {
  .custom-line {
    stroke: #838d9b;
    stroke-width: 2;

    &.hovered {
      stroke: #838d9b !important;
    }
  }

  &.selected {
    .custom-line {
      stroke: rgb(var(--primary-6)) !important;
    }
  }
}

.vue-flow__edge-labels {
  .add-node-btn {
    box-sizing: border-box;
    opacity: 0;
    z-index: -1;
    color: '';
    border: 0;

    &.show-add-node-btn {
      display: block;
      opacity: 1;
      z-index: 99;
    }

    &:hover,
    &:active {
      .add-node-btn-icon {
        transform: scale(1.5);
        transform-origin: center;
        box-shadow: inset 0 0 4px 4px;
      }
    }

    .add-node-btn-icon {
      transition: all 0.2s;
      border-radius: 50%;
      cursor: pointer;
    }
  }
}
</style>
