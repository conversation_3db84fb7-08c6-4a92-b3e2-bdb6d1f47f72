<template>
  <div>
    <a-select v-model="selectedModel" placeholder="请选择" allow-search value-key="model" @change="handleChangeModel">
      <!--<template #label="{ data }">
        <span>
          <img :src="renderI18nName(data?.value.provider.provider.icon_small)" alt="" />{{ data?.label }}</span>
      </template>-->
      <!--group-->
      <a-optgroup v-for="(group, index) in textGenerationModelList" :key="index" :label="renderI18nName(group.label)">
        <template #label>
          <a-typography-title :heading="6">{{ renderI18nName(group.label) }}</a-typography-title>
        </template>
        <!--option：绑定的是item对象-->
        <a-option
          v-for="model in group.models"
          :key="model.model"
          :label="model.model"
          :value="model"
          :class="{ active: model.model === selectedModel.model }"
        >
          <a-space fill>
            <!--模型供应商的logo-->
            <a-avatar :size="24">
              <img :src="renderI18nName(group.icon_small)" alt="" />
            </a-avatar>
            <!--模型名称-->
            {{ model.model }}
            <a-tag>{{ modelTypeFormat(model.model_type) }}</a-tag>
            <!--模型大小-->
            <a-tag v-if="model.model_properties.context_size">
              {{ sizeFormat(model.model_properties.context_size as number) }}
            </a-tag>
            <!--是否选中-->
            <icon-check v-if="model.model === selectedModel.model" class="option-check" />
          </a-space>
        </a-option>
      </a-optgroup>
    </a-select>
  </div>
</template>
<script setup lang="ts">
import { modelTypeFormat, renderI18nName, sizeFormat } from '@/views/app/workflow/utils/model'
import { useProviderStore } from '@/stores/modules/workflow/provider'
import { useTextGenerationCurrentProviderAndModelAndModelList } from '@/views/app/workflow/utils/configuration'

const providerStore = useProviderStore()
const currentProvider = ref(providerStore.provider.currentProvider)
const currentModel = ref(providerStore.provider.currentModel)
const textGenerationModelList = ref(providerStore.provider.textGenerationModelList || [])

const selectedModel = ref<any>({})
const provider = textGenerationModelList.value.filter((item) => {
  return item.provider === currentProvider.value.provider
})
if (provider.length > 0) {
  selectedModel.value = provider[0].models.find((v) => {
    return v.model === currentModel.value.model
  })
  console.log(provider, selectedModel)
}
const handleChangeModel = (item: any) => {
  console.log(item)
  const targetProvider = providerStore.provider.textGenerationModelList.find(
    (modelItem) => modelItem.provider === item.provider.provider
  )
  const targetModelItem = targetProvider?.models.find((modelItem) => modelItem.model === item.model)
  const obj = useTextGenerationCurrentProviderAndModelAndModelList({
    provider: item.provider.provider,
    model: item.model
  })

  providerStore.provider.currentProvider = obj.currentProvider
  providerStore.provider.currentModel = obj.currentModel

  providerStore.getParamsList()
  providerStore.provider.completion_params = {}
}
</script>
<style scoped lang="scss"></style>
