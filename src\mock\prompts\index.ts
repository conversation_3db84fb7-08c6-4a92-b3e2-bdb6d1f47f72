import Mock from 'mockjs'
import { defineMock } from '../_base'
import { getDelayTime, resultSuccess } from '../_utils'

const promptList = Mock.mock({
  'list|10-20': [
    {
      id: '@id',
      promptKey: '@title(5, 10)',
      name: '@ctitle(5, 10)',
      content: '@cparagraph(5, 10)',
      description: '@csentence(7, 15)',
      'type|1': ['scene', 'block'],
      'status|1': [1, 2],
      tags: '@cword(2, 4),@cword(2, 4),@cword(2, 4)',
      createdBy: '@name',
      createTime: '@datetime()',
      updatedBy: '@name',
      updateTime: '@datetime()',
      'version|1': ['v1.0', 'v1.2', 'v2.0']
    }
  ]
})

export default defineMock([
  {
    url: '/prompt/page',
    method: 'post',
    timeout: getDelayTime(),
    response: ({ body }) => {
      const { current = 1, size = 10, model = {} } = body
      const { name, type, status, tags } = model

      let records = [...promptList.list]

      if (name) {
        records = records.filter((item) => item.name.includes(name))
      }

      if (type !== undefined) {
        records = records.filter((item) => item.type === type)
      }

      if (status !== undefined) {
        records = records.filter((item) => item.status === status)
      }

      if (tags) {
        records = records.filter((item) => item.tags.includes(tags))
      }

      const pageRecords = records.slice((current - 1) * size, current * size)

      return resultSuccess({
        records: pageRecords,
        total: records.length,
        size,
        current,
        pages: Math.ceil(records.length / size)
      })
    }
  },
  {
    url: '/prompt',
    method: 'post',
    timeout: getDelayTime(),
    response: ({ body }) => {
      const newPrompt = {
        ...body,
        id: Mock.mock('@id'),
        createTime: Mock.mock('@datetime()'),
        updateTime: Mock.mock('@datetime()'),
        createdBy: Mock.mock('@name'),
        updatedBy: Mock.mock('@name')
      }
      promptList.list.unshift(newPrompt)
      return resultSuccess(newPrompt)
    }
  },
  {
    url: '/prompt',
    method: 'put',
    timeout: getDelayTime(),
    response: ({ body }) => {
      const index = promptList.list.findIndex((item) => item.id === body.id)
      if (index !== -1) {
        promptList.list[index] = {
          ...promptList.list[index],
          ...body,
          updateTime: Mock.mock('@datetime()'),
          updatedBy: Mock.mock('@name')
        }
        return resultSuccess(promptList.list[index])
      }
      return resultSuccess(null)
    }
  },
  {
    url: '/prompt',
    method: 'delete',
    timeout: getDelayTime(),
    response: ({ body }) => {
      const ids = body
      ids.forEach((id) => {
        const strId = String(id)
        const index = promptList.list.findIndex((item) => item.id === strId)
        if (index !== -1) {
          promptList.list.splice(index, 1)
        }
      })
      return resultSuccess(true)
    }
  },
  {
    url: '/prompt/:id',
    method: 'get',
    timeout: getDelayTime(),
    response: ({ query }) => {
      const id = query.id
      const prompt = promptList.list.find((item) => item.id === id)
      return resultSuccess(prompt || null)
    }
  }
])
