import { Position } from '@vue-flow/core'

import { uuidTools } from '@/views/app/workflow/utils/common'

import { HTTPMethod, RetrievalMode } from '../types/node'
import {
  AuthorizationType,
  ErrorHandleMode,
  type NodeData,
  type NodePosition,
  NodeType,
  ReasoningMode
} from '../types/node'

export const BaseDimensions = {
  width: 200,
  height: 46
}

export class NodeItem {
  id?: string
  type = 'custom'
  selected: boolean = false
  selectable = true
  draggable = true
  connectable = true
  position?: NodePosition
  parentNode?: string
  positionAbsolute?: NodePosition
  dimensions: {
    width: number
    height: number
  } = {
    width: BaseDimensions.width,
    height: BaseDimensions.height
  }
  label?: string
  parent?: string
  zIndex?: number
  targetPosition = Position.Left
  sourcePosition = Position.Right
  data: NodeData = {
    title: '',
    desc: '',
    type: null,
    selected: false,
    hover: false
  }
  extent?: string

  constructor(config) {
    Object.assign(this.data, config.data)
    this.id = config.id || `${+new Date()}`
    this.position = config.position
    this.selected = config.selected || false
    config.type && (this.type = config.type)
    if (config.dimensions) {
      Object.assign(this.dimensions, config.dimensions)
    }
    if (config.parentNode) {
      this.parentNode = config.parentNode
    }
    if (config.zIndex) {
      this.zIndex = config.zIndex
    }
    if (config.extent) {
      this.extent = config.extent
    }
    if (typeof config.draggable === 'boolean') {
      this.draggable = config.draggable
    }
    if (typeof config.selectable === 'boolean') {
      this.selectable = config.selectable
    }
  }
}

export class StartNode extends NodeItem {
  constructor({ label = '开始', data, ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.开始,
        variables: [],
        ...data
      },
      label,
      ...config
    })
  }
}

export class LLMNode extends NodeItem {
  constructor({ label = '大模型', data, ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.大模型,
        model: {
          completion_params: {
            stop: []
          },
          mode: '',
          name: '',
          provider: ''
        },
        memory: {
          query_prompt_template: '{{#sys.query#}}',
          role_prefix: {
            assistant: '',
            user: ''
          },
          window: {
            enabled: false,
            window: 50
          }
        },
        context: {
          enabled: false,
          variable_selector: []
        },
        prompt_template: [
          {
            role: 'system',
            text: ''
          }
        ],
        prompt_config: {
          jinja2_variables: []
        },
        vision: {
          enabled: false
        },
        variables: [],
        ...data
      },
      label,
      ...config
    })
  }
}

export class AnswerNode extends NodeItem {
  constructor({ label = '直接回复', data, ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.直接回复,
        answer: '',
        variables: [],
        ...data
      },
      label,
      ...config
    })
  }
}

export class KnowledgeRetrievalNode extends NodeItem {
  constructor({ label = '知识检索', ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.知识检索,
        query_variable_selector: [],
        dataset_ids: [],
        retrieval_mode: RetrievalMode.N选1召回,
        multiple_retrieval_config: {
          reranking_model: {
            model: '',
            provider: ''
          },
          score_threshold: null,
          top_k: 2
        },
        single_retrieval_config: {
          model: {
            completion_params: {},
            mode: '',
            name: '',
            provider: ''
          }
        }
      },
      label,
      ...config
    })
  }
}

export class QuestionClassifierNode extends NodeItem {
  constructor({ label = '问题分类', ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.问题分类,
        query_variable_selector: [],
        instruction: '',
        instructions: '',
        model: {
          completion_params: {},
          mode: '',
          name: '',
          provider: ''
        },
        classes: [
          { id: `${+new Date()}-class`, name: '' },
          { id: `${+new Date() + 1}-class`, name: '' }
        ]
      },
      label,
      ...config
    })
  }
}

export class IfElseNode extends NodeItem {
  constructor({ label = '条件分支', type, ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.条件分支,
        conditions: [],
        logical_operator: 'and'
      },
      label,
      ...config
    })
  }
}

export class CodeNode extends NodeItem {
  constructor({ label = '代码执行', ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.代码执行,
        code: '',
        code_language: 'python3',
        dependencies: [],
        variables: [
          { variable: 'arg1', value_selector: [] },
          { variable: 'arg2', value_selector: [] }
        ],
        outputs: {
          result: {
            children: null,
            type: 'number'
          }
        }
      },
      label,
      ...config
    })
  }
}

export class TemplateTransformNode extends NodeItem {
  constructor({ label = '模板转换', ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.模板转换,
        variables: [{ variable: 'arg1', value_selector: [], id: `${+new Date()}` }],
        template: '{{ arg1 }}'
      },
      label,
      ...config
    })
  }
}

export class ParameterExtractorNode extends NodeItem {
  constructor({ label = '参数提取', ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.参数提取,
        model: {
          completion_params: {
            stop: []
          },
          mode: '',
          name: '',
          provider: ''
        },
        parameters: [],
        query: [],
        reasoning_mode: ReasoningMode['Function/Tool Calling'],
        variables: [],
        instruction: ''
      },
      label,
      ...config
    })
  }
}

export class HttpRequestNode extends NodeItem {
  constructor({ label = 'HTTP请求', ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.HTTP请求,
        authorization: {
          config: null,
          type: AuthorizationType.无
        },
        body: {
          data: '',
          type: 'none'
        },
        headers: '',
        method: HTTPMethod.GET,
        url: '',
        params: '',
        timeout: {
          max_connect_timeout: 0,
          max_read_timeout: 0,
          max_write_timeout: 0
        },
        variables: []
      },
      label,
      ...config
    })
  }
}

export class TextToSqlNode extends NodeItem {
  constructor({ label = 'Text To SQL', ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.TextToSQL,
        max_retry: 3,
        data_source: {
          type: '',
          host: '',
          port: null,
          database: '',
          username: '',
          password: '',
          record_limit: 10
        },
        llm_config: {
          model: {
            completion_params: {
              stop: []
            },
            mode: '',
            name: '',
            provider: ''
          },
          memory: {
            query_prompt_template: '{{#sys.query#}}',
            role_prefix: {
              assistant: '',
              user: ''
            },
            window: {
              enabled: false,
              window: 50
            }
          },
          context: {
            enabled: false,
            variable_selector: []
          },
          prompt_template: [
            {
              role: 'system',
              text: ''
            }
          ],
          prompt_config: {
            jinja2_variables: []
          },
          vision: {
            enabled: false
          }
        },
        variables: []
      },
      ...config
    })
  }
}

export class DocumentExtractorNode extends NodeItem {
  constructor({ label = '文档提取器', ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.文档提取器,
        variable_selector: [],
        selected: false,
        ocr: {
          enabled: true
        },
        split: {
          enabled: false,
          split_size: 20000,
          window_size: 1000
        }
      },
      ...config
    })
  }
}

export class LoopNode extends NodeItem {
  constructor({ label = '循环处理', ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.循环,
        error_handle_mode: 'terminated',
        logical_operator: 'and',
        loop_count: 10,
        break_conditions: []
      },
      ...config,
      type: 'resizable',
      dimensions: {
        width: BaseDimensions.width + BaseDimensions.height,
        height: BaseDimensions.height + 90
      }
    })
    this.data.start_node_id = `${this.id}start`
  }
}

export class LoopStartNode extends NodeItem {
  constructor({ label = '循环开始', parentNode, ...config }) {
    super({
      data: {
        title: label,
        isInLoop: true,
        type: NodeType.循环开始
      },
      id: `${parentNode}start`,
      parentNode,
      dimensions: {
        width: BaseDimensions.height,
        height: BaseDimensions.height
      },
      type: 'custom-loop-start',
      extent: 'parent',
      position: {
        x: 24,
        y: 68
      },
      draggable: false,
      selectable: false,
      ...config
    })
  }
}

export class IterationNode extends NodeItem {
  constructor({ label = '迭代处理', start_node_id, ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.迭代,
        is_parallel: false,
        parallel_nums: 10,
        error_handle_mode: ErrorHandleMode.错误终止,
        iterator_selector: [],
        output_selector: [],
        start_node_id
      },
      ...config,
      type: 'resizable',
      dimensions: {
        width: BaseDimensions.width + BaseDimensions.height,
        height: BaseDimensions.height + 90
      }
    })
    this.data.start_node_id = `${this.id}start`
  }
}

export class IterationStartNode extends NodeItem {
  constructor({ label = '迭代开始', parentNode, ...config }) {
    super({
      data: {
        title: label,
        isInIteration: true,
        type: NodeType.迭代开始
      },
      id: `${parentNode}start`,
      parentNode,
      dimensions: {
        width: BaseDimensions.height,
        height: BaseDimensions.height
      },
      position: {
        x: 24,
        y: 68
      },
      type: 'custom-iteration-start',
      extent: 'parent',
      draggable: false,
      selectable: false,
      ...config
    })
  }
}

export class VariableAggregatorNode extends NodeItem {
  constructor({ label = '变量聚合', parentId, ...config }) {
    super({
      data: {
        title: label,
        output_type: 'any',
        type: NodeType.变量聚合,
        variables: [],
        advanced_settings: {
          group_enabled: false,
          groups: [
            {
              groupId: uuidTools(),
              group_name: 'Group1',
              output_type: 'any',
              variables: []
            }
          ]
        }
      },
      ...config
    })
  }
}

export class ToolNode extends NodeItem {
  constructor({ label, name, icon, provider_id, provider_name, provider_type, ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.工具,
        provider_id,
        provider_name,
        provider_type,
        tool_label: label,
        tool_name: name,
        tool_configurations: {},
        tool_parameters: {},
        icon
      },
      label,
      ...config
    })
  }
}

export class EndNode extends NodeItem {
  constructor({ label = '结束', name, icon, provider_id, provider_name, provider_type, ...config }) {
    super({
      data: {
        title: label,
        type: NodeType.结束,
        outputs: []
      },
      label,
      ...config
    })
  }
}

export interface NodeInterface {
  id?: string
  type: 'custom'
  selected: boolean
  hover: boolean
  connectable: boolean
  position?: NodePosition
  dimensions: {
    width: number
    height: number
  }
  label?: string
  parent?: string
  zIndex?: number
  targetPosition: Position.Left
  sourcePosition: Position.Right
  data: NodeData
  positionAbsolute?: NodePosition
  extent?: string
}
