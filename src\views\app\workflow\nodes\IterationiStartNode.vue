<template>
  <div class="custom-node">
    <div class="custom-node-icon" :style="{ backgroundColor: '#296dff' }">
      <AiSvgIcon style="width: 14px; height: 14px" :name="`workflow-${type}`" />
    </div>
    <NodeList class="custom-node-add" :popoverInstance="true" :nodeId="props.id" :node-props="props">
      <Handle id="source" type="source" class="custom-node-handle" :position="Position.Right">
        <icon-plus :style="{ pointerEvents: 'none' }" />
      </Handle>
    </NodeList>
  </div>
</template>

<script setup lang="ts">
import { Handle, Position } from '@vue-flow/core'
import type { NodeProps } from '@vue-flow/core'
import NodeList from './node-list.vue'
const props = defineProps<NodeProps>()
</script>
<style scoped lang="scss">
.custom-node {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  width: 50px;
  background-color: var(--color-bg-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 12px;

  &-icon {
    height: 24px;
    width: 24px;
    border-radius: 8px;
    background-color: var(--color-fill-3);
    text-align: center;
    line-height: 24px;
    color: var(--color-text-1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-text {
    font-size: 18px;
    font-weight: 500;
    color: var(--color-text-2);
  }

  &-handle {
    background: rgb(var(--primary-6));
    height: 10px;
    width: 2px;
    border-radius: 0;
    border: none;
    min-width: 2px;

    .arco-icon {
      display: none;
    }
  }

  &:hover {
    .custom-node-handle {
      background-color: rgb(var(--primary-6));
      border-radius: 50%;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
      cursor: pointer;
    }

    .arco-icon {
      display: inline-block;
      width: 14px;
      height: 14px;
      color: var(--color-white);
    }
  }

  &-add {
    position: absolute;
    right: -5px;
    pointer-events: none;
  }
}
</style>
