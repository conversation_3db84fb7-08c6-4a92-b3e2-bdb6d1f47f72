import {
  useWorkflowAgentLog,
  useWorkflowFailed,
  useWorkflowFinished,
  useWorkflowNodeFinished,
  useWorkflowNodeIterationFinished,
  useWorkflowNodeIterationNext,
  useWorkflowNodeIterationStarted,
  useWorkflowNodeLoopFinished,
  useWorkflowNodeLoopNext,
  useWorkflowNodeLoopStarted,
  useWorkflowNodeRetry,
  useWorkflowNodeStarted,
  useWorkflowStarted,
  useWorkflowTextChunk,
  useWorkflowTextReplace
} from '../use-workflow-run-event'

export const useWorkflowRunEvent = () => {
  const setDataWorkflowStarted = useWorkflowStarted().handleWorkflowStarted
  const setDataWorkflowFinished = useWorkflowFinished().handleWorkflowFinished
  const setDataWorkflowFailed = useWorkflowFailed().handleWorkflowFailed
  const setDataWorkflowNodeStarted = useWorkflowNodeStarted().handleWorkflowNodeStarted
  const setDataWorkflowNodeFinished = useWorkflowNodeFinished().handleWorkflowNodeFinished
  const setDataWorkflowNodeIterationStarted = useWorkflowNodeIterationStarted().handleWorkflowNodeIterationStarted
  const setDataWorkflowNodeIterationNext = useWorkflowNodeIterationNext().handleWorkflowNodeIterationNext
  const setDataWorkflowNodeIterationFinished = useWorkflowNodeIterationFinished().handleWorkflowNodeIterationFinished
  const setDataWorkflowNodeLoopStarted = useWorkflowNodeLoopStarted().handleWorkflowNodeLoopStarted
  const setDataWorkflowNodeLoopNext = useWorkflowNodeLoopNext().handleWorkflowNodeLoopNext
  const setDataWorkflowNodeLoopFinished = useWorkflowNodeLoopFinished().handleWorkflowNodeLoopFinished
  const setDataWorkflowNodeRetry = useWorkflowNodeRetry().handleWorkflowNodeRetry
  const setDataWorkflowTextChunk = useWorkflowTextChunk().handleWorkflowTextChunk
  const setDataWorkflowTextReplace = useWorkflowTextReplace().handleWorkflowTextReplace
  const setDataWorkflowAgentLog = useWorkflowAgentLog().handleWorkflowAgentLog
  return {
    setDataWorkflowStarted,
    setDataWorkflowFinished,
    setDataWorkflowFailed,
    setDataWorkflowNodeStarted,
    setDataWorkflowNodeFinished,
    setDataWorkflowNodeIterationStarted,
    setDataWorkflowNodeIterationNext,
    setDataWorkflowNodeIterationFinished,
    setDataWorkflowNodeLoopStarted,
    setDataWorkflowNodeLoopNext,
    setDataWorkflowNodeLoopFinished,
    setDataWorkflowNodeRetry,
    setDataWorkflowTextChunk,
    setDataWorkflowTextReplace,
    setDataWorkflowAgentLog
  }
}
