<template>
  <div v-for="(item, index) in infoSchemas" :key="index" class="space-y-1 py-2">
    <div class="py-1">
      <div class="flex items-center gap-2">
        <div class="code-sm-semibold text-text-secondary">
          {{ renderName(item.label) }}
        </div>
        <div class="system-xs-regular text-text-tertiary">
          {{ getType(item.type) }}
        </div>
        <div v-if="item.required" class="system-xs-medium text-text-warning-secondary text-yellow-500">必填</div>
      </div>

      <div class="system-xs-regular mt-0.5 text-text-tertiary">
        {{ renderName(item.human_description) }}
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { getType, renderName } from '@/views/app/workflow/utils/configuration'

const props = defineProps(['infoSchemas'])
</script>
<style scoped lang="scss">

</style>
