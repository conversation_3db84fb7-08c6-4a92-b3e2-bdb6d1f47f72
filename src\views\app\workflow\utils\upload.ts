import type { TransferMethod } from '@/views/app/workflow/types/app'

export type FileEntity = {
  id: string
  name: string
  size: number
  type: string
  progress: number
  transferMethod: TransferMethod
  supportFileType: string
  originalFile?: File
  uploadedId?: string
  base64Url?: string
  url?: string
  isRemote?: boolean
}

export const getProcessedFiles = (files: FileEntity[]) => {
  return files
    .filter((file) => file.progress !== -1)
    .map((fileItem) => ({
      type: fileItem.type,
      transfer_method: fileItem.transferMethod,
      url: fileItem.url || '',
      upload_file_id: fileItem.uploadedId || ''
    }))
}
