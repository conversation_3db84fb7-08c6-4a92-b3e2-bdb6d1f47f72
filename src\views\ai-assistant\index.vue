<template>
  <AiPageLayout>
    <!-- 页面标题和分类标签 -->
    <div class="flex items-center mb-6 border-b pb-2">
      <div class="flex items-center">
        <h2 class="text-xl font-normal mr-6">通用助手</h2>
        <h2 class="text-xl font-normal text-primary border-b-2 border-primary pb-2 -mb-[10px]">测试助手</h2>
      </div>
      <div class="flex-1" />
      <a-button type="primary" class="flex items-center" @click="handleCreate">
        <template #icon>
          <div class="w-[18px] h-[18px] bg-[#F2F3F5] rounded-full flex items-center justify-center mr-1">
            <icon-plus />
          </div>
        </template>
        创建智能体
      </a-button>
    </div>

    <!-- 应用助手部分 -->
    <div class="section-container mb-6">
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <AssistantCard v-for="(assistant, index) in assistantList" :key="index" :assistant="assistant" />
      </div>
    </div>

    <!-- 会话记录标题 -->
    <div class="section-title mb-4">
      <h2 class="text-xl font-normal">会话记录</h2>
    </div>

    <!-- 任务执行记录部分 -->
    <div class="section-container" style="overflow: hidden">
      <!-- 表格区域 -->
      <AiTable
        row-key="id"
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ x: '100%', y: '100%' }"
        @change="handleTableChange"
        @refresh="fetchData"
      >
        <!-- 自定义工具栏 -->
        <template #toolbar-left>
          <div class="flex items-center">
            <div class="mr-6 flex items-center">
              <span class="mr-2">会话名称：</span>
              <a-input v-model="queryParams.name" placeholder="请输入" style="width: 220px" />
            </div>
            <div class="mr-6 flex items-center">
              <span class="mr-2">智能体版本：</span>
              <a-select v-model="queryParams.version" placeholder="请选择" style="width: 220px">
                <a-option v-for="version in versionOptions" :key="version.value" :value="version.value">
                  {{ version.label }}
                </a-option>
              </a-select>
            </div>
            <a-button type="primary" class="mr-2" @click="handleSearch">查 询</a-button>
            <a-button @click="resetSearch">重 置</a-button>
          </div>
        </template>

        <!-- 序号列 -->
        <template #index="{ rowIndex }">
          {{ formatIndex(rowIndex) }}
        </template>

        <!-- 会话名称列 -->
        <template #name="{ record }">
          <div class="flex items-center">
            <span>{{ record.name || record.taskName || record.assistantName }}</span>
          </div>
        </template>

        <!-- 智能体版本列 -->
        <template #version="{ record }">
          <span>{{ record.version || 'V1.8' }}</span>
        </template>

        <!-- 要素提取列 -->
        <template #extract="{ record }">
          <div class="flex items-center justify-center">
            <div class="w-[30px] h-[30px]">
              <a-progress
                :percent="record.extractProgress || Math.round(record.progress * 0.3)"
                :stroke-color="getProgressColor(record.extractProgress || Math.round(record.progress * 0.3))"
                :track-color="'#E9E9E9'"
                :show-text="false"
                :stroke-width="3"
                type="circle"
                size="mini"
              />
            </div>
          </div>
          <div class="text-center mt-1 text-xs">{{ record.extractProgress || Math.round(record.progress * 0.3) }}%</div>
        </template>

        <!-- 正交结合列 -->
        <template #combine="{ record }">
          <div class="flex items-center justify-center">
            <div class="w-[30px] h-[30px]">
              <a-progress
                :percent="record.combineProgress || Math.round(record.progress * 0.3)"
                :stroke-color="getProgressColor(record.combineProgress || Math.round(record.progress * 0.3))"
                :track-color="'#E9E9E9'"
                :show-text="false"
                :stroke-width="3"
                type="circle"
                size="mini"
              />
            </div>
          </div>
          <div class="text-center mt-1 text-xs">{{ record.combineProgress || Math.round(record.progress * 0.3) }}%</div>
        </template>

        <!-- 案例生成列 -->
        <template #generate="{ record }">
          <div class="flex items-center justify-center">
            <div class="w-[30px] h-[30px]">
              <a-progress
                :percent="record.generateProgress || Math.round(record.progress * 0.4)"
                :stroke-color="getProgressColor(record.generateProgress || Math.round(record.progress * 0.4))"
                :track-color="'#E9E9E9'"
                :show-text="false"
                :stroke-width="3"
                type="circle"
                size="mini"
              />
            </div>
          </div>
          <div class="text-center mt-1 text-xs">
            {{ record.generateProgress || Math.round(record.progress * 0.4) }}%
          </div>
        </template>

        <!-- 会话创建时间列 -->
        <template #createTime="{ record }">
          <span>{{ record.createTime || record.executeTime }}</span>
        </template>

        <!-- 评测列 -->
        <template #assessment="{ record }">
          <div
            v-if="record.assessment || (record.evaluation && record.evaluation !== '未评价')"
            class="flex items-center justify-center"
          >
            <div class="assessment-circle">
              <span>{{ getAssessmentValue(record) }}</span>
            </div>
          </div>
          <div v-else class="flex items-center justify-center">
            <a-tag class="status-tag">未开始</a-tag>
          </div>
        </template>

        <!-- 操作列 -->
        <template #operations="{ record }">
          <div class="text-primary cursor-pointer">
            <template v-if="record.status === 'completed'">
              <span class="mr-2" @click="handleExport(record)">导出</span>
              <span class="mr-2" @click="handleRegenerate(record)">重新生成</span>
              <span @click="handleReimport(record)">重新导入生成</span>
            </template>
            <template v-else-if="record.status === 'processing'">
              <span class="mr-2" @click="handleDetail(record)">详情</span>
              <span class="mr-2" @click="handleTerminate(record)">终止</span>
              <span @click="handleDelete(record)">删除</span>
            </template>
            <template v-else-if="record.status === 'terminated'">
              <span class="mr-2" @click="handleDetail(record)">详情</span>
              <span class="mr-2" @click="handleContinue(record)">继续</span>
              <span @click="handleDelete(record)">删除</span>
            </template>
            <template v-else>
              <span class="mr-2" @click="handleDetail(record)">详情</span>
              <span @click="handleDelete(record)">删除</span>
            </template>
          </div>
        </template>
      </AiTable>
    </div>

    <!-- 创建助手模态窗口 -->
    <CreateAssistantModal v-model:visible="createModalVisible" @submit="handleCreateSuccess" />
  </AiPageLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import AssistantCard from './components/AssistantCard.vue'
import CreateAssistantModal from './components/CreateAssistantModal.vue'
import { getAssistantList, getTaskList } from '../../apis/ai-assistant'
import { useTable } from '@/hooks/modules/useTable'
import { getFileUrl } from '@/utils'

// 类型定义
interface Assistant {
  id: string
  name: string
  description: string
  icon: string
  updateTime: string
  count: string
  version: string
  type: string
}

interface TaskItem {
  id: string
  name?: string
  assistantId?: string
  assistantName?: string
  taskName?: string
  icon?: string
  version?: string
  extractProgress?: number
  combineProgress?: number
  generateProgress?: number
  progress?: number
  status: string
  createTime?: string
  executeTime?: string
  completeTime?: string
  evaluation?: string
  assessment?: string
}

// 查询参数
const queryParams = reactive({
  name: '',
  version: ''
})

// 助手列表数据
const assistantList = ref<Assistant[]>([])

// 版本选项
const versionOptions = [
  { label: 'V1.2', value: 'V1.2' },
  { label: 'V1.8', value: 'V1.8' },
  { label: 'V2.4', value: 'V2.4' }
]

// 表格列定义
const columns = [
  { title: '序号', slotName: 'index', width: 80, align: 'center' },
  { title: '会话名称', slotName: 'name', width: 300 },
  { title: '智能体版本', slotName: 'version', width: 120, align: 'center' },
  { title: '要素提取', slotName: 'extract', width: 120, align: 'center' },
  { title: '正交结合', slotName: 'combine', width: 120, align: 'center' },
  { title: '案例生成', slotName: 'generate', width: 120, align: 'center' },
  { title: '会话创建时间', slotName: 'createTime', width: 180, align: 'center' },
  { title: '评测', slotName: 'assessment', width: 80, align: 'center' },
  { title: '操作', slotName: 'operations', fixed: 'right', width: 240, align: 'center' }
]

// 使用useTable hook管理表格数据
const getTaskListApi = (params: any) => {
  return getTaskList({
    name: queryParams.name,
    version: queryParams.version,
    page: params.page,
    pageSize: params.size
  })
}

const { tableData, loading, pagination, search, refresh } = useTable<TaskItem>(getTaskListApi)

// 格式化序号
const formatIndex = (index: number) => {
  return index < 9 ? `0${index + 1}` : `${index + 1}`
}

// 获取进度条颜色
const getProgressColor = (progress: number) => {
  return progress === 100 ? '#5147FF' : '#C1E89D'
}

// 获取评测值
const getAssessmentValue = (record: TaskItem) => {
  if (record.assessment) {
    return record.assessment
  }
  if (record.evaluation && record.evaluation !== '未评价') {
    return record.evaluation.replace(/[^0-9]/g, '') || '1'
  }
  return '1'
}

// 处理表格变化
const handleTableChange = (data: any) => {
  if (data.pagination) {
    pagination.current = data.pagination.current
    pagination.pageSize = data.pagination.pageSize
    refresh()
  }
}

// 处理搜索
const handleSearch = () => {
  search()
}

// 重置搜索
const resetSearch = () => {
  queryParams.name = ''
  queryParams.version = ''
  search()
}

// 创建助手模态窗口控制
const createModalVisible = ref(false)

// 处理创建任务
const handleCreate = () => {
  createModalVisible.value = true
}

// 处理创建助手成功
const handleCreateSuccess = (data: any) => {
  Message.success('创建助手成功')
  fetchData()
}

// 处理详情查看
const handleDetail = (record: TaskItem) => {
  Message.info(`查看详情: ${record.id}`)
}

// 处理终止任务
const handleTerminate = (record: TaskItem) => {
  Message.info(`终止任务: ${record.id}`)
}

// 处理继续任务
const handleContinue = (record: TaskItem) => {
  Message.info(`继续任务: ${record.id}`)
}

// 处理删除任务
const handleDelete = (record: TaskItem) => {
  Message.info(`删除任务: ${record.id}`)
}

// 处理导出
const handleExport = (record: TaskItem) => {
  Message.info(`导出: ${record.id}`)
}

// 处理重新生成
const handleRegenerate = (record: TaskItem) => {
  Message.info(`重新生成: ${record.id}`)
}

// 处理重新导入生成
const handleReimport = (record: TaskItem) => {
  Message.info(`重新导入生成: ${record.id}`)
}

// 获取助手列表数据
const fetchAssistantData = async () => {
  try {
    const res = await getAssistantList()
    if (res && res.data) {
      assistantList.value = res.data
    }
  } catch (error) {
    console.error('获取助手列表失败', error)
    Message.error('获取助手列表失败')
  }
}

// 获取所有数据
const fetchData = () => {
  fetchAssistantData()
  refresh()
}

// 组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.text-primary {
  color: #5147ff;
}

.assessment-circle {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #5147ff;
  color: white;
  font-size: 12px;
}

/* 调整进度条组件的样式 */
:deep(.arco-progress-circle-text) {
  font-size: 10px;
}

:deep(.arco-progress-circle) {
  transform: scale(0.9);
}

.status-tag {
  background-color: #f4f4f6;
  color: #999999;
  border: none;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

/* 覆盖表格样式 */
:deep(.arco-table-th) {
  background-color: #f5f7fa !important;
  font-weight: normal !important;
  color: rgba(0, 0, 0, 0.85) !important;
  height: 48px !important;
}

:deep(.arco-table-tr) {
  height: 56px !important;
}

:deep(.arco-table-td) {
  border-bottom: 1px solid #e8e8e8 !important;
}
</style>
