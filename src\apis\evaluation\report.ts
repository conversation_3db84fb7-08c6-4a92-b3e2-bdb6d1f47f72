import http from '@/utils/http'
import type * as ReportType from './report-type'

// evalReport
/**
 * @name SaveUsingPost
 * @summary 新增
 * @request POST:/evalReport
 */
export function saveReport(params: ReportType.EvalReportSaveDTO) {
  const url = `/evaluation_api/evalReport`

  return http.post<ReportType.REvalReport>(url, params)
}

/**
 * @name UpdateUsingPut
 * @summary 修改
 * @request PUT:/evalReport
 */
export function updateReport(params: ReportType.EvalReportUpdateDTO) {
  const url = `/evaluation_api/evalReport`

  return http.put<ReportType.REvalReport>(url, params)
}

/**
 * @name DeleteUsingDelete
 * @summary 删除
 * @request DELETE:/evalReport
 */
export function deleteReport(params: number[]) {
  const url = `/evaluation_api/evalReport`

  return http.del<ReportType.RBoolean>(url, params)
}

/**
 * @name UpdateAllUsingPut
 * @summary 修改所有字段
 * @request PUT:/evalReport/all
 */
export function updateAllReport(params: ReportType.EvalReport) {
  const url = `/evaluation_api/evalReport/all`

  return http.put<ReportType.REvalReport>(url, params)
}

/**
 * @name PageUsingPost
 * @summary 分页查询评测报告
 * @request POST:/evalReport/page
 */
export function getReportPage(params: ReportType.PageParamsEvalReportPageQuery) {
  const url = `/evaluation_api/evalReport/page`

  return http.post<ReportType.RIPageEvalReport>(url, params)
}

/**
 * @name QueryUsingPost
 * @summary 批量查询
 * @request POST:/evalReport/query
 */
export function queryReport(params: ReportType.EvalReport) {
  const url = `/evaluation_api/evalReport/query`

  return http.post<ReportType.RListEvalReport>(url, params)
}

/**
 * @name GetUsingGet
 * @summary 单体查询
 * @request GET:/evalReport/{id}
 */
export function getReportDeatil(id: string) {
  const url = `/evaluation_api/evalReport/${id}`

  return http.get<ReportType.REvalReport>(url)
}
