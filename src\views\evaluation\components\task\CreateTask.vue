<template>
  <div class="create-evaluation-task">
    <div class="page-header" @click="router.go(-1)">
      <icon-left />
      <span class="page-title">返回</span>
    </div>

    <!-- 步骤指示器 -->
    <div class="step-indicator">
      <a-steps :current="currentStep" line-less>
        <a-step>选择评测对象</a-step>
        <a-step>准备评测集</a-step>
        <!-- <a-step v-else>准备评测数据</a-step> -->
        <a-step>定制评测规则</a-step>
        <!-- <a-step v-else>开始人工评测</a-step> -->
      </a-steps>
    </div>
    <!-- 步骤内容区域 -->
    <div class="step-content-container">
      <SelectObject v-if="currentStep === 1" :form-data="formData" @next="goToNextStep" @saveForm="saveForm" />
      <PrepareData
        v-else-if="currentStep === 2"
        :form-data="formData"
        @next="goToNextStep"
        @prev="goToPrevStep"
        @saveForm="saveForm"
      />
      <!-- <EvaluationData
        v-else-if="currentStep === 2 && taskType === '2'"
        :form-data="formData"
        @next="goToNextStep"
        @prev="goToPrevStep"
        @saveForm="saveForm"
      /> -->
      <EvaluationRules v-else-if="currentStep === 3" :form-data="formData" @prev="goToPrevStep" @saveForm="saveForm" />
    </div>
  </div>
</template>

<script setup lang="ts">
import SelectObject from './steps/SelectObject.vue'
import PrepareData from './steps/PrepareData.vue'
// import EvaluationData from './steps/EvaluationData.vue'
import EvaluationRules from './steps/EvaluationRules.vue'
import { EvalTaskSaveDTO } from '@/apis/evaluation/task-types'

defineOptions({ name: 'CreateEvaluationTask' })

const currentStep = ref<number>(1)
const route = useRoute()
const router = useRouter()

const { taskType } = route.query
const formData = ref<EvalTaskSaveDTO>({})

const goToNextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value = currentStep.value + 1
  }
}

const goToPrevStep = () => {
  if (currentStep.value > 1) {
    currentStep.value = currentStep.value - 1
  }
}

// 保存每个步骤的数据
const saveForm = (form: EvalTaskSaveDTO) => {
  formData.value = {
    ...formData.value,
    ...form
  }
}
</script>

<style scoped lang="scss">
.create-evaluation-task {
  height: 100%;
  width: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding: 16px;
  position: relative;
  .page-header {
    display: flex;
    cursor: pointer;
    position: absolute;
    align-items: center;
    .page-title {
      font-size: 14px;
    }
  }
}

.step-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  position: relative;
  min-width: 435px;
  margin-left: auto;
  margin-right: auto;
}

.step-content-container {
  flex: 1;
  overflow-y: auto;
  background-color: #fff;
  border-radius: 4px;
  padding: 0;
}
</style>
