<template>
  <div class="custom-node" :style="{ width: data.width, height: data.height }">
    <div class="truncate flex items-center h-[46px]">
      <div class="custom-node-icon" :style="{ backgroundColor: '#f79009' }">
        <AiSvgIcon style="width: 18px; height: 18px" :name="`workflow-${type}`" />
      </div>
      <div class="custom-node-text">
        {{ data.title }}
      </div>
      <Handle id="target" type="target" class="custom-node-handle" :position="Position.Left" />
    </div>
    <div>
      <div class="space-y-1">
        <div
          v-for="item in processedList"
          class="text-primary flex h-8 cursor-pointer items-center justify-between rounded-lg border px-2.5 shadow-xs"
        >
          <div class="flex w-0 grow items-center space-x-1">
            <AiSvgIcon name="workflow-ai-variable" />
            <div
              v-if="item.value_selector"
              class="system-xs-regular max-w-[130px] shrink-0 truncate font-medium text-text-secondary"
            >
              {{ item.value_selector?.join('.') }}
            </div>
            <!-- <div class="shrink-0 text-xs font-medium text-text-quaternary">·</div>
        <div title="code" class="max-w-[130px] truncate text-[13px] font-medium text-text-tertiary">code</div> -->
          </div>
          <div class="ml-2 flex shrink-0 items-center">
            <div class="mr-2 text-xs font-normal text-text-tertiary">
              <span>Sting</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Handle, Position } from '@vue-flow/core'
import type { NodeProps } from '@vue-flow/core'
import { computed } from 'vue'

const props = defineProps<NodeProps>()
const processedList = computed(() => {
  const list = props.data?.outputs?.filter((e) => e.value_selector && e.variable)
  return list
})
</script>
<style scoped lang="scss">
:deep(.vue-flow__handle-left) {
  top: auto;
}

.custom-node {
  min-width: 240px;
  padding: 4px 14px;
  background-color: var(--color-bg-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 12px;

  &-icon {
    margin-right: 8px;
    height: 24px;
    width: 24px;
    border-radius: 8px;
    background-color: var(--color-fill-3);
    text-align: center;
    line-height: 24px;
    color: var(--color-text-1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-text {
    font-size: 18px;
    font-weight: 500;
    color: var(--color-text-2);
  }

  &-handle {
    background: rgb(var(--primary-6));
    height: 10px;
    width: 2px;
    border-radius: 0;
    border: none;
    min-width: 2px;
  }
}
</style>
