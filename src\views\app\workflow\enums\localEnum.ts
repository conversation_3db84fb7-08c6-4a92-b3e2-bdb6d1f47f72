import {
  AnswerNode,
  CodeNode,
  DocumentExtractorNode,
  EndNode,
  HttpRequestNode,
  IfElseNode,
  IterationNode,
  KnowledgeRetrievalNode,
  LLMNode,
  LoopNode,
  ParameterExtractorNode,
  QuestionClassifierNode,
  StartNode,
  TemplateTransformNode,
  TextToSqlNode,
  ToolNode,
  VariableAggregatorNode
} from '@/views/app/workflow/nodes/nodes'
import { NodeType, VisionDetail } from '@/views/app/workflow/types/node'
import {
  APP_ASCRIPTION,
  APP_MORE_OPERATION,
  APP_ORCHESTRATION,
  FeaturesType,
  FieldType,
  UploadFileAllowedFileTypes
} from '@/views/apps-manage/types.d'

import HTTP_ENUM from './httpEnum'
import TABS_ENUM from './tabsEnum'

enum TOOL_TYPE {
  内置 = 'builtin',
  工作流 = 'workflow',
  自定义 = 'api'
}
/**
 * 前端本地业务枚举
 * <AUTHOR>
 */
export const LOCAL_ENUM_DATA = [
  ...HTTP_ENUM,
  ...TABS_ENUM,
  {
    dictCode: 'TOOL_TABS',
    dictName: '工具分类',
    elements: [
      {
        value: 'BUILT_IN',
        name: '内置',
        code: 'BUILT_IN',
        type: TOOL_TYPE.内置
      },
      {
        value: 'WORKFLOW',
        name: '工作流',
        code: 'WORKFLOW',
        type: TOOL_TYPE.工作流
      },
      {
        value: 'CUSTOM',
        name: '自定义',
        code: 'CUSTOM',
        type: TOOL_TYPE.自定义
      }
    ],
    rules: []
  },
  {
    dictCode: 'APP_ASCRIPTION',
    dictName: '应用归属',
    elements: [
      {
        value: APP_ASCRIPTION.我的,
        name: '我的',
        code: 'MY_SELF'
      },
      {
        value: APP_ASCRIPTION.分享,
        name: '分享',
        code: 'SHARE'
      }
    ],
    rules: []
  },
  {
    dictCode: 'APP_ORCHESTRATION',
    dictName: '应用编排方式',
    elements: [
      {
        value: APP_ORCHESTRATION.智能会话,
        name: '智能会话',
        code: 'QUICK',
        icon: 'arcana-llm-platform-quick-dialog',
        type: APP_ORCHESTRATION.智能会话,
        layout: 'arrange',
        introduction: '通过简单的配置，构建基于LLM的会话机器人。'
      },
      {
        value: APP_ORCHESTRATION.对话流,
        name: '对话流',
        code: 'DIALOGUE_FLOW',
        icon: 'arcana-llm-platform-dialog-flow',
        type: APP_ORCHESTRATION.对话流,
        layout: 'dialog_flow',
        introduction: '通过流程编排，构建支持复杂场景的多轮对话工作流。'
      },
      {
        value: APP_ORCHESTRATION.工作流,
        name: '工作流',
        code: 'WORKFLOW',
        icon: 'arcana-llm-platform-workflow',
        type: APP_ORCHESTRATION.工作流,
        layout: 'workflow',
        introduction: '进阶用户适用，构建面向单轮自动化任务的编排工作流。'
      }
    ],
    rules: []
  },
  {
    dictCode: 'APP_MORE_OPERATION',
    dictName: '应用更多操作',
    elements: [
      {
        value: APP_MORE_OPERATION.编排,
        name: '编排',
        code: 'LAYOUT',
        auth: 'Arcana-LLM-App-Manager-ArrangeApp'
      },
      {
        value: APP_MORE_OPERATION.停用,
        name: '停用',
        code: 'STOP',
        auth: 'Arcana-LLM-App-Manager-SwitchAppState'
      },
      {
        value: APP_MORE_OPERATION.启用,
        name: '启用',
        code: 'START',
        auth: 'Arcana-LLM-App-Manager-SwitchAppState'
      },
      {
        value: APP_MORE_OPERATION.编辑信息,
        name: '编辑信息',
        code: 'EDIT',
        auth: 'Arcana-LLM-App-Manager-EditApp'
      },
      {
        value: APP_MORE_OPERATION.总览,
        name: '总览',
        code: 'OVERVIEW',
        auth: 'Arcana-LLM-App-Manager-ArrangeApp'
      },
      {
        value: APP_MORE_OPERATION.对外接口,
        name: '对外接口',
        code: 'ACCESS',
        auth: 'Arcana-LLM-App-Manager-ArrangeApp'
      },
      {
        value: APP_MORE_OPERATION.会话日志,
        name: '会话日志',
        code: 'LOGS',
        auth: 'Arcana-LLM-App-Manager-ArrangeApp'
      },
      {
        value: APP_MORE_OPERATION.标注,
        name: '标注',
        code: 'TAGGING',
        auth: 'Arcana-LLM-App-Manager-ArrangeApp'
      },
      {
        value: APP_MORE_OPERATION.分享,
        name: '分享',
        code: 'SHARE',
        auth: 'Arcana-LLM-App-Manager-Share'
      },
      {
        value: APP_MORE_OPERATION.复制,
        name: '复制',
        code: 'COPY',
        auth: 'Arcana-LLM-App-Manager-Copy'
      },
      {
        value: APP_MORE_OPERATION.导出,
        name: '导出',
        code: 'EXPORT',
        auth: 'Arcana-LLM-App-Manager-Export'
      },
      {
        value: APP_MORE_OPERATION.删除,
        name: '删除',
        code: 'REMOVE',
        auth: 'Arcana-LLM-App-Manager-Delete'
      }
    ],
    rules: [
      {
        ALL_BTN_AUTH: [
          'Arcana-LLM-App-Manager-ArrangeApp',
          'Arcana-LLM-App-Manager-SwitchAppState',
          'Arcana-LLM-App-Manager-EditApp',
          'Arcana-LLM-App-Manager-Share',
          'Arcana-LLM-App-Manager-Copy',
          'Arcana-LLM-App-Manager-Export',
          'Arcana-LLM-App-Manager-Delete'
        ]
      }
    ]
  },
  {
    dictCode: 'RADIO_DATE_DAY',
    dictName: '单选时间',
    elements: [
      {
        value: 'today',
        name: '今天',
        code: 'TODAY'
      },
      {
        value: '7day',
        name: '最近7天',
        code: '7DAY'
      },
      {
        value: '30day',
        name: '最近30天',
        code: '30DAY'
      }
    ]
  },
  {
    dictCode: 'RADIO_DATE_MINUTE',
    dictName: '单选时间',
    elements: [
      {
        value: '15minute',
        name: '最近15分钟',
        code: '15MINUTE'
      },
      {
        value: '1hour',
        name: '最近1小时',
        code: '1HOUR'
      },
      {
        value: '1day',
        name: '最近1天',
        code: '1DAY'
      }
    ]
  },
  {
    dictCode: 'APP_VARIABLE_FIELD_TYPE',
    dictName: '应用变量字段类型',
    elements: [
      {
        value: FieldType.文本,
        name: '文本',
        code: 'TEXT_INPUT',
        outputType: 'String'
      },
      {
        value: FieldType.段落,
        name: '段落',
        code: 'PARAGRAPH',
        outputType: 'String'
      },
      {
        value: FieldType.下拉选项,
        name: '下拉选项',
        code: 'SELECT',
        outputType: 'String'
      },
      {
        value: FieldType.数字,
        name: '数字',
        code: 'NUMBER',
        outputType: 'Number'
      },
      {
        value: FieldType.文件,
        name: '文件',
        code: 'FILE',
        outputType: 'Array[File]'
      }
    ],
    rules: []
  },
  {
    dictCode: 'APP_RECALL_RETRIEVAL_MODEL',
    dictName: '应用召回设置模式',
    elements: [
      {
        value: 'single',
        name: 'N选1召回',
        code: 'SINGLE',
        introduction:
          '根据用户意图和知识库描述，由 Agent 自主判断选择最匹配的单个知识库来查询相关文本，适合知识库区分度大且知识库数量偏少的应用。'
      },
      {
        value: 'multiple',
        name: '多路召回',
        code: 'MULTIPLE',
        introduction:
          '根据用户意图同时匹配所有知识库，从多路知识库查询相关文本片段，经过重排序步骤，从多路查询结果中选择匹配用户问题的最佳结果，需配置 Rerank 模型API。'
      }
    ],
    rules: []
  },
  {
    dictCode: 'APP_KNOWLEDGE_INDEXING_TECHNIQUE',
    dictName: '应用知识库设置索引方式',
    elements: [
      {
        value: 'high_quality',
        name: '高质量',
        code: 'HIGH_QUALITY',
        introduction: '系统自动设置分段规则与预处理规则，无需手动设置参数',
        token_consumption: 2177
      },
      {
        value: 'economy',
        name: '默认',
        code: 'ECONOMY',
        introduction: '使用历险的向量索引、关键词索引等方式，降低了准确度但无需花费 Token',
        token_consumption: 0
      }
    ],
    rules: []
  },
  {
    dictCode: 'APP_FEATURES',
    dictName: '应用功能',
    elements: [
      {
        value: FeaturesType.对话开场白,
        name: '对话开场白',
        code: 'PROLOGUE',
        describe: '您可以通过添加变量让用户输入表单，作为提示词或开场白中的变量。'
      },
      {
        value: FeaturesType.下一步问题建议,
        name: '下一步问题建议',
        code: 'AFTER_ANSWER',
        describe: '启用后，系统会在回答结束时自动给出3个建议。'
      },
      {
        value: FeaturesType.文件上传,
        name: '文件上传',
        code: 'FILE_UPLOAD',
        describe: '启用后，聊天输入框支持文件上传，方便用户基于文件进行对话。'
      },
      {
        value: FeaturesType.引用和归属,
        name: '引用和归属',
        code: 'RET_RES',
        describe: '启用后，显示源文档和生成内容的归属部分。'
      },
      {
        value: FeaturesType.内容审查,
        name: '内容审查',
        code: 'CONTENT_ADVISOR',
        describe: '启用后，您可以通过维护敏感词库来使模型更安全的输出。'
      },
      {
        value: FeaturesType.标注回复,
        name: '标注回复',
        code: 'ANNOTATION_REPLY',
        describe: '启用后，将标注用户的回复，以便在用户重复提问时快速响应。'
      }
    ],
    rules: []
  },
  {
    dictCode: 'TEST_MODEL_MORE_OPERATION',
    dictName: '调试模型更多操作',
    elements: [
      {
        value: 'single-test',
        name: '单一模式调试',
        code: 'SINGLE_TEST'
      },
      {
        value: 'remove',
        name: '移除',
        code: 'REMOVE'
      }
    ],
    rules: []
  },
  {
    dictCode: 'TAGGING_AUDIT_STATE',
    dictName: '标注审核状态',
    elements: [
      {
        value: 0,
        name: '未审核',
        code: '0'
      },
      {
        value: 1,
        name: '审核通过',
        code: '1'
      },
      {
        value: 2,
        name: '审核未通过',
        code: '2'
      }
    ],
    rules: []
  },
  {
    dictCode: 'MODEL_MANAGER_STATUS',
    dictName: '第三方模型状态',
    elements: [
      {
        value: 'all',
        name: '全部',
        code: 'ALL'
      },
      {
        value: 'active',
        name: '已启用',
        code: 'ACTIVE'
      },
      {
        value: 'disabled',
        name: '已停用',
        code: 'DISABLED'
      },
      {
        value: 'no-configure',
        name: '未配置',
        code: 'NO_CONFIGURE'
      }
    ],
    rules: []
  },
  {
    dictCode: 'BUILT_IN_MODEL_STATUS',
    dictName: '内置模型状态',
    elements: [
      {
        value: 'all',
        name: '全部',
        code: 'ALL'
      },
      {
        value: 'not_enabled',
        name: '未启用',
        code: 'NOT_ENABLED'
      },
      {
        value: 'enabling',
        name: '启用中',
        code: 'ENABLING'
      },
      {
        value: 'enable_failed',
        name: '启用失败',
        code: 'ENABLE_FAILED'
      },
      {
        value: 'enabled',
        name: '已启用',
        code: 'ENABLED'
      },
      {
        value: 'disabled',
        name: '已停用',
        code: 'DISABLED'
      }
    ]
  },
  {
    dictCode: 'WORKFLOW_NODE_TYPE',
    dictName: '工作流节点类型',
    elements: [
      {
        value: NodeType.开始,
        name: '开始',
        code: 'START',
        create: (config) => new StartNode(config),
        output: [
          {
            title: `sys.query`,
            selector: ['sys', 'query'],
            id: `sys.query`,
            type: 'String'
          },
          {
            title: `sys.files`,
            selector: ['sys', 'files'],
            id: `sys.files`,
            type: 'Array[File]'
          },
          {
            title: `sys.conversation_id`,
            selector: ['sys', 'conversation_id'],
            id: `sys.conversation_id`,
            type: 'String'
          },
          {
            title: `sys.user_id`,
            selector: ['sys', 'user_id'],
            id: `sys.user_id`,
            type: 'String'
          }
        ]
      },
      {
        value: NodeType.大模型,
        name: '大模型',
        code: 'LLM',
        create: (config) => new LLMNode(config),
        output: [
          {
            title: `text`,
            selector: ['text'],
            id: `text`,
            type: 'String'
          }
        ]
      },
      {
        value: NodeType.知识检索,
        name: '知识检索',
        code: 'KNOWLEDGE_RETRIEVAL',
        create: (config) => new KnowledgeRetrievalNode(config),
        output: [
          {
            title: 'result',
            selector: ['result'],
            id: 'result',
            type: 'Array[Object]'
          }
        ]
      },
      {
        value: NodeType.直接回复,
        name: '直接回复',
        code: 'ANSWER',
        create: (config) => new AnswerNode(config),
        output: []
      },
      {
        value: NodeType.问题分类,
        name: '问题分类',
        code: 'QUESTION_CLASSIFIER',
        create: (config) => new QuestionClassifierNode(config),
        output: [
          {
            title: 'class_name',
            selector: ['class_name'],
            id: 'class_name',
            type: 'String'
          }
        ]
      },
      {
        value: NodeType.条件分支,
        name: '条件分支',
        code: 'IF_ELSE',
        create: (config) => new IfElseNode(config),
        output: []
      },
      {
        value: NodeType.代码执行,
        name: '代码执行',
        code: 'CODE',
        create: (config) => new CodeNode(config),
        output: []
      },
      {
        value: NodeType.模板转换,
        name: '模板转换',
        code: 'TEMPLATE_TRANSFORM',
        create: (config) => new TemplateTransformNode(config),
        output: [
          {
            title: 'output',
            selector: ['output'],
            id: 'output',
            type: 'String'
          }
        ]
      },
      {
        value: NodeType.参数提取,
        name: '参数提取',
        code: 'PARAMETER_EXTRACTOR',
        create: (config) => new ParameterExtractorNode(config),
        output: [
          {
            title: '__is_success',
            selector: ['__is_success'],
            id: '__is_success',
            type: 'Number'
          },
          {
            title: '__reason',
            selector: ['__reason'],
            id: '__reason',
            type: 'String'
          }
        ]
      },
      {
        value: NodeType.HTTP请求,
        name: 'HTTP请求',
        code: 'HTTP_REQUEST',
        create: (config) => new HttpRequestNode(config),
        output: [
          {
            title: 'body',
            selector: ['body'],
            id: 'body',
            type: 'String'
          },
          {
            title: 'status_code',
            selector: ['status_code'],
            id: 'status_code',
            type: 'Number'
          },
          {
            title: 'headers',
            selector: ['headers'],
            id: 'headers',
            type: 'Object'
          },
          {
            title: 'files',
            selector: ['files'],
            id: 'files',
            type: 'Array[File]'
          }
        ]
      },
      {
        value: NodeType.TextToSQL,
        name: 'TextToSQL',
        code: 'TEXT_TO_SQL',
        create: (config) => new TextToSqlNode(config),
        output: [
          {
            title: 'text',
            selector: ['text'],
            id: 'text',
            type: 'String'
          },
          {
            title: 'sql',
            selector: ['sql'],
            id: 'sql',
            type: 'String'
          },
          {
            title: 'status',
            selector: ['status'],
            id: 'status',
            type: 'String'
          }
        ]
      },
      {
        value: NodeType.文档提取器,
        name: '文档提取器',
        code: 'DOCUMENT_EXTRACTOR',
        create: (config) => new DocumentExtractorNode(config),
        output: [
          {
            title: 'text',
            selector: ['text'],
            id: 'text',
            type: 'Array[String]'
          }
        ]
      },
      {
        value: NodeType.工具,
        name: '工具',
        code: 'TOOL',
        create: (config) => new ToolNode(config),
        output: [
          {
            title: 'text',
            selector: ['text'],
            id: 'text',
            type: 'String'
          },
          {
            title: 'files',
            selector: ['files'],
            id: 'files',
            type: 'Array[File]'
          }
        ]
      },
      {
        value: NodeType.循环,
        name: '循环处理',
        code: 'LOOP',
        create: (config) => new LoopNode(config),
        output: []
      },
      {
        value: NodeType.迭代,
        name: '迭代处理',
        code: 'ITERATION',
        create: (config) => new IterationNode(config),
        output: []
      },
      {
        value: NodeType.变量聚合,
        name: '变量聚合',
        code: 'VARIABLE_AGGREGATOR',
        create: (config) => new VariableAggregatorNode(config),
        output: []
      },
      {
        value: NodeType.结束,
        name: '结束',
        code: 'END',
        create: (config) => new EndNode(config),
        output: []
      }
    ],
    rules: [
      {
        NO_RUN: [NodeType.开始, NodeType.条件分支, NodeType.直接回复]
      }
    ]
  },
  {
    dictCode: 'UPLOAD_FILE_ALLOWED_FILE_TYPES',
    dictName: '文件上传支持类型',
    elements: [
      {
        value: UploadFileAllowedFileTypes.文档格式,
        name: '文档格式',
        code: 'DOCUMENT',
        type_desc: 'pdf、doc、docx、xlsx、xls、txt、md、markdown、html、csv',
        icon: '文件上传-文档格式',
        allowedFileExtensions: ['.PDF', '.DOC', '.DOCX', '.XLSX', '.XLS', '.TXT', '.MD', '.MARKDOWN', '.HTML', '.CSV']
      },
      {
        value: UploadFileAllowedFileTypes.图片格式,
        name: '图片格式',
        code: 'IMAGE',
        type_desc: 'jpg、jpeg、png',
        icon: '文件上传-图片格式',
        allowedFileExtensions: ['.JPG', '.JPEG', '.PNG']
      }
    ]
  },
  {
    dictCode: 'DATASETS_DOCUMENT_STATUS',
    dictName: '知识库文档状态',
    elements: [
      {
        value: 'queuing',
        name: '等待中',
        code: 'queuing'
      },
      {
        value: 'indexing',
        name: '索引中',
        code: 'indexing'
      },
      {
        value: 'paused',
        name: '暂停中',
        code: 'paused'
      },
      {
        value: 'available',
        name: '启用中',
        code: 'available'
      },
      {
        value: 'error',
        name: '错误',
        code: 'error'
      },
      {
        value: 'available',
        name: '启用中',
        code: 'available'
      },
      {
        value: 'enabled',
        name: '启用中',
        code: 'enabled'
      },
      {
        value: 'disabled',
        name: '已禁用',
        code: 'disabled'
      },
      {
        value: 'archived',
        name: '已归档',
        code: 'archived'
      }
    ]
  },
  {
    dictCode: 'DATASETS_DOCUMENT_UPLOAD_STATUS',
    dictName: '知识库文档上传状态',
    elements: [
      {
        value: 'waiting',
        name: '等待中',
        code: 'waiting'
      },
      {
        value: 'splitting',
        name: '分段中',
        code: 'splitting'
      },
      {
        value: 'indexing',
        name: '索引中',
        code: 'indexing'
      },
      {
        value: 'parsing',
        name: '分析中',
        code: 'parsing'
      },
      {
        value: 'error',
        name: '错误',
        code: 'error'
      },
      {
        value: 'completed',
        name: '已完成',
        code: 'completed'
      }
    ]
  },
  {
    dictCode: 'DATASETS_DOCUMENT_STATUS_DISABLED',
    dictName: '知识库文档禁用按钮状态',
    elements: [
      {
        value: 'queuing',
        name: '等待中',
        code: 'queuing'
      },
      {
        value: 'indexing',
        name: '索引中',
        code: 'indexing'
      },
      {
        value: 'paused',
        name: '暂停中',
        code: 'paused'
      },
      {
        value: 'error',
        name: '错误',
        code: 'error'
      },
      {
        value: 'archived',
        name: '已归档',
        code: 'archived'
      }
    ]
  },
  {
    dictCode: 'DIS_LIKE_REASON',
    dictName: '点踩原因选项',
    elements: [
      {
        value: '有害信息',
        name: '有害信息',
        code: '有害信息'
      },
      {
        value: '信息有误',
        name: '信息有误',
        code: '信息有误'
      },
      {
        value: '答非所问',
        name: '答非所问',
        code: '答非所问'
      },
      {
        value: '没有帮助',
        name: '没有帮助',
        code: '没有帮助'
      }
    ]
  },
  {
    dictCode: 'COMPARISON_OPERATOR',
    dictName: '条件节点操作符',
    elements: [
      {
        value: 'contains',
        name: '包含',
        code: 'CONTAINS'
      },
      {
        value: 'not contains',
        name: '不包含',
        code: 'NOT_CONTAINS'
      },
      {
        value: 'start with',
        name: '开始是',
        code: 'START_WITH'
      },
      {
        value: 'end with',
        name: '结束是',
        code: 'END_WITH'
      },
      {
        value: '=',
        name: '=',
        code: 'EQUATION'
      },
      {
        value: '≠',
        name: '≠',
        code: 'NOT_EQUAL'
      },
      {
        value: '>',
        name: '>',
        code: 'GREATER_THAN'
      },
      {
        value: '<',
        name: '<',
        code: 'LESS_THAN'
      },
      {
        value: '≥',
        name: '≥',
        code: 'GREATER_THAN_OR_EQUAL'
      },
      {
        value: '≤',
        name: '≤',
        code: 'LESS_THAN_OR_EQUAL'
      },
      {
        value: 'is',
        name: '是',
        code: 'IS'
      },
      {
        value: 'is not',
        name: '不是',
        code: 'IS_NOT'
      },
      {
        value: 'empty',
        name: '为空',
        code: 'EMPTY'
      },
      {
        value: 'not empty',
        name: '不为空',
        code: 'NOT_EMPTY'
      }
    ],
    rules: [
      {
        STRING: ['包含', '不包含', '开始是', '结束是', '是', '不是', '为空', '不为空'],
        STRING_VALUE: ['contains', 'not contains', 'start with', 'end with', 'is', 'is not', 'empty', 'not empty']
      },
      {
        NUMBER: ['=', '≠', '>', '<', '≥', '≤', '是', '不是', '为空', '不为空'],
        NUMBER_VALUE: ['=', '≠', '>', '<', '≥', '≤', 'is', 'is not', 'empty', 'not empty']
      },
      {
        OTHER: ['为空', '不为空'],
        OTHER_VALUE: ['empty', 'not empty']
      }
    ]
  },
  {
    dictCode: 'TOOL_PARAMETERS_TYPE',
    dictName: '工具列表参数类型',
    elements: [
      {
        value: 'string',
        name: '字符串',
        code: 'string'
      },
      {
        value: 'select',
        name: '选择框',
        code: 'select'
      },
      {
        value: 'number',
        name: '数字输入框',
        code: 'number'
      }
    ]
  },
  {
    dictCode: 'KNOWLEDGE_FOLDER_TYPE',
    dictName: '知识库文件夹弹框类型',
    elements: [
      {
        value: 'add',
        name: '新建文件夹',
        code: 'ADD'
      },
      {
        value: 'moveFolder',
        name: '移动文件夹至',
        code: 'MOVE_FOLDER'
      },
      {
        value: 'renameFolder',
        name: '重命名文件夹',
        code: 'RENAME_FOLDER'
      },
      {
        value: 'moveKnowledge',
        name: '移动知识库至',
        code: 'MOVE_KNOWLEDGE'
      }
    ]
  },
  {
    dictCode: 'DATASET_FOLDER_TYPE',
    dictName: '数据集文件夹弹框类型',
    elements: [
      {
        value: 'add',
        name: '新建文件夹',
        code: 'ADD'
      },
      {
        value: 'moveFolder',
        name: '移动文件夹至',
        code: 'MOVE_FOLDER'
      },
      {
        value: 'renameFolder',
        name: '重命名文件夹',
        code: 'RENAME_FOLDER'
      }
    ]
  },
  {
    dictCode: 'KNOWLEDGE_DRAWER_TYPE',
    dictName: '知识库抽屉类型',
    elements: [
      {
        value: 'add',
        name: '新建知识库',
        code: 'ADD'
      },
      {
        value: 'edit',
        name: '编辑知识库',
        code: 'EDIT'
      },
      {
        value: 'renameFolder',
        name: '重命名文件夹',
        code: 'RENAME_FOLDER'
      },
      {
        value: 'renameKnowledge',
        name: '重命名知识库',
        code: 'RENAME_KNOWLEDGE'
      }
    ]
  },
  {
    dictCode: 'KNOWLEDGE_PREVIEW_PAGE_STATUS',
    dictName: '知识库预览页面的状态',
    elements: [
      {
        value: 'processing',
        name: '处理中',
        code: 'PROCESSING'
      },
      {
        value: 'stopped',
        name: '停止处理',
        code: 'STOPPED'
      },
      {
        value: 'finish',
        name: '处理完成',
        code: 'FINISH'
      }
    ]
  },
  {
    dictCode: 'MODEL_TRAINING_METHOD',
    dictName: '精调方法',
    elements: [
      {
        value: 'full',
        name: '全参训练（Full）',
        code: 'FULL',
        introduction: '训练过程中更新模型的全部参数，复杂任务上会有更好效果'
      },
      {
        value: 'lora',
        name: '高效训练（Lora）',
        code: 'LORA',
        introduction: '训练过程中只更新部分参数，在部分场景能降低过拟合概率'
      }
    ],
    rules: []
  },
  {
    dictCode: 'MODEL_EVALUATION_TYPE',
    dictName: '评测方法',
    elements: [
      {
        value: 0,
        name: '自动规则打分',
        code: 'AUTOMATIC_RULE_SCORING',
        introduction: '计算模型预测结果与真实标注的文本相似度指标（例如ROUGE、BLUE等），适合标准选择题或简单问答场景。'
      }
    ],
    rules: []
  },
  {
    dictCode: 'DATASET_DRAWER_TYPE',
    dictName: '数据集抽屉类型',
    elements: [
      {
        value: 'add',
        name: '新建数据集',
        code: 'ADD'
      },
      {
        value: 'import',
        name: '导入数据',
        code: 'IMPORT'
      }
    ]
  },
  {
    dictCode: 'DATASET_DATA_PROCESSING_JOB_STATUS',
    dictName: '数据集数据处理任务状态',
    elements: [
      {
        code: 'FAILED',
        name: '失败',
        value: 3
      },
      {
        code: 'FINISHED',
        name: '已完成',
        value: 2
      },
      {
        code: 'QUEUING',
        name: '队列中',
        value: 0
      },
      {
        code: 'PROCESSING',
        name: '处理中',
        value: 1
      }
    ]
  },

  {
    dictCode: 'MODEL_TRAINING_STAGE',
    dictName: '模型训练阶段',
    elements: [
      {
        value: 'sft',
        name: 'SFT精调训练',
        code: 'SFT',
        introduction: '有监督的精调训练，增强模型指令跟随的能力，提供全参和高效训练方式'
      }
    ],
    rules: []
  },
  {
    dictCode: 'DATASET_DATA_PROCESSING_JOB_COMPONRENT_TYPE',
    dictName: '数据集数据处理任务组件类型',
    elements: [
      {
        code: 'ADD',
        name: '新增任务',
        value: 'add'
      },
      {
        code: 'DETAIL',
        name: '任务详情',
        value: 'detail'
      }
    ]
  },
  {
    dictCode: 'DATASET_DATA_PROCESSING_JOB_OPERATOR_TYPE',
    dictName: '数据集数据处理算子类型',
    elements: [
      {
        code: 'TEXT_NORMALIZATION',
        name: '文本标准化',
        value: 'text_normalization'
      },
      {
        code: 'SPECIAL_CONTENT_REMOVAL',
        name: '特殊内容移除',
        value: 'special_content_removal'
      },
      {
        code: 'PRIVACY_DATA_MASKING',
        name: '隐私信息打码',
        value: 'privacy_data_masking'
      },
      {
        code: 'SPECIAL_CHARACTER_RATIO_FILTER',
        name: '特殊字符占比过滤',
        value: 'special_character_ratio_filter'
      },
      {
        code: 'SENSITIVE_WORD_FILTER',
        name: '敏感词过滤',
        value: 'sensitive_word_filter'
      },
      {
        code: 'LENGTH_FILTER',
        name: '长度过滤',
        value: 'length_filter'
      },
      {
        code: 'MD5_DEDUPLICATION',
        name: 'MD5去重',
        value: 'md5_deduplication'
      }
    ]
  },
  {
    dictCode: 'DATASET_DATA_PROCESSING_JOB_IMPORT_STATUS',
    dictName: '数据集数据处理任务导入状态',
    elements: [
      {
        code: 'FAILED',
        name: '失败',
        value: 3
      },
      {
        code: 'FULL_FAILED',
        name: '失败',
        value: 4
      },
      {
        code: 'PARTIAL_SUCCESS',
        name: '部分成功',
        value: 2
      },
      {
        code: 'NORMAL',
        name: '成功',
        value: 1
      },
      {
        code: 'PROCESSING',
        name: '导入中',
        value: 0
      }
    ]
  },
  {
    dictCode: 'DATASET_DATA_AND_MODEL_TRAIN_JOB_NAME',
    dictName: '数据集和模型精调任务名称',
    elements: [
      {
        code: 'CLEAN_TASKS',
        name: '数据清洗任务',
        value: 'clean_tasks'
      },
      {
        code: 'ENHANCE_TASKS',
        name: '数据增强任务',
        value: 'enhance_tasks'
      },
      {
        code: 'CLASSIFY_TASKS',
        name: '文本分类任务',
        value: 'classify_tasks'
      },
      {
        code: 'MODEL_TRAINS',
        name: '模型精调任务',
        value: 'model_trains'
      },
      {
        code: 'MODEL_TEST',
        name: '模型评测任务',
        value: 'model_test'
      }
    ]
  },
  {
    dictCode: 'WORKFLOW_VISION_CONFIG_DETAIL',
    dictName: '工作流编排视觉分辨率',
    elements: [
      {
        code: 'HIGH',
        name: '高',
        value: VisionDetail.高
      },
      {
        code: 'LOW',
        name: '低',
        value: VisionDetail.低
      }
    ]
  },
  {
    dictCode: 'APP_TASK_PLAN',
    dictName: '任务计划',
    elements: [
      {
        value: 'all',
        name: '全部',
        code: ''
      },
      {
        value: 'enable',
        name: '启用中',
        code: 1
      },
      {
        value: 'disable',
        name: '已停用',
        code: 0
      }
    ],
    rules: []
  }
]
