<template>
  <AiPageLayout>
    <template #header>
      <a-button class="mb-2 back-btn" @click="() => router.go(-1)">
        <template #icon><icon-left /></template>
        返回
      </a-button>
    </template>
    <div class="appmanage flex">
      <div class="appmanage-left">
        <div class="run-box">
          <div class="run-header">
            <a-avatar class="run-header-avatar" shape="square">
              <span>
                <AiSvgIcon :name="`workflow-ai-${appInfo?.app?.mode}`" />
              </span>
            </a-avatar>
            <div class="run-header-content overflow-hidden text-ellipsis whitespace-nowrap">
              {{ appInfo?.app?.name }}
            </div>
          </div>
          <div class="p-4 pb-0">
            <RunOnce
              v-if="promptConfig"
              ref="runOnceRef"
              :inputs="inputs"
              :visionConfig="visionConfig"
              :promptConfig="promptConfig!"
              @onSend="handleSend"
              @onInputsChange="setInputs"
              @onVisionFilesChange="setCompletionFiles"
            />
          </div>
          <div v-if="promptConfig" class="px-4 flex justify-between pb-4">
            <a-button @click="reset">清空</a-button>
            <a-button type="primary" @click="run">运行</a-button>
          </div>
        </div>
      </div>
      <div class="appmanage-right">
        <div class="run-box">
          <a-empty v-if="!runing" style="margin-top: 50%">没有运行数据</a-empty>
          <ResultText
            v-else
            :isRunning="workflowRunningData?.result?.status === 'running' || !workflowRunningData?.result"
            :outputs="workflowRunningData?.result?.outputs"
            :resultText="workflowRunningData?.resultText"
            :allFiles="workflowRunningData?.result?.files"
            :error="workflowRunningData?.result?.error"
            :resultTabActive="workflowRunningData?.resultTabActive"
            :status="workflowRunningData?.result?.status"
          />
        </div>
      </div>
    </div>
  </AiPageLayout>
</template>

<script setup lang="ts">
import { getAppParams, shareRun } from '@/apis/workflow/share'
import type { PromptConfig } from '@/views/app/workflow/types/debug'
import type { SiteInfo } from '@/views/app/workflow/types/share'
import type { MoreLikeThisConfig, TextToSpeechConfig } from '@/views/workspace/ai/share/type'
import { useAppShare } from '@/stores'
import { Resolution, TransferMethod, type VisionFile, type VisionSettings } from '@/views/app/workflow/types/app'
import RunOnce from '../share/text-generation/run-once.vue'
import { handleShareRun } from './share-run'
import { useWorkflowStore } from '@/stores'
import { storeToRefs } from 'pinia'
import ResultText from './result-text.vue'
import { userInputsFormToPromptVariables } from '@/views/app/workflow/utils/model-config'

const router = useRouter()

const { workflowRunningData } = storeToRefs(useWorkflowStore())
const { installedApps } = useAppShare()
const route = useRoute()
const inputs = ref<Record<string, any>>({})
const appId = ref<string>('')
const siteInfo = ref<SiteInfo | null>(null)
const promptConfig = ref<PromptConfig | null>(null)
const moreLikeThisConfig = ref<MoreLikeThisConfig | null>(null)
const textToSpeechConfig = ref<TextToSpeechConfig | null>(null)
const visionConfig = ref<VisionSettings>({
  enabled: false,
  number_limits: 2,
  detail: Resolution.low,
  transfer_methods: [TransferMethod.local_file]
})

const completionFiles = ref<VisionFile[]>([])
const appInfo = ref()
onMounted(async () => {
  const appParams = await getAppParams((route.params.appId as string) || '')
  const currentApp = installedApps.find((ele) => ele.id === route.params.appId)
  appInfo.value = currentApp
  const appData = {
    app_id: currentApp?.id || '',
    site: {
      title: currentApp?.app.name || '',
      prompt_public: false,
      copyright: '',
      icon: currentApp?.app.icon,
      icon_background: currentApp?.app.icon_background
    },
    plan: 'basic'
  }
  if (currentApp) {
    const { app_id, site } = appData
    appId.value = app_id
    siteInfo.value = site

    const { user_input_form, more_like_this, file_upload, text_to_speech }: any = appParams

    visionConfig.value = {
      ...file_upload,
      transfer_methods: file_upload.allowed_file_upload_methods || file_upload.allowed_upload_methods,
      image_file_size_limit: appParams?.system_parameters?.image_file_size_limit,
      fileUploadConfig: appParams?.system_parameters
    }

    const prompt_variables = userInputsFormToPromptVariables(user_input_form)

    promptConfig.value = {
      prompt_template: '',
      prompt_variables
    } as PromptConfig

    moreLikeThisConfig.value = more_like_this
    textToSpeechConfig.value = text_to_speech
  }
})

const setInputs = (newInputs: Record<string, any>) => {
  inputs.value = newInputs
}

const setCompletionFiles = (files: VisionFile[]) => {
  completionFiles.value = files
}
const runing = ref(false)
const run = async () => {
  try {
    runing.value = true
    handleShareRun(route.params.appId as string, {
      inputs: inputs.value,
      response_mode: 'streaming'
    })
  } catch {}
}
const runOnceRef = ref<InstanceType<typeof RunOnce>>()

const reset = () => {
  runOnceRef.value?.reset()
}
const handleSend = () => {}
</script>

<style scoped lang="scss">
.appmanage {
  width: 100%;
  height: 100%;
  box-sizing: border-box;

  &-left {
    width: 50%;
    padding: var(--padding);
    border-right: 1px solid var(--color-border-2);
  }

  .run-box {
    width: 100%;
    height: 100%;
    border-radius: 5px;
    // box-shadow: ;
    box-shadow: 0 0 0 1px var(--color-border-1);
    overflow-y: auto;

    .run-header {
      height: 50px;
      display: flex;
      align-items: center;
      padding: 0 15px;
      border-bottom: 1px solid var(--color-border-2);

      &-avatar {
        height: 24px;
        width: 24px;
        margin-right: 10px;
        border-radius: 4px;
        background: linear-gradient(rgb(var(--primary-6)) 0%, rgb(var(--primary-4)) 100%);
      }

      &-content {
        flex: 1;
      }
    }
  }

  &-right {
    padding: var(--padding);
    width: 50%;
  }
}
</style>
