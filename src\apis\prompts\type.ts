/**
 * Prompt
 * 提示词管理
 */
export interface Prompt {
  promptKey?: string
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string
  /** 创建人ID */
  createdBy?: string
  /**
   * 描述
   * @minLength 0
   * @maxLength 65535
   */
  description?: string
  echoMap?: object
  /** 主键 */
  id?: string
  /**
   * 提示词名称
   * @minLength 0
   * @maxLength 255
   */
  name?: string
  /**
   * 状态：1-草稿，2-已发布
   * @format int32
   */
  status?: number
  /**
   * 提示词类型：1-系统提示词，2-用户提示词
   * @format int32
   */
  type?: string
  /**
   * 提示词内容
   * @minLength 0
   * @maxLength 65535
   */
  content?: string
  /**
   * 最后修改时间
   * @format date-time
   */
  updateTime?: string
  /** 最后修改人ID */
  updatedBy?: string
  /**
   * 标签
   * @minLength 0
   * @maxLength 255
   */
  tags?: (string | number)[]
  version?: string
}

/**
 * PromptPageQuery
 * 提示词查询参数
 */
export interface PromptPageQuery {
  /** 描述 */
  description?: string
  /** 提示词名称 */
  name?: string
  /**
   * 状态：1-草稿，2-已发布
   * @format int32
   */
  status?: number
  /**
   * 提示词类型：系统提示词，用户提示词
   * @format int32
   */
  type?: string
  /** 标签 */
  tags?: (string | number)[]
}

/**
 * PromptSaveDTO
 * 提示词保存DTO
 */
export interface PromptSaveDTO {
  /**
   * 描述
   * @minLength 0
   * @maxLength 65535
   */
  description?: string
  /**
   * 提示词名称
   * @minLength 0
   * @maxLength 255
   */
  name?: string
  /**
   * 状态：1-草稿，2-已发布
   * @format int32
   */
  status?: number
  /**
   * 提示词类型：1-系统提示词，2-用户提示词
   * @format int32
   */
  type?: string
  /**
   * 提示词内容
   * @minLength 0
   * @maxLength 65535
   */
  content?: string
  /**
   * 标签
   * @minLength 0
   * @maxLength 255
   */
  tags?: (string | number)[]
}

export interface ExtendedPromptDTO extends PromptSaveDTO {
  promptKey?: string
  id?: string
  helpInfo?: string
}

/**
 * PromptUpdateDTO
 * 提示词更新DTO
 */
export interface PromptUpdateDTO {
  /**
   * 描述
   * @minLength 0
   * @maxLength 65535
   */
  description?: string
  /** 主键 */
  id?: string
  /**
   * 提示词名称
   * @minLength 0
   * @maxLength 255
   */
  name?: string
  /**
   * 状态：1-草稿，2-已发布
   * @format int32
   */
  status?: number
  /**
   * 提示词类型：1-系统提示词，2-用户提示词
   * @format int32
   */
  type?: string
  /**
   * 提示词内容
   * @minLength 0
   * @maxLength 65535
   */
  content?: string
  /**
   * 标签
   * @minLength 0
   * @maxLength 255
   */
  tags?: (string | number)[]
}

/** IPage«Prompt» */
export interface IPagePrompt {
  /** @format int64 */
  current?: number
  /** @format int64 */
  pages?: number
  records?: Prompt[]
  /** @format int64 */
  size?: number
  /** @format int64 */
  total?: number
}

/**
 * PageParams«PromptPageQuery»
 * 分页参数
 */
export interface PageParamsPromptPageQuery {
  /**
   * 当前页
   * @format int64
   * @example 1
   */
  current?: number
  /** 扩展参数 */
  extra?: object
  /** 查询参数 */
  model: PromptPageQuery
  /**
   * 排序规则, 默认descending
   * @example "descending"
   */
  order?: 'descending' | 'ascending'
  /**
   * 页面大小
   * @format int64
   * @example 10
   */
  size?: number
  /**
   * 排序,默认createTime
   * @example "id"
   */
  sort?: 'id' | 'createTime' | 'updateTime'
}

/** R«Prompt» */
export interface RPrompt {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: Prompt
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}

/** R«IPage«Prompt»» */
export interface RIPagePrompt {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: IPagePrompt
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}

/** R«List«Prompt»» */
export interface RListPrompt {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: Prompt[]
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}

/** R«boolean» */
export interface RBoolean {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: boolean
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}
