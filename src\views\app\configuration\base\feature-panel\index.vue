<template>
  <div class="rounded-xl border-effects-highlight bg-background-section-burn pb-3">
    <!--Header-->
    <div class="px-3 pt-2">
      <div class="flex h-8 items-center justify-between">
        <div class="flex shrink-0 items-center space-x-1">
          <template v-if="slots.headerIcon">
            <div v-if="headerIcon" class="flex h-6 w-6 items-center justify-center">
              <slot name="headerIcon" />
            </div>
          </template>
          <template v-if="slots.title">
            <div class="system-sm-semibold text-text-secondary">
              <slot name="title" />
            </div>
          </template>
        </div>
        <div class="flex items-center gap-2">
          <slot name="headerRight" />
        </div>
      </div>
    </div>
    <!--Body-->
    <div class="mt-1 px-3">
      <slot />
    </div>
  </div>
</template>
<script setup lang="ts">
const slots = useSlots()
const props = withDefaults(
  defineProps<{
    panelTitle?: string
  }>(),
  {
    panelTitle: ''
  }
)
const headerIcon = ref(false)
</script>
<style scoped lang="scss">
.bg-background-section-burn {
  background: #f2f4f7;
}
</style>
