<template>
  <a-drawer
    popup-container="#parentNode"
    :visible="visible"
    :footer="false"
    :mask="false"
    class="run-panel-drawer"
    :width="400"
    unmountOnClose
    :drawerStyle="{ borderRadius: '8px', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }"
    :body-style="{ padding: 0 }"
    @cancel="handleCancel"
  >
    <!-- <template #title>版本历史</template> -->
    <template #header>
      <div class="header">
        <div class="title">版本历史</div>
        <div class="right-btn">
          <a-select
            :default-value="versionType"
            @change="handleChangeVersionType"
            v-model="versionType"
            :style="{ width: '220px' }"
            placeholder="Please select ..."
          >
            <a-option>全部</a-option>
            <a-option>仅你的</a-option>
            <template #footer>
              <div style="padding: 6px 0; display: flex; justify-content: space-around">
                <span>只显示已命名版本</span>
                <a-switch size="small" @change="changeswitch" v-model="switchversion" type="round" />
              </div>
            </template>
          </a-select>
          <icon-close style="margin-left: 10px" @click="handleCancel" />
        </div>
      </div>
    </template>
    <div class="content">
      <a-timeline>
        <a-timeline-item
          v-for="(item, index) in versionData?.items"
          class="item"
          :class="{ selected: selectindex === index }"
          :label="
            item.version !== 'draft'
              ? dayjs(item?.updated_at * 1000)
                  .locale('zh-cn')
                  .format('YY-MM-DD HH:mm:ss') +
                '·' +
                item.created_by.name
              : ''
          "
          :dotColor="selectindex === index ? '#00B42A' : '#D9D9D9'"
          dot-type="hollow"
          :key="item.id"
          @click="toggleSelect(item, index)"
        >
          <template v-if="item.version === 'draft'">
            <div class="text-lg font-medium">当前草稿</div>
          </template>
          <template v-if="item.version !== 'draft'">
            <div class="top-content">
              <div class="text-lg font-medium">
                {{ item.marked_name == '' ? '未命名' : item.marked_name }}
                <a-tag color="arcoblue" style="margin-left: 10px" v-if="index == 1" :default-checked="true">最新</a-tag>
              </div>
              <div class="btn">
                <a-dropdown
                  @select="
                    (value) => {
                      handleSelect(value, item)
                    }
                  "
                >
                  <a-button size="mini">
                    <template #icon>
                      <icon-more />
                    </template>
                  </a-button>
                  <template #content>
                    <a-doption value="restore">恢复</a-doption>
                    <a-doption value="edit">编辑信息</a-doption>
                    <a-doption v-if="index !== 1" value="delete">删除</a-doption>
                  </template>
                </a-dropdown>
              </div>
            </div>
          </template>
        </a-timeline-item>
      </a-timeline>
    </div>
    <editModel
      :editData="editData"
      v-if="editvisable"
      :editvisable="editvisable"
      @hideEditModel="hideEditModel"
    ></editModel>
  </a-drawer>
</template>
<script setup lang="ts">
import dayjs from 'dayjs'
import { Message } from '@arco-design/web-vue'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
dayjs.extend(relativeTime)
import { delversion } from '@/apis/workflow/index'
import editModel from './component/editModel.vue'
import { Modal } from '@arco-design/web-vue'
import { useUserStore } from '@/stores'
const props = defineProps<{
  versionData?: any
  newHash?: any
}>()
const visible = ref(false)
const emits = defineEmits(['hideversionPanel', 'changeworkflow'])
const editvisable = ref(false)
const editData = ref({})
const handleCancel = () => {
  visible.value = false
  emits('hideversionPanel')
}
const versionType = ref('全部')
const switchversion = ref(false)
const isSelected = ref(false)
const workflowRuns = ref([{}])
const selectindex = ref(0)
const toggleSelect = (item, index) => {
  emits('changeworkflow', item)
  selectindex.value = index
}

const handleSelect = (value, item) => {
  console.log(value, item)
  if (value == 'edit') {
    editData.value = item
    editvisable.value = true
  } else if (value == 'restore') {
    restoreData(item)
  } else {
    deleteData(item)
  }
}
const route = useRoute()
const appId = route.params.appId as string

const rootMethods = inject('saveworkFlowversion')
const verhistory = inject('rootMethods')
const userStore = useUserStore()
const changeswitch = () => {
  verhistory.versionhistory(switchversion.value, 'switch')
}
const handleChangeVersionType = () => {
  if (versionType.value == '全部') {
    verhistory.versionhistory()
  } else {
    console.log(userStore.userInfo.id)

    verhistory.versionhistory(userStore.userInfo.id, 'id')
  }
}
const deleteData = (item) => {
  Modal.confirm({
    title: `删除 ${item.marked_name == '' ? '未命名' : item.marked_name}`,
    content: `删除不可逆，请确认。`,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      delversion(appId, item.id).then((res) => {
        Message.success('删除成功')
        verhistory.versionhistory()
      })
    }
  })
}

const restoreData = (item) => {
  Modal.confirm({
    title: `恢复 ${item.marked_name == '' ? '未命名' : item.marked_name}`,
    content: `版本回滚后，当前草稿将被覆盖。`,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      rootMethods.saveDatas({
        conversation_variables: item.conversation_variables,
        environment_variables: item.environment_variables,
        features: item.features,
        graph: item.graph,
        hash: props.newHash
      })
    }
  })
}
const hideEditModel = () => {
  editvisable.value = false
}
defineExpose({
  visible,
  dayjs
})
</script>
<style scoped lang="scss">
.header {
  width: 100%;
  padding: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  .right-btn {
    display: flex;
    align-items: center;
  }
}
.content {
  width: 100%;
  transition: all 0.3s ease;
  cursor: pointer;
  padding: 20px;
  border-radius: 4px;

  .item {
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    line-height: 30px;
    min-height: 60px;
    padding: 10px;
    margin-top: 10px;
    .top-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    &:hover {
      // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      background: #c8ceda33;
    }

    &.selected {
      background-color: #155aef14; // 选中背景色
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      //border-left: 3px solid #1890ff; // 左侧高亮条
    }
    .btn {
      position: absolute;
      right: 0;
      top: 0;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover .btn {
      opacity: 1;
    }
  }
}
</style>
