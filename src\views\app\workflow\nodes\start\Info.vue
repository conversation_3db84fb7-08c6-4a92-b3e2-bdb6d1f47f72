<template>
  <div class="field-info">
    <div class="flex items-center justify-between mb-1">
      <span>输入字段</span>
      <a-button style="width: 24px; height: 24px" type="text" @click="onAdd">
        <template #icon>
          <icon-plus />
        </template>
      </a-button>
    </div>
    <Fieldlist :list="fieldList" @edit="onEdit" @delete="onDelete" />
    <AddFieldModal ref="AddFieldModalRef" @save-success="onSave" />
    <EditFieldModal ref="EditFieldModalRef" @save-success="onEditSave" />
  </div>
</template>

<script setup lang="ts">
import Fieldlist from './components/FieldList.vue'
import AddFieldModal from './components/AddFieldModal.vue'
import EditFieldModal from './components/EditFieldModal.vue'

interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
  min_value?: number
  max_value?: number
  file_types?: string[]
  max_file_size?: number
}
const props = withDefaults(
  defineProps<{
    node?: any
  }>(),
  {
    node: {}
  }
)

const AddFieldModalRef = ref<InstanceType<typeof AddFieldModal>>()
const EditFieldModalRef = ref<InstanceType<typeof EditFieldModal>>()

// 新增
const onAdd = () => {
  AddFieldModalRef.value?.onAdd()
}

// 编辑
const onEdit = (item: FieldType, index: number) => {
  EditFieldModalRef.value?.onEdit(item, index)
}

// 删除
const onDelete = (index: number) => {
  fieldList.value.splice(index, 1)
  console.log('删除字段，当前列表:', fieldList.value)
}

const fieldList = ref<FieldType[]>(props.node.variables)

const onSave = (e: any) => {
  const field: FieldType = {
    label: e.label,
    max_length: e.max_length,
    options: e.options || [],
    required: e.required && e.required.length > 0 ? e.required[0] : false,
    type: e.type,
    variable: e.variable
  }

  // 根据字段类型添加相应的属性
  if (e.min_value !== undefined) {
    field.min_value = e.min_value
  }
  if (e.max_value !== undefined) {
    field.max_value = e.max_value
  }
  if (e.file_types && e.file_types.length > 0) {
    field.file_types = e.file_types
  }
  if (e.max_file_size !== undefined) {
    field.max_file_size = e.max_file_size
  }

  fieldList.value.unshift(field)
  console.log('添加的字段:', field)
}

// 编辑保存
const onEditSave = (e: any) => {
  const field: FieldType = {
    label: e.label,
    max_length: e.max_length,
    options: e.options || [],
    required: e.required && e.required.length > 0 ? e.required[0] : false,
    type: e.type,
    variable: e.variable
  }

  // 根据字段类型添加相应的属性
  if (e.min_value !== undefined) {
    field.min_value = e.min_value
  }
  if (e.max_value !== undefined) {
    field.max_value = e.max_value
  }
  if (e.file_types && e.file_types.length > 0) {
    field.file_types = e.file_types
  }
  if (e.max_file_size !== undefined) {
    field.max_file_size = e.max_file_size
  }

  // 更新指定索引的字段
  if (e.index !== undefined && e.index >= 0 && e.index < fieldList.value.length) {
    fieldList.value[e.index] = field
    console.log('编辑的字段:', field, '索引:', e.index)
  }
}
</script>
<style scoped lang="scss">
.field-info {
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-1);
}
</style>
