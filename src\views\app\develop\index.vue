<template>
  <div class="develop-page">
    <!-- API服务器信息头部 -->
    <div class="develop-header">
      <div class="header-content">
        <ApiServer :app-id="appId" :appInfo="appInfo" />
      </div>
    </div>
    <!-- API文档内容 -->
    <div class="develop-content">
      <component :is="showComponent()" :baseUrl="apiUrl" :inputs="inputs" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import TemplateAdvancedChatZh from '../template/template-advanced-chat-zh.vue'
import TemplateChatZh from '../template/template-chat-zh.vue'
import TemplateWorkflowZh from '../template/template-workflow-zh.vue'
import TemplateZh from '../template/template-zh.vue'
import ApiServer from './components/ApiServer.vue'

defineOptions({ name: '<PERSON>elo<PERSON>' })

const route = useRoute()
const appId = computed(() => route.params.appId as string)

const props = withDefaults(
  defineProps<{
    appInfo: any
  }>(),
  {}
)
const apiUrl = computed(() => {
  return props.appInfo.api_base_url ? `${props.appInfo.api_base_url}` : ``
})
const variables = props.appInfo?.model_config?.configs?.prompt_variables || []

const inputs = variables.reduce((res: any, variable: any) => {
  res[variable.key] = variable.name || ''
  return res
}, {})
const comps = reactive({
  zh: {
    templateChat: shallowRef(TemplateChatZh),
    templateAdvancedChat: shallowRef(TemplateAdvancedChatZh),
    templateWorkflow: shallowRef(TemplateWorkflowZh),
    template: shallowRef(TemplateZh)
  }
})

const showComponent = () => {
  const mode = props.appInfo?.mode || ''
  switch (mode) {
    case 'advanced-chat':
      return comps.zh.templateAdvancedChat
    case 'workflow':
      return comps.zh.templateWorkflow
    case 'completion':
      return comps.zh.template
    default:
      return comps.zh.templateChat
  }
}
</script>

<style lang="scss">
.develop-page {
  display: flex;
  flex-direction: column;
  height: 100%;

  .develop-header {
    flex-shrink: 0;
    background: var(--color-bg-1);
    border-bottom: 1px solid var(--color-border-2);
    padding: 0 16px 16px;

    .header-content {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      max-width: 1200px;
      margin: 0 auto;

      .header-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--color-text-1);
        margin: 0;
      }
    }
  }

  .develop-content {
    flex: 1;
    overflow: hidden;
    overflow-y: auto;
    color: var(--color-text-2);
    padding: 20px;

    h1 {
      font-weight: 700;
      font-size: 24px;
      line-height: 30px;
      margin-bottom: 10px;
    }

    h2 {
      font-size: 18px;
      font-weight: 600;
      line-height: 28px;
      margin: 8px 0;
    }

    h3 {
      font-size: 16px;
      line-height: 24px;
      font-weight: 600;
      margin-top: 40px;
      margin-bottom: 8px;
    }

    hr {
      border: none;
      border-top: var(--color-border-2) solid 1px; // 变量符号 @ → $
      margin: 40px -20px;

      &.inner {
        margin: 0;
      }
    }

    p {
      line-height: 28px;
    }

    code {
      border-radius: 4px;
      padding: 4px 6px;
      box-shadow: inset 0 0 0 1px var(--color-border-2); // 变量符号 @ → $
      background-color: rgb(244 244 245);
    }

    .w {
      width: 80%;
      margin: 0 auto;
    }

    ul {
      padding-left: 26px;
      list-style-type: disc;

      li {
        padding-left: 6px;
        line-height: 26px;
        margin: 8px 0;

        &::marker {
          color: #d4d4d8;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .develop-page {
    .develop-header {
      padding: 12px 16px;

      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }
    }

    .develop-content {
      padding: 16px;
    }
  }
}
</style>
