<template>
  <div>
    <StatusContainer :status="props.status">
      <div class="flex">
        <div :class="cn('max-w-[120px] flex-[33%]', props.status === 'partial-succeeded' && 'min-w-[140px]')">
          <div className="system-2xs-medium-uppercase mb-1 text-text-tertiary">状态</div>
          <div
            :className="
              cn(
                'system-xs-semibold-uppercase flex items-center gap-1',
                props.status === 'succeeded' && 'text-util-colors-green-green-600',
                props.status === 'partial-succeeded' && 'text-util-colors-green-green-600',
                props.status === 'failed' && 'text-util-colors-red-red-600',
                props.status === 'stopped' && 'text-util-colors-warning-warning-600',
                props.status === 'running' && 'text-util-colors-blue-light-blue-light-600'
              )
            "
          >
            <span v-if="props.status === 'running'">Running</span>
            <span v-if="props.status === 'succeeded'">SUCCESS</span>
            <span v-if="props.status === 'partial-succeeded'">PARTIAL SUCCESS</span>
            <span v-if="props.status === 'exception'">EXCEPTION</span>
            <span v-if="props.status === 'failed'">FAIL</span>
            <span v-if="props.status === 'stopped'">STOP</span>
          </div>
        </div>
        <div class="max-w-[152px] flex-[33%]">
          <div class="system-2xs-medium-uppercase mb-1 text-text-tertiary">运行时间</div>
          <div class="system-sm-medium flex items-center gap-1 text-text-secondary">
            <div v-if="props.status === 'running'" class="h-2 w-16 rounded-sm bg-text-quaternary" />
            <span v-if="props.status !== 'running'">{{ props.time ? `${props.time?.toFixed(3)}s` : '-' }}</span>
          </div>
        </div>
        <div class="flex-[33%]">
          <div class="system-2xs-medium-uppercase mb-1 text-text-tertiary">总 token 数</div>
          <div class="system-sm-medium flex items-center gap-1 text-text-secondary">
            <div v-if="props.status === 'running'" class="h-2 w-20 rounded-sm bg-text-quaternary" />
            <span v-if="props.status !== 'running'">{{ `${props.tokens || 0} Tokens` }}</span>
          </div>
        </div>
      </div>
      <div v-if="props.status === 'failed' && props.error">
        <div class="my-2 h-[0.5px] bg-divider-subtle" />
        <div class="system-xs-regular text-text-destructive">{{ props.error }}</div>
        <div v-if="!!props.exceptionCounts">
          <div class="my-2 h-[0.5px] bg-divider-subtle" />
          <div class="system-xs-regular text-text-destructive">
            流程中有 {{ props.exceptionCounts }} 个节点运行异常，请前往追踪查看日志。
          </div>
        </div>
      </div>
      <div v-if="props.status === 'partial-succeeded' && !!props.exceptionCounts">
        <div class="my-2 h-[0.5px] bg-divider-deep" />
        <div class="system-xs-medium text-text-warning">
          流程中有 {{ props.exceptionCounts }} 个节点运行异常，请前往追踪查看日志。
        </div>
      </div>
      <div v-if="props.status === 'exception'">
        <div class="my-2 h-[0.5px] bg-divider-deep" />
        <div class="system-xs-medium text-text-warning">
          {{ props.error }}
        </div>
      </div>
    </StatusContainer>
  </div>
</template>

<script setup lang="ts">
import cn from '@/utils/classnames'
import StatusContainer from './status-container.vue'

const props = withDefaults(
  defineProps<{
    status: string
    time?: number
    tokens?: number
    error?: string
    exceptionCounts?: number
  }>(),
  {}
)
</script>

<style scoped lang="scss"></style>
