<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 500 ? 500 : '100%'"
    draggable
    @before-ok="onSubmit"
    @close="reset"
  >
    <AiForm ref="formRef" v-model="form" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { createEvaluator, getEvaluatorDetail, updateEvaluator } from '@/apis/evaluation'
import type { EvaluatorAddReq } from '@/apis/evaluation/types'
import { type ColumnItem, AiForm } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'
import { getModelTypesHttp } from '@/apis/model-mgmt'

defineOptions({ name: 'EvaluatorAddModal' })

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()

const visible = ref(false)
const evaluatorId = ref<string>('')
const isUpdate = computed(() => !!evaluatorId.value)
const title = computed(() => (isUpdate.value ? '修改评估器' : '新增评估器'))
const formRef = ref<InstanceType<typeof AiForm>>()
const modelOptions = ref<{ label: string; value: string }[]>([])

const [form, resetForm] = useResetReactive<EvaluatorAddReq>({
  name: '',
  description: '',
  modelId: '',
  prompt: ''
})

// 获取模型列表
const fetchModelList = async () => {
  try {
    const res = await getModelTypesHttp('llm')
    if (res.data && Array.isArray(res.data)) {
      const options: { label: string; value: string }[] = []
      res.data.forEach((group) => {
        if (group.models && Array.isArray(group.models)) {
          group.models.forEach((model) => {
            options.push({
              label: model.model,
              value: model.model
            })
          })
        }
      })
      modelOptions.value = options
    }
  } catch (error) {
    console.error('获取模型列表失败', error)
  }
}

onMounted(() => {
  fetchModelList()
})

const columns = reactive<ColumnItem[]>([
  {
    label: '名称',
    field: 'name',
    type: 'input',
    span: 24,
    required: true,
    props: {
      placeholder: '请输入名称',
      allowClear: true,
      maxLength: 50
    }
  },
  {
    label: '描述',
    field: 'description',
    type: 'textarea',
    span: 24,
    props: {
      placeholder: '请输入描述',
      allowClear: true
    }
  },
  {
    label: '模型选择',
    field: 'modelId',
    type: 'select',
    span: 24,
    required: true,
    props: {
      placeholder: '请选择模型',
      allowClear: true,
      options: modelOptions
    }
  },
  {
    label: 'Prompt',
    field: 'prompt',
    type: 'textarea',
    span: 24,
    required: true,
    props: {
      placeholder: '请输入内容，支持按此格式书写变量: {{USER_NAME}}',
      allowClear: true
    }
  }
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 提交
const onSubmit = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false

    if (isUpdate.value) {
      await updateEvaluator(evaluatorId.value, form)
      Message.success('修改成功')
    } else {
      await createEvaluator(form)
      Message.success('创建成功')
    }

    visible.value = false
    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

// 新增
const onAdd = () => {
  reset()
  evaluatorId.value = ''
  visible.value = true
}

// 修改
const onUpdate = async (id: string) => {
  reset()
  evaluatorId.value = id
  try {
    const res = await getEvaluatorDetail(id)
    form.name = res.name
    form.description = res.description || ''
    form.modelId = res.modelId || ''
    form.prompt = res.prompt || ''
    visible.value = true
  } catch (error) {
    // 错误处理
  }
}

// 暴露方法
defineExpose({
  onAdd,
  onUpdate
})
</script>
