import http from '@/utils/http'

export interface FileUploadResponse {
  id: string
  name: string
  size: number
  extension: string
  mime_type: string
  created_by: string
  created_at: number
  preview_url: string | null
}

const BASE_URL = 'console/api'
// 上传文件
export function uploadFile(file: File, onProgress?: (percent: number) => void) {
  const formData = new FormData()
  formData.append('file', file)
  const headers = {
    'Content-Type': 'multipart/form-data'
  }

  return http.post<FileUploadResponse>(`${BASE_URL}/files/upload`, formData, {
    headers: headers,
    onUploadProgress: (progressEvent: any) => {
      if (onProgress && progressEvent.total) {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(percent)
      }
    }
  })
}
