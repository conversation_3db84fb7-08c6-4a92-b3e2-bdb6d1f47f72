import { useVueFlow } from '@vue-flow/core'
import { WorkflowRunningStatus, type WorkflowStartedResponse } from '@/views/app/workflow/types/workflow'
import { useWorkflowStore } from '@/stores'

export const useWorkflowStarted = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowStarted = (params: WorkflowStartedResponse) => {
    const { task_id, data } = params
    const { workflowRunningData, setIterParallelLogMap, setWorkflowRunningData } = workflowStore
    const { nodes, setNodes, edges, setEdges } = useVueFlow()
    setIterParallelLogMap(new Map())
    const workflowData = {
      ...workflowRunningData,
      task_id: task_id,
      result: {
        ...workflowRunningData?.result,
        ...data,
        status: WorkflowRunningStatus.Running
      }
    }
    setWorkflowRunningData(workflowData)

    if (nodes.value?.length) {
      const newNodes = nodes.value.map((node) => {
        node.data._waitingRun = true
        node.data._runningBranchId = undefined
        return node
      })
      setNodes(newNodes)
    }

    if (edges.value?.length) {
      const newEdges = edges.value.map((edge) => {
        edge.data = {
          ...edge.data,
          _sourceRunningStatus: undefined,
          _targetRunningStatus: undefined,
          _waitingRun: true
        }
        return edge
      })
      setEdges(newEdges)
    }
  }

  return {
    handleWorkflowStarted
  }
}
