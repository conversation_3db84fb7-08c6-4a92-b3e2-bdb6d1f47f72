<template>
  <div class="variable-selector-example">
    <h3>变量选择器示例</h3>

    <div class="example-section">
      <h4>基本用法</h4>
      <div class="example-row">
        <div class="example-label">选择变量：</div>
        <div class="example-content">
          <VariableSelector
            :node-id="nodeid"
            :v-model:value-selector="selectedVariable"
            @change="handleVariableChange"
          />
        </div>
      </div>
      <div v-if="selectedVariable.length > 0" class="example-result">
        <div>已选择变量：{{ JSON.stringify(selectedVariable) }}</div>
      </div>
    </div>

    <div class="example-section">
      <h4>只读模式</h4>
      <div class="example-row">
        <div class="example-label">只读变量：</div>
        <div class="example-content">
          <VariableSelector :node-id="nodeid" :value-selector="['node1', 'variable1']" :readonly="true" />
        </div>
      </div>
    </div>

    <div class="example-section">
      <h4>变量类型过滤</h4>
      <div class="example-row">
        <div class="example-label">字符串变量：</div>
        <div class="example-content">
          <VariableSelector
            :node-id="nodeid"
            :value-selector="stringVariable"
            :var-type="VarType.string"
            @change="handleStringVariableChange"
          />
        </div>
      </div>
      <div class="example-row">
        <div class="example-label">数字变量：</div>
        <div class="example-content">
          <VariableSelector
            :node-id="nodeid"
            :value-selector="numberVariable"
            :var-type="VarType.number"
            @change="handleNumberVariableChange"
          />
        </div>
      </div>
    </div>

    <div class="example-section">
      <h4>自定义占位符</h4>
      <div class="example-row">
        <div class="example-label">自定义提示：</div>
        <div class="example-content">
          <VariableSelector :node-id="nodeid" placeholder="请选择一个变量" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import VariableSelector from './VariableSelector.vue'
import { VarType } from '@/views/app/workflow/types/workflow'
// const props = defineProps<{
//   nodeid: string
// }>()

const emit = defineEmits<{
  (e: 'select', valueSelector: string[], varItem: any): void
  (e: 'close'): void
}>()
// 模拟当前节点ID
const nodeid = ref('1748224735835')

// 选中的变量
const selectedVariable = ref<string[]>([])
const stringVariable = ref<string[]>([])
const numberVariable = ref<string[]>([])

// 变量选择事件处理
const handleVariableChange = (valueSelector: string[], varItem: any) => {
  selectedVariable.value = valueSelector
  console.log('Selected variable:', valueSelector, varItem)
}

const handleStringVariableChange = (valueSelector: string[], varItem: any) => {
  stringVariable.value = valueSelector
  console.log('Selected string variable:', valueSelector, varItem)
}

const handleNumberVariableChange = (valueSelector: string[], varItem: any) => {
  numberVariable.value = valueSelector
  console.log('Selected number variable:', valueSelector, varItem)
}
</script>

<style scoped lang="scss">
.variable-selector-example {
  padding: 20px;

  h3 {
    margin-bottom: 20px;
  }

  .example-section {
    margin-bottom: 24px;

    h4 {
      margin-bottom: 12px;
    }
  }

  .example-row {
    display: flex;
    margin-bottom: 12px;

    .example-label {
      width: 100px;
      flex-shrink: 0;
      display: flex;
      align-items: center;
    }

    .example-content {
      flex: 1;
      max-width: 400px;
    }
  }

  .example-result {
    margin-top: 8px;
    padding: 8px;
    background-color: var(--color-fill-2);
    border-radius: 4px;
  }
}
</style>
