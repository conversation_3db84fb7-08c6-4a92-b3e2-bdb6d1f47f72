<template>
  <div class="datasets-tab">
    <!-- 固定顶部筛选区域 -->
    <div class="header-area fixed-header">
      <div class="title-area" />
      <!-- 操作区 -->
      <div class="actions-container">
        <a-checkbox class="mr-4" :model-value="includeAll" @change="toggleIncludeAll">所有知识库</a-checkbox>

        <!-- 新的标签筛选下拉框 -->
        <a-dropdown
          trigger="click"
          class="mr-4 tag-filter-dropdown"
          :popup-visible="tagFilterOpen"
          @popup-visible-change="handleTagFilterVisibleChange"
        >
          <a-button
            class="tag-filter-button"
            :class="{ 'has-selection': selectedTagIds.length > 0, 'is-open': tagFilterOpen }"
          >
            <template #icon>
              <icon-tag class="filter-icon" />
            </template>
            <span class="filter-text">
              {{ selectedTagIds.length > 0 ? `已选${selectedTagIds.length}个标签` : '全部标签' }}
            </span>
            <icon-down class="arrow-icon" :class="{ rotated: tagFilterOpen }" />
          </a-button>
          <template #content>
            <div class="modern-tag-dropdown">
              <div class="tag-search-section">
                <a-input v-model="tagFilterSearchText" placeholder="搜索标签" allow-clear class="tag-search-input">
                  <template #prefix>
                    <icon-search class="search-icon" />
                  </template>
                </a-input>
              </div>
              <div class="tag-options-section">
                <div
                  v-for="tag in filteredTagOptions"
                  :key="tag.id"
                  class="tag-option-item"
                  @click="toggleTagSelection(tag.id)"
                >
                  <a-checkbox :model-value="selectedTagIds.includes(tag.id)" class="tag-checkbox" />
                  <span class="tag-label">{{ tag.name }}</span>
                </div>
                <div v-if="filteredTagOptions.length === 0" class="no-tags-message">暂无匹配的标签</div>
              </div>
            </div>
          </template>
        </a-dropdown>

        <a-input-search
          v-model="searchText"
          class="search-input mr-4"
          allow-clear
          placeholder="搜索"
          :loading="loading"
          :disabled="loading"
          @search="handleSearch"
          @change="handleSearch"
        />
        <!-- <a-button type="outline" shape="round">
          <template #icon>
            <icon-link />
          </template>
          外部知识库 API
        </a-button> -->
      </div>
    </div>

    <!-- 可滚动的知识库列表区域 -->
    <div class="content-area">
      <div class="datasets-grid">
        <!-- 创建知识库卡片 -->
        <div class="dataset-card create-card" @click="goToCreatePage">
          <div class="create-content">
            <div class="create-icon">
              <icon-plus />
            </div>
            <div class="create-title">创建知识库</div>
            <div class="create-desc">导入您自己的文本数据或通过 Webhook 实时写入数据以增强 LLM 的上下文。</div>
            <!-- <a-button class="outer-btn" status="normal" type="text">
            连接外部知识库
            <template #icon><icon-right /></template>
          </a-button> -->
          </div>
        </div>

        <!-- 知识库卡片列表 -->
        <div
          v-for="dataset in datasets"
          :key="dataset.id"
          class="dataset-card"
          @mouseenter="dataset.showActions = true"
          @mouseleave="
            !activeMenuDatasetId || activeMenuDatasetId !== dataset.id ? (dataset.showActions = false) : null
          "
        >
          <div class="card-content" @click="goToDocumentsPage(dataset.id)">
            <div class="card-icon">
              <icon-folder size="24" />
            </div>
            <div class="card-info">
              <div class="card-title">{{ dataset.name }}</div>
              <div class="card-meta">
                <span>{{ dataset.document_count }}文档</span>
                <span class="dot-separator" />
                <span>{{ Math.floor(dataset.word_count / 1000) }}千字符</span>
                <span class="dot-separator" />
                <span>{{ dataset.app_count }}次被引用</span>
              </div>
            </div>
          </div>
          <div class="card-description" @click="goToDocumentsPage(dataset.id)">
            {{ dataset.description }}
          </div>

          <!-- 卡片底部操作区 -->
          <div
            v-if="
              dataset.tags?.length > 0 ||
              dataset.showActions ||
              activeDropdownDatasetId === dataset.id ||
              activeMenuDatasetId === dataset.id
            "
            class="card-actions"
          >
            <div class="action-buttons">
              <div class="tag-area">
                <template v-if="dataset.tags && dataset.tags.length > 0">
                  <div class="tag-selected">
                    <span class="tag-text" @click="toggleTagDropdown(dataset.id)">
                      <icon-tag />
                      {{ dataset.tags.map((tag) => tag.name).join(', ') }}
                    </span>
                  </div>
                </template>
                <a-button
                  v-else-if="
                    dataset.showActions || activeDropdownDatasetId === dataset.id || activeMenuDatasetId === dataset.id
                  "
                  class="tag-btn"
                  type="text"
                  size="small"
                  @click="toggleTagDropdown(dataset.id)"
                >
                  <template #icon><icon-tag /></template>
                  添加标签
                </a-button>
              </div>
              <a-dropdown
                v-if="
                  dataset.showActions || activeDropdownDatasetId === dataset.id || activeMenuDatasetId === dataset.id
                "
                trigger="click"
                :popup-visible="activeMenuDatasetId === dataset.id"
                @select="handleMenuSelect($event, dataset)"
                @popup-visible-change="(visible: boolean) => handleMenuVisibleChange(visible, dataset.id)"
              >
                <a-button class="menu-btn" type="text" size="small">
                  <template #icon><icon-more /></template>
                </a-button>
                <template #content>
                  <a-doption value="settings">设置</a-doption>
                  <a-doption value="delete">删除</a-doption>
                </template>
              </a-dropdown>
            </div>

            <!-- 标签下拉框 -->
            <div v-if="activeDropdownType === 'tag' && activeDropdownDatasetId === dataset.id" class="tag-dropdown">
              <div class="tag-search">
                <a-input v-model="tagSearchText" placeholder="搜索或者创建" allow-clear @keyup.enter="handleCreateTag">
                  <template #prefix>
                    <icon-search />
                  </template>
                </a-input>
              </div>
              <div class="tag-list">
                <div v-for="tag in filteredTags" :key="tag.id" class="tag-item-row">
                  <a-checkbox
                    :model-value="isTagSelected(tag, dataset)"
                    @change="(checked: boolean) => handleTagCheck(checked, tag, dataset)"
                  >
                    {{ tag.name }}
                  </a-checkbox>
                </div>
                <div class="tag-divider" />
                <div class="tag-manage" @click="showTagManagement = true">
                  <icon-tag />
                  管理标签
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 固定底部分页区域 -->
    <div class="pagination-area fixed-bottom">
      <a-pagination
        v-model:current="currentPage"
        v-model:page-size="pageSize"
        :total="totalCount"
        :show-total="true"
        :show-jumper="true"
        :show-page-size="true"
        :page-size-options="[12, 24, 48, 96]"
        class="datasets-pagination"
        @change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <template #total="{ total }">
          <span class="pagination-total">共 {{ total }} 个知识库</span>
        </template>
      </a-pagination>
    </div>

    <!-- 管理标签对话框 -->
    <a-modal
      v-model:visible="showTagManagement"
      title="管理标签"
      width="500px"
      :mask-closable="false"
      :unmount-on-close="false"
      title-align="start"
      draggable
      modal-class="tag-management-modal"
      @cancel="showTagManagement = false"
    >
      <template #close-btn>
        <a-button class="modal-close-btn" type="text" size="mini" @click="showTagManagement = false">
          <icon-close />
        </a-button>
      </template>
      <div class="tag-management-content">
        <div class="create-tag-input">
          <a-input
            v-model="newTagName"
            placeholder="创建新标签"
            allow-clear
            class="tag-input"
            @keyup.enter="createNewTag"
          />
          <a-button type="primary" :disabled="!newTagName.trim()" @click="createNewTag">创建新标签</a-button>
        </div>
        <div class="tag-management-list">
          <div v-for="tag in availableTags" :key="tag.id" class="tag-management-item">
            <div class="tag-name">
              <template v-if="editingTagId === tag.id">
                <a-input
                  ref="tagEditInput"
                  v-model="editingTagName"
                  size="small"
                  class="tag-edit-input"
                  :maxLength="50"
                  @keyup.enter="saveEditedTag(tag)"
                  @keyup.esc="cancelEditTag(tag)"
                />
              </template>
              <a-tag v-else size="small">{{ tag.name }}</a-tag>
            </div>
            <div class="tag-edit">
              <template v-if="editingTagId === tag.id">
                <a-button type="text" size="small" status="success" @click="saveEditedTag(tag)">
                  <template #icon><icon-check /></template>
                </a-button>
                <a-button type="text" size="small" @click="cancelEditTag(tag)">
                  <template #icon><icon-close /></template>
                </a-button>
              </template>
              <template v-else>
                <a-button type="text" size="small" @click="editTag(tag)">
                  <template #icon><icon-edit /></template>
                </a-button>
                <a-popconfirm
                  :title="`删除标签 '${tag.name}'`"
                  ok-text="确认"
                  cancel-text="取消"
                  :ok-button-props="{ status: 'danger' }"
                  position="top"
                  @ok="doDeleteTag"
                  @cancel="showDeleteTagConfirm = false"
                >
                  <template #content>
                    <p class="popconfirm-content">
                      标签{{ Number(tag.binding_count) > 0 ? '正在使用中' : '' }}，是否删除？
                    </p>
                  </template>
                  <a-button type="text" size="small" status="danger" @click="confirmDeleteTag(tag)">
                    <template #icon><icon-delete /></template>
                  </a-button>
                </a-popconfirm>
              </template>
            </div>
          </div>
        </div>
      </div>
    </a-modal>

    <!-- 知识库设置对话框 -->
    <a-modal
      v-model:visible="showSettingsModal"
      title="知识库设置"
      ok-text="保存"
      cancel-text="取消"
      :mask-closable="false"
      :unmount-on-close="false"
      title-align="start"
      width="600px"
      draggable
      modal-class="dataset-settings-modal"
      @ok="saveDatasetSettings"
      @cancel="showSettingsModal = false"
    >
      <template #close-btn>
        <a-button class="modal-close-btn" type="text" size="mini" @click="showSettingsModal = false">
          <icon-close />
        </a-button>
      </template>
      <div class="settings-form">
        <div class="form-item">
          <div class="form-label">知识库名称</div>
          <a-input v-model="editingDataset.name" placeholder="请输入知识库名称" class="settings-input" />
        </div>
        <div class="form-item">
          <div class="form-label">知识库描述</div>
          <a-textarea
            v-model="editingDataset.description"
            placeholder="描述该数据集的内容。详细描述可以让 AI 更快地访问数据集的内容。如果为空，LangGenius 将使用默认的命中策略。"
            allow-clear
            :auto-size="{ minRows: 4, maxRows: 8 }"
            class="settings-textarea"
          />
        </div>
      </div>
    </a-modal>

    <!-- 删除确认对话框 -->
    <a-modal
      v-model:visible="showDeleteConfirm"
      title="要删除知识库吗?"
      ok-text="确认"
      cancel-text="取消"
      :ok-button-props="{ status: 'danger' }"
      @ok="confirmDelete"
      @cancel="showDeleteConfirm = false"
    >
      <p>删除知识库是不可逆的。用户将无法再访问您的知识库，所有的提示配置和日志将被永久删除。</p>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  getDatasetList,
  getDatasetTags,
  createDatasetTag,
  updateDatasetTag,
  deleteDatasetTag,
  createTagBinding,
  deleteTagBinding,
  checkDatasetUsage,
  deleteDataset,
  updateDataset
} from '@/apis/datasets'
import type { Dataset, DatasetTag } from '@/apis/datasets/type'
import { Message } from '@arco-design/web-vue'

const router = useRouter()

// 加载状态
const loading = ref(false)
const tagsLoading = ref(false)

// 筛选参数
const currentPage = ref(1)
const pageSize = ref(12) // 修改默认每页显示数量
const searchText = ref('')
const selectedTagIds = ref<string[]>([])
const includeAll = ref(false)

// 新的标签筛选相关状态
const tagFilterOpen = ref(false)
const tagFilterSearchText = ref('')

// 知识库数据
const datasets = ref<(Dataset & { showActions?: boolean })[]>([])
const totalCount = ref(0)

// 可用标签
const availableTags = ref<DatasetTag[]>([])

// 控制下拉框和菜单状态
const activeDropdownType = ref<string | null>(null) // 'tag' 或 null
const activeDropdownDatasetId = ref<string | null>(null)
const activeMenuDatasetId = ref<string | null>(null)

const tagSearchText = ref('')
const showTagManagement = ref(false)
const newTagName = ref('')
const editingTagId = ref<string | null>(null)
const editingTagName = ref('')
const showDeleteTagConfirm = ref(false)
const tagToDelete = ref<DatasetTag | null>(null)

// 知识库设置相关
const showSettingsModal = ref(false)
const editingDataset = ref<Partial<Dataset>>({})

// 删除确认相关
const showDeleteConfirm = ref(false)
const datasetToDelete = ref<Dataset | null>(null)

// 获取知识库列表
const fetchDatasets = async () => {
  try {
    loading.value = true
    const params: any = {
      page: currentPage.value,
      limit: pageSize.value,
      include_all: includeAll.value
    }

    // 添加标签过滤
    if (selectedTagIds.value.length > 0) {
      params.tag_ids = selectedTagIds.value.join(',')
    }

    // 添加关键字搜索
    if (searchText.value.trim()) {
      params.keyword = searchText.value.trim()
    }

    console.log('Fetching datasets with params:', params)

    const response = await getDatasetList(params)
    // 确保每个数据集都有tags数组
    datasets.value = response.data.map((dataset) => ({
      ...dataset,
      tags: dataset.tags || [],
      showActions: false
    }))
    totalCount.value = response.total

    console.log('Fetched datasets:', datasets.value)
  } catch (error) {
    console.error('获取知识库列表失败:', error)
    Message.error('获取知识库列表失败')
  } finally {
    loading.value = false
  }
}

// 获取标签列表
const fetchTags = async () => {
  try {
    tagsLoading.value = true
    const response = await getDatasetTags()
    availableTags.value = response
  } catch (error) {
    console.error('获取标签列表失败:', error)
    Message.error('获取标签列表失败')
  } finally {
    tagsLoading.value = false
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchDatasets()
  fetchTags()
})

// 过滤标签列表
const filteredTags = computed(() => {
  const searchText = tagSearchText.value.toLowerCase().trim()
  if (!searchText) return availableTags.value
  return availableTags.value.filter((tag) => tag.name.toLowerCase().includes(searchText))
})

// 新的标签筛选下拉框的过滤标签列表
const filteredTagOptions = computed(() => {
  const searchText = tagFilterSearchText.value.toLowerCase().trim()
  if (!searchText) return availableTags.value
  return availableTags.value.filter((tag) => tag.name.toLowerCase().includes(searchText))
})

// 切换标签下拉框显示
const toggleTagDropdown = (datasetId: string) => {
  if (activeDropdownDatasetId.value === datasetId && activeDropdownType.value === 'tag') {
    // 关闭当前打开的标签下拉框
    activeDropdownDatasetId.value = null
    activeDropdownType.value = null
  } else {
    // 打开标签下拉框
    activeDropdownDatasetId.value = datasetId
    activeDropdownType.value = 'tag'
    tagSearchText.value = ''
  }
}

// 处理菜单显示状态变化
const handleMenuVisibleChange = (visible: boolean, datasetId: string) => {
  if (visible) {
    activeMenuDatasetId.value = datasetId
  } else {
    activeMenuDatasetId.value = null
  }
}

// 处理菜单选择
const handleMenuSelect = (key: string, dataset: Dataset) => {
  activeMenuDatasetId.value = null

  if (key === 'settings') {
    // 深拷贝对象，避免直接修改原对象
    editingDataset.value = JSON.parse(JSON.stringify(dataset))
    showSettingsModal.value = true
  } else if (key === 'delete') {
    handleDeleteDataset(dataset)
  }
}

// 检查标签是否已选择
const isTagSelected = (tag: DatasetTag, dataset: Dataset) => {
  if (!dataset.tags) return false
  return dataset.tags.some((t) => t.id === tag.id)
}

// 处理标签勾选
const handleTagCheck = async (checked: boolean, tag: DatasetTag, dataset: Dataset) => {
  if (!dataset.tags) {
    dataset.tags = []
  }

  const index = dataset.tags.findIndex((t) => t.id === tag.id)

  try {
    if (checked) {
      if (index === -1) {
        // 添加标签
        const params = {
          tag_ids: [tag.id],
          target_id: dataset.id,
          type: 'knowledge'
        }

        await createTagBinding(params)

        // 更新UI
        dataset.tags.push({
          id: tag.id,
          name: tag.name,
          type: 'knowledge'
        })

        Message.success(`已添加标签"${tag.name}"`)
      }
    } else {
      if (index > -1) {
        // 移除标签
        const params = {
          tag_id: tag.id,
          target_id: dataset.id,
          type: 'knowledge'
        }

        await deleteTagBinding(params)

        // 更新UI
        dataset.tags.splice(index, 1)

        Message.success(`已移除标签"${tag.name}"`)
      }
    }
  } catch (error) {
    console.error('更新知识库标签失败:', error)
    Message.error('更新知识库标签失败')
    // 回滚UI状态
    fetchDatasets()
  }
}

// 创建新标签（从搜索框）
const handleCreateTag = async () => {
  const tagName = tagSearchText.value.trim()
  if (!tagName) return

  // 检查标签是否已存在
  if (availableTags.value.some((t) => t.name.toLowerCase() === tagName.toLowerCase())) {
    Message.warning('标签已存在')
    return
  }

  try {
    const newTag = await createDatasetTag({ name: tagName })
    availableTags.value.push(newTag)
    tagSearchText.value = ''
    Message.success('标签创建成功')
  } catch (error) {
    console.error('创建标签失败:', error)
    Message.error('创建标签失败')
  }
}

// 创建新标签（从管理对话框）
const createNewTag = async () => {
  const tagName = newTagName.value.trim()
  if (!tagName) return

  // 检查标签是否已存在
  if (availableTags.value.some((t) => t.name.toLowerCase() === tagName.toLowerCase())) {
    Message.warning('标签已存在')
    return
  }

  try {
    const newTag = await createDatasetTag({ name: tagName })
    availableTags.value.push(newTag)
    newTagName.value = ''
    Message.success('标签创建成功')
  } catch (error) {
    console.error('创建标签失败:', error)
    Message.error('创建标签失败')
  }
}

// 编辑标签
const editTag = (tag: DatasetTag) => {
  editingTagId.value = tag.id
  editingTagName.value = tag.name

  // 确保DOM已更新后再聚焦输入框
  setTimeout(() => {
    const inputEl = document.querySelector('.tag-edit-input input') as HTMLInputElement
    if (inputEl) {
      inputEl.focus()
      inputEl.select()
    }
  }, 50)
}

// 保存编辑后的标签
const saveEditedTag = async (tag: DatasetTag) => {
  if (editingTagId.value !== tag.id) return

  const newName = editingTagName.value.trim()
  if (!newName || newName === tag.name) {
    // 如果没有修改或为空，取消编辑
    editingTagId.value = null
    return
  }

  // 检查标签名是否已存在
  if (availableTags.value.some((t) => t.id !== tag.id && t.name.toLowerCase() === newName.toLowerCase())) {
    Message.warning('标签名已存在')
    editingTagName.value = tag.name // 恢复原名称
    return
  }

  try {
    const updatedTag = await updateDatasetTag(tag.id, { name: newName })
    const index = availableTags.value.findIndex((t) => t.id === tag.id)
    if (index > -1) {
      availableTags.value[index].name = updatedTag.name || newName
    }
    Message.success('标签更新成功')
  } catch (error) {
    console.error('更新标签失败:', error)
    Message.error('更新标签失败')
  } finally {
    editingTagId.value = null
  }
}

// 取消编辑标签
const cancelEditTag = (_tag: DatasetTag) => {
  editingTagId.value = null
  editingTagName.value = ''
}

// 确认删除标签
const confirmDeleteTag = (tag: DatasetTag) => {
  tagToDelete.value = tag
  showDeleteTagConfirm.value = true
}

// 执行删除标签操作
const doDeleteTag = async () => {
  if (!tagToDelete.value) return

  try {
    await deleteDatasetTag(tagToDelete.value.id)
    const index = availableTags.value.findIndex((t) => t.id === tagToDelete.value?.id)
    if (index > -1) {
      availableTags.value.splice(index, 1)
    }
    Message.success('标签删除成功')
  } catch (error) {
    console.error('删除标签失败:', error)
    Message.error('删除标签失败')
  } finally {
    showDeleteTagConfirm.value = false
    tagToDelete.value = null
  }
}

// 处理删除知识库
const handleDeleteDataset = async (dataset: Dataset) => {
  try {
    // 先检查是否被使用
    const checkResult = await checkDatasetUsage(dataset.id)

    if (checkResult.is_using) {
      Message.warning('该知识库正在被使用，无法删除')
      return
    }

    datasetToDelete.value = dataset
    showDeleteConfirm.value = true
  } catch (error) {
    console.error('检查知识库使用状态失败:', error)
    Message.error('检查知识库使用状态失败')
  }
}

// 保存知识库设置
const saveDatasetSettings = async () => {
  if (!editingDataset.value || !editingDataset.value.id) return

  try {
    // 调用更新知识库的API (现在使用PATCH请求)
    await updateDataset(editingDataset.value.id, {
      name: editingDataset.value.name,
      description: editingDataset.value.description
    })

    // 更新本地数据
    const index = datasets.value.findIndex((d) => d.id === editingDataset.value.id)
    if (index >= 0) {
      datasets.value[index].name = editingDataset.value.name ?? datasets.value[index].name
      datasets.value[index].description = editingDataset.value.description ?? datasets.value[index].description
    }

    showSettingsModal.value = false
    Message.success('知识库设置已保存')
  } catch (error) {
    console.error('更新知识库失败:', error)
    Message.error('更新知识库失败')
  }
}

// 确认删除知识库
const confirmDelete = async () => {
  if (!datasetToDelete.value) return

  try {
    await deleteDataset(datasetToDelete.value.id)

    // 更新本地数据
    const index = datasets.value.findIndex((d) => d.id === datasetToDelete.value?.id)
    if (index >= 0) {
      datasets.value.splice(index, 1)
    }

    showDeleteConfirm.value = false
    datasetToDelete.value = null
    Message.success('知识库删除成功')
  } catch (error) {
    console.error('删除知识库失败:', error)
    Message.error('删除知识库失败')
  }
}

// 搜索知识库
const handleSearch = () => {
  // 重置页码
  currentPage.value = 1
  fetchDatasets()
}

// 新的标签筛选下拉框相关函数
const handleTagFilterVisibleChange = (visible: boolean) => {
  tagFilterOpen.value = visible
  if (!visible) {
    tagFilterSearchText.value = ''
  }
}

const toggleTagSelection = (tagId: string) => {
  const index = selectedTagIds.value.indexOf(tagId)
  if (index > -1) {
    selectedTagIds.value.splice(index, 1)
  } else {
    selectedTagIds.value.push(tagId)
  }
  currentPage.value = 1
  fetchDatasets()
}

// 分页相关函数
const handlePageChange = (page: number) => {
  currentPage.value = page
  fetchDatasets()
}

const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchDatasets()
}

// 切换显示全部知识库
const toggleIncludeAll = () => {
  includeAll.value = !includeAll.value
  currentPage.value = 1
  fetchDatasets()
}

// 跳转到创建知识库页面
const goToCreatePage = () => {
  router.push('/datasets/create')
}

// 跳转到文档页面
const goToDocumentsPage = (datasetId: string) => {
  router.push(`/datasets/${datasetId}/documents`)
}
</script>

<style scoped lang="scss">
// 新的标签筛选下拉框样式
.tag-filter-dropdown {
  .tag-filter-button {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border: 1px solid var(--color-border-2);
    border-radius: 8px;
    background-color: var(--color-bg-1);
    color: var(--color-text-2);
    transition: all 0.2s ease;
    cursor: pointer;

    &:hover {
      border-color: var(--color-primary-light-3);
      background-color: var(--color-fill-1);
    }

    &.has-selection {
      border-color: var(--color-primary-light-3);
      background-color: var(--color-primary-light-5);
      color: var(--color-primary);
    }

    &.is-open {
      border-color: var(--color-primary);
      box-shadow: 0 0 0 2px var(--color-primary-light-4);
    }

    .filter-icon {
      font-size: 14px;
    }

    .filter-text {
      font-size: 13px;
      font-weight: 500;
    }

    .arrow-icon {
      font-size: 12px;
      transition: transform 0.2s ease;

      &.rotated {
        transform: rotate(180deg);
      }
    }
  }
}

.modern-tag-dropdown {
  width: 240px;
  background-color: var(--color-bg-1);
  // border: 1px solid var(--color-border-2);
  // border-radius: 12px;
  // box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  overflow: hidden;

  .tag-search-section {
    padding: 12px;
    border-bottom: 1px solid var(--color-border-3);

    .tag-search-input {
      .arco-input-wrapper {
        border-radius: 8px;
        border: 1px solid var(--color-border-2);
        background-color: var(--color-fill-1);

        &:hover {
          border-color: var(--color-primary-light-3);
        }

        &:focus-within {
          border-color: var(--color-primary);
          box-shadow: 0 0 0 2px var(--color-primary-light-4);
        }
      }

      .search-icon {
        color: var(--color-text-3);
      }
    }
  }

  .tag-options-section {
    max-height: 280px;
    overflow-y: auto;
    padding: 8px;

    .tag-option-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: var(--color-fill-2);
      }

      .tag-checkbox {
        flex-shrink: 0;
      }

      .tag-label {
        flex: 1;
        font-size: 13px;
        color: var(--color-text-1);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .no-tags-message {
      padding: 16px 12px;
      text-align: center;
      color: var(--color-text-3);
      font-size: 13px;
    }
  }
}

.datasets-tab {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;

  // 固定顶部筛选区域
  .header-area.fixed-header {
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: var(--color-bg-1);
    // border-bottom: 1px solid var(--color-border-2);
    padding-bottom: 16px;
    margin-bottom: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .actions-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    justify-content: flex-end;

    .search-input {
      width: 200px;
    }
  }

  .mr-4 {
    margin-right: 16px;
  }

  // 可滚动的内容区域
  .content-area {
    flex: 1;
    overflow-y: auto;
  }

  // 固定底部分页区域
  .pagination-area.fixed-bottom {
    position: sticky;
    bottom: 0;
    z-index: 100;
    background-color: var(--color-bg-1);
    border-top: 1px solid var(--color-border-2);
    padding: 16px 0 0;
    margin-top: auto;

    .datasets-pagination {
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .pagination-total {
        font-size: 13px;
        color: var(--color-text-2);
        margin-right: 16px;
      }
    }
  }

  .datasets-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 16px;
    padding: 10px;
    margin-bottom: 0;

    .dataset-card {
      padding: 16px;
      border: 1px solid var(--color-border-2);
      border-radius: 12px;
      transition: all 0.3s;
      position: relative;
      display: flex;
      flex-direction: column;
      height: 100%;
      min-height: 180px;

      &:hover {
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      }

      &.create-card {
        border: 1px dashed var(--color-border-2);
        cursor: pointer;
        background-color: var(--color-fill-1);

        &:hover {
          border-color: var(--color-primary-light-3);
          background-color: var(--color-fill-2);
        }

        .create-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          text-align: center;
          height: 100%;

          .create-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: var(--color-fill-2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 12px;
            color: var(--color-text-1);

            &:hover {
              background-color: var(--color-primary-light-4);
              color: var(--color-primary);
            }
          }

          .create-title {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 8px;
          }

          .create-desc {
            font-size: 13px;
            color: var(--color-text-3);
            line-height: 1.5;
          }

          .outer-btn {
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px dashed var(--color-border-2);
          }
        }
      }

      .card-content {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;
        cursor: pointer;

        .card-icon {
          padding: 8px;
          margin-right: 12px;
          background-color: rgb(245 248 255 / var(--tw-bg-opacity, 1));
          border-radius: 8px;
          color: rgb(var(--primary-6));
          flex-shrink: 0;
        }

        .card-info {
          flex: 1;

          .card-title {
            font-weight: 500;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .card-meta {
            font-size: 12px;
            color: var(--color-text-3);
            display: flex;
            align-items: center;
            flex-wrap: wrap;

            .dot-separator {
              display: inline-block;
              width: 4px;
              height: 4px;
              border-radius: 50%;
              background-color: var(--color-text-3);
              margin: 0 8px;
            }
          }
        }
      }

      .card-description {
        font-size: 13px;
        color: var(--color-text-2);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        margin-bottom: 12px;
        flex: 1;
        cursor: pointer;
      }

      .card-tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-bottom: 8px;

        .tag-item {
          background-color: var(--color-fill-2);
          color: var(--color-text-2);
          border-radius: 4px;
          font-size: 12px;
        }
      }

      .card-actions {
        margin-top: auto;
        min-height: 30px;
        position: relative;

        .action-buttons {
          display: flex;
          justify-content: space-between;
          // height: 38px;

          .tag-area {
            flex: 1;
            display: flex;
            align-items: center;
            overflow: hidden;

            .tag-selected {
              display: flex;
              align-items: center;

              .tag-text {
                font-size: 12px;
                color: var(--color-text-2);
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                max-width: 240px;
                cursor: pointer;

                &:hover {
                  color: var(--color-text-1);
                }
              }
            }
          }

          .tag-btn,
          .tag-selected {
            font-size: 12px;
            display: flex;
            justify-content: flex-start;
          }

          .menu-btn {
            margin-left: auto;
          }

          .tag-btn,
          .menu-btn {
            padding: 0 8px;
            font-size: 12px;
            color: var(--color-text-3);

            &:hover {
              color: var(--color-text-1);
            }
          }
        }

        .tag-dropdown {
          position: absolute;
          top: 100%;
          left: 0;
          width: 100%;
          background-color: var(--color-bg-2);
          border: 1px solid var(--color-border-2);
          border-radius: 8px;
          box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
          z-index: 10;
          margin-top: 8px;
          max-height: 300px;
          overflow-y: auto;

          .tag-search {
            padding: 8px;
            border-bottom: 1px solid var(--color-border-3);
          }

          .tag-list {
            padding: 8px;

            .tag-item-row {
              padding: 8px;
              cursor: pointer;
              border-radius: 4px;

              &:hover {
                background-color: var(--color-fill-2);
              }
            }

            .tag-divider {
              height: 1px;
              background-color: var(--color-border-2);
              margin: 8px 0;
            }

            .tag-manage {
              padding: 8px;
              cursor: pointer;
              color: var(--color-text-3);
              display: flex;
              align-items: center;

              &:hover {
                color: var(--color-text-1);
              }
            }
          }
        }
      }
    }
  }

  .bottom-tip {
    margin-top: 40px;
    padding: 16px;
    background-color: var(--color-fill-2);
    border-radius: 12px;

    .tip-title {
      font-weight: 500;
      margin-bottom: 8px;
    }

    .tip-content {
      color: var(--color-text-2);
      font-size: 14px;
      line-height: 1.5;
    }
  }

  .tag-management-content {
    .create-tag-input {
      display: flex;
      margin-bottom: 16px;
      gap: 8px;
    }

    .tag-management-list {
      max-height: 300px;
      overflow-y: auto;

      .tag-management-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 8px;
        border-bottom: 1px solid var(--color-border-3);

        &:last-child {
          border-bottom: none;
        }

        .tag-name {
          flex: 1;
          overflow: hidden;

          .tag-edit-input {
            width: 100%;
            max-width: 250px;
            background-color: rgba(200, 206, 218, 0.25);
            border-radius: 4px;

            .arco-input,
            .arco-input-wrapper {
              background-color: rgba(200, 206, 218, 0.25);
              border-radius: 4px;
            }
          }
        }

        .tag-edit {
          display: flex;
          gap: 8px;

          .arco-btn {
            padding: 0 4px;
          }
        }
      }
    }
  }

  .my-icon-folder {
    color: rgb(68 76 231 / var(--tw-text-opacity, 1));
  }

  .tag-dropdown-container {
    max-height: 300px;
    overflow-y: auto;
    min-width: 150px;
    padding: 8px;

    .tag-filter-item {
      padding: 8px 12px;
      white-space: nowrap;
    }
  }
}

.dataset-settings-modal {
  border-radius: 12px;
  overflow: hidden;

  .arco-modal-header {
    padding: 20px 24px;

    .arco-modal-title {
      font-weight: 500;
      font-size: 16px;
    }
  }

  .arco-modal-body {
    padding: 24px;

    .settings-form {
      .form-item {
        margin-bottom: 34px;

        &:last-child {
          margin-bottom: 0;
        }

        .form-label {
          font-size: 14px;
          margin-bottom: 12px;
          color: var(--color-text-1);
          font-weight: 500;
          line-height: 20px;
        }

        .settings-input,
        .settings-textarea {
          background-color: rgba(200, 206, 218, 0.25);
          border-radius: 8px;

          .arco-textarea-mirror,
          .arco-textarea,
          .arco-input,
          .arco-input-wrapper {
            background-color: rgba(200, 206, 218, 0.25);
            border-radius: 8px;
          }

          &:hover,
          &:focus {
            background-color: rgba(200, 206, 218, 0.35);
          }
        }
      }
    }
  }

  .arco-modal-footer {
    padding: 16px 24px;
    // border-top: 1px solid var(--color-border-2);

    .arco-btn {
      border-radius: 6px;
      padding: 6px 16px;
    }
  }

  .modal-close-btn {
    position: absolute;
    right: 20px;
    top: 20px;
    color: var(--color-text-3);

    &:hover {
      color: var(--color-text-1);
      background-color: var(--color-fill-2);
    }
  }
}

.tag-management-modal {
  border-radius: 12px;
  overflow: hidden;

  .arco-modal-header {
    padding: 20px 24px;

    .arco-modal-title {
      font-weight: 500;
      font-size: 16px;
    }
  }

  .arco-modal-body {
    padding: 24px;

    .tag-management-content {
      .create-tag-input {
        display: flex;
        margin-bottom: 24px;
        gap: 8px;

        .tag-input {
          background-color: rgba(200, 206, 218, 0.25);
          border-radius: 8px;

          .arco-input-wrapper,
          .arco-input {
            background-color: rgba(200, 206, 218, 0.25);
            border-radius: 8px;
          }

          &:hover,
          &:focus {
            background-color: rgba(200, 206, 218, 0.35);
          }
        }

        .arco-btn {
          border-radius: 6px;
        }
      }

      .tag-management-list {
        max-height: 300px;
        overflow-y: auto;

        .tag-management-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 8px;
          border-bottom: 1px solid var(--color-border-3);

          &:last-child {
            border-bottom: none;
          }

          .tag-name {
            flex: 1;
            overflow: hidden;

            .tag-edit-input {
              width: 100%;
              max-width: 250px;
              background-color: rgba(200, 206, 218, 0.25);
              border-radius: 4px;

              .arco-input,
              .arco-input-wrapper {
                background-color: rgba(200, 206, 218, 0.25);
                border-radius: 4px;
              }
            }
          }

          .tag-edit {
            display: flex;
            gap: 8px;

            .arco-btn {
              padding: 0 4px;
            }
          }
        }
      }
    }
  }

  .arco-modal-footer {
    padding: 16px 24px;

    .arco-btn {
      border-radius: 6px;
      padding: 6px 16px;
    }
  }

  .modal-close-btn {
    position: absolute;
    right: 20px;
    top: 20px;
    color: var(--color-text-3);

    &:hover {
      color: var(--color-text-1);
      background-color: var(--color-fill-2);
    }
  }
}

.popconfirm-content {
  margin: 8px 0;
  font-size: 14px;
  color: var(--color-text-2);
}
</style>
