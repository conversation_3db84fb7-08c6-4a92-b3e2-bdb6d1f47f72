// 用户item
export interface UserItemInterface {
  id: string
  name: string
  avatar: string
  avatar_url: string
  email: string
  last_active_at: string
  created_at: string
  role: string
  status: string
  last_active_at_name: string
}

// 用户list
export interface UserListInterface {
  accounts: UserItemInterface[]
}

// 更新角色接口的入参
export type RoleType = {
  role: string
}

export type InvitationItem = {
  email: string
  status: string
  url: string
}
