<template>
  <div class="field-info">
    <Fieldlist :list="fieldList" :nodeInfo="nodeInfo" :nodeId="nodeId" />
  </div>
</template>

<script setup lang="ts">
import Fieldlist from './components/FieldList.vue'

interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
}

const props = defineProps<{
  isChatMode?: false
  nodeInfo?: Object
  nodeId: any
}>()
const fieldList = ref<FieldType[]>([])
</script>
<style scoped lang="scss">
.field-info {
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-1);
}
</style>
