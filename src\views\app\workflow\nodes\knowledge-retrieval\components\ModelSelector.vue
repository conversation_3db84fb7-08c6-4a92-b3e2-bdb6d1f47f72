<template>
  <div class="model-selector">
    <div class="model-config-row">
      <label>提供商:</label>
      <a-select
        v-model="localModel.provider"
        placeholder="选择提供商"
        :options="providerOptions"
        @change="handleProviderChange"
      />
    </div>

    <div class="model-config-row">
      <label>模型:</label>
      <a-select
        v-model="localModel.name"
        placeholder="选择模型"
        :options="modelOptions"
        :disabled="!localModel.provider"
        @change="handleModelChange"
      />
    </div>

    <div class="model-config-row">
      <label>模式:</label>
      <a-select v-model="localModel.mode" placeholder="选择模式" :options="modeOptions" @change="handleModeChange" />
    </div>

    <!-- 高级参数配置 -->
    <div v-if="showAdvanced" class="advanced-config">
      <div class="advanced-header">
        <span>高级参数</span>
        <a-button type="text" size="small" @click="showAdvanced = false">收起</a-button>
      </div>

      <div class="model-config-row">
        <label>温度:</label>
        <a-input-number
          v-model="localModel.completion_params.temperature"
          :min="0"
          :max="2"
          :step="0.1"
          placeholder="0.0-2.0"
          @change="handleParamChange"
        />
      </div>

      <div class="model-config-row">
        <label>最大标记:</label>
        <a-input-number
          v-model="localModel.completion_params.max_tokens"
          :min="1"
          :max="8192"
          placeholder="最大输出标记数"
          @change="handleParamChange"
        />
      </div>

      <div class="model-config-row">
        <label>Top P:</label>
        <a-input-number
          v-model="localModel.completion_params.top_p"
          :min="0"
          :max="1"
          :step="0.1"
          placeholder="0.0-1.0"
          @change="handleParamChange"
        />
      </div>

      <div class="model-config-row">
        <label>Top K:</label>
        <a-input-number
          v-model="localModel.completion_params.top_k"
          :min="1"
          :max="100"
          placeholder="1-100"
          @change="handleParamChange"
        />
      </div>
    </div>

    <div v-else class="show-advanced">
      <a-button type="text" size="small" @click="showAdvanced = true">显示高级参数</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'

const props = withDefaults(
  defineProps<{
    modelValue?: any
  }>(),
  {
    modelValue: () => ({
      provider: '',
      name: '',
      mode: 'chat',
      completion_params: {
        temperature: 0.7,
        max_tokens: 1000,
        top_p: 1.0,
        top_k: 50
      }
    })
  }
)

const emit = defineEmits<{
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}>()

const showAdvanced = ref(false)
const providers = ref([])
const models = ref([])

const localModel = ref({
  provider: '',
  name: '',
  mode: 'chat',
  completion_params: {
    temperature: 0.7,
    max_tokens: 1000,
    top_p: 1.0,
    top_k: 50
  }
})

const providerOptions = computed(() =>
  providers.value.map((provider) => ({
    label: provider.name,
    value: provider.id
  }))
)

const modelOptions = computed(() =>
  models.value.map((model) => ({
    label: model.name,
    value: model.id
  }))
)

const modeOptions = [
  { label: '聊天模式', value: 'chat' },
  { label: '补全模式', value: 'completion' }
]

onMounted(async () => {
  await loadProviders()

  if (props.modelValue) {
    Object.assign(localModel.value, props.modelValue)
    if (localModel.value.provider) {
      await loadModels(localModel.value.provider)
    }
  }
})

const loadProviders = async () => {
  try {
    // 模拟提供商数据
    providers.value = [
      { id: 'openai', name: 'OpenAI' },
      { id: 'anthropic', name: 'Anthropic' },
      { id: 'zhipu', name: '智谱AI' },
      { id: 'qwen', name: '通义千问' }
    ]
  } catch (error) {
    Message.error('加载模型提供商失败')
  }
}

const loadModels = async (providerId: string) => {
  try {
    // 模拟不同提供商的模型数据
    const modelMap = {
      openai: [
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' },
        { id: 'gpt-4', name: 'GPT-4' },
        { id: 'gpt-4-turbo', name: 'GPT-4 Turbo' }
      ],
      anthropic: [
        { id: 'claude-3-haiku', name: 'Claude 3 Haiku' },
        { id: 'claude-3-sonnet', name: 'Claude 3 Sonnet' },
        { id: 'claude-3-opus', name: 'Claude 3 Opus' }
      ],
      zhipu: [
        { id: 'glm-4', name: 'GLM-4' },
        { id: 'glm-3-turbo', name: 'GLM-3 Turbo' }
      ],
      qwen: [
        { id: 'qwen-turbo', name: 'Qwen Turbo' },
        { id: 'qwen-plus', name: 'Qwen Plus' },
        { id: 'qwen-max', name: 'Qwen Max' }
      ]
    }

    models.value = modelMap[providerId] || []
  } catch (error) {
    Message.error('加载模型列表失败')
  }
}

const handleProviderChange = async (providerId: string) => {
  localModel.value.name = ''
  models.value = []

  if (providerId) {
    await loadModels(providerId)
  }

  emitChange()
}

const handleModelChange = () => {
  emitChange()
}

const handleModeChange = () => {
  emitChange()
}

const handleParamChange = () => {
  emitChange()
}

const emitChange = () => {
  emit('update:modelValue', localModel.value)
  emit('change', localModel.value)
}

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      Object.assign(localModel.value, newValue)
    }
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.model-selector {
  .model-config-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    label {
      min-width: 80px;
      font-size: 13px;
      color: var(--color-text-2);
      margin-right: 12px;
    }

    .arco-select,
    .arco-input-number {
      flex: 1;
    }
  }

  .advanced-config {
    margin-top: 16px;
    padding: 12px;
    background-color: var(--color-bg-3);
    border-radius: 6px;
    border: 1px solid var(--color-border-2);

    .advanced-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      font-size: 13px;
      font-weight: 500;
      color: var(--color-text-1);
    }
  }

  .show-advanced {
    margin-top: 8px;
    text-align: center;
  }
}
</style>
