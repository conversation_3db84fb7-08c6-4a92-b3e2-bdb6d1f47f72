<template>
  <div class="metadata-config-panel">
    <!-- 过滤模式选择 -->
    <div class="config-section">
      <div class="section-title">过滤模式</div>
      <div class="section-content">
        <a-radio-group v-model="localConfig.metadata_filtering_mode" @change="handleFilterModeChange">
          <a-radio :value="MetadataFilteringModeEnum.disabled">禁用</a-radio>
          <a-radio :value="MetadataFilteringModeEnum.automatic">自动</a-radio>
          <a-radio :value="MetadataFilteringModeEnum.manual">手动</a-radio>
        </a-radio-group>
      </div>
    </div>

    <!-- 自动模式配置 -->
    <div v-if="localConfig.metadata_filtering_mode === MetadataFilteringModeEnum.automatic" class="config-section">
      <div class="section-title">自动过滤配置</div>
      <div class="section-content">
        <div class="auto-config-desc">系统将使用AI模型自动分析查询内容，生成合适的元数据过滤条件</div>
        <ModelSelector v-model="localConfig.metadata_model_config" @change="handleMetadataModelChange" />
      </div>
    </div>

    <!-- 手动模式配置 -->
    <div v-if="localConfig.metadata_filtering_mode === MetadataFilteringModeEnum.manual" class="config-section">
      <div class="section-title">手动过滤配置</div>
      <div class="section-content">
        <MetadataConditionList
          :conditions="localConfig.metadata_filtering_conditions"
          :metadata-list="metadataList"
          :node-id="nodeId"
          @add-condition="handleAddMetadataCondition"
          @remove-condition="handleRemoveMetadataCondition"
          @update-condition="handleUpdateMetadataCondition"
          @toggle-logical-operator="handleToggleLogicalOperator"
        />
      </div>
    </div>

    <!-- 移除可用元数据字段展示 -->

    <!-- 配置预览 -->
    <div v-if="localConfig.metadata_filtering_conditions.length > 0" class="config-section">
      <div class="section-title">配置预览</div>
      <div class="section-content">
        <div class="config-preview">
          <pre>{{ JSON.stringify(localConfig.metadata_filtering_conditions, null, 2) }}</pre>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import ModelSelector from './ModelSelector.vue'
import MetadataConditionList from './MetadataConditionList.vue'
import { MetadataFilteringModeEnum } from '@/views/app/workflow/types/node'

const props = withDefaults(
  defineProps<{
    modelValue?: any
    metadataList?: any[]
    nodeId?: string
  }>(),
  {
    modelValue: () => ({
      metadata_filtering_mode: MetadataFilteringModeEnum.disabled,
      metadata_filtering_conditions: [],
      metadata_model_config: null
    }),
    metadataList: () => [],
    nodeId: ''
  }
)

const emit = defineEmits<{
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}>()

const localConfig = ref({
  metadata_filtering_mode: MetadataFilteringModeEnum.disabled,
  metadata_filtering_conditions: [],
  metadata_model_config: null
})

onMounted(() => {
  if (props.modelValue) {
    Object.assign(localConfig.value, props.modelValue)
  }
})

const handleFilterModeChange = () => {
  if (localConfig.value.metadata_filtering_mode === MetadataFilteringModeEnum.disabled) {
    localConfig.value.metadata_filtering_conditions = []
    localConfig.value.metadata_model_config = null
  }
  emitChange()
}

const handleMetadataModelChange = (model: any) => {
  localConfig.value.metadata_model_config = model
  emitChange()
}

const handleAddMetadataCondition = (metadataItem: any) => {
  const newCondition = {
    id: Date.now().toString(),
    metadata_key: metadataItem.name,
    operator: 'contains',
    value: '',
    value_type: 'constant',
    value_selector: []
  }
  localConfig.value.metadata_filtering_conditions.push(newCondition)
  emitChange()
}

const handleRemoveMetadataCondition = (conditionId: string) => {
  localConfig.value.metadata_filtering_conditions = localConfig.value.metadata_filtering_conditions.filter(
    (condition) => condition.id !== conditionId
  )
  emitChange()
}

const handleUpdateMetadataCondition = (conditionId: string, newCondition: any) => {
  const index = localConfig.value.metadata_filtering_conditions.findIndex((condition) => condition.id === conditionId)
  if (index !== -1) {
    localConfig.value.metadata_filtering_conditions[index] = { ...newCondition, id: conditionId }
    emitChange()
  }
}

const handleToggleLogicalOperator = () => {
  // 切换逻辑操作符的实现
  emitChange()
}

const emitChange = () => {
  emit('update:modelValue', localConfig.value)
  emit('change', localConfig.value)
}

watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      Object.assign(localConfig.value, newValue)
    }
  },
  { deep: true }
)
</script>

<style scoped lang="scss">
.metadata-config-panel {
  max-height: 60vh;
  overflow-y: auto;

  .config-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--color-text-1);
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid var(--color-border-2);
    }

    .section-content {
      .auto-config-desc {
        padding: 12px;
        background-color: var(--color-bg-2);
        border-radius: 6px;
        border: 1px solid var(--color-border-2);
        font-size: 13px;
        color: var(--color-text-2);
        margin-bottom: 16px;
      }

      // 移除metadata-fields相关样式

      .config-preview {
        background-color: var(--color-bg-2);
        border: 1px solid var(--color-border-2);
        border-radius: 6px;
        padding: 12px;
        max-height: 200px;
        overflow-y: auto;

        pre {
          margin: 0;
          font-size: 12px;
          color: var(--color-text-2);
          white-space: pre-wrap;
          word-break: break-all;
        }
      }
    }
  }
}
</style>
