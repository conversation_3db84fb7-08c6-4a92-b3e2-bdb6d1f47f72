<template>
  <AiPageLayout>
    <a-tabs ref="tabsRef" v-model:active-key="tabsKey" :default-active-key="tabsKey" lazy-load justify size="large">
      <template #extra>
        <a-space class="float-right">
          <a-checkbox v-model="params.is_created_by_me" @change="search">我创建的</a-checkbox>
          <a-select
            v-model="tagValue"
            style="min-width: 160px"
            placeholder="全部标签"
            multiple
            :max-tag-count="1"
            @change="tagChange"
          >
            <template #label="{ data }">
              <span>
                <icon-tag style="margin-right: 4px" />
                {{ data?.label }}
              </span>
            </template>
            <a-option v-for="item in tags" :key="item.id" :value="item.id">{{ item.name }}</a-option>
          </a-select>
          <a-input-search v-model="params.name" placeholder="搜索名称/描述" allow-clear @search="search" />
        </a-space>
      </template>
      <a-tab-pane key="1" title="通用">
        <AppCard
          :tags="tags"
          :installAppListData="installAppListData"
          :appListData="appListData"
          @operate-success="getAppListFn"
        />
      </a-tab-pane>
      <a-tab-pane key="2" title="测试助手">
        <AiAssistant />
      </a-tab-pane>
    </a-tabs>
  </AiPageLayout>
</template>

<script setup lang="ts">
import AppCard from './AppCard.vue'
import { getAppList as getAppListApi, getTags, getInstalleApps } from '@/apis'
import { useAppShare } from '@/stores'
import AiAssistant from './ai-assistant/index.vue'
defineOptions({ name: 'AppsManage' })
const { setInstalledApps } = useAppShare()

const router = useRouter()

const tabsKey = ref<string>('1')
const appListData = ref()
const installAppListData = ref()

const tags = ref()
const tagValue = ref()
const params = reactive<any>({
  page: '1',
  limit: '30',
  name: '',
  is_created_by_me: false
})
const getAppListFn = async () => {
  const res = await getAppListApi(params)
  appListData.value = res
}
const tagsParams = reactive<any>({
  type: 'app'
})
const getTagsFn = async () => {
  const res = await getTags(tagsParams)
  tags.value = res
}

const getInsallAppListFn = async () => {
  const res = await getInstalleApps()
  installAppListData.value = res.installed_apps
  setInstalledApps(res.installed_apps)
}
onMounted(() => {
  getAppListFn()
  getTagsFn()
  getInsallAppListFn()
})

// 搜索
const search = () => {
  getAppListFn()
}
const tagChange = (e) => {
  if (e && e.length > 0) {
    params.tag_ids = e.join(',')
  } else {
    delete params.tag_ids
  }
  getAppListFn()
}
</script>

<style scoped lang="scss">
:deep(.arco-select-view-tag) {
  border: none !important;
  background-color: var(--color-fill-2) !important;
  border-radius: 5px !important;
}
</style>
