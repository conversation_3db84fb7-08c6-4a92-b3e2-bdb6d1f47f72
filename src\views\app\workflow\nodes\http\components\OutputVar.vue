<template>
  <Field :fieldTitle="'输出变量'" :tooltip="''">
    <div
      v-for="param in outputData.filter((v) => v.category.some((val) => val === nodeInfo.type))"
      :key="param.paramsName"
      class="param-item"
    >
      <a-space direction="vertical" :size="'mini'">
        <a-space align="center">
          <a-typography-text class="params-name">{{ param.paramsName }}</a-typography-text>
          <a-typography-text type="secondary" class="params-type">{{ param.paramsType }}</a-typography-text>
        </a-space>
        <a-typography-text type="secondary" class="params-desc">
          {{ param.paramsDesc }}
        </a-typography-text>
      </a-space>
    </div>
  </Field>
</template>

<script setup lang="ts">
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'

const props = defineProps(['nodeInfo'])

// 数据由父组件传：因为有动态变量的场景。
const outputData = [
  // http
  { paramsName: 'body', paramsType: 'string', paramsDesc: '响应内容', category: ['http-request'] },
  { paramsName: 'status_code', paramsType: 'number', paramsDesc: '响应状态码', category: ['http-request'] },
  { paramsName: 'headers', paramsType: 'object', paramsDesc: '响应头列表 JSON', category: ['http-request'] },
  { paramsName: 'files', paramsType: ' Array[File]', paramsDesc: '文件列表', category: ['http-request'] },

  // LLM
  { paramsName: 'text', paramsType: 'string', paramsDesc: '生成内容', category: ['llm'] },
  { paramsName: 'text', paramsType: 'string', paramsDesc: '生成内容', category: ['parameter-extractor'] }
]
</script>

<style scoped lang="scss">
.param-item {
  margin-top: var(--margin);

  .params-name {
    font-size: 16px;
    font-weight: 500;
  }
}
</style>
