import Enum from './Enum'
import type { ENUM_DATA_LIST } from './types'
const LocalEnumData: ENUM_DATA_LIST = []
let EnumInstance
export const createEnum = function () {
  if (EnumInstance) return EnumInstance
  EnumInstance = new Enum(LocalEnumData)
  return EnumInstance
}

export const useEnum = function () {
  if (!EnumInstance) {
    createEnum()
  }
  return EnumInstance.value
}

export default {
  get instance() {
    if (EnumInstance) createEnum()
    return EnumInstance
  },
  install(app) {
    let $enum = EnumInstance
    if (!EnumInstance) {
      $enum = createEnum()
      // eslint-disable-next-line @typescript-eslint/no-empty-object-type
    } else if (Object.keys(EnumInstance.value as {}).filter((i) => i !== '_key').length === 0) {
      EnumInstance.updateEnum(LocalEnumData)
    }

    app.config.globalProperties.$enum = $enum.value

    app.provide('$enum', $enum)
  }
}
