<template>
  <div class="space-y-1">
    <div v-for="(item, index) in list" key="index" class="flex items-center space-x-1">
      <a-input v-model="item.variable" :style="{ width: '110px' }" placeholder="变量名" allow-clear />
      <VariableSelector v-model:value-selector="item.value_selector" :node-id="nodeId" />
      <a-button style="width: 35px; height: 24px" type="text" @click="list.splice(index, 1)">
        <template #icon>
          <icon-delete :size="18" />
        </template>
      </a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import VariableSelector from '@/views/app/workflow/components/variable-selector/VariableSelector.vue'

interface FieldType {
  value_selector: string[]
  variable: string
  selector: string
}
const props = defineProps<{
  list: FieldType[]
  isChatMode?: false
  nodeId: string
}>()
const change = (e, item) => {
  item.value_selector = e.split('.')
}
</script>
<style scoped lang="scss">
:deep(.arco-select-option-content) {
  width: 100%;
}
</style>
