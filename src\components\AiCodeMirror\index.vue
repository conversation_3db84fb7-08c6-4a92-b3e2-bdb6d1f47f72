<template>
  <CodeMirror
    ref="codeMirror"
    :model-value="codeValue"
    :tab-size="config.tabSize"
    :basic="config.basic"
    :dark="config.dark"
    :readonly="config.readonly"
    :extensions="extensions"
    :style="{ height: '100%' }"
  />
</template>

<script setup lang="ts">
import CodeMirror from 'vue-codemirror6'
import { javascript } from '@codemirror/lang-javascript'
import { json } from '@codemirror/lang-json'
import { vue } from '@codemirror/lang-vue'
import { githubLight } from '@ddietr/codemirror-themes/github-light'
import { oneDark } from '@codemirror/theme-one-dark'
import { useAppStore } from '@/stores'
import { EditorView } from '@codemirror/view'
const props = withDefaults(defineProps<Props>(), {
  type: 'json',
  codeJson: ''
})
const appStore = useAppStore()
const isDark = computed(() => appStore.theme === 'dark')

interface Props {
  type?: 'javascript' | 'vue' | 'json'
  codeJson?: string
}
const defaultConfig = {
  tabSize: 2,
  basic: true,
  dark: true,
  readonly: true
}
const config = defaultConfig

const codeValue = computed(() => {
  if (props.type === 'json') {
    return JSON.stringify(JSON.parse(props.codeJson), null, 2)
  }
  return props.codeJson
})

const extensions = computed(() => {
  const arr = [isDark.value ? oneDark : githubLight, EditorView.lineWrapping]
  if (props.type === 'javascript') {
    arr.push(javascript())
  }
  if (props.type === 'vue') {
    arr.push(vue())
  }
  if (props.type === 'json') {
    arr.push(json())
  }
  return arr
})
</script>

<style scoped lang="scss">
:deep(.ͼ1 .cm-scroller) {
  font-family:
    source-code-pro,
    Menlo,
    Monaco,
    Consolas,
    Courier New,
    monospace;
}
</style>
