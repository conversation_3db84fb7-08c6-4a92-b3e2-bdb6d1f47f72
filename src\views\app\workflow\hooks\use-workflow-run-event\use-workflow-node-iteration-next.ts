import { useVueFlow } from '@vue-flow/core'
import { useWorkflowStore } from '@/stores'
import type { IterationNextResponse } from '@/views/app/workflow/types/workflow'

export const useWorkflowNodeIterationNext = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowNodeIterationNext = (params: IterationNextResponse) => {
    const { iterTimes, setIterTimes } = workflowStore

    const { data } = params
    const { nodes, setNodes } = useVueFlow()
    if (nodes.value?.length) {
      const newNodes = nodes.value.map((node) => {
        if (node.id === data.node_id) {
          node.data._iterationIndex = iterTimes
        }
        return node
      })
      setNodes(newNodes)
    }
    setIterTimes(iterTimes! + 1)
  }

  return {
    handleWorkflowNodeIterationNext
  }
}
