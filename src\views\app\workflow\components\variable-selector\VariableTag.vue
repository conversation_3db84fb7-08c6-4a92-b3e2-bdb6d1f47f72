<template>
  <div class="variable-tag">
    <a-tag size="small">
      <div class="variable-tag-content">
        <template v-if="isSystemVar">
          <span class="variable-tag-name">{{ variableName }}</span>
        </template>
        <template v-else>
          <template v-if="showNodeName && nodeName">
            <span class="variable-tag-node">{{ nodeName }}</span>
            <span class="variable-tag-separator">/</span>
          </template>
          <span class="variable-tag-name">{{ variableName }}</span>
        </template>
      </div>
    </a-tag>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = withDefaults(
  defineProps<{
    valueSelector: string[]
    availableNodes?: any[]
    nodesOutputVars?: any[]
    showNodeName?: boolean
  }>(),
  {
    availableNodes: () => [],
    nodesOutputVars: () => [],
    showNodeName: true
  }
)

// Check if it's a system variable
const isSystemVar = computed(() => {
  return (
    props.valueSelector[0] === 'sys' || props.valueSelector[0] === 'env' || props.valueSelector[0] === 'conversation'
  )
})

// Get the node name
const nodeName = computed(() => {
  if (isSystemVar.value) return '开始'

  const nodeId = props.valueSelector[0]
  const node = props.availableNodes.find((node) => node.id === nodeId)
  if (!node) {
    const nodeVar = props.nodesOutputVars.find((v) => v.nodeId === nodeId)
    return nodeVar?.title || ''
  }

  return node.data?.title || ''
})

// Get the variable name
const variableName = computed(() => {
  if (isSystemVar.value) {
    return props.valueSelector.join('.')
  }

  if (props.valueSelector.length < 2) return ''

  return props.valueSelector[1]
})
</script>

<style scoped lang="scss">
.variable-tag {
  display: inline-flex;
  align-items: center;

  &-content {
    display: flex;
    align-items: center;
  }

  &-node {
    font-weight: 500;
  }

  &-separator {
    margin: 0 4px;
    color: var(--color-text-3);
  }

  &-name {
    font-weight: 400;
  }
}
</style>
