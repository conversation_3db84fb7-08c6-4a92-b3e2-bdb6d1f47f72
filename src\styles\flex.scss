/* stylelint-disable order/properties-order */
/* stylelint-disable property-no-vendor-prefix */
// 常用flex布局封装
@mixin flex(
  $flex-direction: row,
  $justify-content: flex-start,
  $align-items: center
) {
  display: flex;
  display: -webkit-flex;
  flex-direction: $flex-direction;
  -webkit-flex-direction: $flex-direction;
  -moz-flex-direction: $flex-direction;
  -ms-flex-direction: $flex-direction;
  -o-flex-direction: $flex-direction;
  justify-content: $justify-content;
  -webkit-justify-content: $justify-content;
  -moz-justify-content: $justify-content;
  -ms-justify-content: $justify-content;
  -o-justify-content: $justify-content;
  align-items: $align-items;
  -webkit-align-items: $align-items;
  -moz-align-items: $align-items;
  -ms-align-items: $align-items;
  -o-align-items: $align-items;
}

// Row 排列组合
.flexRowStarStar { @include flex(row, flex-start, flex-start); }
.flexRowStarCen { @include flex(row, flex-start, center); }
.flexRowStarEnd { @include flex(row, flex-start, flex-end); }
.flexRowStarSpb { @include flex(row, flex-start, space-between); }
.flexRowStarSpa { @include flex(row, flex-start, space-around); }

.flexRowCenStar { @include flex(row, center, flex-start); }
.flexRowCenCen { @include flex(row, center, center); }
.flexRowCenEnd { @include flex(row, center, flex-end); }
.flexRowCenSpb { @include flex(row, center, space-between); }
.flexRowCenSpa { @include flex(row, center, space-around); }

.flexRowEndStar { @include flex(row, flex-end, flex-start); }
.flexRowEndCen { @include flex(row, flex-end, center); }
.flexRowEndEnd { @include flex(row, flex-end, flex-end); }
.flexRowEndSpb { @include flex(row, flex-end, space-between); }
.flexRowEndSpa { @include flex(row, flex-end, space-around); }

.flexRowSpbStar { @include flex(row, space-between, flex-start); }
.flexRowSpbCen { @include flex(row, space-between, center); }
.flexRowSpbEnd { @include flex(row, space-between, flex-end); }
.flexRowSpbSpb { @include flex(row, space-between, space-between); }
.flexRowSpbSpa { @include flex(row, space-between, space-around); }

.flexRowSpaStar { @include flex(row, space-around, flex-start); }
.flexRowSpaCen { @include flex(row, space-around, center); }
.flexRowSpaEnd { @include flex(row, space-around, flex-end); }
.flexRowSpaSpb { @include flex(row, space-around, space-between); }
.flexRowSpaSpa { @include flex(row, space-around, space-around); }

// Column 排列组合
.flexColStarStar { @include flex(column, flex-start, flex-start); }
.flexColStarCen { @include flex(column, flex-start, center); }
.flexColStarEnd { @include flex(column, flex-start, flex-end); }
.flexColStarSpb { @include flex(column, flex-start, space-between); }
.flexColStarSpa { @include flex(column, flex-start, space-around); }

.flexColCenStar { @include flex(column, center, flex-start); }
.flexColCenCen { @include flex(column, center, center); }
.flexColCenEnd { @include flex(column, center, flex-end); }
.flexColCenSpb { @include flex(column, center, space-between); }
.flexColCenSpa { @include flex(column, center, space-around); }

.flexColEndStar { @include flex(column, flex-end, flex-start); }
.flexColEndCen { @include flex(column, flex-end, center); }
.flexColEndEnd { @include flex(column, flex-end, flex-end); }
.flexColEndSpb { @include flex(column, flex-end, space-between); }
.flexColEndSpa { @include flex(column, flex-end, space-around); }

.flexColSpbStar { @include flex(column, space-between, flex-start); }
.flexColSpbCen { @include flex(column, space-between, center); }
.flexColSpbEnd { @include flex(column, space-between, flex-end); }
.flexColSpbSpb { @include flex(column, space-between, space-between); }
.flexColSpbSpa { @include flex(column, space-between, space-around); }

.flexColSpaStar { @include flex(column, space-around, flex-start); }
.flexColSpaCen { @include flex(column, space-around, center); }
.flexColSpaEnd { @include flex(column, space-around, flex-end); }
.flexColSpaSpb { @include flex(column, space-around, space-between); }
.flexColSpaSpa { @include flex(column, space-around, space-around); }

// 弹性项
.flex1 { flex: 1; }
.flexAuto { flex: auto; }

// 间距
.flex-gap-10 { gap: 10px; }
.flex-gap-16 { gap: 16px; }