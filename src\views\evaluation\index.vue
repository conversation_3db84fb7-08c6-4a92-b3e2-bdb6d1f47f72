<template>
  <AiPageLayout>
    <a-tabs v-model:activeKey="activeTab" @change="tableChange">
      <template v-if="activeTab === 'evaluationSet'" #extra>
        <a-button style="margin-right: -16px" @click="useGuide">
          <icon-bulb />
          使用指南
        </a-button>
      </template>
      <a-tab-pane key="evaluationSets" title="评测集管理" />
      <a-tab-pane key="evaluator" title="评估器" />
      <a-tab-pane key="task" title="评测任务管理" />
      <a-tab-pane key="report" title="评测报告管理" />
    </a-tabs>
    <component :is="PaneMap[activeTab]" />
    <GuideModal ref="guideModal" />
  </AiPageLayout>
</template>

<script setup lang="ts">
import EvaluationSets from './components/evaluation-sets/index.vue'
import Task from './components/task/index.vue'
import Report from './components/report/index.vue'
import Evaluator from './components/evaluator/index.vue'
import GuideModal from './modal/GuideModal.vue'

defineOptions({ name: 'Evaluation' })

const PaneMap: Record<string, Component> = {
  evaluationSets: EvaluationSets,
  evaluator: Evaluator,
  task: Task,
  report: Report
}

const router = useRouter()
const route = useRoute()
const activeTab = ref<string>((route?.query?.tab as string) || 'evaluationSets')
const guideModal = ref(null)

const useGuide = () => {
  if (guideModal?.value) {
    guideModal.value?.open()
  }
}

const tableChange = (key) => {
  router.replace({ path: '/evaluation/index', query: { tab: key } })
}
</script>

<style scoped lang="scss">
:deep(.arco-tabs .arco-tabs-nav-type-card-gutter .arco-tabs-tab-active) {
  box-shadow:
    inset 0 2px 0 rgb(var(--primary-6)),
    inset -1px 0 0 var(--color-border-2),
    inset 1px 0 0 var(--color-border-2);
  position: relative;
}

:deep(.arco-tabs-nav-type-card-gutter .arco-tabs-tab) {
  border-radius: var(--border-radius-medium) var(--border-radius-medium) 0 0;
}

:deep(.arco-tabs-type-card-gutter > .arco-tabs-content) {
  border: none;
}

:deep(.arco-tabs-nav::before) {
  left: -20px;
  right: -20px;
}

:deep(.arco-tabs) {
  overflow: visible;
}

:deep(.arco-tabs-nav) {
  overflow: visible;
}
</style>
