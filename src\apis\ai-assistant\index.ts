import http from '@/utils/http'
import type { QueryParams, RegenerateParams } from './type'
export type * from './type'

const BASE_URL = '/case_generator'

/**
 * 获取任务列表
 * @param params 查询参数
 * @returns 任务列表数据
 */
export function getTaskList(params?: QueryParams) {
  return http.get(`${BASE_URL}/tasks`, params)
}

/**
 * 获取助手列表
 * @param params 查询参数
 * @returns 助手列表数据
 */
export function getAssistantList(params?: any) {
  return http.get(`${BASE_URL}/assistants`, { params })
}

/**
 * 创建助手
 * @param data 助手数据
 * @returns 创建结果
 */
export function createAssistant(data: any) {
  return http.post(`${BASE_URL}/assistants`, data)
}

/**
 * 获取默认提示词
 * @param phase 阶段
 * @returns 默认提示词
 */
export function getDefaultPrompt(phase: string) {
  return http.get(`${BASE_URL}/default-prompt/${phase}`)
}

/**
 * 创建任务（上传文档并创建任务）
 * @param data 任务数据
 * @returns 创建结果
 */
export function createTask(data: FormData) {
  return http.post(`${BASE_URL}/elements/extract`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 重新生成要素
 * @param taskId 任务ID
 * @param data 表单数据
 * @returns 操作结果
 */
export function regenerateElements(taskId: string, data: FormData) {
  return http.post(`${BASE_URL}/elements/regenerate/${taskId}`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出要素抽取结果
 * @param taskId 任务ID
 * @returns 文件流
 */
export function exportElements(taskId: string) {
  return http.get(`${BASE_URL}/elements/export/${taskId}`, {
    responseType: 'blob'
  })
}

/**
 * 上传正交组合文件
 * @param taskId 任务ID
 * @param data 表单数据
 * @returns 操作结果
 */
export function uploadOrthogonal(taskId: string, data: FormData) {
  // 将taskId添加到FormData中
  data.append('task_id', taskId)
  return http.post(`${BASE_URL}/orthogonal/generate`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出正交组合结果
 * @param taskId 任务ID
 * @returns 文件流
 */
export function exportOrthogonal(taskId: string) {
  return http.get(`${BASE_URL}/orthogonal/export/${taskId}`, {
    responseType: 'blob'
  })
}

/**
 * 重新生成正交组合
 * @param taskId 任务ID
 * @param data 重新生成参数
 * @returns 操作结果
 */
export function regenerateOrthogonal(taskId: string, data: RegenerateParams) {
  return http.post(`${BASE_URL}/orthogonal/regenerate/${taskId}`, data)
}

/**
 * 生成案例
 * @param taskId 任务ID
 * @param file 文件
 * @returns 操作结果
 */
export function generateCase(taskId: string, file: File) {
  const formData = new FormData()
  formData.append('file', file)
  return http.post(`${BASE_URL}/case/generate/${taskId}`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 导出案例
 * @param taskId 任务ID
 * @returns 文件流
 */
export function exportCase(taskId: string) {
  return http.get(`${BASE_URL}/case/export/${taskId}`, {
    responseType: 'blob'
  })
}

/**
 * 获取任务详情
 * @param taskId 任务ID
 * @returns 任务详情数据
 */
export function getTaskDetail(taskId: string) {
  return http.get(`${BASE_URL}/tasks/${taskId}`)
}

/**
 * 终止任务
 * @param taskId 任务ID
 * @returns 操作结果
 */
export function terminateTask(taskId: string) {
  return http.post(`${BASE_URL}/tasks/${taskId}/terminate`)
}

/**
 * 继续任务
 * @param taskId 任务ID
 * @returns 操作结果
 */
export function continueTask(taskId: string) {
  return http.post(`${BASE_URL}/tasks/${taskId}/continue`)
}

/**
 * 删除任务
 * @param taskId 任务ID
 * @returns 操作结果
 */
export function deleteTask(taskId: string) {
  return http.del(`${BASE_URL}/tasks/${taskId}`)
}

/**
 * 评价任务
 * @param taskId 任务ID
 * @param data 评价数据
 * @returns 操作结果
 */
export function evaluateTask(taskId: string, data: any) {
  return http.post(`${BASE_URL}/tasks/${taskId}/evaluate`, data)
}
