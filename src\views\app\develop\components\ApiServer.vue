<template>
  <div class="api-server-container">
    <!-- API服务器信息 -->
    <div class="api-server-info">
      <div class="server-item">
        <div class="server-label">API 服务器</div>
        <div class="server-content">
          <div class="server-url-wrapper">
            <span class="server-url">{{ appInfo.api_base_url }}</span>
            <a-button type="text" size="small" class="copy-btn" @click="handleCopy(appInfo.api_base_url)">
              <template #icon>
                <icon-copy />
              </template>
            </a-button>
          </div>
        </div>
      </div>

      <div class="status-item">
        <a-tag :color="appInfo?.enable_api ? 'green' : 'red'" size="small" class="status-tag">
          {{ appInfo?.enable_api ? '运行中' : '已停用' }}
        </a-tag>
      </div>

      <div class="api-key-item">
        <a-button type="outline" size="small" class="api-key-btn" @click="showApiKeyModal = true">
          <template #icon>
            <icon-link />
          </template>
          API 密钥
        </a-button>
      </div>
    </div>

    <!-- API密钥管理弹窗 -->
    <a-modal
      v-model:visible="showApiKeyModal"
      title="API 密钥"
      width="800px"
      :footer="false"
      @cancel="showApiKeyModal = false"
    >
      <div class="api-key-modal">
        <div class="modal-description">
          <a-alert type="warning" show-icon>
            <template #default>
              如果不想你的 API 被滥用，请保护好你的 API Key， 最佳实践是避免在前端代码中明文引用。
            </template>
          </a-alert>
        </div>

        <!-- 密钥列表 -->
        <div class="api-keys-section">
          <div v-if="loading" class="loading-container">
            <a-spin />
          </div>

          <div v-else-if="apiKeys.length === 0" class="empty-container">
            <a-empty description="暂无API密钥">
              <a-button type="primary" @click="handleCreateKey">创建第一个密钥</a-button>
            </a-empty>
          </div>

          <div v-else class="keys-table">
            <!-- 表头 -->
            <div class="table-header">
              <div class="header-cell key-cell">密钥</div>
              <div class="header-cell created-cell">创建时间</div>
              <div class="header-cell used-cell">最后使用</div>
              <div class="header-cell actions-cell">操作</div>
            </div>

            <!-- 表格内容 -->
            <div class="table-body">
              <div v-for="key in apiKeys" :key="key?.id || Math.random()" class="table-row">
                <div class="body-cell key-cell">
                  <span class="key-token">{{ formatToken(key?.token || '') }}</span>
                </div>
                <div class="body-cell created-cell">
                  {{ formatDateTime(key?.created_at) }}
                </div>
                <div class="body-cell used-cell">
                  {{ key?.last_used_at ? formatDateTime(key.last_used_at) : '从未' }}
                </div>
                <div class="body-cell actions-cell">
                  <a-space>
                    <a-button type="text" size="small" @click="handleCopy(key?.token || '')">
                      <template #icon>
                        <icon-copy />
                      </template>
                    </a-button>
                    <a-popconfirm
                      title="确定要删除这个API密钥吗？"
                      content="删除后无法恢复，正在使用的应用可能会受到影响。"
                      @ok="handleDeleteKey(key?.id || '')"
                    >
                      <a-button type="text" size="small" status="danger">
                        <template #icon>
                          <icon-delete />
                        </template>
                      </a-button>
                    </a-popconfirm>
                  </a-space>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 创建密钥按钮 - 只在有密钥时显示 -->
        <div v-if="apiKeys.length > 0" class="create-key-section">
          <a-button type="primary" :loading="createLoading" @click="handleCreateKey">
            <template #icon>
              <icon-plus />
            </template>
            创建密钥
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 新密钥展示弹窗 -->
    <a-modal v-model:visible="showNewKeyModal" title="API密钥创建成功" :footer="false" :closable="false">
      <div class="new-key-modal">
        <a-alert type="success" show-icon>
          <template #title>密钥创建成功</template>
          <template #default>请将此密钥保存在安全且可访问的地方。关闭此窗口后将无法再次查看完整密钥。</template>
        </a-alert>

        <div class="new-key-display">
          <a-input :model-value="newApiKey" readonly class="new-key-input">
            <template #suffix>
              <a-button type="text" @click="handleCopy(newApiKey)">
                <icon-copy />
              </a-button>
            </template>
          </a-input>
        </div>

        <div class="modal-actions">
          <a-button type="primary" @click="handleCloseNewKeyModal">我已保存，关闭</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { getApiKeys, createApiKey, deleteApiKey, type ApiKey } from '@/apis/apps'
import { copyToClipboard, dateFormat } from '@/utils'

// 使用真实API接口

const props = defineProps<{
  appId: string
  appInfo: any
}>()

// 数据状态
const loading = ref(false)
const createLoading = ref(false)
const apiKeys = ref<ApiKey[]>([])
const apiBaseUrl = ref('')

// 弹窗状态
const showApiKeyModal = ref(false)
const showNewKeyModal = ref(false)
const newApiKey = ref('')

// 初始化
onMounted(async () => {
  await loadApiKeys()
})

// 加载API密钥列表
const loadApiKeys = async () => {
  try {
    loading.value = true
    const res = await getApiKeys(props.appId)
    // 确保数据格式正确
    if (res && res.data && Array.isArray(res.data)) {
      apiKeys.value = res.data
    } else {
      apiKeys.value = []
    }
  } catch (error) {
    apiKeys.value = []
  } finally {
    loading.value = false
  }
}

// 创建API密钥
const handleCreateKey = async () => {
  try {
    createLoading.value = true
    const response = await createApiKey(props.appId, {})
    // 处理不同的响应格式
    const token = response.data?.token || response.token
    if (token) {
      newApiKey.value = token
      // 不关闭密钥列表弹窗，只显示新密钥弹窗
      showNewKeyModal.value = true
      await loadApiKeys()
      Message.success('API密钥创建成功')
    } else {
      throw new Error('未获取到密钥信息')
    }
  } catch (error) {
    console.error('创建API密钥失败:', error)
    Message.error('创建API密钥失败')
  } finally {
    createLoading.value = false
  }
}

// 删除API密钥
const handleDeleteKey = async (keyId: string) => {
  try {
    await deleteApiKey(props.appId, keyId)
    Message.success('API密钥已删除')
    await loadApiKeys()
  } catch (error) {
    console.error('删除API密钥失败:', error)
    Message.error('删除API密钥失败')
  }
}

// 关闭新密钥弹窗
const handleCloseNewKeyModal = () => {
  showNewKeyModal.value = false
  newApiKey.value = ''
}

// 格式化Token显示
const formatToken = (token: string) => {
  if (!token) return ''
  const prefix = token.substring(0, 8)
  const suffix = token.substring(token.length - 4)
  return `${prefix}••••••••••••••••${suffix}`
}

// 格式化日期时间
const formatDateTime = (dateStr: string | number) => {
  if (!dateStr) return '-'

  try {
    // 处理时间戳（秒或毫秒）
    let date: Date
    if (typeof dateStr === 'number') {
      // 如果是时间戳，判断是秒还是毫秒
      date = new Date(dateStr < 10000000000 ? dateStr * 1000 : dateStr)
    } else {
      // 如果是字符串
      date = new Date(dateStr)
    }

    // 检查日期是否有效
    if (isNaN(date.getTime())) {
      return '-'
    }

    return dateFormat(date, 'yyyy-MM-dd HH:mm')
  } catch (error) {
    console.error('日期格式化错误:', error, dateStr)
    return '-'
  }
}

// 处理复制
const handleCopy = async (text: string) => {
  try {
    const success = await copyToClipboard(text)
    if (success) {
      Message.success('已复制到剪贴板')
    } else {
      Message.error('复制失败')
    }
  } catch (error) {
    Message.error('复制失败')
  }
}
</script>

<style scoped lang="scss">
.api-server-container {
  .api-server-info {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;

    .server-item {
      display: flex;
      align-items: center;
      background: var(--color-bg-2);
      border: 1px solid var(--color-border-2);
      border-radius: 6px;
      padding: 4px 12px;

      .server-label {
        font-size: 11px;
        color: var(--color-text-3);
        background: var(--color-bg-3);
        padding: 2px 6px;
        border-radius: 3px;
        margin-right: 8px;
        border: 1px solid var(--color-border-3);
      }

      .server-content {
        .server-url-wrapper {
          display: flex;
          align-items: center;

          .server-url {
            font-size: 13px;
            font-weight: 500;
            color: var(--color-text-2);
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .copy-btn {
            margin-left: 8px;
            padding: 0 4px;

            .arco-icon {
              font-size: 12px;
            }
          }
        }
      }
    }

    .status-item {
      .status-tag {
        font-size: 12px;
        font-weight: 600;
      }
    }

    .api-key-item {
      .api-key-btn {
        font-size: 12px;
        height: 28px;

        .arco-icon {
          font-size: 12px;
        }
      }
    }
  }
}

.api-key-modal {
  .modal-description {
    margin-bottom: 20px;
  }

  .api-keys-section {
    margin-bottom: 20px;

    .loading-container,
    .empty-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 120px;
    }

    .keys-table {
      .table-header {
        display: flex;
        align-items: center;
        padding: 12px 0;
        border-bottom: 1px solid var(--color-border-2);
        font-size: 12px;
        font-weight: 600;
        color: var(--color-text-3);

        .header-cell {
          padding: 0 12px;

          &.key-cell {
            flex: 1;
            min-width: 200px;
          }

          &.created-cell,
          &.used-cell {
            width: 150px;
          }

          &.actions-cell {
            width: 100px;
          }
        }
      }

      .table-body {
        .table-row {
          display: flex;
          align-items: center;
          padding: 12px 0;
          border-bottom: 1px solid var(--color-border-3);

          &:last-child {
            border-bottom: none;
          }

          .body-cell {
            padding: 0 12px;
            font-size: 13px;
            color: var(--color-text-2);

            &.key-cell {
              flex: 1;
              min-width: 200px;

              .key-token {
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 12px;
              }
            }

            &.created-cell,
            &.used-cell {
              width: 150px;
              font-size: 12px;
            }

            &.actions-cell {
              width: 100px;
            }
          }
        }
      }
    }
  }

  .create-key-section {
    display: flex;
    justify-content: flex-start;
  }
}

.new-key-modal {
  .new-key-display {
    margin: 16px 0;

    .new-key-input {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
  }

  .modal-actions {
    display: flex;
    justify-content: center;
    margin-top: 16px;
  }
}

@media (max-width: 768px) {
  .api-server-container {
    .api-server-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .server-item {
        width: 100%;

        .server-content {
          .server-url-wrapper {
            .server-url {
              max-width: 200px;
            }
          }
        }
      }
    }
  }

  .api-key-modal {
    .keys-table {
      .table-header,
      .table-body .table-row {
        flex-direction: column;
        align-items: flex-start;

        .header-cell,
        .body-cell {
          width: 100%;
          padding: 4px 0;
        }
      }
    }
  }
}
</style>
