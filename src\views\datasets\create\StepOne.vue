<template>
  <div class="step-content step1-content">
    <div class="content-container">
      <div class="left-section">
        <!-- 数据源类型选择 - 仅在非添加文档模式下显示 -->
        <!-- <div v-if="!isAddingDocument" class="step-title">选择数据源</div> -->
        <!-- <div v-if="!isAddingDocument" class="source-type-selection">
          <a-radio-group default-value="upload" v-model="selectedSourceType">
            <a-radio value="upload">
              <template #radio="{ checked }">
                <div class="source-type-item" :class="{ active: checked }">
                  <icon-file class="source-icon" />
                  <span>导入已有文本</span>
                </div>
              </template>
            </a-radio>
            <a-radio value="notion">
              <template #radio="{ checked }">
                <div class="source-type-item" :class="{ active: checked }">
                  <icon-code-square class="source-icon" />
                  <span>同步自 Notion 内容</span>
                </div>
              </template>
            </a-radio>
            <a-radio value="web">
              <template #radio="{ checked }">
                <div class="source-type-item" :class="{ active: checked }">
                  <icon-relation class="source-icon" />
                  <span>同步自 Web 站点</span>
                </div>
              </template>
            </a-radio>
          </a-radio-group>
        </div> -->
        <div class="source-content">
          <!-- 导入已有文本 -->
          <div v-if="selectedSourceType === 'upload'" class="upload-section">
            <div class="step-title">上传文本文件</div>
            <a-upload
              :multiple="true"
              :limit="10"
              :accept="'.pdf,.txt,.docx,.pptx,.xlsx,.xls,.md,.html,.htm,.csv'"
              :custom-request="handleUpload"
              class="upload-area"
              draggable
              :file-list="innerUploadedFiles"
              :show-file-list="false"
            >
              <template #upload-button>
                <div class="upload-trigger">
                  <div class="upload-icon">
                    <icon-upload />
                  </div>
                  <div class="upload-text">
                    <div class="tips">
                      拖拽文件至此，或者
                      <a-link>选择文件</a-link>
                    </div>
                    <div class="file-types">
                      已支持 TXT、MARKDOWN、MDX、PDF、HTML、XLSX、XLS、DOCX、CSV、MD、HTM 等个文件不超过 15MB。
                    </div>
                  </div>
                </div>
              </template>
            </a-upload>
            <!-- 自定义上传文件列表 -->
            <div class="uploaded-files-list">
              <div
                v-for="file in innerUploadedFiles"
                :key="file.uid"
                class="uploaded-file-item"
                @click="handleFileClick(file)"
              >
                <icon-file class="file-icon" />
                <div class="file-info">
                  <div class="file-name">{{ file.name }}</div>
                  <div class="file-size">{{ formatFileSize(file.size) }}</div>
                </div>
                <a-button type="text" status="danger" @click.stop="removeFile(file)">
                  <icon-delete />
                </a-button>
              </div>
            </div>
          </div>

          <!-- 同步自 Notion 内容 -->
          <!-- <div v-else-if="selectedSourceType === 'notion'" class="notion-section">
            <div class="notion-status">
              <div class="notion-icon">
                <icon-code-square />
              </div>
              <div class="notion-info">
                <div class="notion-title">Notion 未绑定</div>
                <div class="notion-desc">同步 Notion 内容前，须先绑定 Notion 空间</div>
                <a-button type="primary" class="notion-bind-btn">去绑定</a-button>
              </div>
            </div>
          </div> -->

          <!-- 同步自 Web 站点 -->
          <!-- <div v-else-if="selectedSourceType === 'web'" class="web-section">
            <h3>选择工具</h3>
            <div class="web-tools">
              <a-radio-group v-model="selectedWebTool">
                <a-radio value="jina">
                  <template #radio="{ checked }">
                    <div class="web-tool-item" :class="{ active: checked }">
                      <icon-robot class="tool-icon" />
                      <span>Jina Reader</span>
                    </div>
                  </template>
                </a-radio>
                <a-radio value="firecrawl">
                  <template #radio="{ checked }">
                    <div class="web-tool-item" :class="{ active: checked }">
                      <icon-fire class="tool-icon" />
                      <span>Firecrawl</span>
                    </div>
                  </template>
                </a-radio>
                <a-radio value="watercrawl">
                  <template #radio="{ checked }">
                    <div class="web-tool-item" :class="{ active: checked }">
                      <icon-filter class="tool-icon" />
                      <span>WaterCrawl</span>
                    </div>
                  </template>
                </a-radio>
              </a-radio-group>
            </div>
            <div class="tool-config">
              <div class="jina-reader-config">
                <div class="tool-status">
                  <div class="tool-icon"><icon-robot /></div>
                  <div class="tool-info">
                    <div class="tool-title">Jina Reader 未配置</div>
                    <div class="tool-desc">请配置 Jina Reader 的免费 API 密钥和密码。</div>
                    <a-button type="primary" class="tool-config-btn">配置</a-button>
                  </div>
                </div>
              </div>
            </div>
          </div> -->
        </div>

        <!-- 底部操作区 -->
        <div class="step-footer step-footer1">
          <a-button
            type="primary"
            class="next-btn"
            :disabled="!canProceedToNextStep"
            size="small"
            @click="$emit('next-step')"
          >
            下一步
          </a-button>
        </div>
        <!-- 创建空知识库选项 - 仅在非添加文档模式下显示 -->
        <template v-if="!isAddingDocument">
          <a-divider />
          <div class="empty-dataset">
            <icon-folder-add class="icon" />
            <a-link @click="$emit('create-empty-dataset')">创建一个空知识库</a-link>
          </div>
        </template>
      </div>

      <div class="right-section">
        <!-- 点击上传的文件列表时，预览当前点击的文件 -->
        <div class="preview-container">
          <div v-if="innerUploadedFiles.length === 0" class="empty-preview">
            <div class="empty-state">
              <div class="empty-icon">📄</div>
              <p>尚未上传文件</p>
              <p class="empty-desc">上传文件后将在此处显示</p>
            </div>
          </div>
          <div v-else-if="!currentPreviewFile" class="empty-preview">
            <div class="empty-state">
              <div class="empty-icon">🔍</div>
              <p>点击左侧文件进行预览</p>
            </div>
          </div>
          <a-spin v-else-if="previewLoading" />
          <div v-else-if="previewError" class="error-preview">
            <div class="error-state">
              <div class="error-icon">❌</div>
              <p>文件预览失败</p>
              <p class="error-desc">无法加载文件内容</p>
            </div>
          </div>
          <div v-else-if="filePreviewContent" class="file-content-preview">
            <div class="preview-header">
              <div class="preview-file-name">
                <component
                  :is="
                    currentPreviewFile?.name.split('.').pop()?.toLowerCase() === 'pdf' ? 'icon-file-pdf' : 'icon-file'
                  "
                  class="icon"
                />
                <span>{{ currentPreviewFile?.name }}</span>
              </div>
              <div class="preview-file-info">
                {{ formatFileSize(currentPreviewFile?.size || 0) }}
              </div>
            </div>
            <div class="preview-content" v-html="filePreviewContent" />
          </div>
          <div v-else class="empty-preview">
            <div class="empty-state">
              <div class="empty-icon">❓</div>
              <p>无预览内容</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { uploadFile, getFilePreview } from '@/apis/datasets'

// Props定义
const props = defineProps({
  uploadedFiles: {
    type: Array,
    default: () => []
  },
  // 新增的属性，表示是否是添加文档模式
  isAddingDocument: {
    type: Boolean,
    default: false
  },
  // 新增的属性，表示目标知识库ID
  datasetId: {
    type: String,
    default: ''
  }
})

// Emits定义
const emit = defineEmits(['update:uploadedFiles', 'next-step', 'create-empty-dataset'])

// 内部状态
const selectedSourceType = ref('upload')
const selectedWebTool = ref('jina')
const innerUploadedFiles = ref<any[]>(props.uploadedFiles || [])
const currentPreviewFile = ref<any>(null)
const previewLoading = ref(false)
const previewError = ref(false)
const filePreviewContent = ref('')

// 文件类型定义
interface UploadFile {
  uid: string
  name: string
  size: number
  status?: string
  response?: any
  [key: string]: any
}

// 同步内部和外部上传文件列表
watch(
  () => props.uploadedFiles,
  (newVal) => {
    innerUploadedFiles.value = newVal
  },
  { deep: true }
)

watch(
  innerUploadedFiles,
  (newVal) => {
    emit('update:uploadedFiles', newVal)
  },
  { deep: true }
)

// 判断是否可以进入下一步
const canProceedToNextStep = computed(() => {
  if (selectedSourceType.value === 'upload') {
    return innerUploadedFiles.value.length > 0
  }
  return false
})

// 处理文件上传
const handleUpload = async (options) => {
  const { fileItem, onProgress, onSuccess, onError } = options
  let file = fileItem.file

  try {
    // 验证文件对象存在且有效
    if (!file || typeof file !== 'object') {
      console.error('文件对象无效:', file)
      onError && onError(new Error('文件对象无效'))
      return
    }

    // 确保文件有size属性
    if (file.size === undefined) {
      console.error('文件对象缺少size属性:', file)
      onError && onError(new Error('文件对象缺少size属性'))
      return
    }

    // 限制文件大小为15MB
    const MAX_FILE_SIZE = 15 * 1024 * 1024 // 15MB
    if (file.size > MAX_FILE_SIZE) {
      Message.error(`文件 ${file.name} 超过15MB限制`)
      onError && onError(new Error('文件大小超过限制'))
      return
    }

    try {
      // 使用API上传文件
      const response = await uploadFile(file, (percent) => {
        onProgress && onProgress(percent)
      })

      // 创建符合Arco Design上传组件要求的文件项
      const uploadItem = {
        uid: response.id,
        name: response.name,
        file: file,
        status: 'done',
        size: response.size,
        response: response
      }

      // 更新状态
      innerUploadedFiles.value.push(uploadItem)

      Message.success(`文件 ${file.name} 上传成功`)
      onSuccess && onSuccess(response)
    } catch (apiError: any) {
      console.error('文件上传API错误:', apiError)
      Message.error(`文件上传失败: ${apiError.message || '未知错误'}`)
      onError && onError(apiError)
    }
  } catch (error: any) {
    console.error('文件上传错误:', error)
    Message.error(`文件上传错误: ${error.message || '未知错误'}`)
    onError && onError(error)
  }
}

// 处理文件点击预览
const handleFileClick = async (file: UploadFile) => {
  if (!file || !file.response || !file.response.id) {
    Message.warning('文件上传中或上传失败，无法预览')
    return
  }

  currentPreviewFile.value = file
  previewLoading.value = true
  previewError.value = false
  filePreviewContent.value = ''

  try {
    const response = await getFilePreview(file.response.id)
    filePreviewContent.value = response.content || '文件内容为空'
  } catch (error) {
    console.error('获取文件预览失败:', error)
    previewError.value = true
  } finally {
    previewLoading.value = false
  }
}

// 移除文件
const removeFile = (file: UploadFile) => {
  const fileIndex = innerUploadedFiles.value.findIndex((f) => f.uid === file.uid)
  if (fileIndex !== -1) {
    innerUploadedFiles.value.splice(fileIndex, 1)
  }

  if (currentPreviewFile.value && currentPreviewFile.value.uid === file.uid) {
    currentPreviewFile.value = null
    filePreviewContent.value = ''
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + 'MB'
  }
}

// 获取MIME类型
const getMimeType = (filename) => {
  const ext = filename.split('.').pop()?.toLowerCase() || ''
  const mimeMap = {
    pdf: 'application/pdf',
    txt: 'text/plain',
    html: 'text/html',
    htm: 'text/html',
    md: 'text/markdown',
    csv: 'text/csv',
    docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    doc: 'application/msword',
    xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    xls: 'application/vnd.ms-excel',
    pptx: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    ppt: 'application/vnd.ms-powerpoint'
  }
  return mimeMap[ext] || 'application/octet-stream'
}

// 提供默认导出
defineExpose({
  // 如果需要暴露内部方法或属性，可以在这里添加
})
</script>

<style scoped lang="scss">
.step-content {
  &.step1-content {
    .content-container {
      display: flex;
      gap: 24px;
      height: calc(100vh - 120px); /* 设置左右容器整体高度 */
      overflow: hidden;

      .left-section,
      .right-section {
        flex: 1;
        height: 100%;
        overflow-y: auto; /* 允许左右两侧独立滚动 */
        padding-right: 8px; /* 为滚动条预留空间 */
      }

      .left-section {
        .step-title {
          font-size: 14px;
          font-weight: 600;
          line-height: 20px;
          margin-bottom: 12px;
        }

        .source-type-selection {
          margin-bottom: 24px;

          :deep(.arco-radio-group) {
            display: flex;
            gap: 16px;

            .arco-radio {
              margin-right: 0;
            }
          }

          .source-type-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px 20px;
            border: 1px solid var(--color-border-2);
            border-radius: 8px;
            transition: all 0.3s;
            cursor: pointer;

            &.active {
              border-color: var(--color-primary-light-3);
              background-color: var(--color-primary-light-1);

              .source-icon {
                color: var(--color-primary);
              }
            }

            .source-icon {
              font-size: 18px;
              color: var(--color-text-3);
            }
          }
        }

        .source-content {
          background-color: var(--color-bg-2);
          border-radius: 12px;
          margin-bottom: 24px;
          height: auto;

          h3 {
            font-size: 14px;
            font-weight: 500;
            margin-top: 0;
            margin-bottom: 12px;
          }

          .section-desc {
            color: var(--color-text-3);
            margin-bottom: 16px;
          }

          .upload-area {
            width: 100%;
            padding: 20px 0;

            .upload-trigger {
              width: 80%;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              border: 1px dashed var(--color-border-1);
              border-radius: 8px;
              background-color: var(--color-fill-1);
              padding: 40px 0;
              cursor: pointer;
              transition: all 0.3s;

              &:hover {
                border-color: var(--color-primary-light-2);
                background-color: var(--color-fill-2);
              }

              .upload-icon {
                font-size: 28px;
                color: var(--color-text-3);
                margin-bottom: 16px;
              }

              .upload-text {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;

                .tips {
                  font-size: 14px;
                  color: var(--color-text-1);
                  margin-bottom: 8px;
                }

                .file-types {
                  font-size: 12px;
                  color: var(--color-text-3);
                  max-width: 500px;
                }
              }
            }
          }

          // 上传文件列表样式
          .uploaded-files-list {
            width: 80%;
            .uploaded-file-item {
              display: flex;
              align-items: center;
              gap: 12px;
              padding: 8px 12px;
              background-color: var(--color-fill-1);
              border-radius: 4px;
              cursor: pointer;
              transition: background-color 0.2s;
              margin-bottom: 8px;

              &:hover {
                background-color: var(--color-fill-2);
              }

              .file-icon {
                color: var(--color-text-3);
                font-size: 18px;
              }

              .file-info {
                flex: 1;
                font-size: 14px;
                color: var(--color-text-1);

                .file-name {
                  font-size: 14px;
                  margin-bottom: 4px;
                }

                .file-size {
                  font-size: 12px;
                  color: var(--color-text-3);
                }
              }
            }
          }

          .notion-status,
          .tool-status {
            display: flex;
            align-items: flex-start;
            flex-direction: column;
            justify-content: flex-start;
            gap: 16px;
            padding: 24px;
            background-color: var(--color-fill-1);
            width: 80%;
            border-radius: 16px;

            .notion-icon,
            .tool-icon {
              font-size: 48px;
              color: var(--color-text-3);
            }

            .notion-info,
            .tool-info {
              .notion-title,
              .tool-title {
                font-size: 14px;
                font-weight: 500;
                margin-bottom: 4px;
              }

              .notion-desc,
              .tool-desc {
                font-size: 14px;
                color: var(--color-text-3);
                margin-bottom: 12px;
              }
            }
          }

          .web-tools {
            margin-bottom: 24px;

            :deep(.arco-radio-group) {
              display: flex;
              gap: 16px;

              .arco-radio {
                margin-right: 0;
              }
            }

            .web-tool-item {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 8px 16px;
              border: 1px solid var(--color-border-2);
              border-radius: 8px;
              transition: all 0.3s;
              cursor: pointer;

              &.active {
                border-color: var(--color-primary-light-3);
                background-color: var(--color-primary-light-1);

                .tool-icon {
                  color: var(--color-primary);
                }
              }

              .tool-icon {
                font-size: 14px;
                color: var(--color-text-3);
              }
            }
          }
        }

        .step-footer {
          display: flex;
          align-items: center;

          .empty-dataset {
            display: flex;
            align-items: center;
            gap: 8px;
            color: var(--color-text-3);
            margin-bottom: 30px;
            .icon {
              font-size: 14px;
            }
          }

          .prev-btn,
          .next-btn {
            min-width: 80px;
            height: 36px;
            border-radius: 6px;
          }
        }
        .step-footer1 {
          justify-content: flex-end;
        }
      }

      .right-section {
        .preview-container {
          background-color: var(--color-bg-2);
          border-radius: 12px;
          padding: 24px;
          margin-bottom: 5px;
          height: auto;
          min-height: 400px;
          display: flex;
          flex-direction: column;

          .empty-preview {
            height: 400px;
            display: flex;
            justify-content: center;
            align-items: center;

            .empty-state {
              display: flex;
              flex-direction: column;
              align-items: center;
              text-align: center;

              .empty-icon {
                font-size: 40px;
                color: var(--color-text-3);
                margin-bottom: 16px;
              }

              p {
                margin: 4px 0;
                color: var(--color-text-3);
              }

              .empty-desc {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

.file-content-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: var(--color-fill-1);
    border-bottom: 1px solid var(--color-border-2);

    .preview-file-name {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;

      .icon {
        color: var(--color-text-3);
      }
    }

    .preview-file-info {
      font-size: 12px;
      color: var(--color-text-3);
    }
  }

  .preview-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    overflow-x: hidden;
    line-height: 1.6;
    font-size: 14px;
    background-color: #fff;
    height: calc(100% - 45px);
    white-space: pre-wrap;
    word-break: break-word;
  }
}

.error-preview {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-fill-1);
  border-radius: 8px;

  .error-state {
    text-align: center;

    .error-icon {
      font-size: 40px;
      margin-bottom: 16px;
    }

    p {
      margin: 4px 0;
      color: var(--color-text-3);
    }

    .error-desc {
      font-size: 12px;
    }
  }
}
</style>
