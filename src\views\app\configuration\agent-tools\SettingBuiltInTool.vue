<template>
  <a-drawer
    :width="540"
    :visible="visible"
    unmountOnClose
    @ok="handleOk"
    @cancel="handleCancel"

  >
    <template #title>
      <div class="flex grow items-center">
        <div class="flex items-center">
          <img
            v-if="typeof toolCollection.icon === 'string'"
            :src="toolCollection.icon"
            style="width: 18px; height: 18px; display: inline-block"
            alt=""
          />
          <div v-else :style="{ backgroundColor: toolCollection.icon ? toolCollection.icon.background : '#ffffff' }">
            {{ toolCollection.icon?.content }}
          </div>

          <div class="flex h-4 items-center space-x-0.5 ml-4">
            <div class="system-xs-regular shrink-0 text-text-tertiary">{{ toolCollection.author }}</div>
            <div class="system-xs-regular shrink-0 text-text-quaternary">/</div>
            <div class="system-xs-regular truncate text-text-tertiary">
              {{ toolCollection.name.split('/').pop() || '' }}
            </div>
          </div>
        </div>
      </div>
    </template>
    <div>
      <div class="system-md-semibold mt-1 text-text-primary">{{ renderName(toolCollection.label) }}</div>
      <div class="h-10 line-clamp-2 system-sm-regular text-text-tertiary mt-3">
        {{ renderName(toolCollection.description) }}
      </div>

      <a-tabs :type="'capsule'" :size="'small'">
        <a-tab-pane key="info" title="参数">
          <InfoTab :infoSchemas="infoSchemas" />
        </a-tab-pane>
        <a-tab-pane key="setting" title="设置">

        </a-tab-pane>
      </a-tabs>
    </div>
  </a-drawer>
</template>
<script setup lang="ts">
import { addDefaultValue, renderName, toolParametersToFormSchemas } from '@/views/app/workflow/utils/configuration'
import { CollectionType } from '@/views/app/workflow/types/variable'
import InfoTab from '@/views/app/configuration/agent-tools/InfoTab.vue'
import {
  fetchBuiltInToolList,
  fetchCustomToolList,
  fetchModelToolList,
  fetchWorkflowToolList
} from '@/apis/workflow/configuration'

const props = defineProps(['toolCollection', 'toolName', 'setting'])
const emits = defineEmits(['closeDrawer'])
const visible = ref(true)
const handleOk = () => {
  emits('closeDrawer', 'ok')
}
const handleCancel = () => {
  emits('closeDrawer', 'cancel')
}

const toolList = ref([])
const formSchemas = ref([])
const infoSchemas = ref([])
const getToolList = async () => {
  console.log('props:', props.toolCollection)
  let serviceName = fetchModelToolList
  let params: any = ''
  if (props.toolCollection.type === CollectionType.model) {
    serviceName = fetchModelToolList
    params = { provider: props.toolCollection.name }
  } else if (props.toolCollection.type === CollectionType.builtIn) {
    params = props.toolCollection.name
    serviceName = fetchBuiltInToolList
  } else if (props.toolCollection.type === CollectionType.workflow) {
    params = props.toolCollection.id
    serviceName = fetchWorkflowToolList
  } else {
    params = { provider: props.toolCollection.name }
    serviceName = fetchCustomToolList
  }
  const res: any[] = await serviceName(params)
  console.log('serviceName:', res)
  toolList.value = res || []

  const currentTool = toolList.value.find((tool) => tool.name === props.toolName)
  if (currentTool) {
    const formSchemasTemp = toolParametersToFormSchemas(currentTool.parameters)
    formSchemas.value = formSchemasTemp
    infoSchemas.value = formSchemas.value.filter((item) => item.form === 'llm')
    const forms = addDefaultValue(props.setting, formSchemasTemp)
    console.log('forms:', forms)
  }
}

onMounted(() => {
  getToolList()
})
</script>

<style scoped lang="scss">

</style>
