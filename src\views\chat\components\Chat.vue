<template>
  <!-- <dev class="chat-container"> -->
  <!-- 消息展示区域 -->
  <a-layout-content style="overflow: hidden">
    <a-scrollbar ref="scrollbarRef" style="height: 100%; overflow: auto" outer-class="message-area">
      <div ref="innerRef" class="message-container">
        <div v-for="message in messages" :key="message.id" class="message-wrapper">
          <!-- AI 消息 -->
          <a-comment v-if="!message.isUser" align="left" class="ai-message">
            <template #avatar>
              <a-avatar>
                <IconRobot />
              </a-avatar>
            </template>
            <template #content>
              <div class="message-bubble ai-bubble">
                {{ message.content }}
              </div>
              <div class="message-actions">
                <a-space>
                  <a-button type="text">
                    <template #icon>
                      <IconCopy />
                    </template>
                  </a-button>
                  <a-button type="text">
                    <template #icon>
                      <IconRefresh />
                    </template>
                  </a-button>
                  <a-button type="text">
                    <template #icon>
                      <IconThumbUp />
                    </template>
                  </a-button>
                  <a-button type="text">
                    <template #icon>
                      <IconThumbDown />
                    </template>
                  </a-button>
                </a-space>
              </div>
            </template>
          </a-comment>

          <!-- 用户消息 -->
          <a-comment v-else align="right" class="user-message">
            <template #content>
              <div class="message-bubble user-bubble">
                {{ message.content }}
              </div>
              <div class="message-actions">
                <a-space>
                  <a-button type="text">
                    <template #icon>
                      <IconCopy />
                    </template>
                  </a-button>
                  <a-button type="text">
                    <template #icon>
                      <IconEdit />
                    </template>
                  </a-button>
                </a-space>
              </div>
            </template>
          </a-comment>
        </div>
      </div>
    </a-scrollbar>
  </a-layout-content>
  <!-- 输入区域 -->
  <a-layout-footer>
    <div class="input-area">
      <a-textarea
        v-model="inputMessage"
        :auto-size="{
          minRows: 2,
          maxRows: 8
        }"
        placeholder="输入您的问题..."
        @keyup.enter="handleSend"
      />
      <a-button style="margin-left: 20px" type="primary" :loading="sending" @click="handleSend">
        <template #icon>
          <icon-send />
        </template>
      </a-button>
    </div>
  </a-layout-footer>
  <!-- </a-layout> -->
</template>

<script setup lang="ts">
import { nextTick, reactive, ref } from 'vue'
import { Message } from '@arco-design/web-vue'

interface MessageItem {
  id: number
  content: string
  isUser: boolean
  timestamp: number
}

// 消息数据
const messages = reactive<MessageItem[]>([
  {
    id: 1,
    content: '您好！我是DeepSeek助手，有什么可以帮您？',
    isUser: false,
    timestamp: Date.now()
  }
])

const inputMessage = ref('')
const sending = ref(false)
const scrollbarRef = ref()
const innerRef = ref()

// 发送消息
const handleSend = async (event) => {
  if (!inputMessage.value.trim()) {
    Message.warning('请输入有效内容')
    event.preventDefault()
    return
  }

  // 添加用户消息
  messages.push({
    id: Date.now(),
    content: inputMessage.value.trim(),
    isUser: true,
    timestamp: Date.now()
  })

  // 清空输入框
  const userMessage = inputMessage.value
  inputMessage.value = ''
  // 滚动到底部
  const scrollToBottom = () => {
    if (scrollbarRef.value) {
      scrollbarRef.value.scrollTo({ y: scrollbarRef.value.$el.scrollHeight })
    }
  }
  // 滚动到底部
  await nextTick()
  scrollToBottom()

  // 模拟AI回复
  sending.value = true
  setTimeout(() => {
    messages.push({
      id: Date.now(),
      content: `已收到：${userMessage} \n这是AI的回复。`,
      isUser: false,
      timestamp: Date.now()
    })
    sending.value = false
    scrollToBottom()
  }, 1000)
}
</script>

<style scoped lang="less">
.message-container {
  padding: 20px;
  margin: 0 auto;
  max-width: 80%;
}
.message-area {
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
}

.message-wrapper {
  margin: 12px 0;
}

.message-bubble {
  max-width: 90%;
  padding: 12px 16px;
  border-radius: 8px;
  line-height: 1.5;
}

.ai-bubble {
  background-color: var(--color-fill-2);
  margin-right: auto;
  text-align: left;
}

.user-bubble {
  background-color: var(--color-primary-light-1);
  margin-left: auto;
}

.message-actions {
  font-size: 18px;
  color: var(--color-text-3);
  margin-top: 4px;
  max-width: 90%;
  text-align: right;
  font-weight: 500;
}
.user-message {
  align-items: flex-end;
  justify-content: flex-end;
  .arco-space {
    display: none;
  }
  &:hover {
    .arco-space {
      display: inline-flex;
    }
  }
}
.input-area {
  width: 100%;
  max-width: 80%;
  margin: 0 auto 40px;
  padding: 10px;
  display: flex;
  background-color: var(--color-bg-2);
  border: 1px solid var(--color-border-2);
  border-radius: 8px;
  justify-content: space-between;
  align-items: center;
}
.message-actions {
  height: 32px;
  .arco-space {
    display: none;
  }
}
.ai-message:hover {
  .arco-space {
    display: inline-flex;
  }
}
.arco-avatar-circle {
  background: linear-gradient(rgb(var(--primary-6)) 0%, rgb(var(--primary-4)) 100%);
  transition: all 0.2s;
}
:deep(.arco-comment-inner) {
  flex: inherit;
}

:deep(.arco-textarea-wrapper) {
  border: none;
  .arco-textarea {
    border: none;
    box-shadow: none;
    background-color: var(--color-bg-2);
    padding: 0;
    &:focus {
      box-shadow: none;
      border-color: var(--color-primary-1);
    }
  }
}
:deep(.arco-layout-footer) {
  font-weight: 400;
  font-size: 14px;
  // line-height: 48px;
  padding-bottom: 40px !important;
}
:deep(.arco-layout-footer),
:deep(.arco-layout-content) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  color: var(--color-white);
  font-size: 16px;
  font-stretch: condensed;
  text-align: center;
}
</style>
