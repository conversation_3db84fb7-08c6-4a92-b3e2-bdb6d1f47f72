<template>
  <div class="settings-container">
    <div class="settings-header">
      <h2>知识库设置</h2>
      <div class="description">在这里，您可以修改此知识库的属性和检索设置</div>
    </div>

    <div class="settings-form">
      <div class="form-section">
        <div class="form-item">
          <div class="form-label">知识库名称</div>
          <a-input v-model="datasetInfo.name" placeholder="请输入知识库名称" class="full-width" />
        </div>

        <div class="form-item">
          <div class="form-label">知识库描述</div>
          <a-textarea
            v-model="datasetInfo.description"
            placeholder="useful for when you want to answer queries about the 捷科专属服务方案.pdf"
            :auto-size="{ minRows: 5, maxRows: 8 }"
            allow-clear
            class="full-width"
          />
        </div>

        <div class="form-item">
          <div class="form-label">可见权限</div>
          <div class="permission-select-container">
            <!-- 权限选择下拉框 -->
            <div class="permission-dropdown">
              <div class="selected-option" @click="togglePermissionDropdown">
                <div class="selected-option-content">
                  <div v-if="permission === 'only_me'" class="avatar-circle owner">
                    <icon-user />
                  </div>
                  <div v-else-if="permission === 'all_team_members'" class="avatar-circle team">
                    <icon-user-group />
                  </div>
                  <div v-else-if="permission === 'partial_members'" class="avatar-circle team-part">
                    <icon-user-add />
                  </div>
                  <span>{{ getPermissionText() }}</span>
                </div>
                <icon-down />
              </div>

              <!-- 下拉菜单 -->
              <div v-if="permissionDropdownOpen" class="dropdown-menu">
                <div
                  class="dropdown-item"
                  :class="{ active: permission === 'only_me' }"
                  @click="selectPermission('only_me')"
                >
                  <div class="avatar-circle owner">
                    <icon-user />
                  </div>
                  <span>只有我</span>
                  <icon-check v-if="permission === 'only_me'" class="check-icon" />
                </div>

                <div
                  class="dropdown-item"
                  :class="{ active: permission === 'all_team_members' }"
                  @click="selectPermission('all_team_members')"
                >
                  <div class="avatar-circle team">
                    <icon-user-group />
                  </div>
                  <span>所有团队成员</span>
                  <icon-check v-if="permission === 'all_team_members'" class="check-icon" />
                </div>

                <div
                  class="dropdown-item"
                  :class="{ active: permission === 'partial_members' }"
                  @click="selectPartialMembers"
                >
                  <div class="avatar-circle team-part">
                    <icon-user-add />
                  </div>
                  <span>部分团队成员</span>
                  <icon-check v-if="permission === 'partial_members'" class="check-icon" />
                </div>

                <!-- 将成员选择区域移到这里，作为dropdown-menu的一部分 -->
                <div v-if="permission === 'partial_members'" class="member-selection">
                  <!-- 搜索框 -->
                  <div class="search-box">
                    <icon-search class="search-icon" />
                    <input
                      v-model="memberSearchQuery"
                      type="text"
                      placeholder="搜索"
                      @input="filterTeamMembers"
                      @click.stop
                    />
                  </div>

                  <!-- 团队成员列表 -->
                  <div class="member-list">
                    <div
                      v-for="member in filteredTeamMembers"
                      :key="member.id"
                      class="member-item"
                      :class="{ selected: isSelectedMember(member.id) }"
                      @click.stop="toggleMemberSelection(member)"
                    >
                      <div class="member-avatar">
                        <img v-if="member.avatar_url" :src="member.avatar_url" alt="avatar" />
                        <div v-else class="default-avatar">{{ getInitials(member.name) }}</div>
                      </div>
                      <div class="member-info">
                        <div class="member-name">{{ member.name }} {{ isCurrentUser(member.id) ? '(你)' : '' }}</div>
                        <div class="member-email">{{ member.email }}</div>
                      </div>
                      <icon-check v-if="isSelectedMember(member.id)" class="member-check" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="form-item">
          <div class="form-label">索引模式</div>
          <IndexType :indexing-method="indexMode" @indexing-method-change="indexMode = $event" />
        </div>

        <div class="form-item">
          <div class="form-label">Embedding 模型</div>
          <EmbeddingModel
            class="embedding-model-box"
            :embedding-model="embeddingModel"
            :embedding-model-options="embeddingModelOptions"
            @embedding-model-change="embeddingModel = $event"
          />
        </div>

        <div class="form-item">
          <div class="form-label">检索设置</div>
          <RetrievalSetting
            class="retrieval-setting-box"
            :search-method="retrieval.searchMethod"
            :vector-settings="vectorSettings"
            :full-text-settings="fullTextSettings"
            :hybrid-settings="hybridSettings"
            :rerank-models="rerankModels"
            @search-method-change="handleSearchMethodChange"
            @vector-settings-change="handleVectorSettingsChange"
            @full-text-settings-change="handleFullTextSettingsChange"
            @hybrid-settings-change="handleHybridSettingsChange"
          />
        </div>
      </div>

      <div class="form-actions">
        <a-button type="primary" :loading="saving" @click="saveSettings">保存</a-button>
      </div>
    </div>

    <!-- 确认对话框 -->
    <a-modal
      v-model:visible="confirmVisible"
      :title="confirmTitle"
      :unmount-on-close="true"
      ok-text="确认"
      cancel-text="取消"
      :ok-button-props="{ status: confirmType }"
      @ok="handleConfirm"
      @cancel="cancelConfirm"
    >
      <p>{{ confirmMessage }}</p>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
  getDatasetDetail,
  updateDataset,
  deleteDataset,
  getWorkspaceMembers,
  updateDatasetSettings
} from '@/apis/datasets'
import IndexType from '@/views/datasets/components/IndexType.vue'
import EmbeddingModel from '@/views/datasets/components/EmbeddingModel.vue'
import RetrievalSetting from '@/views/datasets/components/RetrievalSetting.vue'

// 定义props
const props = defineProps({
  datasetId: {
    type: String,
    required: true
  }
})

// 定义emit
const emit = defineEmits(['dataset-deleted'])

// 数据状态
const datasetInfo = reactive({
  name: '',
  description: ''
})

// 检索设置相关变量
const retrieval = reactive({
  searchMethod: 'hybrid',
  topK: 3,
  scoreThreshold: 0.5,
  scoreThresholdEnabled: true,
  rerankingEnabled: true,
  rerankingModel: 'gte-rerank'
})

// 向量检索设置
const vectorSettings = reactive({
  useRerank: true,
  rerankModel: 'netease-youdao/bce-reranker-base_v1',
  topK: 3,
  useScoreThreshold: true,
  scoreThreshold: 0.5
})

// 全文检索设置
const fullTextSettings = reactive({
  useRerank: true,
  rerankModel: 'netease-youdao/bce-reranker-base_v1',
  topK: 3,
  useScoreThreshold: true,
  scoreThreshold: 0.5
})

// 混合检索设置
const hybridSettings = reactive({
  mode: 'rerank' as 'rerank' | 'weight',
  vectorWeight: 0.5,
  rerankModel: 'netease-youdao/bce-reranker-base_v1',
  topK: 3,
  useScoreThreshold: true,
  scoreThreshold: 0.5
})

// Rerank模型列表
const rerankModels = ref([
  {
    model: 'netease-youdao/bce-reranker-base_v1',
    label: {
      zh_Hans: 'bce-reranker-base_v1',
      en_US: 'bce-reranker-base_v1'
    },
    model_type: 'rerank',
    features: {},
    fetch_from: 'model_provider',
    model_properties: {
      context_size: 512
    },
    deprecated: false,
    status: 'online',
    load_balancing_enabled: true
  },
  {
    model: 'gte-rerank',
    label: {
      zh_Hans: 'gte-rerank',
      en_US: 'gte-rerank'
    },
    model_type: 'rerank',
    features: {},
    fetch_from: 'model_provider',
    model_properties: {
      context_size: 512
    },
    deprecated: false,
    status: 'online',
    load_balancing_enabled: true
  }
])

const permission = ref('only_me')
const indexMode = ref('high_quality')
const embeddingModel = ref('text-embedding-v3')
const embeddingModelOptions = ref([
  { value: 'text-embedding-v3', label: 'text-embedding-v3' },
  { value: 'text-embedding-ada-002', label: 'text-embedding-ada-002' }
])
const saving = ref(false)

// 确认对话框状态
const confirmVisible = ref(false)
const confirmTitle = ref('')
const confirmMessage = ref('')
const confirmType = ref<'success' | 'warning' | 'normal' | 'danger'>('normal')
const confirmAction = ref('')

// 成员选择相关变量
const permissionDropdownOpen = ref(false)
// 定义TeamMember接口
interface TeamMember {
  id: string
  name: string
  avatar: string | null
  avatar_url: string | null
  email: string
  last_login_at: number | null
  last_active_at: number
  created_at: number
  role: 'owner' | 'admin' | 'editor' | 'normal'
  status: 'active' | 'pending'
}
const teamMembers = ref<TeamMember[]>([])
const filteredTeamMembers = ref<TeamMember[]>([])
const selectedMemberIds = ref<string[]>([])
const memberSearchQuery = ref('')
const currentUserId = ref('')

// 生命周期钩子
onMounted(() => {
  fetchDatasetInfo()
  fetchTeamMembers()

  // 点击外部关闭权限下拉菜单
  document.addEventListener('click', closePermissionDropdownOnClickOutside)
})

// 处理检索方法变更
const handleSearchMethodChange = (method) => {
  retrieval.searchMethod = method
}

// 处理向量检索设置变更
const handleVectorSettingsChange = (settings) => {
  Object.assign(vectorSettings, settings)
  updateRetrievalSettings()
}

// 处理全文检索设置变更
const handleFullTextSettingsChange = (settings) => {
  Object.assign(fullTextSettings, settings)
  updateRetrievalSettings()
}

// 处理混合检索设置变更
const handleHybridSettingsChange = (settings) => {
  Object.assign(hybridSettings, settings)
  updateRetrievalSettings()
}

// 更新retrieval对象中的设置
const updateRetrievalSettings = () => {
  const currentSettings =
    retrieval.searchMethod === 'vector'
      ? vectorSettings
      : retrieval.searchMethod === 'fulltext'
        ? fullTextSettings
        : hybridSettings

  retrieval.topK = currentSettings.topK
  retrieval.scoreThresholdEnabled = currentSettings.useScoreThreshold
  retrieval.scoreThreshold = currentSettings.scoreThreshold

  if (retrieval.searchMethod === 'hybrid') {
    retrieval.rerankingEnabled = hybridSettings.mode === 'rerank'
  } else {
    retrieval.rerankingEnabled = 'useRerank' in currentSettings ? currentSettings.useRerank : false
  }

  retrieval.rerankingModel = currentSettings.rerankModel
}

// 获取知识库信息
const fetchDatasetInfo = async () => {
  try {
    const response = await getDatasetDetail(props.datasetId)

    // 更新基本信息
    datasetInfo.name = response.name || ''
    datasetInfo.description = response.description || ''

    // 更新权限
    permission.value = response.permission || 'only_me'

    // 更新选中的成员列表
    if (response.partial_member_list && Array.isArray(response.partial_member_list)) {
      selectedMemberIds.value = response.partial_member_list
    }

    // 更新索引模式
    indexMode.value = (response as any).index_mode || 'high_quality'

    // 更新Embedding模型
    embeddingModel.value = (response as any).embedding_model || 'text-embedding-v3'

    // 更新检索设置
    if (response.retrieval_model_dict) {
      const retrievalConfig = response.retrieval_model_dict

      // 设置检索方法
      retrieval.searchMethod = retrievalConfig.search_method || 'hybrid'

      // 设置相关参数
      retrieval.topK = retrievalConfig.top_k || 3
      retrieval.scoreThresholdEnabled = retrievalConfig.score_threshold_enabled || true
      retrieval.scoreThreshold = retrievalConfig.score_threshold || 0.5
      retrieval.rerankingEnabled = retrievalConfig.reranking_enable || true

      if (retrievalConfig.reranking_model) {
        retrieval.rerankingModel =
          retrievalConfig.reranking_model.reranking_model_name || 'netease-youdao/bce-reranker-base_v1'
      }

      // 同步设置到各检索方式的详细配置
      syncRetrievalSettings()
    }
  } catch (error) {
    console.error('获取知识库信息失败:', error)
    Message.error('获取知识库信息失败')
  }
}

// 同步retrieval对象的设置到各检索方式的详细配置
const syncRetrievalSettings = () => {
  // 同步向量检索设置
  vectorSettings.topK = retrieval.topK
  vectorSettings.useScoreThreshold = retrieval.scoreThresholdEnabled
  vectorSettings.scoreThreshold = retrieval.scoreThreshold
  vectorSettings.useRerank = retrieval.rerankingEnabled
  vectorSettings.rerankModel = retrieval.rerankingModel

  // 同步全文检索设置
  fullTextSettings.topK = retrieval.topK
  fullTextSettings.useScoreThreshold = retrieval.scoreThresholdEnabled
  fullTextSettings.scoreThreshold = retrieval.scoreThreshold
  fullTextSettings.useRerank = retrieval.rerankingEnabled
  fullTextSettings.rerankModel = retrieval.rerankingModel

  // 同步混合检索设置
  hybridSettings.topK = retrieval.topK
  hybridSettings.useScoreThreshold = retrieval.scoreThresholdEnabled
  hybridSettings.scoreThreshold = retrieval.scoreThreshold
  hybridSettings.mode = retrieval.rerankingEnabled ? 'rerank' : 'weight'
  hybridSettings.rerankModel = retrieval.rerankingModel
}

// 获取团队成员列表
const fetchTeamMembers = async () => {
  try {
    const response = await getWorkspaceMembers()
    teamMembers.value = response.accounts
    filteredTeamMembers.value = [...teamMembers.value]

    // 设置当前用户ID
    const currentUser = teamMembers.value.find((member) => member.role === 'owner')
    if (currentUser) {
      currentUserId.value = currentUser.id

      // 如果是部分团队成员且没有选择成员，则默认选择当前用户
      if (permission.value === 'partial_members' && selectedMemberIds.value.length === 0) {
        selectedMemberIds.value = [currentUser.id]
      }
    }
  } catch (error) {
    console.error('获取团队成员失败:', error)
  }
}

// 切换权限下拉菜单
const togglePermissionDropdown = () => {
  permissionDropdownOpen.value = !permissionDropdownOpen.value
}

// 点击外部关闭下拉菜单
const closePermissionDropdownOnClickOutside = (event) => {
  const target = event.target
  if (!target.closest('.permission-dropdown')) {
    permissionDropdownOpen.value = false
  }
}

// 获取权限文本
const getPermissionText = () => {
  switch (permission.value) {
    case 'only_me':
      return '只有我'
    case 'all_team_members':
      return '所有团队成员'
    case 'partial_members':
      // 显示已选成员的数量
      const count = selectedMemberIds.value.length
      if (count === 0) return '部分团队成员'

      // 获取已选成员的名字
      const selectedMembers = teamMembers.value.filter((member) => selectedMemberIds.value.includes(member.id))
      const selectedNames = selectedMembers.map((member) => member.name)

      // // 如果成员太多，只显示前3个
      // if (selectedNames.length > 3) {
      //   return `已选择 ${count} 位成员: ${selectedNames.slice(0, 3).join('、')}等`;
      // } else {
      //   return `已选择 ${count} 位成员: ${selectedNames.join('、')}`;
      // }
      return `${selectedNames.join('、')}`
    default:
      return '只有我'
  }
}

// 点击"部分团队成员"选项
const selectPartialMembers = () => {
  permission.value = 'partial_members'
  // 不关闭下拉菜单

  // 如果没有选择任何成员，默认添加当前用户
  if (selectedMemberIds.value.length === 0 && currentUserId.value) {
    selectedMemberIds.value = [currentUserId.value]
  }
}

// 选择权限
const selectPermission = (permissionValue) => {
  permission.value = permissionValue
  permissionDropdownOpen.value = false

  // 如果不是部分团队成员，清空已选成员
  if (permissionValue !== 'partial_members') {
    selectedMemberIds.value = []
  } else if (selectedMemberIds.value.length === 0 && currentUserId.value) {
    // 如果是部分团队成员且没有选择任何成员，默认添加当前用户
    selectedMemberIds.value = [currentUserId.value]
  }
}

// 根据搜索关键词过滤成员
const filterTeamMembers = () => {
  if (!memberSearchQuery.value) {
    filteredTeamMembers.value = [...teamMembers.value]
    return
  }

  const query = memberSearchQuery.value.toLowerCase()
  filteredTeamMembers.value = teamMembers.value.filter(
    (member) => member.name.toLowerCase().includes(query) || member.email.toLowerCase().includes(query)
  )
}

// 切换成员选择状态
const toggleMemberSelection = (member) => {
  const index = selectedMemberIds.value.indexOf(member.id)
  if (index === -1) {
    selectedMemberIds.value.push(member.id)
  } else if (member.id !== currentUserId.value) {
    // 允许移除非当前用户
    selectedMemberIds.value.splice(index, 1)
  }
}

// 判断成员是否被选中
const isSelectedMember = (memberId) => {
  return selectedMemberIds.value.includes(memberId)
}

// 判断是否是当前用户
const isCurrentUser = (memberId) => {
  return memberId === currentUserId.value
}

// 获取名称首字母
const getInitials = (name) => {
  if (!name) return ''
  return name.charAt(0).toUpperCase()
}

// 保存设置
const saveSettings = async () => {
  if (!datasetInfo.name.trim()) {
    Message.warning('知识库名称不能为空')
    return
  }

  saving.value = true

  try {
    // 构建更新参数
    const params: any = {
      name: datasetInfo.name,
      description: datasetInfo.description,
      permission: permission.value,
      indexing_technique: indexMode.value,
      embedding_model: embeddingModel.value,
      retrieval_model: {
        search_method: retrieval.searchMethod,
        top_k: retrieval.topK,
        score_threshold_enabled: retrieval.scoreThresholdEnabled,
        score_threshold: retrieval.scoreThreshold,
        reranking_enable: retrieval.rerankingEnabled,
        reranking_model: retrieval.rerankingEnabled
          ? {
              reranking_provider_name: 'netease-youdao',
              reranking_model_name: retrieval.rerankingModel
            }
          : null
      }
    }

    // 如果是部分团队成员，添加成员列表
    if (permission.value === 'partial_members') {
      params.partial_member_list = selectedMemberIds.value.map((id) => ({
        user_id: id,
        role: id === currentUserId.value ? 'owner' : 'normal'
      }))
    }

    await updateDatasetSettings(props.datasetId, params)
    Message.success('设置保存成功')
  } catch (error) {
    console.error('保存设置失败:', error)
    Message.error('保存设置失败')
  } finally {
    saving.value = false
  }
}

// 确认对话框相关
const handleConfirm = async () => {
  confirmVisible.value = false
}

const cancelConfirm = () => {
  confirmVisible.value = false
}
</script>

<style scoped lang="scss">
.settings-container {
  max-width: 800px;
  padding: 16px;

  .settings-header {
    margin-bottom: 32px;

    h2 {
      font-size: 20px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .description {
      color: var(--color-text-3);
      font-size: 14px;
    }
  }

  .settings-form {
    .form-section {
      margin-bottom: 32px;
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .form-item {
      margin-bottom: 24px;
      display: flex;
      flex-direction: row;
      &:last-child {
        margin-bottom: 0;
      }
    }

    .form-label {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;
      width: 200px;

      &.required::after {
        content: '*';
        color: var(--color-danger);
        margin-left: 4px;
      }
    }

    .retrieval-settings-note {
      margin-bottom: 16px;
      color: var(--color-text-3);
      font-size: 14px;
    }

    .full-width {
      width: 100%;
    }

    .select-prefix {
      display: flex;
      align-items: center;
      margin-right: 8px;
    }

    .avatar-circle {
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background-color: #f97316;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;

      &.team {
        background-color: #3b82f6;
      }

      &.team-part {
        background-color: #8b5cf6;
      }
    }

    .permission-option {
      display: flex;
      align-items: center;

      .option-icon {
        margin-right: 8px;
      }
    }

    .index-mode-options {
      display: flex;
      gap: 16px;

      .mode-option {
        flex: 1;
        border: 1px solid var(--color-border-2);
        border-radius: 8px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.2s;

        &.active {
          border-color: var(--color-primary);
          background-color: var(--color-primary-light-1);
        }

        .mode-header {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .mode-icon {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;

            &.quality {
              background-color: var(--color-success-light-1);
              color: var(--color-success);
            }

            &.economy {
              background-color: var(--color-warning-light-1);
              color: var(--color-warning);
            }
          }

          .mode-title {
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 8px;
          }
        }

        .mode-description {
          font-size: 13px;
          color: var(--color-text-3);
          line-height: 1.5;
        }
      }
    }

    .recommend-tag {
      color: var(--color-success);
      background-color: var(--color-success-light-1);
      border: none;
    }

    .retrieval-methods {
      .retrieval-method-item {
        display: flex;
        margin-bottom: 24px;
        padding: 16px;
        background-color: var(--color-fill-1);
        border-radius: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .method-icon {
          width: 40px;
          height: 40px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;
          font-size: 20px;

          &.vector {
            background-color: var(--color-primary-light-1);
            color: var(--color-primary);
          }

          &.fulltext {
            background-color: var(--color-warning-light-1);
            color: var(--color-warning);
          }

          &.hybrid {
            background-color: var(--color-success-light-1);
            color: var(--color-success);
          }
        }

        .method-content {
          flex: 1;

          .method-title {
            font-weight: 500;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 8px;
          }

          .method-description {
            font-size: 13px;
            color: var(--color-text-3);
            line-height: 1.5;
            margin-bottom: 16px;
          }

          .method-settings {
            .setting-row {
              display: flex;
              align-items: center;
              margin-bottom: 8px;

              .setting-label {
                font-weight: 500;
                margin-right: 4px;
              }

              .help-icon {
                color: var(--color-text-3);
                font-size: 14px;
                cursor: pointer;
              }
            }

            .setting-select {
              margin-bottom: 16px;
            }

            .setting-slider {
              display: flex;
              align-items: center;
              gap: 16px;
              margin-bottom: 16px;

              .arco-slider {
                flex: 1;
              }
            }
          }
        }
      }
    }
  }

  .form-actions {
    margin-top: 32px;
    display: flex;
    justify-content: center;
  }
}
.setting-card {
  margin-left: 50px;
}
.embedding-model-box {
  margin-left: -50px;
  width: 80%;
}
.retrieval-setting-box {
  margin-left: 20px;
  .setting-card-header {
    display: none;
  }
}

// 权限选择组件样式
.permission-select-container {
  width: 100%;
}

.permission-dropdown {
  position: relative;
  width: 100%;
}

.selected-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border: 1px solid var(--color-border-2);
  border-radius: 4px;
  cursor: pointer;
  background-color: white;

  &:hover {
    border-color: var(--color-primary);
  }

  .selected-option-content {
    display: flex;
    align-items: center;
    gap: 8px;
  }
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  margin-top: 4px;
  background-color: white;
  border: 1px solid var(--color-border-2);
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  cursor: pointer;

  &:hover {
    background-color: var(--color-fill-1);
  }

  &.active {
    background-color: var(--color-primary-light-1);
  }

  span {
    margin-left: 8px;
    flex: 1;
  }

  .check-icon {
    color: var(--color-primary);
  }
}

.member-selection {
  border-top: 1px solid var(--color-border-2);
  max-height: 300px;
  overflow-y: auto;
}

.search-box {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid var(--color-border-2);
  background-color: var(--color-fill-1);

  .search-icon {
    color: var(--color-text-3);
    margin-right: 8px;
  }

  input {
    flex: 1;
    border: none;
    outline: none;
    font-size: 14px;
    background-color: transparent;

    &::placeholder {
      color: var(--color-text-3);
    }
  }
}

.member-list {
  max-height: 250px;
  overflow-y: auto;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  cursor: pointer;
  border-bottom: 1px solid var(--color-border-1);

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: var(--color-fill-1);
  }

  &.selected {
    background-color: var(--color-primary-light-1);
  }

  .member-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    margin-right: 12px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .default-avatar {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--color-fill-3);
      color: var(--color-text-2);
      font-weight: 500;
    }
  }

  .member-info {
    flex: 1;

    .member-name {
      font-size: 14px;
      font-weight: 500;
    }

    .member-email {
      font-size: 12px;
      color: var(--color-text-3);
      margin-top: 2px;
    }
  }

  .member-check {
    color: var(--color-primary);
  }
}
</style>
<style lang="scss">
.index-options {
  display: flex;
  flex-direction: row;
  gap: 12px;
  .index-option {
    display: flex;
    align-items: flex-start;
    padding: 12px;
    border-radius: 6px;
    border: 1px solid var(--color-border-2);
    cursor: pointer;
    transition: all 0.2s;
    flex: 1;
    &.active {
      border-color: var(--color-primary);
      background-color: var(--color-primary-light-1);
    }
    .option-radio {
      margin-right: 8px;
      padding-top: 2px;
    }
    .option-content {
      display: flex;
      flex: 1;
      .option-icon {
        font-size: 16px;
        margin-right: 8px;
        color: var(--color-text-2);
      }
      .option-info {
        flex: 1;
        .option-title {
          font-weight: 500;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          gap: 6px;
        }
        .option-desc {
          font-size: 12px;
          color: var(--color-text-3);
          line-height: 1.5;
        }
      }
    }
  }
}
.setting-card-header {
  display: none;
}
</style>
