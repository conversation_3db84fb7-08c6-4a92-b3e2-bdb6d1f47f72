// types/app.ts
export enum TtsAutoPlay {
  enabled = 'enabled',
  disabled = 'disabled'
}

export enum AgentStrategy {
  functionCall = 'function_call',
  react = 'react'
}

export enum AppType {
  chat = 'chat',
  completion = 'completion'
}

export enum ModelModeType {
  chat = 'chat',
  completion = 'completion',
  unset = ''
}

export enum Resolution {
  low = 'low',
  high = 'high'
}

export enum TransferMethod {
  all = 'all',
  local_file = 'local_file',
  remote_url = 'remote_url'
}

export enum SupportUploadFileTypes {
  image = 'image',
  document = 'document',
  audio = 'audio',
  video = 'video',
  custom = 'custom'
}

export const AppModes = ['advanced-chat', 'agent-chat', 'chat', 'completion', 'workflow'] as const
export type AppMode = (typeof AppModes)[number]

export type TextTypeFormItem = {
  default: string
  label: string
  variable: string
  required: boolean
  max_length: number
}

export type SelectTypeFormItem = {
  default: string
  label: string
  variable: string
  required: boolean
  options: string[]
}

export type ParagraphTypeFormItem = {
  default: string
  label: string
  variable: string
  required: boolean
}

export type UserInputFormItem =
  | { 'text-input': TextTypeFormItem }
  | { select: SelectTypeFormItem }
  | { paragraph: TextTypeFormItem }

export type ToolItem =
  | {
      dataset: {
        enabled: boolean
        id: string
      }
    }
  | {
      'sensitive-word-avoidance': {
        enabled: boolean
        words: string[]
        canned_response: string
      }
    }

export interface CompletionParams {
  max_tokens: number
  temperature: number
  top_p: number
  echo: boolean
  stop: string[]
  presence_penalty: number
  frequency_penalty: number
}

export interface Model {
  provider: string
  name: string
  mode: ModelModeType
  completion_params: CompletionParams
}

export interface VisionSettings {
  enabled: boolean
  number_limits: number
  detail: Resolution
  transfer_methods: TransferMethod[]
  image_file_size_limit?: number | string
}

export interface UploadFileSetting {
  allowed_file_upload_methods: TransferMethod[]
  allowed_file_types: SupportUploadFileTypes[]
  allowed_file_extensions?: string[]
  max_length: number
  number_limits?: number
}

export interface VisionFile {
  id?: string
  type: string
  transfer_method: TransferMethod
  url: string
  upload_file_id: string
  belongs_to?: string
}

export interface ModelConfig {
  opening_statement: string
  suggested_questions?: string[]
  pre_prompt: string
  prompt_type: string // 假设 PromptMode 已定义
  chat_prompt_config: Record<string, any> | object
  completion_prompt_config: Record<string, any> | object
  user_input_form: UserInputFormItem[]
  dataset_query_variable?: string
  more_like_this: {
    enabled?: boolean
  }
  suggested_questions_after_answer: {
    enabled: boolean
  }
  speech_to_text: {
    enabled: boolean
  }
  text_to_speech: {
    enabled: boolean
    voice?: string
    language?: string
    autoPlay?: TtsAutoPlay
  }
  retriever_resource: {
    enabled: boolean
  }
  sensitive_word_avoidance: {
    enabled: boolean
  }
  annotation_reply?: Record<string, any> // 假设 AnnotationReplyConfig 已定义
  agent_mode: {
    enabled: boolean
    strategy?: AgentStrategy
    tools: ToolItem[]
  }
  model: Model
  dataset_configs: Record<string, any> // 假设 DatasetConfigs 已定义
  file_upload?: {
    image: VisionSettings
  } & UploadFileSetting
  files?: VisionFile[]
  created_at?: number
  updated_at?: number
}

export type AppIconType = 'image' | 'emoji'

export interface SiteConfig {
  access_token: string
  title: string
  description: string
  chat_color_theme: string
  chat_color_theme_inverted: boolean
  author: string
  support_email: string
  default_language: string
  customize_domain: string
  theme: string
  customize_token_strategy: 'must' | 'allow' | 'not_allow'
  prompt_public: boolean
  app_base_url: string
  copyright: string
  privacy_policy: string
  custom_disclaimer: string
  icon_type: AppIconType | null
  icon: string
  icon_background: string | null
  icon_url: string | null
  show_workflow_steps: boolean
  use_icon_as_answer_icon: boolean
}

export interface Tag {
  id: string
  name: string
  type: string
  binding_count: number
}

export interface App {
  id: string
  name: string
  description: string
  icon_type: AppIconType | null
  icon: string
  icon_background: string | null
  icon_url: string | null
  use_icon_as_answer_icon: boolean
  mode: AppMode
  enable_site: boolean
  enable_api: boolean
  api_rpm: number
  api_rph: number
  is_demo: boolean
  model_config: ModelConfig
  app_model_config: ModelConfig
  created_at: number
  site: SiteConfig
  api_base_url: string
  tags: Tag[]
  workflow?: {
    id: string
    created_at: number
    created_by?: string
    updated_at: number
    updated_by?: string
  }
}
