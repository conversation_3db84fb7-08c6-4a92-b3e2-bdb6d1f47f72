import { defineStore } from 'pinia'
import {
  filterVar,
  getBeforeNodesInSameBranchIncludeParent,
  toNodeAvailableVars
} from '@/views/app/workflow/utils/variable'
import type { Node } from '@vue-flow/core'

export const useNodesStore = defineStore('nodesStore', () => {
  // workflow中所有的节点
  const nodes = ref([])
  // workflow中所有的edge
  const edges = ref([])
  // 当前点击的节点
  const currentNode = ref<any>({})
  // 当前节点的父节点
  const parentNode = computed(() => {
    const nodes: Node[] = localStorage.getItem('ksNodes') ? JSON.parse(localStorage.getItem('ksNodes') as string) : []
    const node = nodes.find((v) => {
      return v.id == currentNode.value.parentId
    })
    return node ? node : null
  })

  const parentNodes = ref([])

  const setNodes = (list: any) => {
    nodes.value = list
  }
  const setEdges = (list: any) => {
    edges.value = list
  }

  const setCurrentNode = (node: any) => {
    currentNode.value = node
  }
  const setParentNodes = (newVal) => {
    parentNodes.value = newVal
  }

  // 变量List
  const parentNodesVarList = computed(() => {
    let varList: any = []

    const beforeList = getBeforeNodesInSameBranchIncludeParent(currentNode.value.id)
    // const beforeList2 = getBeforeNodesInSameBranch(currentNode.value.id)
    console.log('获取到父元素：', beforeList)

    // const res = toNodeOutputVars(beforeList, false)
    const res = toNodeAvailableVars(parentNode.value, beforeList, false, filterVar, [], [])

    varList = res
      .map((node) => ({
        ...node
        // vars: node.isStartNode ? node.vars.filter(v => !v.variable.startsWith('sys.')) : node.vars,
      }))
      .filter((item) => item.vars.length > 0)
    console.log('获取到所有varList：', varList)
    return varList
  })
  return {
    nodes,
    edges,
    currentNode,
    parentNode,
    parentNodes,
    parentNodesVarList,
    setNodes,
    setEdges,
    setCurrentNode,
    setParentNodes
  }
})
