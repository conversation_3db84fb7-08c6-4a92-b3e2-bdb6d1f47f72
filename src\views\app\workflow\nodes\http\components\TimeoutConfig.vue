<template>
  <!--TODO-todo 组件处理-->
  <Field :fieldTitle="'超时设置'" :tooltip="''">
    <!--超时设置：timeout是有默认值的。直接用就行。-->
    <div class="timeout-config-item">
      <a-space class="mb-[6px]">
        <a-typography-text>连接超时</a-typography-text>
        <a-typography-text type="secondary">输入连接超时（以秒为单位）</a-typography-text>
      </a-space>
      <a-input-number
        v-model="nodeInfo.timeout.connect"
        placeholder="请输入连接超时（以秒为单位）"
        class="input-demo"
      />
    </div>
    <div class="timeout-config-item">
      <a-space class="mb-[6px]">
        <a-typography-text>读取超时</a-typography-text>
        <a-typography-text type="secondary">输入读取超时（以秒为单位）</a-typography-text>
      </a-space>
      <a-input-number v-model="nodeInfo.timeout.read" placeholder="请输入读取超时（以秒为单位）" class="input-demo" />
    </div>
    <div class="timeout-config-item">
      <a-space class="mb-[6px]">
        <a-typography-text>写入超时</a-typography-text>
        <a-typography-text type="secondary">输入写入超时（以秒为单位）</a-typography-text>
      </a-space>
      <a-input-number v-model="nodeInfo.timeout.write" placeholder="请输入写入超时（以秒为单位）" class="input-demo" />
    </div>
  </Field>
</template>
<script setup lang="ts">
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'

const props = defineProps(['nodeInfo'])
</script>
<style scoped lang="scss">
.timeout-config-item {
  margin: 12px 0;
}
</style>
