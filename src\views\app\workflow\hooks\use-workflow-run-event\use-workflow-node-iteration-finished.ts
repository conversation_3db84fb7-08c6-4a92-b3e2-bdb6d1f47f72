import { useVueFlow } from '@vue-flow/core'
import { useWorkflowStore } from '@/stores'
import type { IterationFinishedResponse } from '@/views/app/workflow/types/workflow'

export const useWorkflowNodeIterationFinished = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowNodeIterationFinished = (params: IterationFinishedResponse) => {
    const { data } = params
    const { workflowRunningData, setWorkflowRunningData, setIterTimes } = workflowStore
    const { nodes, setNodes, edges, setEdges } = useVueFlow()
    const workflowData = workflowRunningData
    const currentIndex = workflowRunningData.tracing!.findIndex((item) => item.id === data.id)
    if (currentIndex > -1) {
      workflowData.tracing![currentIndex] = {
        ...workflowData.tracing![currentIndex],
        ...data
      }
    }
    setWorkflowRunningData(workflowData)
    setIterTimes(1)

    if (nodes.value?.length) {
      const newNodes = nodes.value.map((node) => {
        if (node.id === data.node_id) {
          node.data._runningStatus = data.status
        }
        return node
      })

      setNodes(newNodes)
    }

    if (edges.value?.length) {
      const newEdges = edges.value.map((edge) => {
        if (edge.target === data.node_id) {
          edge.data = {
            ...edge.data,
            _targetRunningStatus: data.status
          }
        }
        return edge
      })

      setEdges(newEdges)
    }
  }

  return {
    handleWorkflowNodeIterationFinished
  }
}
