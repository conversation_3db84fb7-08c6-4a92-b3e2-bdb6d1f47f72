<template>
  <!-- 变量选择器弹窗 - 直接渲染到body -->
  <Teleport to="body">
    <div v-if="showPicker" ref="pickerRef" class="variable-picker" :style="pickerStyle" @click.stop>
      <div class="variable-picker-header">
        <span>选择变量</span>
        <a-button type="text" size="mini" @click.stop="closePicker">
          <template #icon>
            <icon-close />
          </template>
        </a-button>
      </div>
      <div class="variable-picker-content">
        <VariableList
          :vars="availableVars"
          :filter-var="filterVar"
          @select="handleVariableSelect"
          @close="closePicker"
        />
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, onUnmounted } from 'vue'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import {
  getBeforeNodesInSameBranchIncludeParent,
  toNodeAvailableVars,
  filterVar as filterVarByType
} from '@/views/app/workflow/utils/variable'
import VariableList from './variable-selector/VariableList.vue'
import { VarType } from '@/views/app/workflow/types/workflow'

interface VariableInserterProps {
  nodeId: string
  varType?: VarType
  filterVar?: (v: any) => boolean
  availableNodes?: any[]
  nodesOutputVars?: any[]
}

const props = withDefaults(defineProps<VariableInserterProps>(), {
  varType: VarType.string,
  filterVar: () => true,
  availableNodes: () => [],
  nodesOutputVars: () => []
})

const emit = defineEmits<{
  (e: 'insert', variableText: string, variable: any): void
}>()

const nodesStore = useNodesStore()
const showPicker = ref(false)
const pickerRef = ref<HTMLElement | null>(null)
const pickerStyle = ref<Record<string, any>>({})
const triggerPosition = ref(0)
const targetTextarea = ref<HTMLTextAreaElement | null>(null)

// 获取可用的节点和变量
const availableVars = computed(() => {
  if (props.availableNodes.length > 0 && props.nodesOutputVars.length > 0) {
    return props.nodesOutputVars
  }

  const beforeNodes = getBeforeNodesInSameBranchIncludeParent(props.nodeId)
  const parentNode = nodesStore.parentNode

  return toNodeAvailableVars(parentNode, beforeNodes, false, props.filterVar || filterVarByType(props.varType), [], [])
})

// 显示变量选择器
const showPickerAt = (textarea: HTMLTextAreaElement, position: number) => {
  targetTextarea.value = textarea
  triggerPosition.value = position
  showPicker.value = true

  nextTick(() => {
    // 添加小延迟确保DOM完全更新
    setTimeout(() => {
      updatePickerPosition()
    }, 10)
  })
}

// 更新选择器位置
const updatePickerPosition = () => {
  if (!targetTextarea.value) {
    return
  }

  try {
    const textarea = targetTextarea.value
    const rect = textarea.getBoundingClientRect()
    // 检查是否在全屏模式下
    const isFullscreen = textarea.closest('.editor-expanded') !== null
    const isModal = textarea.closest('.arco-modal-body') !== null

    let top: number
    let left: number

    // 使用Teleport后，统一使用fixed定位相对于视口
    top = rect.bottom + 8
    left = rect.left

    // 边界检测
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const pickerWidth = 300
    const pickerHeight = 350

    // 水平边界检测
    if (left + pickerWidth > viewportWidth) {
      left = viewportWidth - pickerWidth - 20
    }
    if (left < 10) {
      left = 10
    }

    // 垂直边界检测
    if (top + pickerHeight > viewportHeight) {
      top = rect.top - pickerHeight - 8
    }
    if (top < 10) {
      top = 10
    }

    const finalTop = Math.max(10, top)
    const finalLeft = Math.max(10, left)

    pickerStyle.value = {
      position: 'fixed',
      top: `${finalTop}px`,
      left: `${finalLeft}px`,
      zIndex: 99999
    }
  } catch (error) {
    console.error('更新选择器位置时出错:', error)
    // 后备位置
    pickerStyle.value = {
      position: 'fixed',
      top: '100px',
      left: '100px',
      zIndex: 99999
    }
  }
}

// 关闭选择器
const closePicker = () => {
  showPicker.value = false
  targetTextarea.value = null
}

// 处理变量选择
const handleVariableSelect = (_group: any, variable: any) => {
  // 恢复默认的变量格式：{{nodeId.variable}}
  let variableText = ''

  // 检查是否是系统变量
  const isSystemVar =
    variable.variable.startsWith('sys.') ||
    variable.variable.startsWith('env.') ||
    variable.variable.startsWith('conversation.')

  if (isSystemVar) {
    // 系统变量格式：{{sys.userid}}
    variableText = `{{${variable.variable}}}`
  } else {
    // 普通变量格式：{{nodeId.variable}}
    variableText = `{{${variable.nodeId}.${variable.variable}}}`
  }

  emit('insert', variableText, variable)
  closePicker()
}

// 点击外部关闭
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (showPicker.value && pickerRef.value && !pickerRef.value.contains(target)) {
    closePicker()
  }
}

// 暴露方法给父组件
defineExpose({
  showPickerAt,
  closePicker
})

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style lang="scss">
.variable-picker {
  background: #ffffff;
  border: 1px solid var(--color-border-2);
  border-radius: 6px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  max-height: 350px;
  overflow: hidden;
  width: 300px;

  .variable-picker-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: var(--color-fill-2);
    border-bottom: 1px solid var(--color-border-2);
    font-size: 12px;
    font-weight: 600;
    color: var(--color-text-2);
  }

  .variable-picker-content {
    max-height: 300px;
    overflow-y: auto;
  }
}
</style>
