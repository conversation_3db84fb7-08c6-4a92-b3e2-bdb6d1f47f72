import http from '@/utils/http'
import type * as DataContracts from './type'

// evalDataset
/**
 * @name SaveUsingPost
 * @summary 新增
 * @request POST:/evalDataset
 */
export function saveUsingPost(params: DataContracts.EvalDatasetSaveDTO) {
  const url = `/api/evalDataset`

  return http.post<DataContracts.REvalDataset>(url, params)
}

/**
 * @name UpdateUsingPut
 * @summary 修改
 * @request PUT:/evalDataset
 */
export function updateUsingPut(params: DataContracts.EvalDatasetUpdateDTO) {
  const url = `/api/evalDataset`

  return http.put<DataContracts.REvalDataset>(url, params)
}

/**
 * @name DeleteUsingDelete
 * @summary 删除
 * @request DELETE:/evalDataset
 */
export function deleteUsingDelete(params: number[]) {
  const url = `/api/evalDataset`

  return http.del<DataContracts.RBoolean>(url, params)
}

/**
 * @name UpdateAllUsingPut
 * @summary 修改所有字段
 * @request PUT:/evalDataset/all
 */
export function updateAllUsingPut(params: DataContracts.EvalDataset) {
  const url = `/api/evalDataset/all`

  return http.put<DataContracts.REvalDataset>(url, params)
}

/**
 * @name PageUsingPost
 * @summary 分页列表查询
 * @request POST:/evalDataset/page
 */
export function pageUsingPost(params: DataContracts.PageParamsEvalDatasetPageQuery) {
  const url = `/api/evalDataset/page`

  return http.post<DataContracts.RIPageEvalDataset>(url, params)
}

/**
 * @name QueryUsingPost
 * @summary 批量查询
 * @request POST:/evalDataset/query
 */
export function queryUsingPost(params: DataContracts.EvalDataset) {
  const url = `/api/evalDataset/query`

  return http.post<DataContracts.RListEvalDataset>(url, params)
}

/**
 * @name GetUsingGet
 * @summary 单体查询
 * @request GET:/evalDataset/{id}
 */
export function getUsingGet(id: string) {
  const url = `/api/evalDataset/${id}`

  return http.get<DataContracts.REvalDataset>(url)
}
