import type { IOtherOptions } from '@/apis/workflow/type'
import { useWorkflowRunEvent } from '../hooks/use-workflow-run-event/use-workflow-run-event'
import { useWorkflowStore } from '@/stores'
import { WorkflowRunningStatus } from '@/views/app/workflow/types/workflow'
import { workflowRunFetch } from '@/apis/workflow'
const {
  handleWorkflowStarted,
  handleWorkflowFinished,
  handleWorkflowFailed,
  handleWorkflowNodeStarted,
  handleWorkflowNodeFinished,
  handleWorkflowNodeIterationStarted,
  handleWorkflowNodeIterationNext,
  handleWorkflowNodeIterationFinished,
  handleWorkflowNodeRetry,
  handleWorkflowAgentLog,
  handleWorkflowTextChunk,
  handleWorkflowTextReplace
} = useWorkflowRunEvent()

export const handleRun = async (appId: string, params: any, callback?: IOtherOptions) => {
  const workflowStore = useWorkflowStore()

  const { workflowRunningData, setHistoryWorkflowData, setWorkflowRunningData } = workflowStore
  const {
    onWorkflowStarted,
    onWorkflowFinished,
    onNodeStarted,
    onNodeFinished,
    onIterationStart,
    onIterationNext,
    onIterationFinish,
    onLoopStart,
    onLoopNext,
    onLoopFinish,
    onNodeRetry,
    onAgentLog,
    onError,
    ...restCallback
  } = callback || {}

  setHistoryWorkflowData({ historyWorkflowData: undefined })

  await setWorkflowRunningData({
    result: {
      status: WorkflowRunningStatus.Running
    },
    tracing: [],
    resultText: ''
  })
  // console.log(workflowRunningData,111)
  workflowRunFetch(
    appId,
    {
      body: params
    },
    {
      onWorkflowStarted: (params) => {
        handleWorkflowStarted(params)
        console.log(params, 'onWorkflowStarted')

        if (onWorkflowStarted) onWorkflowStarted(params)
      },
      onWorkflowFinished: (params) => {
        handleWorkflowFinished(params)
        console.log(params, 'WorkflowFinished')
        if (onWorkflowFinished) onWorkflowFinished(params)
      },
      onError: (params) => {
        handleWorkflowFailed()
        console.log(params, 'onError')

        if (onError) onError(params)
      },
      onNodeStarted: (params) => {
        handleWorkflowNodeStarted(params)
        console.log(params, 'onNodeStarted')

        if (onNodeStarted) onNodeStarted(params)
      },
      onNodeFinished: (params) => {
        handleWorkflowNodeFinished(params)

        if (onNodeFinished) onNodeFinished(params)
      },
      onIterationStart: (params) => {
        handleWorkflowNodeIterationStarted(params)

        if (onIterationStart) onIterationStart(params)
      },
      onIterationNext: (params) => {
        handleWorkflowNodeIterationNext(params)

        if (onIterationNext) onIterationNext(params)
      },
      onIterationFinish: (params) => {
        handleWorkflowNodeIterationFinished(params)

        if (onIterationFinish) onIterationFinish(params)
      },
      onNodeRetry: (params) => {
        handleWorkflowNodeRetry(params)

        if (onNodeRetry) onNodeRetry(params)
      },
      onAgentLog: (params) => {
        handleWorkflowAgentLog(params)

        if (onAgentLog) onAgentLog(params)
      },
      onTextChunk: (params) => {
        handleWorkflowTextChunk(params)
      },
      onTextReplace: (params) => {
        handleWorkflowTextReplace(params)
      },
      ...restCallback
    }
  )
}
