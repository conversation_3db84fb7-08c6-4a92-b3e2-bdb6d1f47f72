<template>
  <div class="field-info">
    <div class="field-info__item">
      <span>节点信息</span>
    </div>
    <div class="field-info__item">
      <span>节点信息</span>
    </div>
    <div class="field-info__item">
      <span>节点信息</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
const props = defineProps({
  popupContainer: String
})
</script>
<style scoped lang="scss"></style>
