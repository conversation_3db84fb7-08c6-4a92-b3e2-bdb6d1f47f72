<template>
  <div class="setting-card">
    <div class="setting-card-header">
      <div class="setting-title">Embedding 模型</div>
    </div>
    <div class="form-item">
      <a-select
        :model-value="embeddingModel"
        placeholder="请选择Embedding模型"
        class="model-select"
        @update:model-value="handleModelChange"
      >
        <a-option v-for="option in embeddingModelOptions" :key="option.value" :value="option.value">
          {{ option.label }}
        </a-option>
      </a-select>
    </div>
  </div>
</template>
<script setup lang="ts">
interface ModelOption {
  value: string
  label: string
}

// 接收的props
const props = defineProps({
  embeddingModel: {
    type: String,
    default: ''
  },
  embeddingModelOptions: {
    type: Array as () => ModelOption[],
    default: () => []
  }
})

// 定义事件
const emit = defineEmits(['embedding-model-change'])

// 处理模型变更
const handleModelChange = (value: string) => {
  emit('embedding-model-change', value)
}
</script>
