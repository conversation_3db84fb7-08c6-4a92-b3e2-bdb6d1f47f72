<template>
  <div class="knowledge-retrieval-config">
    <!-- 查询变量选择 -->
    <div class="config-field">
      <div class="field-header">
        <label class="field-label">查询变量</label>
      </div>
      <div class="field-content">
        <VariableSelector
          :node-id="nodeId"
          :value-selector="formData.query_variable_selector"
          placeholder="选择查询变量"
          @change="handleQueryVariableChange"
        />
      </div>
    </div>

    <!-- 知识库选择 -->
    <div class="config-field">
      <div class="field-header">
        <label class="field-label">知识库</label>
        <div class="field-operations">
          <a-button
            v-if="selectedDatasets.length > 0"
            type="text"
            size="small"
            @click="showRetrievalConfigModal = true"
          >
            <icon-settings />
            召回设置
          </a-button>
          <a-button type="text" size="small" @click="showAddDatasetModal = true">
            <icon-plus />
            添加
          </a-button>
        </div>
      </div>
      <div class="field-content">
        <!-- 已选择的知识库列表 -->
        <div v-if="selectedDatasets.length > 0" class="dataset-list">
          <div v-for="dataset in selectedDatasets" :key="dataset.id" class="dataset-item">
            <div class="dataset-icon">
              <icon-book />
            </div>
            <div class="dataset-info">
              <div class="dataset-name">{{ dataset.name }}</div>
            </div>
            <a-button type="text" size="small" @click="removeDataset(dataset.id)">
              <icon-close />
            </a-button>
          </div>
        </div>
        <div v-else class="empty-datasets">
          <div class="empty-text">请选择知识库</div>
        </div>
      </div>
    </div>

    <!-- 元数据过滤 -->
    <div v-if="selectedDatasets.length > 0 && availableMetadata.length > 0" class="config-field">
      <div class="field-header">
        <label class="field-label">元数据过滤</label>
        <a-button
          v-if="formData.metadata_filtering_mode !== MetadataFilteringModeEnum.disabled"
          type="text"
          size="small"
          @click="showMetadataConfigModal = true"
        >
          <icon-settings />
          配置
        </a-button>
      </div>
      <div class="field-content">
        <a-radio-group v-model="formData.metadata_filtering_mode" @change="handleMetadataFilterModeChange">
          <a-radio :value="MetadataFilteringModeEnum.disabled">禁用</a-radio>
          <a-radio :value="MetadataFilteringModeEnum.automatic">自动</a-radio>
          <a-radio :value="MetadataFilteringModeEnum.manual">手动</a-radio>
        </a-radio-group>
      </div>
    </div>

    <!-- 输出变量 -->
    <div class="config-field">
      <div class="field-header">
        <label class="field-label">输出变量</label>
      </div>
      <div class="field-content">
        <div class="output-vars">
          <div class="var-item main-var">
            <span class="var-name">result</span>
            <span class="var-type">Array[Object]</span>
            <span class="var-desc">检索结果列表</span>
          </div>
          <div class="var-item sub-var">
            <span class="var-name">├─ content</span>
            <span class="var-type">string</span>
            <span class="var-desc">文档内容</span>
          </div>
          <div class="var-item sub-var">
            <span class="var-name">├─ title</span>
            <span class="var-type">string</span>
            <span class="var-desc">文档标题</span>
          </div>
          <div class="var-item sub-var">
            <span class="var-name">├─ url</span>
            <span class="var-type">string</span>
            <span class="var-desc">文档链接</span>
          </div>
          <div class="var-item sub-var">
            <span class="var-name">├─ icon</span>
            <span class="var-type">string</span>
            <span class="var-desc">文档图标</span>
          </div>
          <div class="var-item sub-var">
            <span class="var-name">└─ metadata</span>
            <span class="var-type">object</span>
            <span class="var-desc">元数据信息</span>
          </div>
          <div class="var-item main-var">
            <span class="var-name">content</span>
            <span class="var-type">string</span>
            <span class="var-desc">合并后的文本内容</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加知识库弹窗 -->
    <a-modal
      v-model:visible="showAddDatasetModal"
      title="选择知识库"
      width="600px"
      @ok="handleAddDataset"
      @cancel="showAddDatasetModal = false"
    >
      <DatasetSelector v-model="selectedNewDatasets" :excluded-ids="formData.dataset_ids" />
    </a-modal>

    <!-- 召回设置弹窗 -->
    <a-modal
      v-model:visible="showRetrievalConfigModal"
      title="召回设置"
      width="500px"
      @ok="handleRetrievalConfigSave"
      @cancel="showRetrievalConfigModal = false"
    >
      <RetrievalConfigPanel v-model="retrievalConfig" :selected-datasets="selectedDatasets" />
    </a-modal>

    <!-- 元数据配置弹窗 -->
    <a-modal
      v-model:visible="showMetadataConfigModal"
      title="元数据过滤配置"
      width="600px"
      @ok="handleMetadataConfigSave"
      @cancel="showMetadataConfigModal = false"
    >
      <MetadataConfigPanel v-model="metadataConfig" :metadata-list="availableMetadata" :node-id="nodeId" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import VariableSelector from '@/views/app/workflow/components/variable-selector/VariableSelector.vue'
import DatasetSelector from './components/DatasetSelector.vue'
import RetrievalConfigPanel from './components/RetrievalConfigPanel.vue'
import MetadataConfigPanel from './components/MetadataConfigPanel.vue'
import { RetrievalMode, MetadataFilteringModeEnum } from '@/views/app/workflow/types/node'
import { getDatasetList } from '@/apis'
import type { Dataset } from '@/apis/datasets/type'

const props = withDefaults(
  defineProps<{
    nodeInfo: any
    nodeId: string
  }>(),
  {
    nodeInfo: {},
    nodeId: ''
  }
)

const emit = defineEmits<{
  (e: 'update:nodeInfo', value: any): void
  (e: 'change', value: any): void
}>()

// 表单数据
const formData = ref({
  query_variable_selector: [],
  dataset_ids: [],
  retrieval_mode: RetrievalMode.多路召回,
  multiple_retrieval_config: {
    top_k: 3,
    score_threshold: 0.5,
    reranking_enable: false,
    reranking_model: {
      provider: '',
      model: ''
    }
  },
  single_retrieval_config: {
    model: {
      provider: '',
      name: '',
      mode: 'chat',
      completion_params: {}
    }
  },
  metadata_filtering_mode: MetadataFilteringModeEnum.disabled,
  metadata_filtering_conditions: [],
  metadata_model_config: null
})

// 弹窗状态
const showAddDatasetModal = ref(false)
const showRetrievalConfigModal = ref(false)
const showMetadataConfigModal = ref(false)

// 数据状态
const selectedNewDatasets = ref([])
const selectedDatasets = ref<Dataset[]>([])
const availableMetadata = ref([])

// 配置数据
const retrievalConfig = ref({
  retrieval_mode: RetrievalMode.多路召回,
  multiple_retrieval_config: {
    top_k: 3,
    score_threshold: 0.5,
    reranking_enable: false,
    reranking_model: {
      provider: '',
      model: ''
    }
  },
  single_retrieval_config: {
    model: {
      provider: '',
      name: '',
      mode: 'chat',
      completion_params: {}
    }
  }
})

const metadataConfig = ref({
  metadata_filtering_mode: MetadataFilteringModeEnum.disabled,
  metadata_filtering_conditions: [],
  metadata_model_config: null
})

// 初始化数据
onMounted(async () => {
  // 从 nodeInfo 初始化表单数据
  if (props.nodeInfo) {
    Object.assign(formData.value, props.nodeInfo)
    Object.assign(retrievalConfig.value, {
      retrieval_mode: props.nodeInfo.retrieval_mode || RetrievalMode.多路召回,
      multiple_retrieval_config:
        props.nodeInfo.multiple_retrieval_config || retrievalConfig.value.multiple_retrieval_config,
      single_retrieval_config: props.nodeInfo.single_retrieval_config || retrievalConfig.value.single_retrieval_config
    })
    Object.assign(metadataConfig.value, {
      metadata_filtering_mode: props.nodeInfo.metadata_filtering_mode || MetadataFilteringModeEnum.disabled,
      metadata_filtering_conditions: props.nodeInfo.metadata_filtering_conditions || [],
      metadata_model_config: props.nodeInfo.metadata_model_config || null
    })
    await loadSelectedDatasets()
  }
})

// 加载已选择的知识库详情
const loadSelectedDatasets = async () => {
  if (formData.value.dataset_ids.length === 0) return

  try {
    const response = await getDatasetList({
      page: 1,
      ids: formData.value.dataset_ids
    })
    selectedDatasets.value = response.data

    // 提取元数据信息
    extractMetadata()
  } catch (error) {
    Message.error('加载知识库详情失败')
  }
}

// 提取元数据信息
const extractMetadata = () => {
  const metadataSet = new Set()
  selectedDatasets.value.forEach((dataset) => {
    if (dataset.doc_metadata) {
      dataset.doc_metadata.forEach((meta) => {
        metadataSet.add(JSON.stringify(meta))
      })
    }
  })

  availableMetadata.value = Array.from(metadataSet).map((meta) => JSON.parse(JSON.stringify(meta)))
}

// 事件处理函数
const handleQueryVariableChange = (valueSelector: string[], varItem: any) => {
  formData.value.query_variable_selector = valueSelector
}

const removeDataset = (datasetId: string) => {
  formData.value.dataset_ids = formData.value.dataset_ids.filter((id) => id !== datasetId)
  selectedDatasets.value = selectedDatasets.value.filter((dataset) => dataset.id !== datasetId)
  extractMetadata()
}

const handleMetadataFilterModeChange = () => {
  metadataConfig.value.metadata_filtering_mode = formData.value.metadata_filtering_mode
  if (formData.value.metadata_filtering_mode === MetadataFilteringModeEnum.disabled) {
    formData.value.metadata_filtering_conditions = []
    formData.value.metadata_model_config = null
    metadataConfig.value.metadata_filtering_conditions = []
    metadataConfig.value.metadata_model_config = null
  }
}

const handleAddDataset = async () => {
  // 添加新选择的知识库ID
  const newDatasetIds = selectedNewDatasets.value.filter((id) => !formData.value.dataset_ids.includes(id))
  formData.value.dataset_ids.push(...newDatasetIds)

  // 重新加载已选择的知识库详情
  await loadSelectedDatasets()

  // 关闭弹窗并清空选择
  showAddDatasetModal.value = false
  selectedNewDatasets.value = []
}

const handleRetrievalConfigSave = () => {
  // 将召回配置同步到表单数据
  formData.value.retrieval_mode = retrievalConfig.value.retrieval_mode
  formData.value.multiple_retrieval_config = { ...retrievalConfig.value.multiple_retrieval_config }
  formData.value.single_retrieval_config = { ...retrievalConfig.value.single_retrieval_config }

  showRetrievalConfigModal.value = false
}

const handleMetadataConfigSave = () => {
  // 将元数据配置同步到表单数据
  formData.value.metadata_filtering_mode = metadataConfig.value.metadata_filtering_mode
  formData.value.metadata_filtering_conditions = [...metadataConfig.value.metadata_filtering_conditions]
  formData.value.metadata_model_config = metadataConfig.value.metadata_model_config

  showMetadataConfigModal.value = false
}

// emitChange函数已移除，现在使用watch自动监听

// 监听 nodeInfo 变化
watch(
  () => props.nodeInfo,
  (newValue) => {
    if (newValue && JSON.stringify(newValue) !== JSON.stringify(formData.value)) {
      Object.assign(formData.value, newValue)
      // 同步到配置数据
      Object.assign(retrievalConfig.value, {
        retrieval_mode: newValue.retrieval_mode || RetrievalMode.多路召回,
        multiple_retrieval_config:
          newValue.multiple_retrieval_config || retrievalConfig.value.multiple_retrieval_config,
        single_retrieval_config: newValue.single_retrieval_config || retrievalConfig.value.single_retrieval_config
      })
      Object.assign(metadataConfig.value, {
        metadata_filtering_mode: newValue.metadata_filtering_mode || MetadataFilteringModeEnum.disabled,
        metadata_filtering_conditions: newValue.metadata_filtering_conditions || [],
        metadata_model_config: newValue.metadata_model_config || null
      })
      loadSelectedDatasets()
    }
  },
  { deep: true, immediate: true }
)

// 监听表单数据变化，确保能触发保存
watch(
  formData,
  (newValue) => {
    emit('update:nodeInfo', { ...newValue })
    emit('change', { ...newValue })
  },
  { deep: true }
)

// 移除不再使用的接口定义
</script>

<style scoped lang="scss">
.knowledge-retrieval-config {
  padding: 16px;
  background-color: var(--color-bg-1);

  .config-field {
    margin-bottom: 20px;

    .field-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;

      .field-label {
        font-size: 14px;
        font-weight: 500;
        color: var(--color-text-1);
      }

      .field-operations {
        display: flex;
        align-items: center;
        gap: 8px;

        .arco-btn {
          display: flex;
          align-items: center;
          gap: 4px;
          font-size: 12px;

          .arco-icon {
            font-size: 14px;
          }
        }
      }
    }

    .field-content {
      .dataset-list {
        border: 1px solid var(--color-border-2);
        border-radius: 6px;
        overflow: hidden;

        .dataset-item {
          display: flex;
          align-items: center;
          padding: 12px;
          border-bottom: 1px solid var(--color-border-2);
          background-color: var(--color-bg-1);
          transition: background-color 0.2s;

          &:last-child {
            border-bottom: none;
          }

          &:hover {
            background-color: var(--color-bg-2);
          }

          .dataset-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            background-color: var(--color-primary-light-1);
            border-radius: 6px;
            margin-right: 12px;

            .arco-icon {
              color: var(--color-primary);
              font-size: 16px;
            }
          }

          .dataset-info {
            flex: 1;

            .dataset-name {
              font-size: 14px;
              font-weight: 500;
              color: var(--color-text-1);
            }
          }

          .arco-btn {
            opacity: 0.6;
            transition: opacity 0.2s;

            &:hover {
              opacity: 1;
            }
          }
        }
      }

      .empty-datasets {
        padding: 24px;
        text-align: center;
        border: 1px dashed var(--color-border-2);
        border-radius: 6px;
        background-color: var(--color-bg-2);

        .empty-text {
          font-size: 13px;
          color: var(--color-text-3);
        }
      }

      .output-vars {
        .var-item {
          display: flex;
          align-items: center;
          padding: 4px 8px;
          margin-bottom: 2px;

          &.main-var {
            margin-bottom: 4px;

            .var-name {
              font-weight: 600;
            }
          }

          &.sub-var {
            margin-left: 16px;
            margin-bottom: 1px;

            .var-name {
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              color: var(--color-text-3);
            }
          }

          .var-name {
            font-size: 13px;
            font-weight: 500;
            color: var(--color-text-1);
            min-width: 120px;
          }

          .var-type {
            font-size: 11px;
            color: var(--color-text-3);
            background-color: var(--color-bg-3);
            padding: 2px 6px;
            border-radius: 3px;
            margin: 0 8px;
          }

          .var-desc {
            font-size: 12px;
            color: var(--color-text-2);
            flex: 1;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .knowledge-retrieval-config {
    padding: 12px;

    .config-field {
      margin-bottom: 16px;

      .field-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;

        .field-operations {
          width: 100%;
          justify-content: flex-end;
        }
      }
    }
  }
}
</style>
