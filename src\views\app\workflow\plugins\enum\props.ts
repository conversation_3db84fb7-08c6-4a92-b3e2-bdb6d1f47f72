import { Type } from '@/views/app/workflow/utils/common'

import type {
  ATTRIBUTE,
  TRANSFORM_ENUM_DATA,
  TRANSFORM_ENUM_DATA_LIST,
  TRANSFORM_ENUM_ELEMENT,
  TRANSFORM_ENUM_ELEMENTS
} from './types'
/**
 * 代理规则
 */
export const PROXY_RULE: ProxyHandler<TRANSFORM_ENUM_DATA_LIST | TRANSFORM_ENUM_DATA | TRANSFORM_ENUM_ELEMENT> = {
  get(target: TRANSFORM_ENUM_DATA_LIST | TRANSFORM_ENUM_DATA | TRANSFORM_ENUM_ELEMENT, key: any) {
    if (!Type.isObject(target) || !key) return ''
    if (typeof key !== 'string') return target[key]
    const isCustomKey = Object.keys(target).includes(key)
    const defaultResult = target[key]
    let result
    switch (target._level) {
      case 0:
        result = defaultResult || {}
        !isCustomKey &&
          !['toJSON', 'value', 'toString'].includes(key) &&
          !key.startsWith('_') &&
          !Reflect.has(Object.prototype, key) &&
          !Reflect.has(Array.prototype, key) &&
          !Reflect.has(Number.prototype, key) &&
          !Reflect.has(String.prototype, key) &&
          !Reflect.has(Boolean.prototype, key) &&
          !Reflect.has(Date.prototype, key) &&
          console.error(`$enum 错误: 该系统枚举字典中不存在枚举类型 ${key}`)
        break
      case 1: {
        const TARGET: TRANSFORM_ENUM_DATA = target
        const $ATTRIBUTE = TARGET.$ATTRIBUTE as ATTRIBUTE
        let isAttributeKey = Type.isMap(TARGET.$ATTRIBUTE) && $ATTRIBUTE.has(key)
        if (key.startsWith('$')) {
          if (key.startsWith('$LIST')) {
            const trueKey = key.startsWith('$LIST_') ? key.replace('$LIST_', '') : '$LIST'
            if (!Array.isArray(TARGET.$LIST))
              // TARGET.$LIST = Array.from($ATTRIBUTE).map((item) => deepCopy(item[1]));
              result = TARGET.$LIST
            if (trueKey !== '$LIST') {
              isAttributeKey =
                Array.isArray(TARGET.$LIST) && TARGET.$LIST.length > 0 && Reflect.has(TARGET.$LIST[0], trueKey)
              // result = TARGET.$LIST.map((item) => item[trueKey]);
            }
          } else if (key.startsWith('$MATCH')) {
            const trueKey = key.startsWith('$MATCH_') ? key.replace('$MATCH_', '') : 'name'
            !Array.isArray(TARGET.$LIST)
            // (TARGET.$LIST = Array.from($ATTRIBUTE).map((item) => deepCopy(item[1])));
            isAttributeKey =
              Array.isArray(TARGET.$LIST) && TARGET.$LIST.length > 0 && Reflect.has(TARGET.$LIST[0], trueKey)
            result = (value, key = 'value') => {
              if ([null, undefined].includes(value)) return '--'
              const RESULT_OBJ = (TARGET.$LIST as TRANSFORM_ENUM_ELEMENTS).find((item) => value === item[key])
              return RESULT_OBJ ? RESULT_OBJ[trueKey] : '--'
            }
          } else if (key === '$RULE') {
            result = TARGET.$RULE
          } else if (!['$LIST', '$RULE', '$NAME', '$ATTRIBUTE', '$MATCH'].includes(key)) {
            const trueKey = key.substr(1)
            const trueTarget = $ATTRIBUTE.get(trueKey)
            isAttributeKey = !!trueTarget
            isAttributeKey && (result = trueTarget)
          }
        } else {
          if (isAttributeKey) result = defaultResult || ($ATTRIBUTE.get(key) as TRANSFORM_ENUM_ELEMENT).value
        }
        !isCustomKey && !isAttributeKey && console.error(`$enum 错误: ${TARGET.$NAME} 中不存在枚举值 ${key}`)
        break
      }
      case 2: {
        const ESSENTIAL_ATTRIBUTE = ['toJSON', 'length', 'constructor']
        const TARGET: TRANSFORM_ENUM_ELEMENT = target
        isCustomKey && (result = TARGET[key])
        !isCustomKey &&
          !result &&
          !ESSENTIAL_ATTRIBUTE.includes(key) &&
          console.error(`$enum 错误: ${TARGET.$NAME} 中不存在属性值 '${key}'`)
        break
      }
    }
    return result
  },
  set(target, key, newValue, receiver) {
    switch (target._level) {
      case 0: {
        if (Reflect.has(target, key) && (!Type.isObject(newValue) || !newValue.isUpdate)) {
          console.error('禁止写入')
          return false
        }
        Reflect.set(target, key, newValue, receiver)
        return true
      }

      default: {
        console.error('禁止写入')
        return false
      }
    }
  }
}
