import type { App } from '@/views/app/workflow/types/app'
import type { AppBasicInfo } from '@/views/app/workflow/types/explore'

/** 应用响应类型 */
export interface AppListResponse {
  data: App[]
  has_more: boolean
  limit: number
  page: number
  total: number
}
/** 应用详情类型 */
// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export interface AppDetailResponse extends App {}

export interface createAppType {
  name: string
  icon_type: string
  icon: string
  icon_background?: string | null
  mode: string
  description?: string
}
/** 复制请求参数 */
export interface CopyAppResp {
  id: string
  name: string
  icon_type: string
  icon: string
  icon_background?: string | null
  mode: string
  description?: string
}
/** 导出 */
export interface ExportApp {
  data: string
}

export type InstalledApp = {
  app: AppBasicInfo
  id: string
  uninstallable: boolean
  is_pinned: boolean
}
/** 标签 */
export interface TagsResponse {
  binding_count: string
  id: string
  name: string
  type: string
}
export interface AppSite {
  access_token: string
  app_base_url: string
  code: string
  copyright: any
  custom_disclaimer: any
  customize_domain: any
  customize_token_strategy: string
  default_language: string
  description: string
  icon: string
  icon_background: string
  privacy_policy: any
  prompt_public: boolean
  title: string
}

/** 应用配置详情 */
export interface AppConfigResponse {
  api_base_url: string
  created_at: number
  deleted_tools: any[]
  description: string
  enable_api: boolean
  enable_site: boolean
  icon: string
  icon_background: string
  id: string
  mode: string
  model_config?: any
  name: string
  site?: AppSite
  app_id: string
  installed_id: string
  modify_status: boolean
  draft_updated_at?: number
}

export interface ApiKey {
  id: string
  token: string
  last_used_at: string
  created_at: string
  type: string
  is_active: boolean
}
export interface ApiKeyResponse {
  data: ApiKey[]
}
// API密钥创建响应
export interface CreateApiKeyResponse {
  id: string
  token: string
  created_at: string
}

// 日志相关类型定义
export interface LogEntry {
  id: string
  conversation_id: string
  message_id: string
  user_id: string
  created_at: string
  status: LogStatus
  elapsed_time: number
  total_tokens: number
  input: Record<string, any>
  output: Record<string, any>
  error?: string
  feedback?: LogFeedback
  from_end_user_session_id?: string
  from_account_name?: string
  message_count?: number
  name?: string
  read_at?: string
}

export interface LogListResponse {
  data: LogEntry[]
  total: number
  page: number
  limit: number
  has_more: boolean
}

export interface LogDetailResponse {
  data: LogEntry
}

export interface LogQueryParams {
  page?: number
  limit?: number
  keyword?: string
  created_at__after?: string // 开始时间
  created_at__before?: string // 结束时间
  status?: LogStatus | 'all'
  user_id?: string
  period?: string
  annotation_status?: string
  // sort_by?: string
}

export interface ChatMessageParams {
  conversation_id: string
  limit?: number
  first_id?: string
}

export interface ChatMessageResponse {
  data: any[]
  total?: number
  has_more?: boolean
}

export interface LogFeedback {
  rating: 'like' | 'dislike'
  content?: string
}

export type LogStatus = 'success' | 'error' | 'stopped' | 'running' | 'succeeded' | 'failed'

// 应用模式枚举
export enum AppMode {
  ADVANCED_CHAT = 'advanced-chat', // 对话流
  WORKFLOW = 'workflow', // 工作流
  AGENT_CHAT = 'agent-chat', // 智能会话
  COMPLETION = 'completion' // 未知
}
