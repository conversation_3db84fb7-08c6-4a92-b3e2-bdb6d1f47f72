import { useWorkflowStore } from '@/stores'
import type { TextChunkResponse } from '@/views/app/workflow/types/workflow'

export const useWorkflowTextChunk = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowTextChunk = (params: TextChunkResponse) => {
    const {
      data: { text }
    } = params
    const { workflowRunningData, setWorkflowRunningData } = workflowStore

    const workflowData = workflowRunningData

    workflowData.resultTabActive = true
    workflowData.resultText += text

    setWorkflowRunningData(workflowData)
  }

  return {
    handleWorkflowTextChunk
  }
}
