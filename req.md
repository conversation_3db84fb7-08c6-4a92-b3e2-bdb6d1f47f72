# 评测管理模块产品需求文档

## 1. 产品概述

### 1.1 产品背景

   评测管理模块旨在建立标准化的输入数据集和理想的输出数据集，通过自动化评测和人工评测两种方式，对被评测的智能体应用的生成结果进行评测，从客观和主观两类评测指标来综
合评估智能体应用的生成效果、评测结果比对分析，从而有助于智能体应用研发人员快速了解生成效果和改进方向、帮助智能体应用实施交付人员与最终用户之间遵从统一的验收标准。
   智能体应用分为对话类和工作流类两种，对话类应用的输入主要是prompt，工作流类应用的输入有多种文档格式以及多个文档，输出也存在不同文档格式，如markdown、excel、
word等，输出的成果物数量一般只有一个。

### 1.2 产品目标

1. 标准化评测流程，确保结果一致性  
2. 支持自动化/人工双评测模式  
3. 提供多维度结果对比分析  
4. 建立统一验收标准，提升交付效率  
5. 可视化管理评测全流程  

### 1.3 用户画像

- **研发人员**：分析应用表现，驱动迭代  
- **交付人员**：提供验收依据  
- **评测人员**：高效执行评测任务  
- **项目管理者**：监控开发质量与进度  
- **最终用户代表**：参与验收确认  

## 2. 功能模块设计

评测管理模块包含三个主要功能模块：
1.评测集管理
2.评测任务管理
3.评测报告管理

### 2.1 评测集管理

#### 2.1.1 评测集创建

**功能描述**：创建标准化评测数据集，支持两类应用类型  

**功能细节**：  

1. **基本信息**
   - 必填：名称、应用类型（对话类/工作流类）、根据类型上传不同文件（以下说明）
   - 选填：描述、标签  
   - 系统自动记录：创建人、时间  

2. **对话类评测集**
   - 仅支持Excel格式，提供标准模板（含SessionID/queryID/Input/Output字段）  
   - 支持上传文件或在线编辑，可批量导入用例  

3. **工作流类评测集**  
   - **输入文档**：单文件上传，支持docx/pdf/xlsx（≤100MB）  
   - **参考文档**（可选）：最多10个文件，单文件≤50MB，总计≤300MB  
   - **理想输出文档**：单文件上传，支持xmind/docx/markdown等（≤50MB）  

4. **预览与校验**  
   - 对话类：表格预览，支持筛选排序  
   - 工作流类：文档列表预览  
   - 自动检查格式错误、数据缺失  

#### 2.1.2 评测集管理

**功能描述**：全生命周期管理评测集  

**功能细节**：

1. **新增按钮**：支持新增评测集
2. **列表展示**：名称/类型/状态/版本号/创建时间，支持分页（默认10条/页  
3. **搜索筛选**：关键词搜索、按类型/状态/时间/标签筛选  
4. **列表操作功能**  
   - 编辑（仅草稿状态）、复制、发布（发布后不可修改）  
   - 导出（对话类Excel，工作流类ZIP）、删除（被引用时不可删除）  
   - 版本管理：创建新版本、对比差异、回退历史版本  
5. **详情页**：基本信息、内容预览、使用记录、版本历史  

### 2.2 评测任务管理

#### 2.2.1 评测任务创建

**功能描述**：创建评测任务，通过自动化或人工方式评测智能体应用的性能

**功能细节**：

1. **基本信息**：名称、描述、评测方式（必选）、标签
2. **向导式流程**  
   - **步骤1：选择评测对象**：搜索智能体应用及版本  
   - **步骤2：准备评测集**：引用已有或上传新评测集（工作流类需上传多文档）  
   - **步骤3：设置规则，详细说明参考2.2.2及2.2.3**  
     - **自动评测**：选择裁判模型、客观/主观指标（如向量相似度/完整性）、权重配置、自定义规则提示词  
     - **人工评测**：选择指标（如采纳度/正确性）、评分标准（1-5分）、分配评测人员、截止时间  
3. **配置预览**：确认后创建任务  

#### 2.2.2 自动评测任务执行

**功能描述**：执行自动评测任务，系统根据配置的评测规则自动评估智能体应用的性能

**功能细节**：  

1. **状态**：待执行/执行中/完成/失败（支持中断）  
2. **流程**：加载数据→调用智能体→收集结果→规则评估→存储结果  
3. **监控**：实时进度、执行日志、异常重试  
4. **通知**：系统消息/邮件（可选）  

#### 2.2.3 人工评测任务执行

**功能描述**：执行人工评测任务，评测人员手动评估智能体应用的性能  

**功能细节**：  

1. **状态**：待评测/评测中/完成/过期  
2. **流程**：  
   - 对话类：行级评测（标记段落问题）  
   - 工作流类：区域评测（文档特定位置批注）

3. **协作**：多人分配任务、进度同步、结果合并（标记不一致）、在线讨论  

#### 2.2.4 评测任务管理

**功能描述**：提供评测任务的全生命周期管理功能

**功能细节**：  

1. **列表展示**：名称/类型/状态/评测对象，支持分页  
2. **列表操作功能**：编辑（未执行时）、复制、执行/取消、导出配置、删除（执行中不可删）  
3. **详情页**：基本信息、配置详情、执行日志、结果快照  
4. **新增**：支持新增评测任务

### 2.3 评测报告管理

#### 2.3.1 评测报告生成

**功能描述**：基于评测任务结果生成评测报告，提供智能体应用性能的全面分析

**功能细节**：

1. **触发方式**：自动评测完成后自动生成，人工评测后手动触发  
2. **内容组成**

   - 基本信息：任务关联、评测对象  
   - 结果摘要：总体评分、指标汇总、通过率、可视化图表  
   - 详细结果：按用例/文档展示输入/输出/评分  
   - 问题分析：类型统计、典型案例  
   - 改进建议：自动生成，按优先级排序  
3. **格式**：在线交互报告（支持筛选）、可导出PDF/HTML/Excel  

#### 2.3.2 评测报告管理

**功能描述**：报告全生命周期管理  

**功能细节**:

1. **列表展示**：名称/任务/评分/生成时间，支持筛选  
2. **操作功能**：编辑基本信息、共享（权限设置）、导出、删除、复制  
3. **详情页**：交互式数据、可视化图表、原始数据下载  

#### 2.3.3 评测报告对比分析

**功能描述**：多报告差异分析  

**功能细节**：

1. **对比范围**：支持两报告对比（同应用不同版本或同类应用）  
2. **分析内容**：  
   - 自动分析：大模型生成结构化结论，对比评分/指标/问题分布  
   - 可视化展示：差异值计算、趋势箭头、Diff高亮显示  
3. **输出**：生成对比报告，支持导出和配置保存  

## 3. 用户功能旅程  

### 3.1 对话类智能体评测旅程

#### 3.1.1 创建评测集

1. 进入评测集管理→点击创建→选择“对话类”  
2. 上传Excel模板或在线编辑（必填字段：SessionID/queryID/Input/Output）  
3. 预览校验→保存为草稿或发布  

#### 3.1.2 创建自动评测任务

1. 选择“自动评测”→配置评测对象→引用/上传评测集  
2. 设置裁判模型、指标权重→确认执行→查看进度  

#### 3.1.3 创建人工评测任务

1. 选择“人工评测”→分配评测人员→设置截止时间  
2. 评测人员对输出内容行级评分，添加批注→提交生成报告  

### 3.2 工作流类智能体评测旅程

#### 3.2.1 创建评测集

1. 选择“工作流类”→上传输入文档、参考文档（可选）、理想输出文档  
2. 保存后发布  

#### 3.2.2 创建人工评测任务

1. 配置评测规则→指定评测人员→评测人员对文档区域标记评分  
2. 提交后生成包含区域分析的报告  

## 4. 非功能需求  

### 4.1 性能需求

- **响应时间**：页面加载≤2秒，报告生成≤30秒  
- **容量**：单个评测集支持1000条用例，文档总大小≤300MB  
- **并发**：支持50用户同时操作，10个自动任务并行执行  

### 4.2 安全需求

- **权限控制**：RBAC角色权限，细粒度操作控制（查看/创建/删除）  
- **数据安全**：敏感数据加密存储，传输加密，定期备份  
- **审计日志**：记录全量操作，不可篡改  

### 4.3 可用性需求

- **界面设计**：响应式布局，符合WCAG 2.1 AA标准  
- **帮助支持**：提供手册、上下文提示、FAQ  

## 5. 系统集成

- **智能体平台**：同步应用列表、调用接口、权限共享
- **用户管理系统**：SSO单点登录，组织架构权限同步  
- **数据分析平台**：支持评测数据定时导出与分析结果导入  

## 6. 数据模型

### 6.1 评测集模型

- **基础字段**：ID/名称/类型/状态/版本/创建人/时间  
- **对话类字段**：SessionID/queryID/Input/Output  
- **工作流类字段**：文档类型/路径/格式/大小  

### 6.2 评测任务模型

- **基础字段**：ID/名称/评测方式/状态/创建时间  
- **配置字段**：裁判模型/指标权重/评测人员  
- **结果字段**：实际输出/评分/批注  

### 6.3 评测报告模型

- **基础字段**：ID/关联任务/生成时间/权限  
- **内容字段**：总体评分/指标统计/问题分析/建议  

### prompt

角色：你是以为非常专业的高级前端开发人员，请基于当前文档 @req.md 评测管理模块产品需求文档-功能模块设计的内容进行前端功能开发，首先要认真分析功能模块设计下的功能点，在分析完成后再进行代码开发。在代码开发过程中要遵循 @vue-rule.mdc 配置的规则，详细要求如下：

1. 根据需求设计生成应对的前端代码文件（文件存放目录在 @views  下 ），优先使用封装的公共组件；
2. 在 @route.ts  下配置相应的路由，参考该文件下 `/template` 路由配置规则，注意hidden不要设置为true；
3. 在 @apis  生成相应的api文件，参考 @index.ts  ;
4. 在 @mock 下生成相应的模拟请求文件，参考 @index.ts

提示：业务页面开发时，参考文件如下：

1. 列表页面请参考 @index.vue
2. 新增弹窗请参考 @AppAddModal.vue
3. 详情抽屉请参考 @AppDetailDrawer.vue
4. 更多业务代码可借鉴工程里面的其他代码文件
