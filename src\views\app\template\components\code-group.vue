<template>
  <div class="code-group" :style="'width:' + props.width" :class="{ 'no-title': !props.title, gap: props.gap }">
    <div v-if="props.title" class="title flexRowStarCen">{{ props.title }}</div>
    <div v-if="props.tag || props.label" class="lebel flexRowStarCen">
      <div v-if="props.tag" class="tag">
        <a-tag :color="tagColor">{{ props.tag }}</a-tag>
      </div>
      <div v-if="props.label" class="label">
        {{ props.label }}
      </div>
    </div>
    <div class="content">
      <slot />
      <div class="copy" @click="copyFn">
        <icon-copy />
        Copy
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="code-group">
import { computed, useSlots } from 'vue'
import { Message } from '@arco-design/web-vue'

import { copyText } from '@/views/app/workflow/utils/common'
const slot = useSlots()
const defaultsSlot = slot.default && slot.default()[0].children
const props = withDefaults(
  defineProps<{
    width?: string
    title?: string
    tag?: string
    label?: string
    content?: string
    gap?: boolean
  }>(),
  {
    width: '100%',
    title: '',
    tag: '',
    label: '',
    content: '',
    gap: false
  }
)
const tagColor = computed(() => {
  let color = ''
  switch (props.tag) {
    case 'GET':
      color = 'green'
      break
    case 'DELETE':
      color = 'red'

      break
    case 'POST':
      color = 'blue'

      break
    case 'PUT':
      color = 'orange'
      break
  }
  return color
})
const copyFn = () => {
  if (copyText(defaultsSlot as string)) {
    Message.success('复制成功！')
  } else {
    Message.error('复制失败！')
  }
}
</script>
<style lang="less" scoped>
.code-group {
  border-radius: 12px;
  background-color: rgb(24 24 27);
  color: #fff;
  overflow: hidden;

  &.gap {
    margin-bottom: 20px;
  }

  &.no-title {
    .content {
      padding-top: 30px;
    }
  }

  .title {
    height: 49px;
    padding: 0 16px;
    border-bottom: rgb(63 63 70) solid 1px;
    background: rgb(39 39 42);
  }

  .lebel {
    height: 36px;
    font-size: 12px;
    padding: 0 16px;
    border-bottom: #fff solid 1px;

    .tag {
      :deep(span) {
        border: none;
        background: none;
        padding: 0;
        width: 40px;

        // &.@{ant-prefix}-tag-blue {
        //   color: rgb(14 165 233);
        // }

        // &.@{ant-prefix}-tag-green {
        //   color: rgb(16 185 129);
        // }

        // &.@{ant-prefix}-tag-red {
        //   color: rgb(239 68 68);
        // }
      }
    }

    .label {
      color: var(--color-text-3);
    }
  }

  .content {
    padding: 16px;
    line-height: 16px;
    font-size: 12px;
    position: relative;

    :deep(pre) {
      background: none;
      box-shadow: none;
      padding: 0;
      margin: 0;
    }

    .copy {
      border-radius: 40px;
      color: rgb(161 161 170);
      display: none;
      position: absolute;
      right: 16px;
      top: 16px;
      cursor: pointer;
      padding: 4px 8px;
      background: rgb(39 39 42);

      &:hover {
        color: #fff;
      }
    }

    &:hover {
      .copy {
        display: block;
      }
    }
  }
}
</style>
