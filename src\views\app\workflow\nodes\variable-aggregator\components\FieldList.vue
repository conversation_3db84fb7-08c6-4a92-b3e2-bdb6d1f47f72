<template>
  <div class="space-y-1">
    <!--没有选中switch的时候，展示variables数据-->
    <template v-if="!nodeInfo.advanced_settings || !nodeInfo.advanced_settings.group_enabled">
      <div class="flex justify-between align-center">
        <span>变量赋值</span>

        <a-space>
          <a-tag>{{ formatUpperCase(nodeInfo.output_type) }}</a-tag>
          <a-button type="text" size="small" class="arco-btn-only-icon" @click="handleAddItem(null)">
            <icon-plus class="cursor-pointer" />
          </a-button>
        </a-space>
      </div>
      <div>
        <div v-for="(item, index) in nodeInfo.variables">
          <a-space direction="vertical" fill size="middle" class="mb-[8px] mt-[8px]">
            <div class="w-full flex justify-between items-center">
              <!--选择变量value-->
              <!--<SelectVarValue
                style="flex:1; margin-right: 8px;"
                :nodeInfo="nodeInfo"
                placeholder="设置变量值"
                :varValue="item"
                @change="(val) => handleChangeVar(nodeInfo.variables,index,val)"
              />-->
              <VariableSelector
                :nodeId="nodeId"
                :value-selector="item"
                style="flex: 1; margin-right: 8px"
                @change="(val) => handleChangeVar(nodeInfo.variables, index, val)"
              />

              <a-button
                type="text"
                size="small"
                class="arco-btn-only-icon"
                @click="handleDeleteItem(nodeInfo.variables, index)"
              >
                <icon-delete />
              </a-button>
            </div>
          </a-space>
        </div>

        <a-alert v-if="nodeInfo.variables.length == 0" type="normal" banner center class="mt-[8px] mb-[8px]">
          添加需要赋值的变量
        </a-alert>
      </div>
    </template>

    <!--选中后，先把variables给了advanced_settings一份。然后以advanced_settings为主了。false的时候，把advanced_settings给variable赋值。-->
    <template v-else>
      <template v-for="(group, groupIndex) in nodeInfo.advanced_settings?.groups" :key="groupIndex">
        <div class="flex justify-between align-center">
          <div>
            <div class="flex justify-start items-center h-[32px]">
              <icon-folder class="text-[16px]" />

              <!--groupName-->
              <a-typography class="mx-[8px] edit-group-name">
                <a-typography-paragraph v-model:editText="group.group_name" editable @change="handleChange">
                  {{ group.group_name }}
                </a-typography-paragraph>
              </a-typography>

              <!--删除group-->
              <a-button
                v-if="nodeInfo.advanced_settings?.groups.length > 1"
                type="text"
                size="small"
                @click="handleDeleteGroup(group, groupIndex)"
              >
                <template #icon>
                  <icon-delete />
                </template>
              </a-button>
            </div>
          </div>

          <div>
            <a-space>
              <a-tag>{{ formatUpperCase(group.output_type) }}</a-tag>
              <a-button type="text" size="small" class="arco-btn-only-icon" @click="handleAddItem(group)">
                <icon-plus />
              </a-button>
            </a-space>
          </div>
        </div>

        <div v-for="(item, index) in group.variables">
          <a-space direction="vertical" fill size="middle" class="mb-[8px] mt-[8px]">
            <div class="w-full flex justify-between items-center">
              <!--选择变量value-->
              <!--<SelectVarValue
                style="flex:1; margin-right: 8px;"
                :nodeInfo="nodeInfo"
                placeholder="设置变量值"
                :varValue="item"
                @change="(val) => handleChangeVar(group.variables,index,val)"
              />-->
              <VariableSelector
                :nodeId="nodeId"
                :value-selector="item"
                style="flex: 1; margin-right: 8px"
                @change="(val) => handleChangeVar(group.variables, index, val)"
              />
              <a-button
                type="text"
                size="small"
                class="arco-btn-only-icon"
                @click="handleDeleteItem(group.variables, index)"
              >
                <icon-delete />
              </a-button>
            </div>
          </a-space>
        </div>

        <a-alert v-if="group.variables.length == 0" type="normal" banner center class="mt-[8px] mb-[8px]">
          添加需要赋值的变量
        </a-alert>

        <a-divider :margin="16" />
      </template>
    </template>

    <!--添加分组-->
    <a-button v-if="nodeInfo.advanced_settings?.group_enabled" long @click="handleAddGroup">
      <icon-plus />
      添加分组
    </a-button>

    <Field fieldTitle="聚合分组" tooltip="开启该功能后，变量聚合器内可以同时聚合多组变量">
      <template #operation>
        <a-switch
          :default-checked="nodeInfo.advanced_settings?.group_enabled"
          size="medium"
          type="round"
          @change="handleChangeState"
        />
      </template>
    </Field>

    <a-divider :margin="16" />

    <!--输出变量-->
    <Field v-if="nodeInfo.advanced_settings && nodeInfo.advanced_settings.group_enabled" fieldTitle="输出变量">
      <OutputVarCustom :nodeInfo="nodeInfo" :outputData="outputData" />
    </Field>
  </div>
</template>

<script setup lang="ts">
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'
import SelectVarValue from '@/views/app/workflow/nodes/code/components/SelectVarValue.vue'
import VariableSelector from '@/views/app/workflow/components/variable-selector/VariableSelector.vue'
import OutputVarCustom from '@/views/app/workflow/nodes/http/components/OutputVarCustom.vue'

// TODO: 选择变量后要更新类型。类型还要过滤。
interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
}

const props = defineProps<{
  list: FieldType[]
  isChatMode?: false
  nodeInfo?: any
  nodeId: string
}>()

onMounted(() => {})

const formatUpperCase = (value: string) => {
  if (!value) {
    return value
  }
  return value.toUpperCase()
}
const getGroupName = () => {
  const list = props.nodeInfo.advanced_settings.groups
  const nameList = list.map((item) => item.group_name)

  // 使用正则表达式提取数字并找出最大值
  const maxNumber = Math.max(
    ...nameList.map((item) => {
      const match = item.match(/\d+/)
      return match ? parseInt(match[0]) : 0
    })
  )

  // 生成下一个 Group 值
  const nextGroup = `Group${maxNumber + 1}`
  return nextGroup
}
const str = ref('test1')

const handleChange = (val) => {
  console.log('vale:', val)
}

// 删除group下的item
const handleDeleteGroup = (group, groupIndex) => {
  props.nodeInfo.advanced_settings?.groups.splice(groupIndex, 1)
}
// 添加group下的item
const handleAddItem = (group) => {
  if (!group) {
    // 如果是没有选中分组，那么给variables添加
    props.nodeInfo.variables.push([])
  } else {
    // 否则给advanced_settings 下的 groups 添加
    group.variables.push([])
  }
}

const handleChangeVar = (group, index, val) => {
  group[index] = val
}

/**
 * 添加分组
 */
const handleAddGroup = () => {
  props.nodeInfo.advanced_settings.groups.push({
    // groupId: '',
    group_name: getGroupName(), // group可以计算 number
    output_type: 'any', // 默认是any类型
    variables: []
  })
}

// 切换状态，要给advanced_settings赋值的
const handleChangeState = (val: boolean | string | number) => {
  if (!val) {
    props.nodeInfo.variables = [...props.nodeInfo.advanced_settings.groups[0].variables]
    props.nodeInfo.output_type = props.nodeInfo.advanced_settings.groups[0].output_type
  }
  if (!props.nodeInfo.advanced_settings) {
    props.nodeInfo.advanced_settings = {
      group_enabled: val,
      groups: [
        {
          group_name: 'Group1',
          output_type: props.nodeInfo.output_type,
          variables: [...props.nodeInfo.variables]
        }
      ]
    }
  } else {
    props.nodeInfo.advanced_settings.group_enabled = val
  }
}

const handleDeleteItem = (list, index) => {
  list.splice(index, 1)
}

const outputData = computed(() => {
  return props.nodeInfo.advanced_settings.groups.map((item) => ({
    name: `${item.group_name}.output`,
    type: item.output_type,
    desc: `${item.group_name}的输出变量`
  }))
})
watch(
  () => props.nodeInfo.advanced_settings,
  () => {
    if (!props.nodeInfo.advanced_settings) {
    }
  },
  { immediate: true }
)
</script>
<style scoped lang="scss">
.edit-group-name {
  :deep(.arco-typography-edit-content) {
    left: 0px;
    margin: 0px;
    .arco-input-wrapper {
    }
  }
}
</style>
