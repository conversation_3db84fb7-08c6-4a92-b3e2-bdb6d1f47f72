<template>
  <div class="property">
    <div class="flexRowStarCen title">
      <a-tag v-if="props.name">{{ props.name }}</a-tag>
      <small>{{ props.type }}</small>
    </div>
    <p v-if="props.p">
      <slot />
    </p>
    <template v-else>
      <slot />
    </template>
  </div>
</template>
<script setup name="property" lang="ts">
const props = withDefaults(
  defineProps<{
    name?: string
    type?: string
    p?: boolean
  }>(),
  {
    name: '',
    type: '',
    p: false
  }
)
</script>
<style scoped lang="less">
.property {
  border-bottom: var(--color-border-2) solid 1px;

  .title {
    margin: 15px 0;

    small {
      font-size: 12px;
      color: var(--color-text-3);
      padding-left: 10px;
    }
  }
}
</style>
