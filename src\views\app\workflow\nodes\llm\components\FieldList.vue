<template>
  <div class="space-y-1">
    <!--模型也拿出去吧。。。。-->
    <Field :fieldTitle="'模型'">
      <!-- trigger="click"-->
      <a-popover
        position="left"
        trigger="click"
        :content-style="{ width: '320px' }"
        @popup-visible-change="handleChangeVisible"
      >
        <a-select v-model="nodeInfo.model.name" :popup-visible="false" />
        <template #content>
          <Field fieldTitle="模型">
            <a-select
              v-model="selectedModel"
              placeholder="请选择"
              allow-search
              value-key="model"
              @change="handleChangeModel"
            >
              <a-optgroup v-for="(group, index) in modelObj.llmList" :key="index" :label="renderI18nName(group.label)">
                <a-option v-for="model in group.models" :key="model.model" :label="model.model" :value="model">
                  <a-space>
                    <a-avatar :size="24">
                      <img :src="DOMAIN_NAME + renderI18nName(group.icon_small)" alt="" />
                    </a-avatar>
                    {{ model.model }}
                    <a-tag>{{ modelTypeFormat(model.model_type) }}</a-tag>
                    <a-tag v-if="model.model_properties.context_size">
                      {{ sizeFormat(model.model_properties.context_size as number) }}
                    </a-tag>
                  </a-space>
                </a-option>
              </a-optgroup>
            </a-select>
          </Field>
          <Field fieldTitle="参数">
            <LlmParams :paramsList="paramsList" :nodeInfo="nodeInfo" />
          </Field>
        </template>
      </a-popover>
    </Field>

    <Field :fieldTitle="'上下文'" tooltip="您可以导入知识库作为上下文">
      <!--v1：cascader-->
      <!--v2:select-->
      <!--v3:div-->
      <!--<SelectVar
        :nodeInfo="nodeInfo"
        :varItem="nodeInfo.context"
        :varCode="'variable_selector'"
        :placeholder="'设置变量值'"
        @change="handleSelectedVar"
      />-->
      <!--v4:common component 1.没有清空；2.sys没有开始；-->
      <VariableSelector
        v-model:value-selector="nodeInfo.context.variable_selector"
        :nodeId="nodeId"
        @change="handleSelectedVar"
      />

      <!--提示词：-->
      <div v-for="(item, index) in nodeInfo.prompt_template" :key="index" class="template-item bg-gray-100">
        <div class="flex justify-between items-center">
          <a-space size="mini">
            <template v-if="index === 0">
              <span>{{ item.role === 'system' ? 'SYSTEM' : item.role === 'user' ? 'USER' : 'ASSISTANT' }}</span>
            </template>
            <a-dropdown v-if="index !== 0" @select="handleSelect(item, $event)">
              <a-link>
                {{ item.role === 'system' ? 'SYSTEM' : item.role === 'user' ? 'USER' : 'ASSISTANT' }}
                <icon-swap />
              </a-link>
              <template #content>
                <a-doption :value="{ value: 'assistant' }">ASSISTANT</a-doption>
                <a-doption :value="{ value: 'user' }">USER</a-doption>
              </template>
            </a-dropdown>

            <a-tooltip :content="getTooltip(item.role)">
              <icon-question-circle />
            </a-tooltip>
          </a-space>

          <a-space>
            <!--<a-link>-->
            <!--  <icon-plus @click="handleAddVar(item,index)" />-->
            <!--</a-link>-->
            <a-link>
              <icon-delete v-if="index != 0" @click="handleDeletePrompt(item, index)" />
            </a-link>
          </a-space>
        </div>

        <!--<a-textarea v-model="item.text" placeholder="在这里写你的提示词，输入'{' 插入变量、输入'/' 插入提示内容块"></a-textarea>-->

        <!--简版富文本选择变量-->
        <RichText :nodeInfo="nodeInfo" :varString="item.text" @handleInputVar="(val) => handleInputVar(item, val)" />
      </div>

      <a-button long class="mt-4" @click="addTemplateItem">
        <icon-plus />
        添加消息
      </a-button>
    </Field>

    <a-divider />

    <!--视觉-->
    <ConfigVision :nodeInfo="nodeInfo" />

    <a-divider />

    <!--输出变量-->
    <OutputVar :nodeInfo="nodeInfo" />

    <a-divider />

    <!--retryConfig-->
    <RetryConfig :nodeInfo="nodeInfo" />

    <a-divider />

    <!--异常处理-->
    <ErrorStrategy :nodeInfo="nodeInfo" />
  </div>
</template>

<script setup lang="ts">
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'
import { modelTypeFormat, renderI18nName, sizeFormat } from '@/views/app/workflow/utils/model'
import { DOMAIN_NAME } from '@/views/app/workflow/constant/common'
import { getModelTypesHttp, getParamsRulesHttp } from '@/apis/model-mgmt'
import { omit } from 'lodash-es'
import type { Model } from '@/apis/model-mgmt/type.ts'
import LlmParams from './LlmParams.vue'
import ConfigVision from '@/views/app/workflow/nodes/http/components/ConfigVision.vue'
import RetryConfig from '@/views/app/workflow/nodes/http/components/RetryConfig.vue'
import ErrorStrategy from '@/views/app/workflow/nodes/http/components/ErrorStrategy.vue'
import OutputVar from '@/views/app/workflow/nodes/http/components/OutputVar.vue'
import SelectVar from '@/views/app/workflow/nodes/http/components/SelectVar.vue'
import RichText from '@/views/app/workflow/nodes/http/components/rich-text/index.vue'
import VariableSelector from '@/views/app/workflow/components/variable-selector/VariableSelector.vue'

interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
}

const props = defineProps<{
  list: FieldType[]
  isChatMode?: false
  nodeInfo?: any
  nodeId: string
}>()
console.log('llm-props:', props)

const defaultList = [
  {
    label: '',
    required: false,
    readonly: true,
    type: 'string',
    variable: 'sys.user_id',
    value_selector: ['sys', 'user_id']
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'array[file]',
    variable: 'sys.files',
    value_selector: ['sys', 'files']
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.app_id',
    value_selector: ['sys', 'app_id']
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_id',
    value_selector: ['sys', 'workflow_id']
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_run_id',
    value_selector: ['sys', 'workflow_run_id']
  }
]

const selectedModel = ref<any>({})
const modelObj = reactive({
  llmList: [] as Model[]
})
const getModelTypes = async (modelType: string) => {
  const res = await getModelTypesHttp(modelType)
  const result = res.data || []
  result.forEach((item) => {
    item.models.forEach((model) => {
      model.provider = {
        ...omit(item, 'models')
      }
    })
  })
  if (modelType === 'llm') {
    modelObj.llmList = res.data || []
  }
}
getModelTypes('llm')

const paramsList = ref([])

const getParamsList = async () => {
  const res = await getParamsRulesHttp(props.nodeInfo.model.provider, props.nodeInfo.model.name)
  console.log('模型的参数配置res：', res)
  paramsList.value = res.data || []
}

onMounted(() => {
  console.log(
    props.list.map((e) => {
      console.log(e)
    })
  )
})

const handleChangeVisible = () => {
  const nodeModel = props.nodeInfo.model
  const provider = modelObj.llmList.filter((item) => {
    return item.provider === nodeModel.provider
  })
  if (provider.length > 0) {
    selectedModel.value = provider[0].models.find((v) => {
      return v.model === nodeModel.name
    })
  }
  getParamsList()
}

const handleChangeModel = (item) => {
  console.log(item)
  props.nodeInfo.model.name = item.model
  props.nodeInfo.model.provider = item.provider.provider

  getParamsList()
}

const addTemplateItem = () => {
  let type = props.nodeInfo.prompt_template[props.nodeInfo.prompt_template.length - 1].role
  let newType = type === 'system' ? 'assistant' : type === 'assistant' ? 'user' : 'assistant'
  props.nodeInfo.prompt_template.push({
    text: '',
    role: newType
  })
}

// 添加变量
const handleAddVar = (item, index) => {}
const handleDeletePrompt = (item, index) => {
  props.nodeInfo.prompt_template.splice(index, 1)
}

const getTooltip = (role: string) => {
  let tip = '为对话提供高层指导'
  if (role === 'system') {
    tip = '为对话提供高层指导'
  } else if (role === 'user') {
    tip = '向模型提供指令、查询或任何基于文本的输入'
  } else if (role === 'assistant') {
    tip = '基于用户消息的模型回复'
  }
  return tip
}

// 切换提示词类型：system、assistant、user
const handleSelect = (item, a) => {
  if (item.role === a.value) return false
  item.role = item.role == 'user' ? 'assistant' : 'user'
}

// 富文本输入更新
const handleInputVar = (item, val) => {
  item.text = val
}

// 上下文切换变量
const handleSelectedVar = (val) => {
  if (val.length == 0) {
    props.nodeInfo.context.enabled = false
  } else {
    props.nodeInfo.context.enabled = true
  }

  // props.nodeInfo.context.variable_selector = val
}
watch(
  () => props.nodeInfo,
  () => {
    const nodeData = props.nodeInfo
    // if (nodeData.context.variable_selector.length > 0 && nodeData.context.variable_selector[0] == 'sys') {
    //   // contextContent.context = [nodeData.context.variable_selector.join('.')]
    // }
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
.template-item {
  background: rgb(242 244 247);
  border-radius: 8px;
  margin-top: var(--margin);
  padding: 8px;
}

// 公共样式，后续拿走
.arco-divider.arco-divider-horizontal {
  margin: var(--margin) 0;
}
</style>
