import { useWorkflowStore } from '@/stores'
import type { AgentLogResponse } from '@/views/app/workflow/types/workflow'

export const useWorkflowAgentLog = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowAgentLog = (params: AgentLogResponse) => {
    const { data } = params
    const { workflowRunningData, setWorkflowRunningData } = workflowStore

    const workflowData = workflowRunningData
    const currentIndex = workflowData.tracing!.findIndex((item) => item.node_id === data.node_id)

    if (currentIndex > -1) {
      const current = workflowData.tracing![currentIndex]

      if (current.execution_metadata) {
        if (current.execution_metadata.agent_log) {
          const currentLogIndex = current.execution_metadata.agent_log.findIndex((log) => log.id === data.id)
          if (currentLogIndex > -1) {
            current.execution_metadata.agent_log[currentLogIndex] = {
              ...current.execution_metadata.agent_log[currentLogIndex],
              ...data
            }
          } else {
            current.execution_metadata.agent_log.push(data)
          }
        } else {
          current.execution_metadata.agent_log = [data]
        }
      } else {
        current.execution_metadata = {
          agent_log: [data]
        } as any
      }
    }

    setWorkflowRunningData(workflowData)
  }

  return {
    handleWorkflowAgentLog
  }
}
