<template>
  <a-drawer v-model:visible="visible" title="评估器详情" width="600px">
    <a-spin :loading="loading">
      <a-descriptions :data="descData" layout="inline-vertical" />
    </a-spin>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { getEvaluatorDetail } from '@/apis/evaluation'
import type { EvaluatorResp } from '@/apis/evaluation/types'

defineOptions({ name: 'EvaluatorDetailDrawer' })

// 抽屉可见性
const visible = ref(false)
// 加载状态
const loading = ref(false)
// 评估器数据
const evaluatorData = ref<EvaluatorResp>({} as EvaluatorResp)

// 打开抽屉
const onOpen = async (id: string) => {
  try {
    visible.value = true
    loading.value = true
    const res = await getEvaluatorDetail(id)
    evaluatorData.value = res.data
  } catch (error) {
    // 错误处理
  } finally {
    loading.value = false
  }
}

// 详情数据
const descData = computed(() => [
  {
    label: '评估器名称',
    value: evaluatorData.value.name
  },
  {
    label: '最新版本',
    value: evaluatorData.value.version
  },
  {
    label: '描述',
    value: evaluatorData.value.description
  },
  {
    label: '更新人',
    value: evaluatorData.value.updateUserString
  },
  {
    label: '更新时间',
    value: evaluatorData.value.updateTime
  },
  {
    label: '创建人',
    value: evaluatorData.value.createUserString
  },
  {
    label: '创建时间',
    value: evaluatorData.value.createTime
  }
])

// 暴露方法
defineExpose({
  onOpen
})
</script>
