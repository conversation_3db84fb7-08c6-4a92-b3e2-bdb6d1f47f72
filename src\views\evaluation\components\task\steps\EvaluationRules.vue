<template>
  <div class="evaluation-rules">
    <AiForm ref="formRef" v-model="localFormData" :columns="columns">
      <!-- 评测规则 -->
      <template #evalRuleSystem>
        <a-checkbox-group v-model="defaultEvalRuleIds" @change="evalRulechange">
          <a-checkbox value="1">完整性</a-checkbox>
          <a-checkbox value="2">合理性</a-checkbox>
          <a-checkbox value="3">正确性</a-checkbox>
          <a-checkbox value="4">相关性</a-checkbox>
        </a-checkbox-group>
      </template>
      <template #evalRuleAdd>
        <div class="flex items-center">
          <a-space>
            <a-input
              v-model="customEvalRules"
              placeholder="分析测试需求，生成测试案例"
              style="width: 300px"
              @press-enter="addTag"
            >
              <template #suffix>
                <icon-pen />
              </template>
            </a-input>
            <a-space>
              <a-tag v-for="(tag, index) of evalTags" :key="tag + index" closable @close="handleRemove(tag)">
                {{ tag }}
              </a-tag>
            </a-space>
          </a-space>
        </div>
      </template>
    </AiForm>

    <!-- 底部按钮 -->
    <div class="action-buttons">
      <a-space>
        <a-button @click="handlePrev">上一步</a-button>
        <a-button type="primary" @click="runEvaluation">开始测评</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { addTask } from '@/apis/evaluation/task'
import { EvalTaskSaveDTO } from '@/apis/evaluation/task-types'
import { ColumnItem } from '@/components/AiForm'

const props = defineProps({
  formData: {
    type: Object as () => EvalTaskSaveDTO,
    required: true
  }
})

const emit = defineEmits(['prev', 'saveForm'])

const router = useRouter()
const route = useRoute()

const localFormData = ref<EvalTaskSaveDTO>({
  evalRuleSystem: '1,2,3,4',
  evalRuleAdd: '',
  evalRulePromptWord: ''
})

const defaultEvalRuleIds = ref(['1', '2', '3', '4'])
const customEvalRules = ref('')
const evalTags = ref([])
const columns: ColumnItem[] = [
  {
    label: '内置评测规则',
    field: 'evalRuleSystem',
    span: 24,
    rules: [{ required: true, message: '请选择内置评测规则' }]
  },
  {
    label: '新增评测规则',
    field: 'evalRuleAdd',
    span: 24,
    props: {
      placeholder: '请输入评测规则'
    }
  },
  {
    label: '评测规则提示词',
    field: 'evalRulePromptWord',
    type: 'textarea',
    span: 24,
    props: {
      maxLength: 0,
      autoSize: {
        minRows: 10,
        maxRows: 10
      }
    }
  }
]

watchEffect(() => {
  if (props.formData) {
    localFormData.value = { ...localFormData.value, ...props.formData }
  }
})

const evalRulechange = (value) => {
  localFormData.value.evalRuleSystem = value?.join(',') || ''
}

const addTag = (e) => {
  if (e.target.value.trim() !== '') {
    evalTags.value.push(e.target.value?.trim())
    customEvalRules.value = ''
    localFormData.value.evalRuleAdd = evalTags.value?.join(',') || ''
  }
}

const handleRemove = (tag) => {
  evalTags.value = evalTags.value?.filter((item) => item !== tag)
  localFormData.value.evalRuleAdd = evalTags.value?.join(',') || ''
}

const handlePrev = () => {
  emit('saveForm', localFormData.value)
  emit('prev')
}

const runEvaluation = async () => {
  const params = JSON.parse(
    JSON.stringify({
      ...props.formData,
      ...localFormData.value,
      taskType: Number(route.query.taskType),
      status: 0
    })
  )
  const res = await addTask(params)
  if (res.isSuccess) {
    router.go(-1)
  }
}

onMounted(() => {
  if (props.formData.evalRuleSystem) {
    defaultEvalRuleIds.value = props.formData.evalRuleSystem?.split(',') || []
  }
  if (props.formData.evalRuleAdd) {
    evalTags.value = props.formData.evalRuleAdd?.split(',') || []
  }
})
</script>

<style scoped lang="scss">
.evaluation-rules {
  width: 1000px;
  height: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  .arco-form {
    flex: 1;
  }
}

.action-buttons {
  text-align: right;
}
</style>
