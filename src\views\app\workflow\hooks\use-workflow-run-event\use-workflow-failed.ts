import { useWorkflowStore } from '@/stores'
import { WorkflowRunningStatus } from '@/views/app/workflow/types/workflow'

export const useWorkflowFailed = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowFailed = () => {
    const { workflowRunningData, setWorkflowRunningData } = workflowStore

    const workflowData = workflowRunningData

    workflowData.result = {
      ...workflowRunningData?.result,
      status: WorkflowRunningStatus.Failed
    }
    setWorkflowRunningData(workflowData)
  }

  return {
    handleWorkflowFailed
  }
}
