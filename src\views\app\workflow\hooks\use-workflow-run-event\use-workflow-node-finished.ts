import { useVueFlow } from '@vue-flow/core'
import { useWorkflowStore } from '@/stores'
import {
  BlockEnum,
  ErrorHandleTypeEnum,
  NodeRunningStatus,
  type NodeFinishedResponse
} from '@/views/app/workflow/types/workflow'

export const useWorkflowNodeFinished = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowNodeFinished = (params: NodeFinishedResponse) => {
    const { data } = params
    const { workflowRunningData, setWorkflowRunningData } = workflowStore
    const { nodes, setNodes, edges, setEdges } = useVueFlow()

    const workflowData = workflowRunningData
    const currentIndex = workflowRunningData.tracing!.findIndex((item) => item.id === data.id)
    if (currentIndex > -1) {
      workflowData.tracing![currentIndex] = {
        ...workflowData.tracing![currentIndex],
        ...data
      }
    }
    setWorkflowRunningData(workflowData)

    if (nodes.value?.length) {
      const newNodes = nodes.value.map((node) => {
        if (node.id === data.node_id) {
          node.data._runningStatus = data.status
        }
        if (data.status === NodeRunningStatus.Exception) {
          if (data.execution_metadata?.error_strategy === ErrorHandleTypeEnum.failBranch)
            node.data._runningBranchId = ErrorHandleTypeEnum.failBranch
        } else {
          if (data.node_type === BlockEnum.IfElse) node.data._runningBranchId = data?.outputs?.selected_case_id

          if (data.node_type === BlockEnum.QuestionClassifier) node.data._runningBranchId = data?.outputs?.class_id
        }
        return node
      })
      setNodes(newNodes)
    }

    if (edges.value) {
      const newEdges = edges.value.map((edge) => {
        if (edge.target === data.node_id) {
          edge.data = {
            ...edge.data,
            _targetRunningStatus: data.status
          }
        }
        return edge
      })

      setEdges(newEdges)
    }
  }

  return {
    handleWorkflowNodeFinished
  }
}
