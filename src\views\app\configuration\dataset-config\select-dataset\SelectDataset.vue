<template>
  <div>
    <a-modal :visible="true" title="选择引用知识库" @cancel="handleOk('cancel')" @ok="handleOk('ok')">
      <div>
        <a-list
          :scrollbar="scrollbar"
          :bordered="false"
          :max-height="240"
          class="table-container"
          @reach-bottom="fetchData"
        >
          <template #scroll-loading>
            <div v-if="bottom">已全部加载</div>
            <a-spin v-else />
          </template>
          <div v-for="item in datasets" :key="item.id" class="flex justify-between items-center my-20">
            {{ item.name }}
            <div v-if="item.indexing_technique">
              <a-tag size="small">
                {{
                  formatIndexingTechniqueAndMethod(item.indexing_technique, item.retrieval_model_dict?.search_method)
                }}
              </a-tag>
            </div>
            <div v-if="item.provider === 'external'">
              <a-tag size="small">外部</a-tag>
            </div>
          </div>
        </a-list>
      </div>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import { getDatasetList } from '@/apis'
import { formatIndexingTechniqueAndMethod } from '@/views/app/workflow/utils/configuration'

const emits = defineEmits(['closeDatasetDialog'])
const handleOk = (type: string): void => {
  console.log(type)
  emits('closeDatasetDialog', type)
}
const datasets = ref([])
const page = ref(1)
const hasMore = ref(true)
const isLoading = ref(false)
const getDatasetListFn = async () => {
  const params = {
    page: page.value,
    limit: 5
  }
  isLoading.value = true
  const { data, has_more } = await getDatasetList(params)
  console.log('知识库列表res：', data, has_more)
  hasMore.value = has_more
  datasets.value.push(...data)
  page.value++
  isLoading.value = false
}
getDatasetListFn()

const scrollbar = ref(true)
const bottom = ref(false)

const fetchData = () => {
  console.log('reach bottom!', hasMore.value)
  if (hasMore.value && !isLoading.value) {
    getDatasetListFn()
  } else if (!hasMore.value) {
    bottom.value = true
  }
}
</script>
<style scoped lang="scss">
.table-container {
  //height: 300px;
  //overflow: auto;
}
</style>
