<template>
  <a-drawer
    v-model:visible="visible"
    title="应用详情"
    :width="width >= 600 ? 600 : '100%'"
    :footer="false"
    @cancel="close"
  >
    <a-descriptions :column="2" size="large" class="general-description">
      <a-descriptions-item label="名称">{{ dataDetail?.name }}</a-descriptions-item>
      <a-descriptions-item label="类型">
        <span>{{ dataDetail?.type === 1 ? '对话类' : '工作流类' }}</span>
      </a-descriptions-item>
      <a-descriptions-item label="状态">
        <a-tag v-if="dataDetail.status === 1" color="orange">草稿</a-tag>
        <a-tag v-else color="green">已发布</a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="版本">{{ dataDetail?.version }}</a-descriptions-item>
      <a-descriptions-item label="描述">{{ dataDetail?.description }}</a-descriptions-item>
    </a-descriptions>
  </a-drawer>
</template>

<script setup lang="ts">
import { useWindowSize } from '@vueuse/core'
import { getUsingGet } from '@/apis/template'
import { EvalDataset } from '@/apis/template/type'

const props = defineProps<{
  detailVisible: boolean
  appId: string
}>()

const emit = defineEmits<{
  (e: 'hide-drawer'): void
}>()

const { width } = useWindowSize()

const dataDetail = ref<EvalDataset>({})
const visible = ref(props.detailVisible)

onMounted(async () => {
  if (props.appId) {
    const { data } = await getUsingGet(props.appId)
    dataDetail.value = data || {}
  }
})

const close = () => {
  emit('hide-drawer')
}
</script>

<style scoped lang="scss"></style>
