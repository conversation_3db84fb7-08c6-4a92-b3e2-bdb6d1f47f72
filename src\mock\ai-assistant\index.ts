import type { MockMethod } from 'vite-plugin-mock'

// 助手列表数据
const assistantList = [
  {
    id: '1',
    name: '测试分析助手',
    description: '自动生成测试用例',
    icon: 'assistant-test.png',
    updateTime: '2025.05.14',
    count: '1,436',
    version: 'V1.8',
    type: '测试类'
  },
  {
    id: '2',
    name: '智能问答助手',
    description: '智能回答问题',
    icon: 'assistant-qa.png',
    updateTime: '2025.05.14',
    count: '1,438',
    version: 'V1.2',
    type: '问答类'
  },
  {
    id: '3',
    name: '代码审查助手',
    description: '代码质量分析',
    icon: 'assistant-code.png',
    updateTime: '2025.05.14',
    count: '1,764',
    version: 'V1.3',
    type: '测试类'
  },
  {
    id: '4',
    name: '文章写作助手',
    description: '智能生成文章',
    icon: 'assistant-writer.png',
    updateTime: '2025.05.14',
    count: '1,643',
    version: 'V2.4',
    type: '测试类'
  }
]

// 任务列表数据
const taskList = [
  {
    id: '1',
    assistantId: '1',
    assistantName: '测试用例生成助手',
    taskName: '用例生成任务',
    icon: 'assistant-test.png',
    progress: 89,
    status: 'processing',
    executeTime: '2025.05.12 14:32:45',
    completeTime: '',
    evaluation: '',
    assessment: ''
  },
  {
    id: '2',
    assistantId: '1',
    assistantName: '测试用例生成助手',
    taskName: '用例生成任务',
    icon: 'assistant-test.png',
    progress: 100,
    status: 'completed',
    executeTime: '2025.05.12 14:32:45',
    completeTime: '2025.05.12 16:32:53',
    evaluation: '60%',
    assessment: ''
  },
  {
    id: '3',
    assistantId: '1',
    assistantName: '测试用例生成助手',
    taskName: '用例生成任务',
    icon: 'assistant-test.png',
    progress: 100,
    status: 'completed',
    executeTime: '2025.05.12 14:32:45',
    completeTime: '2025.05.12 16:32:53',
    evaluation: '已评测',
    assessment: ''
  },
  {
    id: '4',
    assistantId: '1',
    assistantName: '测试用例生成助手',
    taskName: '用例生成任务',
    icon: 'assistant-test.png',
    progress: 100,
    status: 'terminated',
    executeTime: '2025.05.12 14:32:45',
    completeTime: '2025.05.12 16:32:53',
    evaluation: '未评价',
    assessment: ''
  }
]

// 任务类型定义
interface TaskItem {
  id: string
  assistantId: string
  assistantName: string
  taskName: string
  icon: string
  progress: number
  status: string
  executeTime: string
  completeTime: string
  evaluation: string
  assessment: string
}

export default [
  // 获取助手列表
  {
    url: '/api/ai-assistant/list',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'ok',
        data: assistantList
      }
    }
  },

  // 创建助手
  {
    url: '/api/ai-assistant/create',
    method: 'post',
    response: ({ body }) => {
      const newAssistant = {
        id: String(assistantList.length + 1),
        name: body.name,
        description: body.analysis?.scenarios || '智能助手',
        icon: '/src/assets/assistant-test.png',
        updateTime: new Date().toISOString().split('T')[0].replace(/-/g, '.'),
        count: '0',
        version: 'V1.0',
        type: body.type || '测试类'
      }

      assistantList.push(newAssistant)
      return {
        code: 200,
        message: 'ok',
        data: newAssistant
      }
    }
  },

  // 获取任务列表
  {
    url: '/api/ai-assistant/task/list',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'ok',
        data: taskList
      }
    }
  },

  // 创建任务
  {
    url: '/api/ai-assistant/task/create',
    method: 'post',
    response: ({ body }) => {
      // 获取助手信息
      const assistant = assistantList.find((item) => item.id === body.assistantId)

      const newTask: TaskItem = {
        id: String(taskList.length + 1),
        assistantId: body.assistantId,
        assistantName: assistant?.name || '未知助手',
        taskName: body.taskName || '新任务',
        icon: assistant?.icon || '',
        progress: 0,
        status: 'processing',
        executeTime: new Date().toLocaleString(),
        completeTime: '',
        evaluation: '',
        assessment: ''
      }

      taskList.push(newTask)
      return {
        code: 200,
        message: 'ok',
        data: newTask
      }
    }
  },

  // 获取任务详情
  {
    url: /\/api\/ai-assistant\/task\/\d+/,
    method: 'get',
    response: ({ url }) => {
      const id = url.match(/\/api\/ai-assistant\/task\/(\d+)/)[1]
      const task = taskList.find((item) => item.id === id)
      if (task) {
        return {
          code: 200,
          message: 'ok',
          data: task
        }
      }
      return {
        code: 404,
        message: '任务不存在',
        data: null
      }
    }
  },

  // 终止任务
  {
    url: /\/api\/ai-assistant\/task\/\d+\/terminate/,
    method: 'post',
    response: ({ url }) => {
      const id = url.match(/\/api\/ai-assistant\/task\/(\d+)\/terminate/)[1]
      const task = taskList.find((item) => item.id === id)
      if (task && task.status === 'processing') {
        task.status = 'terminated'
        task.completeTime = new Date().toLocaleString()
        return {
          code: 200,
          message: 'ok',
          data: task
        }
      }
      return {
        code: 400,
        message: '任务状态不允许终止',
        data: null
      }
    }
  },

  // 继续任务
  {
    url: /\/api\/ai-assistant\/task\/\d+\/continue/,
    method: 'post',
    response: ({ url }) => {
      const id = url.match(/\/api\/ai-assistant\/task\/(\d+)\/continue/)[1]
      const task = taskList.find((item) => item.id === id)
      if (task && task.status === 'terminated') {
        task.status = 'processing'
        task.completeTime = ''
        return {
          code: 200,
          message: 'ok',
          data: task
        }
      }
      return {
        code: 400,
        message: '任务状态不允许继续',
        data: null
      }
    }
  },

  // 删除任务
  {
    url: /\/api\/ai-assistant\/task\/\d+/,
    method: 'delete',
    response: ({ url }) => {
      const id = url.match(/\/api\/ai-assistant\/task\/(\d+)/)[1]
      const index = taskList.findIndex((item) => item.id === id)
      if (index !== -1) {
        taskList.splice(index, 1)
        return {
          code: 200,
          message: 'ok',
          data: null
        }
      }
      return {
        code: 404,
        message: '任务不存在',
        data: null
      }
    }
  },

  // 评价任务
  {
    url: /\/api\/ai-assistant\/task\/\d+\/evaluate/,
    method: 'post',
    response: ({ url, body }) => {
      const id = url.match(/\/api\/ai-assistant\/task\/(\d+)\/evaluate/)[1]
      const task = taskList.find((item) => item.id === id)
      if (task && task.status === 'completed') {
        task.evaluation = body.evaluation || '已评测'
        return {
          code: 200,
          message: 'ok',
          data: task
        }
      }
      return {
        code: 400,
        message: '任务状态不允许评价',
        data: null
      }
    }
  }
] as MockMethod[]
