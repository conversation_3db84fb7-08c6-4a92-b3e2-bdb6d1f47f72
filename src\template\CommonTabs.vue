<template>
  <AiPageLayout>
    <a-tabs>
      <!-- 当tab存在操作时使用extra -->
      <template #extra>
        <a-space>
          <a-input-search :style="{ width: '220px' }" placeholder="请输入" allow-clear />
          <a-button type="primary">创建</a-button>
        </a-space>
      </template>
      <a-tab-pane key="1" title="Tab 1" />
      <a-tab-pane key="2" title="Tab 2" />
      <a-tab-pane key="3" title="Tab 3" />
    </a-tabs>
    <!-- 下方为AiTable组件相关代码 -->
  </AiPageLayout>
</template>

<script setup lang="ts">
defineOptions({
  name: 'CommonTabs'
})
</script>

<style scoped lang="scss">
:deep(.arco-tabs .arco-tabs-nav-type-card-gutter .arco-tabs-tab-active) {
  box-shadow:
    inset 0 2px 0 rgb(var(--primary-6)),
    inset -1px 0 0 var(--color-border-2),
    inset 1px 0 0 var(--color-border-2);
  position: relative;
}

:deep(.arco-tabs-nav-type-card-gutter .arco-tabs-tab) {
  border-radius: var(--border-radius-medium) var(--border-radius-medium) 0 0;
}

:deep(.arco-tabs-type-card-gutter > .arco-tabs-content) {
  border: none;
}

:deep(.arco-tabs-nav::before) {
  left: -20px;
  right: -20px;
}

:deep(.arco-tabs) {
  overflow: visible;
}

:deep(.arco-tabs-nav) {
  overflow: visible;
}
</style>
