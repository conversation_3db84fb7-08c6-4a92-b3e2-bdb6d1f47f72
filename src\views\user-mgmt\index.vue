<template>
  <AiPageLayout class="user-mgmt">
    <!-- 用户列表 -->
    <AiTable
      :loading="loading"
      :columns="columns"
      :data="dataList"
      :pagination="pagination"
      :scroll="{ x: '100%', y: '100%' }"
      :disabled-tools="['size']"
      @page-change="((pagination.current = $event), search())"
      @page-size-change="((pagination.pageSize = $event), search())"
      @refresh="search"
    >
      <!-- 工具栏左侧：搜索区域 -->
      <template #toolbar-left>
        <a-input-search
          v-model="params.keywords"
          placeholder="请输入关键词搜索"
          style="width: 220px"
          allow-clear
          @search="search"
        />
        <a-button @click="reset">
          <template #icon><icon-refresh /></template>
          <template #default>重置</template>
        </a-button>
      </template>

      <!-- 工具栏右侧：操作按钮 -->
      <template #toolbar-right>
        <a-button type="primary" @click="handleAdd">
          <template #icon><icon-plus /></template>
          <template #default>添加</template>
        </a-button>
      </template>

      <!-- 用户信息 -->
      <template #name="{ record }">
        <a-space :align="'center'" :size="12">
          <a-avatar :size="24" class="user-avatar">
            <img v-if="record.avatar_url" alt="avatar" :src="DOMAIN_NAME + record.avatar_url" />
            <span v-else>{{ record.name.charAt(0).toUpperCase() }}</span>
          </a-avatar>
          <a-space direction="vertical" :size="0" class="user-info">
            <a-typography-text>
              {{ record.name }}
              <span v-if="record.status === 'pending'" class="pending-status">待定...</span>
              <span v-if="userInfo.email === record.email" class="current-user">（你）</span>
            </a-typography-text>
            <a-typography-text type="secondary">{{ record.email }}</a-typography-text>
          </a-space>
        </a-space>
      </template>

      <!-- 最后活动时间 -->
      <template #last_active_at="{ record }">
        {{ formatLastActiveTime(record.last_active_at || record.created_at) }}
      </template>

      <!-- 用户角色 -->
      <template #role="{ record }">
        <span v-if="record.role === 'owner'">所有者</span>
        <template v-else>
          <a-select
            v-model="record.role"
            placeholder="请选择"
            style="width: 120px"
            :tag-nowrap="true"
            :disabled="hasRoleManagePermission"
            @change="handleChangeRole(record)"
          >
            <a-option v-for="role in roleList" :key="role.value" :value="role.value">
              {{ role.label }}
            </a-option>
          </a-select>
        </template>
      </template>
    </AiTable>

    <!-- 添加用户弹窗 -->
    <AddUser v-if="addUserVisible" @closeAddUserDialog="closeAddUserDialog" />
  </AiPageLayout>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { deleteUserRoleHttp, getUserListHttp, updateUserRoleHttp } from '@/apis/user'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import relativeTime from 'dayjs/plugin/relativeTime'
import { Message } from '@arco-design/web-vue'
import type { UserItemInterface } from '@/apis/user/type.ts'

import AddUser from './components/AddUser.vue'
import { DOMAIN_NAME } from '@/views/app/workflow/constant/common'
import { useTable } from '@/hooks'
import { useUserStore } from '@/stores'

dayjs.extend(relativeTime)
defineOptions({ name: 'UserMgmt' })

interface UserInfo {
  email?: string
  role?: string
}

/**
 * 角色列表配置
 * 不同角色具有不同权限
 */
const roleList = [
  { label: '管理员', value: 'admin', desc: '能够建立应用程序和管理团队设置' },
  { label: '编辑', value: 'editor', desc: '能够建立并编辑应用程序，不能管理团队设置' },
  { label: '成员', value: 'normal', desc: '只能使用应用程序，不能建立应用程序' },
  { label: '知识库管理员', value: 'datasetOperator', desc: '只能管理知识库' },
  { label: '移出团队', value: 'removeFromTeam', desc: '将取消团队访问' }
]

// 获取用户信息
const userStore = useUserStore()
const userInfo = computed<UserInfo>(() => userStore.userInfo || {})

// 判断当前用户是否有管理角色的权限
const hasRoleManagePermission = computed(() => {
  return ['owner', 'admin'].includes(userInfo.value.role || '')
})

// 搜索参数
const params = ref({
  keywords: ''
})

// 表格列配置
const columns = [
  {
    title: '姓名',
    slotName: 'name'
  },
  {
    title: '上次活动时间',
    slotName: 'last_active_at'
  },
  {
    title: '角色',
    slotName: 'role'
  },
  {
    title: 'Email',
    dataIndex: 'email',
    ellipsis: true,
    tooltip: true,
    width: 150
  }
]

/**
 * 使用表格钩子获取用户列表数据
 */
const {
  tableData: dataList,
  loading,
  pagination,
  search
} = useTable<UserItemInterface>((page) => getUserListHttp({ ...params.value, ...page }), {
  immediate: true,
  dataKey: 'accounts'
})

/**
 * 格式化最后活动时间
 */
const formatLastActiveTime = (timestamp: number | string) => {
  return dayjs(Number(timestamp) * 1000)
    .locale('zh-cn')
    .fromNow()
}

/**
 * 重置搜索条件
 */
const reset = () => {
  params.value.keywords = ''
  search()
}

/**
 * 更新用户的角色
 * @param record 用户信息
 */
const handleChangeRole = (record: UserItemInterface) => {
  if (record.role === 'removeFromTeam') {
    deleteUserRole(record)
  } else {
    updateUserRole(record)
  }
}

/**
 * 从团队中删除用户
 */
const deleteUserRole = async (record: UserItemInterface) => {
  try {
    await deleteUserRoleHttp(record.id)
    Message.success('删除成功')
    search()
  } catch (error) {
    console.log(error)
    Message.error('删除失败')
  }
}

/**
 * 更新用户角色
 */
const updateUserRole = async (record: UserItemInterface) => {
  const params = {
    role: record.role || 'editor'
  }
  try {
    await updateUserRoleHttp(record.id, params)
    Message.success('修改成功')
    search()
  } catch (err) {
    console.log(err)
    Message.error('修改失败')
  }
}

// 添加用户弹窗状态
const addUserVisible = ref(false)

/**
 * 打开添加用户弹窗
 */
const handleAdd = () => {
  addUserVisible.value = true
}

/**
 * 关闭添加用户弹窗
 */
const closeAddUserDialog = (type: string) => {
  if (type == 'ok') {
    search()
  }
  addUserVisible.value = false
}
</script>

<style scoped lang="scss">
.user-mgmt {
  .user-avatar {
    background-color: $color-theme;
  }

  .pending-status {
    color: $color-danger;
  }

  .current-user {
    margin-left: 4px;
    color: var(--color-text-secondary);
  }
}
</style>
