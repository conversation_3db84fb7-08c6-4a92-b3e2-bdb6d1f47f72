import { getToken } from '@/utils/auth'
import { handleStream } from '@/views/app/workflow/utils/handle-stream'
import type * as T from './type'

const baseOptions = {
  method: 'GET',
  headers: new Headers()
}

enum ContentType {
  json = 'application/json',
  stream = 'text/event-stream'
}

/** @desc chatflow运行 - 用于advanced-chat模式 */
export function chatflowRunFetch(appId: string, fetchOptions: T.FetchOptionType, otherOptions?: T.IOtherOptions) {
  const {
    onData,
    onCompleted,
    onFile,
    onThought,
    onMessageEnd,
    onMessageReplace,
    onWorkflowStarted,
    onWorkflowFinished,
    onNodeStarted,
    onNodeFinished,
    onIterationStart,
    onIterationNext,
    onIterationFinish,
    onNodeRetry,
    onParallelBranchStarted,
    onParallelBranchFinished,
    onTextChunk,
    onTTSChunk,
    onTTSEnd,
    onTextReplace,
    onAgentLog,
    onError,
    onLoopStart,
    onLoopNext,
    onLoopFinish
  } = otherOptions || {}

  const abortController = new AbortController()
  const token = getToken()
  const options = Object.assign(
    {},
    baseOptions,
    {
      method: 'POST',
      signal: abortController.signal,
      headers: new Headers({
        Authorization: `Bearer ${token}`
      })
    } as RequestInit,
    fetchOptions
  )

  const contentType = (options.headers as Headers).get('Content-Type')
  if (!contentType) (options.headers as Headers).set('Content-Type', ContentType.json)

  const { body } = options
  if (body) options.body = JSON.stringify(body)

  // 使用 advanced-chat 的运行端点
  fetch(`/console/api/apps/${appId}/advanced-chat/workflows/draft/run`, options as RequestInit)
    .then((res) => {
      return handleStream(
        res,
        (str: string, isFirstMessage: boolean, moreInfo: T.IOnDataMoreInfo) => {
          if (moreInfo.errorMessage) {
            onError?.(moreInfo.errorMessage, moreInfo.errorCode)
            if (
              moreInfo.errorMessage !== 'AbortError: The user aborted a request.' &&
              !moreInfo.errorMessage.includes('TypeError: Cannot assign to read only property')
            )
              return
          }

          onData?.(str, isFirstMessage, moreInfo)
        },
        onCompleted,
        onFile,
        onThought,
        onMessageEnd,
        onMessageReplace,
        onWorkflowStarted,
        onWorkflowFinished,
        onNodeStarted,
        onNodeFinished,
        onIterationStart,
        onIterationNext,
        onIterationFinish,
        onLoopStart,
        onLoopNext,
        onLoopFinish,
        onNodeRetry,
        onParallelBranchStarted,
        onParallelBranchFinished,
        onTextChunk,
        onTTSChunk,
        onTTSEnd,
        onTextReplace,
        onAgentLog
      )
    })
    .catch((e) => {
      if (
        e.toString() !== 'AbortError: The user aborted a request.' &&
        !e.toString().includes('TypeError: Cannot assign to read only property')
      ) {
        onError?.(e.toString())
      }
    })

  return abortController
}

/** @desc 停止chatflow运行 */
export function stopChatflowRun(appId: string, taskId: string, user: string = 'default-user') {
  const token = getToken()
  return fetch(`/console/api/apps/${appId}/chat-messages/${taskId}/stop`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      user
    })
  })
}
