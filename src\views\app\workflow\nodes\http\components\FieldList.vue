<template>
  <div class="space-y-1">
    <!--API-->
    <Field :fieldTitle="'API'" :tooltip="''">
      <!--<template v-slot:operation>
        <a-space>
          <a-button size="mini" type="text">鉴权</a-button>
          <a-button size="mini" type="text">导入cURL</a-button>
        </a-space>
      </template>-->
      <ApiInput :nodeInfo="nodeInfo" />
    </Field>

    <!--heards-->
    <Field :fieldTitle="'HEADERS'" :tooltip="''">
      <KeyValueEdit :defaultList="defaultList" :data="headersData" :nodeInfo="nodeInfo" type="headers" />
    </Field>

    <!--params-->
    <Field :fieldTitle="'PARAMS'" :tooltip="''">
      <KeyValueEdit :defaultList="defaultList" :data="paramsData" :nodeInfo="nodeInfo" type="params" />
    </Field>

    <!--body-->
    <Field :fieldTitle="'BODY'" :tooltip="''">
      <a-radio-group v-model="nodeInfo.body.type" @change="handleChangeBodyType">
        <a-radio v-for="item in allTypes" :key="item" :value="item">{{ item }}</a-radio>
      </a-radio-group>

      <template v-if="nodeInfo.body.type == BodyType.none" />
      <template v-else-if="nodeInfo.body.type == BodyType.formData">
        <KeyValueEdit :defaultList="defaultList" :data="nodeInfo.body.data" />
      </template>
      <template v-else-if="nodeInfo.body.type == BodyType.xWwwFormUrlencoded">
        <KeyValueEdit :defaultList="defaultList" :data="nodeInfo.body.data" />
      </template>
      <template v-else-if="nodeInfo.body.type == BodyType.json">
        <a-textarea v-model="nodeInfo.body.data[0].value" placeholder="请输入内容" allow-clear />
      </template>
      <template v-else-if="nodeInfo.body.type == BodyType.rawText">
        <a-textarea v-model="nodeInfo.body.data[0].value" placeholder="请输入内容" allow-clear />
      </template>
      <template v-else-if="nodeInfo.body.type == BodyType.binary">
        <a-select placeholder="请选择">
          <a-option v-for="option in options" :key="option" :value="option.value">{{ option.label }}</a-option>
        </a-select>
      </template>
    </Field>

    <a-divider />

    <!--超时设置-->
    <TimeoutConfig :nodeInfo="nodeInfo" />

    <a-divider />

    <!--输出变量-->
    <OutputVar :nodeInfo="nodeInfo" />

    <a-divider />

    <!--retryConfig-->
    <RetryConfig :nodeInfo="nodeInfo" />

    <a-divider />

    <!--异常处理-->
    <ErrorStrategy :nodeInfo="nodeInfo" />

    <AuthModal v-model:visible="showAuthModal" :authorization="localData.authorization" @update="handleAuthUpdate" />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import Field from './Field.vue'
import ApiInput from './ApiInput.vue'
import KeyValueEdit from './KeyValueEdit.vue'
import RetryConfig from './RetryConfig.vue'
import { uniqueId } from 'lodash-es'
import { BodyType } from '@/views/app/workflow/nodes/http/types'
import TimeoutConfig from './TimeoutConfig.vue'
import OutputVar from '@/views/app/workflow/nodes/http/components/OutputVar.vue'
import ErrorStrategy from '@/views/app/workflow/nodes/http/components/ErrorStrategy.vue'
import AuthModal from '@/views/app/workflow/nodes/http/components/AuthModal.vue'
interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
}

const props = defineProps<{
  list: FieldType[]
  isChatMode?: false
  nodeInfo?: any
}>()
// 响应式数据
const localData = ref<any>({ ...props.nodeInfo })
const defaultList = [
  {
    label: '',
    required: false,
    readonly: true,
    type: 'string',
    variable: 'sys.user_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'array[file]',
    variable: 'sys.files'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.app_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_run_id'
  }
]

onMounted(() => {
  console.log(
    'http：',
    props.list.map((e) => {
      console.log(e)
    })
  )
})

const UNIQUE_ID_PREFIX = 'key-value-'
const strToKeyValueList = (value: string) => {
  return value.split('\n').map((item) => {
    const [key, ...others] = item.split(':')
    return {
      id: uniqueId(UNIQUE_ID_PREFIX),
      key: key.trim(),
      value: others.join(':').trim()
    }
  })
}

interface ValueKeyItem {
  id: string
  value: string
  key: string
  type?: string
}

interface Body {
  type: string
  data: ValueKeyItem[]
}

// headers的内容
const headersData = ref<ValueKeyItem[]>([])
// params
const allTypes = [
  BodyType.none,
  BodyType.formData,
  BodyType.xWwwFormUrlencoded,
  BodyType.json,
  BodyType.rawText,
  BodyType.binary
]

const handleChangeBodyType = () => {
  props.nodeInfo.body.data = [
    {
      id: '',
      key: '',
      type: '',
      value: ''
    }
  ]
}
type OptionItem = {
  label: string
  value: string
}
const options = ref<OptionItem[]>([])
const paramsData = ref<ValueKeyItem[]>([])

const showAuthModal = ref(false)
const handleAuthConfig = () => {
  showAuthModal.value = true
}
const handleAuthUpdate = (auth: any) => {
  localData.value.authorization = auth
  // handleDataChange()
}
// nodeInfo更新后，更新form表单内容
watch(
  () => props.nodeInfo,
  () => {
    const nodeData = props.nodeInfo
    // TODO-todo：这个需要处理，兼容save的场景。
    headersData.value = nodeData.headers
      ? strToKeyValueList(nodeData.headers)
      : [{ id: '', key: '', type: '', value: '' }]
    paramsData.value = nodeData.params ? strToKeyValueList(nodeData.params) : [{ id: '', key: '', type: '', value: '' }]
  },
  { deep: true, immediate: true }
)
</script>
<style scoped lang="scss">
//windicss没有生效？
.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

// 公共样式，后续拿走
.arco-divider.arco-divider-horizontal {
  margin: var(--margin) 0;
}
</style>
