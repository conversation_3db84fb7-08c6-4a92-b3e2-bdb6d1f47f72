<template>
  <div class="manual-evaluation">
    <div class="evaluation-content">
      <div class="evaluation-header">
        <h3 class="evaluation-title">人工评测</h3>
        <a-tooltip content="人工评测说明">
          <icon-question-circle class="question-icon" />
        </a-tooltip>
      </div>

      <div class="evaluation-description">
        <div class="evaluation-image">
          <img src="https://via.placeholder.com/1566x865" alt="评测界面预览" />
        </div>
      </div>

      <div class="evaluation-note">
        <div class="note-icon">
          <icon-user-group class="user-icon" />
        </div>
        <div class="note-text">开始人工评测</div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="action-buttons">
      <a-button @click="handlePrev">上一步</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { EvalTaskSaveDTO } from '@/apis/evaluation/task-types'

const props = defineProps({
  formData: {
    type: Object as () => EvalTaskSaveDTO,
    required: true
  }
})

const emit = defineEmits(['prev'])

// 上一步
const handlePrev = () => {
  emit('prev')
}
</script>

<style scoped lang="scss">
.manual-evaluation {
  width: 100%;
  max-width: 1000px;
  margin: 0 auto;
  padding-bottom: 60px;
  display: flex;
  flex-direction: column;
}

.evaluation-content {
  margin-bottom: 30px;
  border: 1px solid #d0d1dd;
  border-radius: 8px;
  overflow: hidden;
}

.evaluation-header {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #e5e5e7;

  .evaluation-title {
    font-size: 14px;
    font-weight: 400;
    color: #000;
    margin: 0;
    margin-right: 8px;
  }

  .question-icon {
    color: #5048f8;
    cursor: pointer;
  }
}

.evaluation-description {
  padding: 20px;
  display: flex;
  justify-content: center;

  .evaluation-image {
    width: 100%;
    max-width: 1566px;
    overflow: hidden;

    img {
      width: 100%;
      height: auto;
      display: block;
      border-radius: 4px;
    }
  }
}

.evaluation-note {
  display: flex;
  align-items: center;
  padding: 15px 20px;

  .note-icon {
    margin-right: 5px;

    .user-icon {
      color: #5048f8;
      font-size: 16px;
    }
  }

  .note-text {
    font-size: 14px;
    color: #5048f8;
  }
}

/* 底部按钮 */
.action-buttons {
  margin-top: 40px;
  display: flex;
  justify-content: flex-start;

  .arco-btn {
    border-radius: 6px;
  }
}
</style>
