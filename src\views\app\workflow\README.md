## Workflow使用说明


### 整体文件位置：src/views/app/workflow

```ts
workflow/
├── SpecialEdge.vue //临时文件
├── controls
│   └── index.vue //工具栏组件
├── index.vue //vue-flow 画布
├── nodes
│   ├── CustomNode.vue // 临时文件 自定义节点组件
│   ├── EndNode.vue // 结束节点
│   ├── StartNode.vue //开始节点
│   ├── node-list.vue //节点选择器
│   ├── panel.vue //节点编辑抽屉
│   └── start // 开始节点信息文件
│       ├── Info.vue //
│       └── StartNode.vue // 开始节点内容
│    └── end // 结束节点信息文件
│       ├── Info.vue //
│       └── EndNode.vue // 结束节点内容
│   └── https //请求节点
│   └── if-else //条件判断节点
│   ..... //后面根据节点名称创建文件
└── nodes-info
    └── start.vue

```
## 主目录： src/views/app/workflow/index.vue
##### 在文件中引入对应的节点组件，引入的组件名称要对应，vue-flow可自动识别，例如： `StartNode`  组件，在数据中对应  `type: 'start'`, start自动匹配StartNode节点组件，所以组件命名为 `type的value值` + `Node`
```js
<template>
  <div id="parentNode" style="width: 100%; height: 100%; position: relative; overflow: hidden;">
    <VueFlow @click="flowclick" :nodes="nodes" :edges="edges" @node-click="onNodeClick" >
      <template #node-start="StartNodeProps">
        <StartNode v-bind="StartNodeProps" />
      </template>
      <template #node-custom="customNodeProps">
        <CustomNode @addNode="addNode" v-bind="customNodeProps" />
      </template>
      <template #node-end="EndNodeProps">
        <EndNode v-bind="EndNodeProps" />
      </template>
      <template #edge-special="specialEdgeProps">
        <SpecialEdge v-bind="specialEdgeProps" />
      </template>
      <Background
      
        :gap="[14, 14]"
        :size="2"
        pattern-color="#eaeaea"
        class="vue-flow__background"
      />
      <MiniMap
        position="bottom-left"
        pannable
        zoomable
        class="vue-flow__minimap"
        :style="{
          width: 102,
          height: 72,
          bottom: '40px',
        }"
      />
      <Controls/>
      <Panel ref="PaneDrawer" popup-container="#parentNode" />
    </VueFlow>
  </div>
    
</template>

<script setup lang="ts">
import CustomNode from './nodes/CustomNode.vue'
import StartNode from './nodes/start/StartNode.vue'
import EndNode from './nodes/EndNode.vue'
const nodes = ref<Node[]>([
  {
    id: '1',
    type: 'start',
    position: { x: 10, y: 200 },
    data: { label: '开始',color: '#f79009',icon: 'playArrow' },
  },
  {
    id: '2',
    type: 'custom',
    position: { x: 300, y: 200 },
    data: { label: '知识检索',color: '#17b26a',icon: 'book' },
  },

  {
    id: '3',
    type: 'custom',
    position: { x: 700, y: 200 },
    data: { label: '条件分支', color: '#06aed4',icon: 'icon-nodejs' },
  },

  {
    id: '4',
    type: 'end', // <-- this is the custom node type name
    position: { x: 1000, y: 200},
    data: {
      label: '结束',
      hello: 'world',
      color: '#f79009',
      icon: 'icon-nodejs',
    },
  },
])
</script>

```
### 节点信息抽屉：src/views/app/workflow/nodes/panel.vue
#### 引入对应的节点信息文件， `<Start v-if="nodeInfo.type == 'start'" />` 根据type值做判断即可
```js 
<template>
  <a-drawer popup-container="#parentNode" :visible="visible" @ok="handleOk" @cancel="handleCancel" :footer="false"
            :mask="false" :width="400" style="left: auto; right: 5px; top: 10px; bottom: 10px;"
            :drawerStyle="{ borderRadius: '8px', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)', }">
    <template #header>
      <div class="flex items-center justify-between" style="width: 100%">
        <span>{{ nodeInfo.label }}</span>
        ....
      </div>
    </template>
    <template #title> Title </template>
    <div>
      <Start v-if="nodeInfo.type == 'start'" />
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import Start from './start/Info.vue';
```

#### 总结： 
1. 1、在nodesl目录下创建对应的节点文件
2. 2、在src/views/app/workflow/index.vue 中引入 xxxNode 组件
3. 3、节点编辑在src/views/app/workflow/nodes/panel.vue 引入各节点目录下info信息，并根据条件判断，后续会优化引入方式

```js 
blocks: {
  'start': '开始',
  'end': '结束',
  'answer': '直接回复',
  'llm': 'LLM',
  'knowledge-retrieval': '知识检索',
  'question-classifier': '问题分类器',
  'if-else': '条件分支',
  'code': '代码执行',
  'template-transform': '模板转换',
  'http-request': 'HTTP 请求',
  'variable-assigner': '变量赋值器',
  'variable-aggregator': '变量聚合器',
  'assigner': '变量赋值',
  'iteration-start': '迭代开始',
  'iteration': '迭代',
  'parameter-extractor': '参数提取器',
  'document-extractor': '文档提取器',
  'list-operator': '列表操作',
  'agent': 'Agent',
  'loop-start': '循环开始',
  'loop': '循环',
  'loop-end': '退出循环',
  },
  blocksAbout: {
    'start': '定义一个 workflow 流程启动的初始参数',
    'end': '定义一个 workflow 流程的结束和结果类型',
    'answer': '定义一个聊天对话的回复内容',
    'llm': '调用大语言模型回答问题或者对自然语言进行处理',
    'knowledge-retrieval': '允许你从知识库中查询与用户问题相关的文本内容',
    'question-classifier': '定义用户问题的分类条件，LLM 能够根据分类描述定义对话的进展方式',
    'if-else': '允许你根据 if/else 条件将 workflow 拆分成两个分支',
    'code': '执行一段 Python 或 NodeJS 代码实现自定义逻辑',
    'template-transform': '使用 Jinja 模板语法将数据转换为字符串',
    'http-request': '允许通过 HTTP 协议发送服务器请求',
    'variable-assigner': '将多路分支的变量聚合为一个变量，以实现下游节点统一配置。',
    'assigner': '变量赋值节点用于向可写入变量（例如会话变量）进行变量赋值。',
    'variable-aggregator': '将多路分支的变量聚合为一个变量，以实现下游节点统一配置。',
    'iteration': '对列表对象执行多次步骤直至输出所有结果。',
    'loop': '循环执行一段逻辑直到满足结束条件或者到达循环次数上限。',
    'loop-end': '相当于“break” 此节点没有配置项，当循环体内运行到此节点后循环终止。',
    'parameter-extractor': '利用 LLM 从自然语言内推理提取出结构化参数，用于后置的工具调用或 HTTP 请求。',
    'document-extractor': '用于将用户上传的文档解析为 LLM 便于理解的文本内容。',
    'list-operator': '用于过滤或排序数组内容。',
    'agent': '调用大型语言模型回答问题或处理自然语言',
  },
  ```