import { useWorkflowStore } from '@/stores'
import type { TextReplaceResponse } from '@/views/app/workflow/types/workflow'

export const useWorkflowTextReplace = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowTextReplace = (params: TextReplaceResponse) => {
    const {
      data: { text }
    } = params
    const { workflowRunningData, setWorkflowRunningData } = workflowStore

    const workflowData = workflowRunningData

    workflowData.resultText = text

    setWorkflowRunningData(workflowData)
  }

  return {
    handleWorkflowTextReplace
  }
}
