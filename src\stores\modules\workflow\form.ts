import type { TransferMethod } from '@/views/app/workflow/types/app'
import type { FileResponse, HistoryWorkflowData, NodeTracing } from '@/views/app/workflow/types/workflow'
import { defineStore } from 'pinia'

export type RunFile = {
  type: string
  transfer_method: TransferMethod[]
  url?: string
  upload_file_id?: string
  related_id?: string
}

const storeSetup = () => {
  const inputs = ref<Record<string, string>>()
  const files = ref<RunFile[]>()

  const setInputs = (v: Record<string, string>) => {
    inputs.value = v
  }
  // 保存运行时的数据
  const setFiles = (v: RunFile[]) => {
    files.value = v
  }

  return {
    inputs,
    files,
    setInputs,
    setFiles
  }
}

export const useFormStore = defineStore('formStore', storeSetup, { persist: false })
