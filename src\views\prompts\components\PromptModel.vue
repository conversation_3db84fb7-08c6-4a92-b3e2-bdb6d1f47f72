<template>
  <div class="model">
    <div class="model-header font-[500] pb-4">模型配置</div>
    <div class="model-form">
      <AiForm ref="formRef" v-model="modelForm" :columns="modelColumns" />
      <a-divider :margin="10" />
      <div class="model-version">
        <div class="model-header font-[500] mb-4">版本信息</div>
        <div class="model-version-desc">
          <a-descriptions :column="1" size="medium" align="right" class="general-description">
            <a-descriptions-item label="当前版本">{{ versionForm.version || '-' }}</a-descriptions-item>
            <a-descriptions-item label="创建者">{{ versionForm.createBy || '-' }}</a-descriptions-item>
            <a-descriptions-item label="创建时间">{{ versionForm.createTime || '-' }}</a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
      <a-divider :margin="10" />
      <div class="model-help">
        <div class="model-header font-[500] mb-4">帮助信息</div>
        <a-textarea
          v-model="modelForm.helpInfo"
          placeholder="添加使用说明或注意事项"
          :auto-size="{
            minRows: 5,
            maxRows: 8
          }"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type ColumnItem } from '@/components/AiForm'

defineOptions({
  name: 'PromptModel'
})
const modelForm = ref({
  model: 'gpt-3.5-turbo',
  maxTokens: 4096,
  temperature: 0.7,
  topP: 0.9,
  helpInfo: ''
})

const versionForm = ref({
  version: '',
  createBy: '',
  createTime: ''
})

const modelColumns: ColumnItem[] = [
  {
    label: '模型',
    field: 'type',
    type: 'select',
    span: 24,
    props: {
      options: [
        { label: '场景提示词', value: 1 },
        { label: '提示词块', value: 2 }
      ],
      multiple: false,
      allowClear: true
    }
  },
  {
    label: '最大回复长度',
    field: 'maxTokens',
    type: 'slider',
    span: 24,
    props: {
      max: 8192,
      min: 0,
      showInput: true
    }
  },
  {
    label: '生成随机性(Temperature)',
    field: 'temperature',
    type: 'slider',
    span: 24,
    props: {
      max: 2,
      min: 0,
      step: 0.1,
      showInput: true
    }
  },
  {
    label: 'Top P',
    field: 'topP',
    type: 'slider',
    span: 24,
    props: {
      max: 1,
      min: 0,
      step: 0.1,
      showInput: true
    }
  }
]

defineExpose({
  modelForm
})
</script>

<style scoped lang="scss">
.model-form {
  height: 100%;
  overflow-y: auto;
  padding-bottom: 40px;
  :deep(.arco-form) {
    .arco-slider {
      display: flex;
      .arco-slider-track::before {
        height: 4px;
        border-radius: 4px;
      }
      .arco-slider-bar {
        height: 4px;
        border-radius: 4px;
      }
    }
  }
  :deep(.arco-descriptions) {
    .arco-descriptions-row {
      .arco-descriptions-item-label {
        font-weight: 400;
      }
    }
  }
}
</style>
