<template>
  <div class="create-dataset">
    <div class="create-header">
      <div class="header-content">
        <div class="title">
          <a-button type="text" class="back-btn" @click="goBack">
            <template #icon><icon-left /></template>
            知识库
          </a-button>
        </div>
        <div class="steps-nav">
          <a-steps :current="currentStep" :small="true" :changeable="true" @change="stepChange">
            <a-step title="选择数据源" />
            <a-step title="文本分段与清洗" />
            <a-step title="处理并完成" />
          </a-steps>
        </div>
      </div>
    </div>
    <div class="create-content">
      <!-- 步骤1：选择数据源 -->
      <step-one
        v-if="currentStep === 1"
        v-model:uploadedFiles="uploadedFiles"
        :is-adding-document="isAddingDocument"
        :dataset-id="fromDatasetId"
        @next-step="goToNextStep"
        @create-empty-dataset="showEmptyDatasetModal"
      />
      <!-- 步骤2：文本分段与清洗 -->
      <step-second
        v-if="currentStep === 2"
        ref="stepSecondRef"
        v-model:selectedPreviewFileId="selectedPreviewFileId"
        :uploadedFiles="uploadedFiles"
        :segment-preview="segmentPreview"
        :total-segments="totalSegments"
        :preview-loading="previewLoading"
        :is-adding-document="isAddingDocument"
        :dataset-id="fromDatasetId"
        @prev-step="goToPrevStep"
        @next-step="goToNextStep"
        @preview-blocks="previewBlocksWrapper"
        @prev-fetch-dataset-detail="fetchDatasetDetail"
      />
      <!-- 步骤3：处理并完成 -->
      <step-third
        v-if="currentStep === 3"
        :uploadedFiles="uploadedFiles"
        :processingStatus="processingStatus"
        :segmentMode="segmentMode"
        :normalSegmentConfig="normalSegmentConfig"
        :parentChildConfig="parentChildConfig"
        :parentMode="parentMode"
        :indexingMethod="indexingMethod"
        :searchMethod="searchMethod"
        :datasetForm="datasetForm"
        :is-adding-document="isAddingDocument"
        :dataset-id="fromDatasetId"
        :datasetDetail="datasetDetail"
      />
    </div>
    <!-- 创建空知识库弹窗 -->
    <a-modal
      v-model:visible="emptyDatasetModalVisible"
      title="创建空知识库"
      :mask-closable="false"
      :footer="false"
      width="500px"
      @cancel="emptyDatasetModalVisible = false"
      @ok="confirmCreateEmptyDataset"
    >
      <div class="empty-dataset-modal">
        <p class="empty-dataset-desc">空知识库中还没有文档，你可以在今后任何时候上传文档至该知识库。</p>
        <div class="form-item">
          <div class="form-label">知识库名称</div>
          <a-input v-model="emptyDatasetName" placeholder="请输入知识库名称" class="settings-input" />
        </div>
        <div class="modal-footer">
          <a-button @click="emptyDatasetModalVisible = false">取消</a-button>
          <a-button type="primary" :loading="creatingEmptyDataset" @click="confirmCreateEmptyDataset">创建</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, reactive, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import {
  getDatasetTags,
  createDatasetTag,
  createDataset,
  uploadFile,
  previewDocumentSegments,
  getEmbeddingModels,
  getFileContent as fetchFileContent,
  getFilePreview,
  getRerankModels,
  getDefaultRerankModel,
  initDataset,
  getDatasetProcessRule,
  getDatasetIndexingStatus,
  addDocumentsToDataset,
  createEmptyDataset,
  checkDatasetIndexingStatus,
  getDatasetDetail
} from '@/apis/datasets'
import type { DatasetTag } from '@/apis/datasets/type'
import axios from 'axios'
import StepOne from './StepOne.vue'
import StepSecond from './StepSecond.vue'
import StepThird from './StepThird.vue'

const router = useRouter()

// 判断是否是从文档管理页面跳转过来（添加文件）
const isAddingDocument = ref(false)
const fromDatasetId = ref('')

// 步骤控制
const currentStep = ref(1)

// 确保组件挂载时初始步骤为1
onMounted(() => {
  currentStep.value = 1
  fetchTags()
  fetchEmbeddingModels()
  fetchRerankModels()
  // 判断是否从文档管理页面跳转来（添加文件到已有知识库）
  const route = router.currentRoute.value
  console.log('route==>', route, route.query)
  if (route.query.from === 'documents' && route.query.datasetId) {
    isAddingDocument.value = true
    fromDatasetId.value =
      typeof route.query.datasetId === 'string'
        ? route.query.datasetId
        : Array.isArray(route.query.datasetId)
          ? route.query.datasetId[0] || ''
          : ''
    console.log('正在向知识库添加文件，知识库ID:', fromDatasetId.value)
  }
  // 如果已有上传文件，默认选择第一个文件进行预览
  if (uploadedFiles.value.length > 0 && uploadedFiles.value[0].response?.id) {
    selectedPreviewFileId.value = uploadedFiles.value[0].response.id
  }
})

const goToNextStep = () => {
  if (currentStep.value < 3) {
    // 如果当前是第二步，执行保存并处理逻辑
    if (currentStep.value === 2) {
      saveAndProcess()
    } else {
      // 从第一步到第二步的过渡
      if (currentStep.value === 1) {
        // 确保已选择第一个文件
        const firstFileId = uploadedFiles.value.find((file) => file?.response?.id)?.response?.id
        if (firstFileId) {
          selectedPreviewFileId.value = firstFileId
          console.log('步骤1->2: 预设首个文件ID:', firstFileId)
        }
      }
      // 更新步骤
      currentStep.value++
      // 从步骤1到步骤2的自动预览
      if (currentStep.value === 2 && uploadedFiles.value.length > 0) {
        // 确保选择了第一个文件
        const firstFileId = uploadedFiles.value.find((file) => file?.response?.id)?.response?.id
        if (firstFileId && selectedPreviewFileId.value === firstFileId) {
          // 在DOM更新后触发预览
          nextTick(() => {
            console.log('步骤1->2: 自动触发预览，文件ID:', firstFileId)
            // 准备预览参数
            const params = {
              info_list: {
                data_source_type: 'upload_file',
                file_info_list: {
                  file_ids: [firstFileId]
                }
              },
              indexing_technique: indexingMethod.value,
              process_rule: {
                rules: {
                  pre_processing_rules: [
                    {
                      id: 'remove_extra_spaces',
                      enabled: true
                    },
                    {
                      id: 'remove_urls_emails',
                      enabled: false
                    }
                  ],
                  segmentation: {
                    separator: normalSegmentConfig.separator,
                    max_tokens: normalSegmentConfig.maxLength,
                    chunk_overlap: normalSegmentConfig.minLength
                  }
                },
                mode: 'custom'
              },
              doc_form: 'text_model',
              doc_language: 'Chinese Simplified'
            }
            // 调用预览方法
            previewBlocksWrapper(params)
          })
        }
      }
    }
  }
}

const goToPrevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

// 点击步骤条直接切换步骤
const stepChange = (step: number) => {
  // 直接设置当前步骤，简化逻辑
  currentStep.value = step
}

// 为Arco Design的Upload组件定义文件项接口
interface UploadItem {
  uid: string
  name: string
  file: File
  status: string
  percent?: number
  response?: any
  url?: string
  size: number
}

const uploadedFiles = ref<UploadItem[]>([])

// 获取标签列表
const fetchTags = async () => {
  try {
    const response = await getDatasetTags()
    console.log(response)
    availableTags.value = response
  } catch (error) {
    console.error('获取标签列表失败:', error)
    Message.error('获取标签列表失败')
  }
}

// 标签数据
const availableTags = ref<DatasetTag[]>([])
const selectedTags = ref<string[]>([])

// 空知识库相关
const emptyDatasetModalVisible = ref(false)
const emptyDatasetName = ref('')
const creatingEmptyDataset = ref(false)

// 预览文档分段效果
const segmentPreview = ref<any[]>([])
const previewLoading = ref(false)
const totalSegments = ref(0)

// 文件预览相关状态
const selectedPreviewFileId = ref<string>('')
const previewingBlocks = ref(false)
const blocksPreviewContent = ref<any[]>([])
const blocksPreviewError = ref(false)
const blocksPreviewTotal = ref(0)

// 知识库详情（用于添加文档模式）
const datasetDetail = ref({})

// 添加StepSecond的ref引用
const stepSecondRef = ref<any>(null)

// 显示创建空知识库模态框
const showEmptyDatasetModal = () => {
  emptyDatasetModalVisible.value = true
}

// 确认创建空知识库
const confirmCreateEmptyDataset = async () => {
  if (!emptyDatasetName.value.trim()) {
    Message.warning('请输入知识库名称')
    return
  }
  creatingEmptyDataset.value = true
  try {
    // 使用createEmptyDataset API创建空知识库
    await createEmptyDataset({
      name: emptyDatasetName.value.trim(),
      description: '',
      tag_ids: []
    })
    Message.success('创建空知识库成功')
    emptyDatasetModalVisible.value = false
    router.push('/datasets/index')
  } catch (error) {
    console.error('创建空知识库失败:', error)
    Message.error('创建空知识库失败')
  } finally {
    creatingEmptyDataset.value = false
  }
}

// 获取Embedding模型列表
const embeddingModelOptions = ref<{ value: string; label: string }[]>([])
const embeddingModel = ref('')

const fetchEmbeddingModels = async () => {
  try {
    const response = await getEmbeddingModels()
    const providers = response.data
    const options: { value: string; label: string }[] = []
    providers?.forEach((provider) => {
      provider.models.forEach((model) => {
        options.push({
          value: model.model,
          label: model.label.zh_Hans
        })
      })
    })
    embeddingModelOptions.value = options
    // 默认选择第一个模型
    if (options.length > 0 && !embeddingModel.value) {
      embeddingModel.value = options[0].value
    }
  } catch (error) {
    console.error('获取Embedding模型列表失败:', error)
    Message.error('获取Embedding模型列表失败')
  }
}

// 获取分段预览
const getSegmentPreview = async () => {
  if (!selectedPreviewFileId.value) {
    Message.warning('请先选择要预览的文件')
    return
  }
  previewLoading.value = true
  try {
    const params: any = {
      info_list: {
        data_source_type: 'upload_file' as 'upload_file',
        file_info_list: {
          file_ids: [selectedPreviewFileId.value]
        }
      },
      indexing_technique: indexingMethod.value,
      process_rule: {
        rules: {
          pre_processing_rules: [
            {
              id: 'remove_extra_spaces',
              enabled: true
            },
            {
              id: 'remove_urls_emails',
              enabled: false
            }
          ],
          segmentation: {
            separator: '\n\n',
            max_tokens: segmentSettings.maxLength
          }
        },
        mode: 'custom'
      },
      doc_form: 'text_model',
      doc_language: 'Chinese Simplified'
    }
    const response = await previewDocumentSegments(params)
    segmentPreview.value = response.preview
    totalSegments.value = response.total_segments
  } catch (error) {
    console.error('获取分段预览失败:', error)
    Message.error('获取分段预览失败')
  } finally {
    previewLoading.value = false
  }
}

// Rerank模型列表
const rerankModels = ref([
  { model: 'netease-youdao/bce-reranker-base_v1', label: { zh_Hans: 'Netease Youdao Reranker' } }
  // 可以添加更多模型
])

// 获取Rerank模型列表
const fetchRerankModels = async () => {
  try {
    // 获取模型列表
    const modelsResponse = await getRerankModels()
    // 处理模型数据
    if (modelsResponse.data && modelsResponse.data.length > 0) {
      const allModels: any[] = []
      // 遍历所有提供商及其模型
      modelsResponse.data.forEach((provider) => {
        if (provider.models && provider.models.length > 0) {
          provider.models.forEach((model) => {
            allModels.push(model)
          })
        }
      })
      // 更新模型列表
      rerankModels.value = allModels
    }
    // 获取默认模型
    const defaultModelResponse = await getDefaultRerankModel()
    if (defaultModelResponse.data && defaultModelResponse.data.model) {
      // 更新所有使用Rerank的设置
      vectorSettings.rerankModel = defaultModelResponse.data.model
      fullTextSettings.rerankModel = defaultModelResponse.data.model
      hybridSettings.rerankModel = defaultModelResponse.data.model
    }
  } catch (error) {
    console.error('获取Rerank模型列表失败:', error)
    Message.error('获取Rerank模型列表失败')
  }
}

// 监听uploadedFiles变化，自动选择第一个文件
watch(uploadedFiles, (files) => {
  if (files.length > 0 && files[0].response?.id && !selectedPreviewFileId.value) {
    selectedPreviewFileId.value = files[0].response.id
    // 自动触发预览
    if (currentStep.value === 2) {
      getSegmentPreview()
    }
  }
})

// 返回
const goBack = () => {
  router.push('/datasets/index')
}

// 步骤2：分段设置
const segmentMode = ref('normal') // 'normal' 或 'parent-child'

// 通用分段配置
const normalSegmentConfig = reactive({
  separator: '\\n\\n',
  maxLength: 1024,
  minLength: 50
})

// 父子分段配置
const parentChildConfig = reactive({
  parent: {
    separator: '\\n\\n',
    maxLength: 500
  },
  child: {
    separator: '\\n',
    maxLength: 200
  }
})

// 父块模式
const parentMode = ref('paragraph') // 'paragraph' 或 'fulltext'

const segmentSettings = reactive({
  mode: 'byParagraph',
  maxLength: 1024,
  minLength: 50,
  separator: '\\n\\n',
  rules: '移除连续空白字符，换行符和制表符'
})

const processingRules = reactive({
  removeSpaces: true,
  removeUrls: false
})

const indexingMethod = ref('high_quality')
const searchMethod = ref('hybrid') // 'vector', 'fulltext', 'hybrid'

// 基础数据
const datasetForm = ref({
  name: '',
  description: '',
  files: [] as File[],
  fileIds: [] as string[],
  datasetId: ''
})

// 全文检索设置
const fullTextSettings = reactive({
  useRerank: true,
  rerankModel: 'netease-youdao/bce-reranker-base_v1',
  topK: 3,
  useScoreThreshold: false,
  scoreThreshold: 0.5
})

// 混合检索设置
const hybridSettings = reactive({
  mode: 'rerank', // 'weight' or 'rerank'
  vectorWeight: 0.5,
  rerankModel: 'netease-youdao/bce-reranker-base_v1',
  topK: 3,
  useScoreThreshold: false,
  scoreThreshold: 0.5
})

// 向量检索设置
const vectorSettings = reactive({
  useRerank: true,
  rerankModel: 'netease-youdao/bce-reranker-base_v1',
  topK: 3,
  useScoreThreshold: false,
  scoreThreshold: 0.5
})

// 文档处理状态
const processingStatus = reactive({
  loading: true,
  percent: 0,
  completed: false,
  error: false,
  errorMessage: '',
  datasetId: '',
  batchId: ''
})

// 定时检查器
let processingStatusChecker: any = null

// 声明一个用于获取embedding模型提供商的函数
const getEmbeddingProviderFromModel = (modelName: string) => {
  const model = embeddingModelOptions.value.find((option) => option.value === modelName)
  // 从模型名称中提取提供商信息，格式通常是 "provider/model_name"
  if (model && modelName.includes('/')) {
    return modelName.split('/')[0]
  }
  // 如果无法确定，返回默认值
  // return "langgenius/siliconflow/siliconflow";
  return ''
}

// 声明一个用于获取rerank模型提供商的函数
const getRerankProviderFromModel = (modelName: string) => {
  const model = rerankModels.value.find((model) => model.model === modelName)
  // 从模型名称中提取提供商信息，格式通常是 "provider/model_name"
  if (model && modelName.includes('/')) {
    return modelName.split('/')[0]
  }
  // 如果无法确定，返回默认值
  // return "langgenius/siliconflow/siliconflow";
  return ''
}

// 获取语言对应的显示文本
const getLanguageDisplayText = (langCode: string) => {
  switch (langCode) {
    case 'zh_CN':
      return 'Chinese Simplified'
    case 'en_US':
      return 'English'
    default:
      return 'Chinese Simplified'
  }
}

// 保存并处理知识库
const saveAndProcess = async (params = {}) => {
  // 更新fileIds，确保获取最新状态
  datasetForm.value.fileIds = uploadedFiles.value
    .filter((file) => file.response && file.response.id)
    .map((file) => file.response.id)
  if (!datasetForm.value.fileIds.length) {
    Message.warning('请先上传文件')
    return
  }
  try {
    // 设置知识库名称为第一个文件名（去除扩展名）
    if (uploadedFiles.value.length > 0) {
      datasetForm.value.name = uploadedFiles.value[0].name.replace(/\.\w+$/, '')
    } else {
      datasetForm.value.name = '未命名知识库'
    }
    // 获取StepSecond组件的所有设置
    const settings = stepSecondRef.value?.getSettings?.() || {}
    // 获取文档语言
    const docLanguage = getLanguageDisplayText(settings.qaLanguage || 'zh_CN')
    // 构建保存并处理知识库参数
    const requestParams = {
      data_source: {
        type: 'upload_file',
        info_list: {
          data_source_type: 'upload_file' as 'upload_file',
          file_info_list: {
            file_ids: datasetForm.value.fileIds
          }
        }
      },
      indexing_technique: indexingMethod.value,
      process_rule: buildProcessRule(),
      doc_form: segmentMode.value === 'normal' ? 'text_model' : 'hierarchical_model',
      doc_language: docLanguage, // 使用动态获取的语言设置
      retrieval_model: buildRetrievalModel(),
      embedding_model: embeddingModel.value,
      embedding_model_provider: getEmbeddingProviderFromModel(embeddingModel.value) // 从模型中动态获取提供商
    }
    console.log('保存并处理参数:', JSON.stringify(requestParams))
    // 检查是否是添加文件到已有知识库
    let response
    if (isAddingDocument.value && fromDatasetId.value) {
      // 使用向已有知识库添加文件的API
      console.log('向已有知识库添加文件:', fromDatasetId.value)
      response = await addDocumentsToDataset(fromDatasetId.value, requestParams)
      console.log('添加文件响应:', response)
      // 创建一个与initDataset返回格式兼容的对象
      const compatResponse = {
        dataset: {
          id: fromDatasetId.value
          // 其他字段在后续处理中不会使用，可以省略
        },
        documents: response.documents,
        batch: response.batch
      }
      response = compatResponse
    } else {
      // 使用创建新知识库的API
      response = await initDataset(requestParams)
      console.log('创建知识库响应:', response)
    }
    // 保存返回的dataset信息
    const { dataset, documents, batch } = response
    // 保存数据集ID便于后续使用
    datasetForm.value.datasetId = dataset.id
    // 保存状态信息用于轮询
    processingStatus.datasetId = dataset.id
    processingStatus.batchId = batch
    processingStatus.loading = true
    processingStatus.percent = 0
    // 调用后续接口1: 根据documents ID获取处理规则
    if (documents && documents.length > 0) {
      const documentId = documents[0].id
      await getDatasetProcessRule(documentId)
    }
    // 调用后续接口2: 获取索引状态
    if (dataset.id && batch) {
      await checkIndexingStatus(dataset.id, batch)
      // 开始定时检查处理状态
      startProcessingStatusChecker()
    }
    // 进入第三步
    currentStep.value++
    // 显示成功消息
    if (isAddingDocument.value) {
      Message.success('文件添加成功，正在处理中...')
    } else {
      // Message.success('知识库创建成功，正在处理中...');
    }
  } catch (error) {
    console.error('保存并处理知识库失败:', error)
    Message.error('保存并处理知识库失败')
    processingStatus.error = true
    processingStatus.errorMessage = '保存并处理知识库失败'
  }
}

// 检查索引状态
const checkIndexingStatus = async (datasetId: string, batchId: string) => {
  try {
    const response = await getDatasetIndexingStatus(datasetId, batchId)
    if (response.data && response.data.length > 0) {
      const status = response.data[0]
      // 计算进度百分比
      if (status.total_segments > 0) {
        processingStatus.percent = Math.floor(status.completed_segments / status.total_segments)
      }
      // 检查是否完成
      if (status.completed_at) {
        processingStatus.completed = true
        processingStatus.loading = false
        stopProcessingStatusChecker()
      } else if (status.error) {
        processingStatus.error = true
        processingStatus.errorMessage = status.error
        processingStatus.loading = false
        stopProcessingStatusChecker()
      }
    }
  } catch (error) {
    console.error('获取索引状态失败:', error)
  }
}

// 开始定时检查处理状态
const startProcessingStatusChecker = () => {
  // 清除可能存在的旧定时器
  stopProcessingStatusChecker()
  // 创建新的定时器，每5秒检查一次
  processingStatusChecker = setInterval(() => {
    if (processingStatus.datasetId && processingStatus.batchId) {
      checkIndexingStatus(processingStatus.datasetId, processingStatus.batchId)
    }
  }, 3000)
}

// 停止定时检查
const stopProcessingStatusChecker = () => {
  if (processingStatusChecker) {
    clearInterval(processingStatusChecker)
    processingStatusChecker = null
  }
}

// 组件卸载时清除定时器
onUnmounted(() => {
  stopProcessingStatusChecker()
})

// 构建处理规则
const buildProcessRule = () => {
  let processRule: any = {
    rules: {
      pre_processing_rules: [
        {
          id: 'remove_extra_spaces',
          enabled: processingRules.removeSpaces
        },
        {
          id: 'remove_urls_emails',
          enabled: processingRules.removeUrls
        }
      ]
    },
    mode: segmentMode.value === 'normal' ? 'custom' : 'hierarchical'
  }
  if (segmentMode.value === 'normal') {
    processRule.rules.segmentation = {
      separator: normalSegmentConfig.separator,
      max_tokens: normalSegmentConfig.maxLength,
      chunk_overlap: normalSegmentConfig.minLength
    }
  } else {
    // 父子分段模式
    processRule.rules.segmentation = {
      separator: parentChildConfig.parent.separator,
      max_tokens: parentChildConfig.parent.maxLength
    }
    processRule.rules.parent_mode = parentMode.value === 'fulltext' ? 'full-doc' : 'paragraph'
    processRule.rules.subchunk_segmentation = {
      separator: parentChildConfig.child.separator,
      max_tokens: parentChildConfig.child.maxLength
    }
  }
  return processRule
}

// 构建检索模型配置
const buildRetrievalModel = () => {
  let retrieval: any = {
    top_k: 6,
    score_threshold_enabled: false,
    score_threshold: 0.5
  }
  // 根据选择的检索方法设置配置
  switch (searchMethod.value) {
    case 'vector':
      retrieval.search_method = 'vector_search'
      retrieval.reranking_enable = vectorSettings.useRerank
      if (vectorSettings.useRerank) {
        retrieval.reranking_model = {
          reranking_provider_name: getRerankProviderFromModel(vectorSettings.rerankModel),
          reranking_model_name: vectorSettings.rerankModel
        }
        retrieval.reranking_mode = 'standalone'
        retrieval.score_threshold_enabled = vectorSettings.useScoreThreshold
        retrieval.score_threshold = vectorSettings.scoreThreshold
        retrieval.top_k = vectorSettings.topK
      }
      break
    case 'fulltext':
      retrieval.search_method = 'full_text_search'
      retrieval.reranking_enable = fullTextSettings.useRerank
      if (fullTextSettings.useRerank) {
        retrieval.reranking_model = {
          reranking_provider_name: getRerankProviderFromModel(fullTextSettings.rerankModel),
          reranking_model_name: fullTextSettings.rerankModel
        }
        retrieval.reranking_mode = 'standalone'
        retrieval.score_threshold_enabled = fullTextSettings.useScoreThreshold
        retrieval.score_threshold = fullTextSettings.scoreThreshold
        retrieval.top_k = fullTextSettings.topK
      }
      break
    case 'hybrid':
      retrieval.search_method = 'hybrid_search'
      retrieval.reranking_enable = true
      retrieval.reranking_model = {
        reranking_provider_name: getRerankProviderFromModel(hybridSettings.rerankModel),
        reranking_model_name: hybridSettings.rerankModel
      }
      if (hybridSettings.mode === 'weight') {
        retrieval.reranking_mode = 'weighted_score'
        retrieval.weights = {
          weight_type: 'customized',
          vector_setting: {
            vector_weight: hybridSettings.vectorWeight,
            embedding_provider_name: '',
            embedding_model_name: ''
          },
          keyword_setting: {
            keyword_weight: 1 - hybridSettings.vectorWeight
          }
        }
      } else {
        retrieval.reranking_mode = 'rerank'
      }
      retrieval.score_threshold_enabled = hybridSettings.useScoreThreshold
      retrieval.score_threshold = hybridSettings.scoreThreshold
      retrieval.top_k = hybridSettings.topK
      break
  }
  return retrieval
}

// 预览块的包装方法，从StepSecond接收参数并调用API
const previewBlocksWrapper = async (params: any) => {
  try {
    previewLoading.value = true
    const response = await previewDocumentSegments(params)
    segmentPreview.value = response.preview
    totalSegments.value = response.total_segments
    return response
  } catch (error) {
    console.error('获取分段预览失败:', error)
    Message.error('获取分段预览失败')
    throw error
  } finally {
    previewLoading.value = false
  }
}

// 获取知识库详情
const fetchDatasetDetail = async (datasetId) => {
  try {
    const response = await getDatasetDetail(datasetId)
    datasetDetail.value = response
    console.log('获取知识库详情成功:', response)
  } catch (error) {
    console.error('获取知识库详情失败:', error)
    Message.error('获取知识库详情失败')
  }
}
</script>

<style scoped lang="scss">
.create-dataset {
  width: 100%;
  min-height: calc(100vh - 60px);
  display: flex;
  flex-direction: column;
  background-color: #fff;

  .create-header {
    height: 52px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--color-border-2);
    padding: 0 16px;
    margin-bottom: 24px;
    font-size: 12px;
    font-weight: 600;

    .header-content {
      display: flex;
      align-items: center;
      width: 100%;

      .title {
        display: flex;
        align-items: center;
        margin-right: 24px;

        .back-btn {
          padding: 0;
        }
      }

      .steps-nav {
        flex: 1;
        display: flex;
        justify-content: center;
        font-size: 13px;

        :deep(.arco-steps) {
          width: 100%;
          max-width: 800px;

          .arco-steps-item {
            flex: 1;

            .arco-steps-item-content {
              text-align: center;
              min-width: 100px;

              .arco-steps-item-title {
                font-size: 14px;
                white-space: nowrap;
                overflow: visible;
              }
            }

            &:not(:last-child) .arco-steps-item-line {
              width: calc(100% - 30px);
              right: -50%;
              top: 10px;
            }
          }
        }
      }
    }
  }

  .create-content {
    flex: 1;
    padding: 0 16px;
    font-size: 14px;
    max-height: calc(100vh - 76px);
    /* 减去header高度 */
    overflow: hidden;

    .step-content {
      margin-bottom: 40px;

      .content-container {
        display: flex;
        gap: 24px;
        height: calc(100vh - 120px);
        /* 设置左右容器整体高度 */
        overflow: hidden;

        .left-section,
        .right-section {
          flex: 1;
          height: 100%;
          overflow-y: auto;
          /* 允许左右两侧独立滚动 */
          padding-right: 8px;
          /* 为滚动条预留空间 */
        }

        .left-section {
          .step-title {
            font-size: 14px;
            font-weight: 600;
            line-height: 20px;
            margin-bottom: 12px;
          }

          .source-type-selection {
            margin-bottom: 24px;

            :deep(.arco-radio-group) {
              display: flex;
              gap: 16px;

              .arco-radio {
                margin-right: 0;
              }
            }

            .source-type-item {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 10px 20px;
              border: 1px solid var(--color-border-2);
              border-radius: 8px;
              transition: all 0.3s;
              cursor: pointer;

              &.active {
                border-color: var(--color-primary-light-3);
                background-color: var(--color-primary-light-1);

                .source-icon {
                  color: var(--color-primary);
                }
              }

              .source-icon {
                font-size: 18px;
                color: var(--color-text-3);
              }
            }
          }

          .source-content {
            background-color: var(--color-bg-2);
            border-radius: 12px;
            margin-bottom: 24px;
            height: auto;

            h3 {
              font-size: 14px;
              font-weight: 500;
              margin-top: 0;
              margin-bottom: 12px;
            }

            .section-desc {
              color: var(--color-text-3);
              margin-bottom: 16px;
            }

            .upload-area {
              width: 100%;
              padding: 20px 0;

              .upload-trigger {
                width: 80%;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                border: 1px dashed var(--color-border-1);
                border-radius: 8px;
                background-color: var(--color-fill-1);
                padding: 40px 0;
                cursor: pointer;
                transition: all 0.3s;

                &:hover {
                  border-color: var(--color-primary-light-2);
                  background-color: var(--color-fill-2);
                }

                .upload-icon {
                  font-size: 28px;
                  color: var(--color-text-3);
                  margin-bottom: 16px;
                }

                .upload-text {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  text-align: center;

                  .tips {
                    font-size: 14px;
                    color: var(--color-text-1);
                    margin-bottom: 8px;
                  }

                  .file-types {
                    font-size: 12px;
                    color: var(--color-text-3);
                    max-width: 500px;
                  }
                }
              }
            }

            // 上传文件列表样式
            .uploaded-files-list {
              width: 80%;

              .uploaded-file-item {
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 8px 12px;
                background-color: var(--color-fill-1);
                border-radius: 4px;
                cursor: pointer;
                transition: background-color 0.2s;
                margin-bottom: 8px;

                &:hover {
                  background-color: var(--color-fill-2);
                }

                .file-icon {
                  color: var(--color-text-3);
                  font-size: 18px;
                }

                .file-info {
                  flex: 1;
                  font-size: 14px;
                  color: var(--color-text-1);

                  .file-name {
                    font-size: 14px;
                    margin-bottom: 4px;
                  }

                  .file-size {
                    font-size: 12px;
                    color: var(--color-text-3);
                  }
                }
              }
            }

            .notion-status,
            .tool-status {
              display: flex;
              align-items: flex-start;
              flex-direction: column;
              justify-content: flex-start;
              gap: 16px;
              padding: 24px;
              background-color: var(--color-fill-1);
              width: 80%;
              border-radius: 16px;

              .notion-icon,
              .tool-icon {
                font-size: 48px;
                color: var(--color-text-3);
              }

              .notion-info,
              .tool-info {
                .notion-title,
                .tool-title {
                  font-size: 14px;
                  font-weight: 500;
                  margin-bottom: 4px;
                }

                .notion-desc,
                .tool-desc {
                  font-size: 14px;
                  color: var(--color-text-3);
                  margin-bottom: 12px;
                }
              }
            }

            .web-tools {
              margin-bottom: 24px;

              :deep(.arco-radio-group) {
                display: flex;
                gap: 16px;

                .arco-radio {
                  margin-right: 0;
                }
              }

              .web-tool-item {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 8px 16px;
                border: 1px solid var(--color-border-2);
                border-radius: 8px;
                transition: all 0.3s;
                cursor: pointer;

                &.active {
                  border-color: var(--color-primary-light-3);
                  background-color: var(--color-primary-light-1);

                  .tool-icon {
                    color: var(--color-primary);
                  }
                }

                .tool-icon {
                  font-size: 14px;
                  color: var(--color-text-3);
                }
              }
            }
          }

          .step-footer {
            display: flex;
            align-items: center;

            .empty-dataset {
              display: flex;
              align-items: center;
              gap: 8px;
              color: var(--color-text-3);
              margin-bottom: 30px;

              .icon {
                font-size: 14px;
              }
            }

            .prev-btn,
            .next-btn {
              min-width: 80px;
              height: 36px;
              border-radius: 6px;
            }
          }

          .step-footer1 {
            justify-content: flex-end;
          }
        }

        .right-section {
          .preview-container {
            background-color: var(--color-bg-2);
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 5px;
            height: auto;
            min-height: 400px;
            display: flex;
            flex-direction: column;

            .empty-preview {
              height: 400px;
              display: flex;
              justify-content: center;
              align-items: center;

              .empty-state {
                display: flex;
                flex-direction: column;
                align-items: center;
                text-align: center;

                .empty-icon {
                  font-size: 40px;
                  color: var(--color-text-3);
                  margin-bottom: 16px;
                }

                p {
                  margin: 4px 0;
                  color: var(--color-text-3);
                }

                .empty-desc {
                  font-size: 12px;
                }
              }
            }
          }

          .guide-section {
            .guide-icon {
              font-size: 24px;
              margin-bottom: 12px;
            }

            .guide-title {
              font-size: 14px;
              font-weight: 500;
              margin-bottom: 16px;
            }

            .guide-text {
              margin: 8px 0;
              color: var(--color-text-2);
            }
          }
        }
      }

      // 步骤2样式
      &.step2-content {
        .split-container {
          display: flex;
          gap: 24px;
          height: calc(100vh - 140px);
          overflow: hidden;

          .settings-panel {
            flex: 1;
            overflow-y: auto;
            padding-right: 16px;

            h3 {
              font-size: 14px;
              font-weight: 500;
              margin-top: 16px;
              margin-bottom: 12px;
              color: var(--color-text-1);
            }

            .setting-card {
              background-color: var(--color-bg-2);
              border-radius: 8px;
              padding: 16px;
              margin-bottom: 16px;

              .setting-card-header {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 8px;

                .setting-icon {
                  color: var(--color-primary);
                }

                .setting-title {
                  font-weight: 500;
                }
              }

              .setting-desc {
                font-size: 12px;
                color: var(--color-text-3);
                margin-bottom: 16px;
              }
            }

            .segment-mode-cards {
              display: flex;
              flex-direction: column;
              gap: 16px;
              margin-bottom: 24px;

              .segment-mode-card {
                border: 1px solid var(--color-border-2);
                border-radius: 8px;
                overflow: hidden;
                transition: border-color 0.3s ease;
                cursor: pointer;

                &.active {
                  border-color: var(--color-primary);
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                }

                .card-header {
                  display: flex;
                  align-items: center;
                  padding: 16px;
                  background-color: var(--color-fill-1);

                  .mode-icon {
                    width: 36px;
                    height: 36px;
                    border-radius: 50%;
                    background-color: var(--color-bg-2);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-right: 12px;

                    .icon {
                      font-size: 18px;
                      color: var(--color-text-1);
                    }
                  }

                  .mode-info {
                    flex: 1;

                    .mode-title {
                      font-weight: 500;
                      margin-bottom: 4px;
                    }

                    .mode-desc {
                      font-size: 12px;
                      color: var(--color-text-3);
                    }
                  }
                }

                .card-content {
                  padding: 16px;
                  background-color: #fff;
                  border-top: 1px solid var(--color-border-2);
                }

                .parent-child-section {
                  margin-bottom: 24px;
                  background-color: var(--color-fill-1);
                  border-radius: 6px;
                  overflow: hidden;

                  .section-header {
                    display: flex;
                    align-items: center;
                    padding: 12px 16px 0 16px;

                    // background-color: var(--color-fill-2);
                    .section-icon {
                      margin-right: 10px;

                      .icon {
                        font-size: 16px;
                        color: var(--color-text-1);
                      }
                    }

                    .section-title {
                      flex: 1;
                      font-weight: 500;
                      font-size: 14px;
                    }
                  }

                  .section-content {
                    padding: 16px;
                  }

                  .section-content1 {
                    display: flex;

                    .flex-1 {
                      flex: 1;
                    }
                  }

                  .segmentation-mode {
                    margin-bottom: 16px;

                    .mode-item {
                      display: flex;
                      align-items: flex-start;
                      padding: 12px;
                      border-radius: 6px;
                      border: 1px solid var(--color-border-2);
                      background-color: #fff;

                      &.selected {
                        border-color: var(--color-primary);
                        background-color: var(--color-primary-light-1);
                      }

                      .mode-icon {
                        margin-right: 12px;
                        color: var(--color-text-2);
                      }

                      .mode-info {
                        flex: 1;

                        .mode-name {
                          font-weight: 500;
                          margin-bottom: 4px;
                        }

                        .mode-desc {
                          font-size: 12px;
                          color: var(--color-text-3);
                          line-height: 1.5;
                        }
                      }
                    }
                  }
                }
              }
            }

            .form-item {
              margin-bottom: 16px;

              .form-label {
                font-size: 13px;
                margin-bottom: 8px;
                display: flex;
                align-items: center;

                .hint-icon {
                  font-size: 14px;
                  color: var(--color-text-3);
                  margin-left: 4px;
                  cursor: pointer;
                }
              }

              .input-with-unit {
                display: flex;
                align-items: center;

                .unit {
                  margin-left: 8px;
                  color: var(--color-text-3);
                  font-size: 13px;
                }
              }

              .form-label-with-checkbox {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 8px;

                .hint-icon {
                  font-size: 14px;
                  color: var(--color-text-3);
                  cursor: pointer;
                }
              }

              .checkbox-list {
                display: flex;
                flex-direction: column;
                gap: 8px;
              }
            }

            .form-row {
              display: flex;
              gap: 16px;
              margin-bottom: 16px;

              .flex-1 {
                flex: 1;
              }
            }

            .form-actions {
              display: flex;
              justify-content: flex-start;
              margin-top: 16px;
            }

            .index-options {
              display: flex;
              flex-direction: row;
              gap: 12px;

              .index-option {
                display: flex;
                align-items: flex-start;
                padding: 12px;
                border-radius: 6px;
                border: 1px solid var(--color-border-2);
                cursor: pointer;
                transition: all 0.2s;

                &.active {
                  border-color: var(--color-primary);
                  background-color: var(--color-primary-light-1);
                }

                .option-radio {
                  margin-right: 8px;
                  padding-top: 2px;
                }

                .option-content {
                  display: flex;
                  flex: 1;

                  .option-icon {
                    font-size: 16px;
                    margin-right: 8px;
                    color: var(--color-text-2);
                  }

                  .option-info {
                    flex: 1;

                    .option-title {
                      font-weight: 500;
                      margin-bottom: 4px;
                      display: flex;
                      align-items: center;
                      gap: 6px;
                    }

                    .option-desc {
                      font-size: 12px;
                      color: var(--color-text-3);
                      line-height: 1.5;
                    }
                  }
                }
              }
            }

            .warning-note {
              font-size: 12px;
              color: var(--color-warning);
              display: flex;
              align-items: center;
              gap: 6px;
              margin-top: 12px;
              padding: 8px 0;
              background-color: var(--color-warning-light);
              border-radius: 4px;

              .warn-icon {
                color: var(--color-warning);
              }
            }

            .model-select {
              width: 100%;
              background-color: var(--color-fill-2);
              border-radius: 4px;

              &:hover {
                background-color: var(--color-fill-3);
              }
            }

            .search-settings {
              display: flex;
              flex-direction: column;
              gap: 12px;

              .search-method-option {
                display: flex;
                align-items: flex-start;
                padding: 12px;
                border-radius: 6px;
                border: 1px solid var(--color-border-2);
                cursor: pointer;

                &.active {
                  border-color: var(--color-primary);
                  background-color: var(--color-primary-light-1);
                }

                .search-icon {
                  font-size: 20px;
                  margin-right: 12px;
                  color: var(--color-text-2);
                }

                .search-info {
                  flex: 1;

                  .search-title {
                    font-weight: 500;
                    margin-bottom: 4px;
                    display: flex;
                    align-items: center;
                    gap: 6px;
                  }

                  .search-desc {
                    font-size: 12px;
                    color: var(--color-text-3);
                    line-height: 1.5;
                  }
                }
              }
            }

            .step-footer {
              display: flex;
              justify-content: space-around;
              gap: 12px;
              margin: 24px 0;
            }
          }

          .preview-panel {
            flex: 1;

            .title {
              color: #155aef;
              font-size: 10px;
              font-weight: 500;
            }

            .file-selection {
              display: flex;
              align-items: center;
              gap: 12px;
              margin-bottom: 20px;

              .selection-label {
                font-size: 14px;
                white-space: nowrap;
              }

              .preview-btn {
                min-width: 140px;
                margin-left: 12px;
              }
            }

            .segment-preview-container {
              height: calc(100% - 50px);
              overflow-y: auto;
              padding: 16px;
              background-color: #f9f9fa;
              border-radius: 8px;
              border: 1px solid var(--color-border-2);

              .empty-preview {
                height: 100%;
                display: flex;
                justify-content: center;
                align-items: center;

                .empty-state {
                  text-align: center;

                  .empty-icon {
                    font-size: 32px;
                    margin-bottom: 12px;
                  }

                  p {
                    color: var(--color-text-3);
                    margin: 0;
                  }
                }
              }

              .segment-results {
                .segment-summary {
                  display: flex;
                  align-items: center;
                  gap: 12px;
                  margin-bottom: 20px;
                  padding: 12px;
                  background-color: #fff;
                  border-radius: 4px;
                  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

                  .summary-icon {
                    font-size: 24px;
                    color: var(--color-primary-light-4);
                  }

                  .summary-text {
                    p {
                      margin: 0;

                      &.summary-hint {
                        font-size: 12px;
                        color: var(--color-text-3);
                        margin-top: 4px;
                      }
                    }

                    .highlight {
                      color: var(--color-primary);
                      font-weight: 600;
                    }
                  }
                }

                .segment-list {
                  .segment-item {
                    margin-bottom: 16px;
                    background-color: #fff;
                    border-radius: 4px;
                    padding: 12px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

                    .segment-header {
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                      margin-bottom: 8px;
                      padding-bottom: 8px;
                      border-bottom: 1px solid var(--color-border-2);

                      .segment-index {
                        font-weight: 500;
                        color: var(--color-primary);
                      }

                      .segment-length {
                        font-size: 12px;
                        color: var(--color-text-3);
                      }
                    }

                    .segment-content {
                      font-size: 14px;
                      color: var(--color-text-1);
                      white-space: pre-wrap;
                      word-break: break-word;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

.empty-dataset-modal {
  .empty-dataset-desc {
    margin-bottom: 30px;
    color: var(--color-text-2);
  }

  .form-item {
    margin-bottom: 20px;

    .form-label {
      margin-bottom: 8px;
    }

    .settings-input {
      background-color: rgba(200, 206, 218, 0.25);
      border-radius: 8px;

      &:hover,
      &:focus {
        background-color: rgba(200, 206, 218, 0.35);
      }
    }
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 24px;
  }
}

.file-content-preview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: var(--color-fill-1);
    border-bottom: 1px solid var(--color-border-2);

    .preview-file-name {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;

      .icon {
        color: var(--color-text-3);
      }
    }

    .preview-file-info {
      font-size: 12px;
      color: var(--color-text-3);
    }
  }

  .preview-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    overflow-x: hidden;
    line-height: 1.6;
    font-size: 14px;
    background-color: #fff;
    height: calc(100% - 45px);
    white-space: pre-wrap;
    word-break: break-word;
  }
}

.error-preview {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-fill-1);
  border-radius: 8px;

  .error-state {
    text-align: center;

    .error-icon {
      font-size: 40px;
      margin-bottom: 16px;
    }

    p {
      margin: 4px 0;
      color: var(--color-text-3);
    }

    .error-desc {
      font-size: 12px;
    }
  }
}

.tooltip-content {
  max-width: 300px;
  font-size: 12px;
  line-height: 1.5;
  color: #fff;
  white-space: normal;
  text-align: left;
}

.parent-mode-options {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .mode-option {
    border: 1px solid var(--color-border-2);
    border-radius: 6px;
    overflow: hidden;
    transition: all 0.2s;
    cursor: pointer;

    &.selected {
      border-color: var(--color-primary);
      background-color: var(--color-primary-light-1);
    }

    .mode-option-header {
      display: flex;
      align-items: flex-start;
      padding: 12px;

      .mode-icon {
        margin-right: 12px;
        font-size: 18px;
        color: var(--color-text-2);
      }

      .mode-info {
        flex: 1;

        .mode-name {
          font-weight: 500;
          margin-bottom: 4px;
        }

        .mode-desc {
          font-size: 12px;
          color: var(--color-text-3);
          line-height: 1.5;
        }
      }
    }

    .mode-option-content {
      padding: 16px;
      background-color: var(--color-fill-1);
      border-top: 1px solid var(--color-border-2);
    }
  }
}

.blocks-preview {
  .segment-summary {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding: 12px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

    .summary-icon {
      font-size: 24px;
      color: var(--color-primary-light-4);
    }

    .summary-text {
      p {
        margin: 0;

        &.summary-hint {
          font-size: 12px;
          color: var(--color-text-3);
          margin-top: 4px;
        }
      }

      .highlight {
        color: var(--color-primary);
        font-weight: 600;
      }
    }
  }

  .segment-list {
    .segment-item {
      margin-bottom: 16px;
      background-color: #fff;
      border-radius: 4px;
      padding: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

      .segment-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;

        .segment-index {
          font-weight: 500;
          color: var(--color-primary);
        }

        .segment-length {
          font-size: 12px;
          color: var(--color-text-3);
        }
      }

      .segment-content {
        font-size: 14px;
        color: var(--color-text-1);
        white-space: pre-wrap;
        word-break: break-word;
      }
    }
  }
}

.retrieval-settings-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 20px;

  .retrieval-option {
    border: 1px solid var(--color-border-2);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s;

    &.active {
      border-color: var(--color-primary);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .option-header {
      display: flex;
      align-items: center;
      padding: 16px;
      background-color: var(--color-fill-1);
      cursor: pointer;

      .option-icon {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        background-color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        .icon {
          font-size: 18px;
          color: var(--color-text-1);
        }
      }

      .option-info {
        flex: 1;

        .option-title {
          font-weight: 500;
          margin-bottom: 4px;
          display: flex;
          align-items: center;
          gap: 6px;
        }

        .option-desc {
          font-size: 12px;
          color: var(--color-text-3);
        }
      }
    }

    .option-content {
      padding: 16px;
      background-color: #fff;
      border-top: 1px solid var(--color-border-2);

      .content-section {
        .content-detail {
          font-size: 13px;
          color: var(--color-text-2);
          margin-bottom: 16px;
          line-height: 1.6;
        }

        .hybrid-modes {
          display: flex;
          flex-direction: row;
          gap: 12px;
          margin-bottom: 20px;

          .hybrid-mode-option {
            flex: 1;
            display: flex;
            align-items: center;
            padding: 12px;
            border: 1px solid var(--color-border-2);
            border-radius: 6px;
            cursor: pointer;

            &.selected {
              border-color: var(--color-primary);
              background-color: var(--color-primary-light-1);
            }

            .mode-icon {
              font-size: 18px;
              margin-right: 12px;
              color: var(--color-text-2);
            }

            .mode-info {
              flex: 1;

              .mode-name {
                font-weight: 500;
                margin-bottom: 4px;
              }

              .mode-desc {
                font-size: 12px;
                color: var(--color-text-3);
                line-height: 1.5;
              }
            }
          }
        }

        .weight-settings {
          padding: 12px;
          background-color: var(--color-fill-1);
          border-radius: 6px;

          .weight-setting-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            .weight-label {
              width: 120px;
              font-size: 13px;
            }

            .arco-slider {
              flex: 1;
              margin: 0 12px;
            }

            .weight-value {
              width: 50px;
              font-size: 12px;
            }
          }

          .weight-note {
            font-size: 12px;
            color: var(--color-text-3);
            text-align: right;
            padding-right: 30px;
          }
        }

        .rerank-settings {
          .rerank-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            .hint-icon {
              font-size: 14px;
              color: var(--color-text-3);
              margin-left: 4px;
              cursor: pointer;
            }
          }

          .select-label {
            font-size: 13px;
            margin-bottom: 8px;
          }

          .arco-select {
            width: 100%;
            margin-bottom: 16px;
          }

          .params-config {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            margin-top: 12px;

            .param-item {
              display: flex;
              align-items: center;
              gap: 8px;

              .param-label {
                font-size: 13px;
                min-width: 80px;
              }

              .param-label1 {
                min-width: 100px;
                display: flex;
                align-items: center;
              }

              .hint-icon {
                font-size: 14px;
                color: var(--color-text-3);
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }
}

.processing-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: #f9f9fa;
  border-radius: 6px;

  p {
    margin: 12px 0;
    color: var(--color-text-2);
  }

  .arco-progress {
    width: 100%;
    margin-top: 8px;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
