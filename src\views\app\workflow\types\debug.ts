export enum PromptMode {
  simple = 'simple',
  advanced = 'advanced'
}

export type PromptConfig = {
  prompt_template: string
  prompt_variables: PromptVariable[]
}

export type PromptVariable = {
  key: string
  name: string
  type: string // "string" | "number" | "select",
  default?: string | number
  required?: boolean
  options?: string[]
  max_length?: number
  is_context_var?: boolean
  enabled?: boolean
  config?: Record<string, any>
  icon?: string
  icon_background?: string
}
