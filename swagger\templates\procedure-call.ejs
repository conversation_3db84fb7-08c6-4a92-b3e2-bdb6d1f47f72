<%
const { utils, route, config } = it;
const { requestBodyInfo, responseBodyInfo, specificArgNameResolver } = route;
const { _, getInlineParseContent, getParseContent, parseSchema, getComponentByRef, require } = utils;
const { parameters, path, method, payload, query, formData, security, requestParams } = route.request;
const { type, errorType, contentTypes } = route.response;
const { HTTP_CLIENT, RESERVED_REQ_PARAMS_ARG_NAMES } = config.constants;
const routeDocs = includeFile("./route-docs", { config, route, utils });
const queryName = (query && query.name) || "query";
const pathParams = _.values(parameters);
const pathParamsNames = _.map(pathParams, "name");

// 更精确地检查类型，判断是否需要添加 DataContracts 前缀
const formatType = (typeStr) => {
  if (!typeStr) return typeStr;
  
  // 已经包含 DataContracts 前缀的直接返回
  if (typeStr.includes('DataContracts.')) {
    return typeStr;
  }
  
  // 基本类型列表（不需要 DataContracts 前缀）
  const basicTypes = [
    'string', 'String',
    'number', 'Number',
    'boolean', 'Boolean',
    'any', 'unknown',
    'null', 'undefined', 'void', 'never',
    'object', 'Object',
    'Record', 'Array',
    'Date', 'Promise',
    'Error'
  ];
  
  // 完全匹配基本类型
  if (basicTypes.includes(typeStr)) {
    return typeStr;
  }
  
  // 基本类型的数组表示（如 number[]、Array<string> 等）
  const arrayTypeRegex1 = new RegExp(`^(${basicTypes.join('|')})\\[\\]$`); // 如 number[]
  const arrayTypeRegex2 = new RegExp(`^Array<(${basicTypes.join('|')})>$`); // 如 Array<string>
  if (arrayTypeRegex1.test(typeStr) || arrayTypeRegex2.test(typeStr)) {
    return typeStr;
  }
  
  // 复杂类型模式（不需要 DataContracts 前缀）
  // 1. 对象字面量如 { prop: string }
  // 2. 数组字面量如 [string, number]
  // 3. 联合类型如 string | number
  // 4. 交叉类型如 Type1 & Type2
  // 5. 泛型如 Promise<T>
  // 6. 函数类型如 () => void
  const isComplexType = 
    typeStr.startsWith('{') || // 对象字面量
    typeStr.startsWith('[') || // 数组字面量
    typeStr.includes('|') ||   // 联合类型
    typeStr.includes('&') ||   // 交叉类型
    typeStr.includes('<') ||   // 泛型
    typeStr.includes('=>') ||  // 函数类型
    typeStr.includes('(') ||   // 函数类型
    typeStr.startsWith('Record<') || // Record 类型
    typeStr.startsWith('Partial<') || // Partial 类型
    typeStr.startsWith('Omit<') ||   // Omit 类型
    typeStr.startsWith('Pick<');     // Pick 类型
    
  if (isComplexType) {
    return typeStr;
  }
  
  // 内置全局类型（不需要 DataContracts 前缀）
  const builtInTypes = [
    'HTMLElement', 'Element', 'Node',
    'Window', 'Document', 'Event',
    'Map', 'Set', 'WeakMap', 'WeakSet',
    'Int8Array', 'Uint8Array', 'Uint8ClampedArray',
    'Int16Array', 'Uint16Array',
    'Int32Array', 'Uint32Array',
    'Float32Array', 'Float64Array',
    'BigInt64Array', 'BigUint64Array',
    'ArrayBuffer', 'DataView',
    'FormData', 'File', 'Blob',
    'URL', 'URLSearchParams',
    'RegExp', 'Function'
  ];
  
  if (builtInTypes.includes(typeStr)) {
    return typeStr;
  }
  
  // 非上述任何类型，则认为是自定义类型，添加 DataContracts 前缀
  return `DataContracts.${typeStr}`;
};

const requestConfigParam = {
    name: "config",
    optional: true,
    type: "AxiosRequestConfig",
    defaultValue: undefined,
}

// 修改参数名逻辑：统一使用params作为参数名
const renameParamToParams = (param) => {
    if (param && param.name) {
        return { ...param, name: "params", optional: true };
    }
    return param;
}

// 自定义一个处理内联类型的函数
const getFormattedInlineParseContent = (schema) => {
  // 获取原始内联类型
  const originalContent = getInlineParseContent(schema);
  
  // 检查是否是简单类型（没有复杂结构）
  if (!originalContent.includes('{') && !originalContent.includes('[') && 
      !originalContent.includes('<') && !originalContent.includes('|') && 
      !originalContent.includes('&')) {
    // 对简单类型应用 formatType
    return formatType(originalContent);
  }
  
  // 对于复杂类型，返回原始内容
  return originalContent;
};

const argToTmpl = ({ name, optional, type, defaultValue }) => 
    `${name}${optional ? '' : ''}: ${type}${defaultValue !== undefined ? ` = ${defaultValue}` : ''}`;
    <!-- `${name}${optional ? '?' : ''}: ${type}${defaultValue !== undefined ? ` = ${defaultValue}` : ''}`; -->

const rawWrapperArgs = config.extractRequestParams ?
    _.compact([
        requestParams && {
          name: "params",
          optional: true,
          type: getFormattedInlineParseContent(requestParams),
        },
        ...(!requestParams ? pathParams : []),
        <!-- requestConfigParam, -->
    ]) :
    _.compact([
        ...pathParams,
        (query || payload) ? {
            name: "params",
            optional: true,
            type: query ? formatType(query.type) : formatType(payload.type),
        } : null,
        <!-- requestConfigParam, -->
    ])

const wrapperArgs = _
    .sortBy(rawWrapperArgs, [o => o.optional])
    .map(argToTmpl)
    .join(', ')

// 判断是否有非路径参数 (query 或 payload)
const hasQueryOrPayload = !!(query || payload);
// 判断是否只有路径参数
const hasOnlyPathParams = pathParams.length > 0 && !hasQueryOrPayload;
// 是否是GET请求
const isGetRequest = method.toLowerCase() === 'get';

// 判断参数类型和数量
let paramsToPass = "params";

// 根据请求方法和参数情况确定传递的参数
if (isGetRequest) {
    if (hasOnlyPathParams && pathParams.length === 1) {
        // 如果是GET请求且只有一个路径参数（如id），构建一个查询对象
        const pathParam = pathParams[0];
        <!-- paramsToPass = pathParam ? `${ pathParam.name }` : "null"; -->
        paramsToPass = ''
    } else if (!hasQueryOrPayload && pathParams.length === 0) {
        // 没有任何参数的GET请求
        paramsToPass = "null";
    }
}

// 格式化响应类型，添加 DataContracts 前缀
const responseType = formatType(type);

%>
/**
<%~ routeDocs.lines %>

 */
export function <%~ route.routeName.usage %>(<%~ wrapperArgs %>)<%~ config.toJS ? `: Promise<${responseType}>` : "" %> {
    const url = `/api<%~ path %>`;
    
    <% if (method.toLowerCase() === 'get') { %>
        <% if (paramsToPass === '') { %>
            return http.get<<%~ responseType %>>(url);
        <% } else { %>
            return http.get<<%~ responseType %>>(url, <%~ paramsToPass %>);
        <% } %>
    <% } else if (method.toLowerCase() === 'post') { %>
    return http.post<<%~ responseType %>>(url, params);
    <% } else if (method.toLowerCase() === 'put') { %>
    return http.put<<%~ responseType %>>(url, params);
    <% } else if (method.toLowerCase() === 'patch') { %>
    return http.patch<<%~ responseType %>>(url, params);
    <% } else if (method.toLowerCase() === 'delete') { %>
    return http.del<<%~ responseType %>>(url, params);
    <% } else { %>
    return http.request<<%~ responseType %>>({
        method: '<%~ _.upperCase(method) %>',
        url,
        <%~ method.toLowerCase() === 'get' ? 'params' : 'data' %>: params,
        ...config
    });
    <% } %>
}
