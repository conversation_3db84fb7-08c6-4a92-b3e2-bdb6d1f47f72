<template>
  <div class="setting-card" :class="{ disabled: disabled }">
    <div class="setting-card-header">
      <div class="setting-title">索引方式</div>
    </div>
    <div class="index-options">
      <div
        class="index-option"
        :class="{ active: indexingMethod === 'high_quality' }"
        @click="selectIndexingMethod('high_quality')"
      >
        <div class="option-radio">
          <a-radio :model-value="indexingMethod === 'high_quality'" :disabled="disabled" />
        </div>
        <div class="option-content">
          <div class="option-icon"><icon-experiment /></div>
          <div class="option-info">
            <div class="option-title">
              高质量
              <a-tag size="small" color="arcoblue">推荐</a-tag>
            </div>
            <div class="option-desc">调用嵌入模型处理文档以实现更精确的检索，可以帮助LLM生成高质量的答案。</div>
          </div>
        </div>
      </div>

      <div
        class="index-option"
        :class="{ active: indexingMethod === 'economy' }"
        @click="selectIndexingMethod('economy')"
      >
        <div class="option-radio">
          <a-radio :model-value="indexingMethod === 'economy'" :disabled="disabled || segmentMode === 'parent-child'" />
        </div>
        <div class="option-content">
          <div class="option-icon"><icon-safe /></div>
          <div class="option-info">
            <div class="option-title">经济</div>
            <div class="option-desc">
              每个数据块使用10个关键词进行检索，不会消耗任何tokens，但会以降低检索准确性为代价。
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="indexingMethod === 'high_quality'" class="warning-note">
      <a-alert type="warning">使用高质量模式进行嵌入后，无法切换回经济模式。</a-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 定义接收的props
const props = defineProps({
  indexingMethod: {
    type: String,
    default: 'high_quality'
  },
  segmentMode: {
    type: String,
    default: 'normal'
  },
  // 添加disabled属性
  disabled: {
    type: Boolean,
    default: false
  }
})

// 定义向父组件发送的事件
const emit = defineEmits(['indexing-method-change'])

// 选择索引方法
const selectIndexingMethod = (method) => {
  if (!props.disabled) {
    emit('indexing-method-change', method)
  }
}
</script>
