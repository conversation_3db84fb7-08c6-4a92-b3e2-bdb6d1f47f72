<template>
  <a-modal
    v-model:visible="visible"
    title="创建评测集"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 600 ? 600 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <AiForm ref="formRef" v-model="form" :columns="columns">
      <template #inputFileId>
        <div class="w-full flex flex-col">
          <div>
            <a-space>
              <icon-file />
              输入文件
            </a-space>
            <AiFileUpload
              v-model="form.inputFileId"
              inputsKey="inputFileId"
              :tips="evaluationType === 1 ? '支持 Excel 格式' : '支持 docx、pdf、xlsx 等格式'"
              :accept="evaluationType === 1 ? '.xlsx' : '.docx,.pdf,.xlsx'"
              :remove-file-fun="removeFile"
              @uploadFile="onUploadFile"
            />
          </div>
        </div>
      </template>
      <template #outputFileId>
        <div class="w-full flex flex-col">
          <div>
            <a-space>
              <icon-file />
              输出文件
            </a-space>
            <AiFileUpload
              v-model="form.outputFileId"
              inputsKey="outputFileId"
              :tips="'支持 docx、pdf、xlsx 等格式'"
              :accept="'.docx,.pdf,.xlsx'"
              :remove-file-fun="removeFile"
              @uploadFile="onUploadFile"
            />
          </div>
        </div>
      </template>
    </AiForm>
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { type ColumnItem, AiForm } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'
import { addEvaluationSets } from '@/apis/evaluation/evaluation-sets'
import { EvalDatasetSaveDTO } from '@/apis/evaluation/evaluation-sets-type'
import { uploadFile } from '@/apis/upload'

const props = defineProps<{
  modalVisible: boolean
  evaluationType: number
}>()

const emit = defineEmits<{
  (e: 'save-success'): void
  (e: 'close'): void
}>()

const { width } = useWindowSize()

const visible = ref(props.modalVisible)
const formRef = ref<InstanceType<typeof AiForm>>()

const [form, resetForm] = useResetReactive<EvalDatasetSaveDTO>({
  name: '',
  type: props.evaluationType,
  inputFileId: '',
  inputFileType: '',
  inputFileName: '',
  outputFileId: '',
  outputFileType: '',
  outputFileName: '',
  description: '',
  status: 2
})

// 处理文件上传
const onUploadFile = async (
  file: File,
  onProgress?: (percent: number) => void,
  key?: string,
  transfer_method?: string,
  callback?: (res) => void
) => {
  const response = await uploadFile(file, (percent) => {
    onProgress && onProgress(percent)
  })
  if (key === 'inputFileId') {
    form.inputFileId = response.id
    form.inputFileType = response.extension
    form.inputFileName = response.name
  }
  if (key === 'outputFileId') {
    form.outputFileId = response.id
    form.outputFileType = response.extension
    form.outputFileName = response.name
  }
  if (callback) {
    callback(response)
  }
}

const removeFile = (fileItem, key) => {
  if (key === 'inputFileId') {
    form.inputFileId = undefined
    form.inputFileType = undefined
    form.inputFileName = undefined
  }
  if (key === 'outputFileId') {
    form.outputFileId = ''
    form.outputFileType = undefined
    form.outputFileName = undefined
  }
}

const columns: ColumnItem[] = [
  {
    label: '评测集名称',
    field: 'name',
    type: 'input',
    span: 24,
    props: {
      maxLength: 255,
      placeholder: '请输入评测集名称'
    },
    rules: [{ required: true, message: '请输入评测集名称' }]
  },
  {
    label: '上传评测文件',
    field: 'inputFileId',
    span: 24,
    props: {
      accept: 'xlsx, docx, pdf, jpg, png, excel'
    },
    styles: {
      'margin-bottom': 0
    },
    rules: [{ required: true, message: '上传输入文件' }]
  },

  {
    label: '描述',
    field: 'description',
    type: 'input',
    span: 24,
    props: {
      maxLength: 100,
      placeholder: '请输入描述'
    }
  }
]

onMounted(() => {
  if (props.evaluationType === 2) {
    columns.splice(2, 0, {
      label: '',
      field: 'outputFileId',
      span: 24,
      props: {
        accept: 'xlsx, docx, pdf, jpg, png, excel'
      },
      styles: {
        'margin-bottom': 0
      },
      rules: [
        { required: false, message: '上传输出文件' },
        {
          validator: (value, callback) => {
            if (!value) {
              callback('上传输出文件')
            } else {
              callback()
            }
          }
        }
      ]
    })
  }
})
// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
  emit('close')
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    const res = await addEvaluationSets(form)
    if (res.isSuccess) {
      Message.success('新增成功')
      emit('save-success')
    }
    return true
  } catch (error) {
    return false
  }
}
</script>
