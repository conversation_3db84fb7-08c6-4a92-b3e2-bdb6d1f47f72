<template>
  <AiPageLayout>
    <a-space>
      <a-input-search v-model="queryForm.description" placeholder="搜索名称/描述" allow-clear @search="search" />
    </a-space>
    <a-row :style="{ marginTop: '10px' }" :gutter="[14, 14]">
      <a-col
        v-for="(item, index) in list"
        :key="index"
        :xs="24"
        :sm="24"
        :md="12"
        :lg="12"
        :xl="8"
        :xxl="8"
        class="card-col"
      >
        <a-card :bordered="true" hoverable>
          <div class="badge badge-right" :style="`background-color: ${item.statusColor}`">{{ item.status }}</div>
          <a-card-meta>
            <template #title>
              <a-space :size="20">
                <img :src="item.logo" width="40px" height="40px" alt="logo" />
                <div>
                  <a-typography-paragraph
                    :style="{ fontSize: '18px' }"
                    :ellipsis="{
                      rows: 1,
                      showTooltip: true,
                      css: true
                    }"
                  >
                    {{ item.alias }}
                  </a-typography-paragraph>
                  <a-typography-text
                    :style="{ fontSize: '14px' }"
                    :ellipsis="{
                      rows: 1,
                      showTooltip: true,
                      css: true
                    }"
                    type="secondary"
                  >
                    {{ item.name }}
                  </a-typography-text>
                </div>
              </a-space>
            </template>
            <template #description>
              <a-typography-paragraph
                :style="{ height: '44px', marginTop: '10px' }"
                :ellipsis="{
                  rows: 2,
                  showTooltip: true,
                  css: true
                }"
              >
                <a-typography-text type="secondary">
                  {{ item.desc }}
                </a-typography-text>
              </a-typography-paragraph>
            </template>

            <template #avatar>
              <div :style="{ display: 'flex', alignItems: 'center' }">
                <span class="button-hover">
                  <a-button type="primary" @click="onAdd(item)">
                    <template #icon><icon-plus /></template>
                    <template #default>添加至我的工作流空间</template>
                  </a-button>
                </span>
              </div>
            </template>
          </a-card-meta>
        </a-card>
      </a-col>
    </a-row>
    <AppAddModal ref="AppAddModalRef" />
  </AiPageLayout>
</template>

<script setup lang="ts">
import AppAddModal from './AppAddModal.vue'

interface AppResp {
  alias: string
  name: string
  owner: string
  desc: string
  logo: string
  url: string
  status: string
  statusColor: string
}
defineOptions({ name: 'AppsStore' })
const list = [
  {
    alias: 'AI 前端面试官',
    name: '快速编排',
    owner: 'ai-web-org',
    desc: '🔥一个模拟的前端面试官，通过提问的方式对前端开发的技能水平进行检验。',
    logo: '/logo.svg',
    url: '#',
    status: '最新',
    statusColor: 'rgb(var(--primary-6))'
  },
  {
    alias: '中英文互译',
    name: '快速编排',
    owner: 'ai-web-org',
    desc: '🔥AI-WEB🔥 自动化ai',
    logo: '/logo.svg',
    url: '#',
    status: '精选',
    statusColor: 'rgb(var(--primary-6))'
  },
  {
    alias: '会议纪要助手',
    name: '快速编排',
    owner: 'ai-web-org',
    desc: '全新 3.x 版本，基于 Ai Demo 前端模板开发的 AI-WEB 前端适配项目。',
    logo: '/logo.svg',
    url: '#',
    status: '精选',
    statusColor: 'rgb(var(--primary-6))'
  },
  {
    alias: 'AI-WEB UI',
    name: 'ai-web-ui-arco',
    owner: 'ai-web-org',
    desc: '2.5 版本，基于 Arco Design Pro 前端模板开发的 AI-WEB 前端适配项目。',
    logo: '/logo.svg',
    url: '#',
    status: '精选',
    statusColor: 'rgb(var(--warning-6))'
  },
  {
    alias: 'ai-web Cloud',
    name: 'ai-web',
    owner: 'ai-web',
    desc: 'AI-WEB 微服务版本。基于 SpringBoot 3.x、Spring Cloud 2023 & Alibaba。',
    logo: '/logo.svg',
    url: '#',
    status: '精选',
    statusColor: 'rgb(var(--danger-6))'
  },
  {
    alias: 'ai-web',
    name: 'ai-web',
    owner: '',
    desc: '基于 VitePress 构建的个人知识库/博客。扩展 VitePress 默认主题：增加ICP备案号、公安备案号显示，增加文章元数据信息（原创标识、作者、发布时间、分类、标签）显示，增加文末版权声明，增加 Aitalk 评论功能，主页美化、自动生成侧边栏、文章内支持 Mermaid 流程图、MD公式、MD脚注、增加我的标签、我的归档等独立页面，以及浏览器滚条等细节优化。',
    logo: '/logo.svg',
    url: '#',
    status: '精选',
    statusColor: 'rgb(var(--warning-6))'
  }
]
const queryForm = reactive<any>({
  sort: ['id,desc']
})

const AppAddModalRef = ref<InstanceType<typeof AppAddModal>>()
// 搜索
const search = (name: any) => {
  console.log(name)
}
// 新增
const onAdd = (record: AppResp) => {
  AppAddModalRef.value?.onAdd(record)
}
</script>

<style scoped lang="scss">
:deep(.arco-card-bordered) {
  border-radius: 10px;

  &:hover {
    border-color: rgb(var(--primary-6));

    .button-hover .arco-btn {
      display: block;
    }
  }
}

:deep(.arco-card-body) {
  position: relative;
  overflow: hidden;

  .badge {
    position: absolute;
    font-size: 11px;
    height: 18px;
    line-height: 18px;
    text-align: center;
    width: 74px;
    color: #fff;
  }

  .badge-left {
    -moz-transform: rotate(-45deg);
    -ms-transform: rotate(-45deg);
    -o-transform: rotate(-45deg);
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
    left: -20px;
    top: 6px;
  }

  .badge-right {
    -moz-transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -o-transform: rotate(45deg);
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    right: -20px;
    top: 6px;
  }
}

.button-hover {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 24px;
  // border-radius: 50%;
  transition: all 1s;
  animation: button-hover-animated 2s ease-in-out infinite;

  .arco-btn {
    color: #ffffff;
    display: none;
    background-color: rgb(var(--primary-6));
  }
}

.button-hover:hover {
  background-color: rgb(var(--primary-6));
}

@keyframes button-hover-animated {
  50% {
    transform: scale(0.9);
  }
}
</style>
