{"name": "ai-web", "type": "module", "version": "1.0.0", "private": "true", "engines": {"node": "^18.18.0", "pnpm": ">=9"}, "scripts": {"bootstrap": "pnpm install --registry=https://registry.npmmirror.com", "dev": "vite --host", "build": "vue-tsc --noEmit && vite build", "build:test": "vue-tsc --noEmit && vite build --mode test", "preview": "vite preview --port 5050", "typecheck": "vue-tsc --noEmit", "lint": "eslint .", "lint:fix": "eslint . --fix", "swagger": "npx swagger-typescript-api generate -p ./swagger/swagger.json -o ./swagger/apis -t ./swagger/templates --clean-output --modular"}, "dependencies": {"@arco-design/color": "^0.4.0", "@arco-themes/vue-ai-theme": "^0.0.1", "@codemirror/commands": "^6.8.1", "@codemirror/lang-javascript": "^6.2.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-python": "^6.2.1", "@codemirror/lang-vue": "^0.1.2", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.8", "@ddietr/codemirror-themes": "^1.4.2", "@vue-flow/background": "^1.3.2", "@vue-flow/controls": "^1.1.2", "@vue-flow/core": "^1.43.1", "@vue-flow/minimap": "^1.5.3", "@vue-flow/node-resizer": "^1.4.0", "@vue-office/docx": "1.6.0", "@vue-office/excel": "1.7.1", "@vue-office/pdf": "1.6.4", "@vueuse/components": "^10.5.0", "@vueuse/core": "^10.5.0", "aieditor": "^1.0.13", "animate.css": "^4.1.1", "axios": "^0.27.2", "classnames": "^2.5.1", "codemirror": "^6.0.1", "crypto-js": "^4.2.0", "dagre": "^0.8.5", "dayjs": "^1.11.13", "echarts": "^5.4.2", "highlight.js": "^11.11.1", "html-to-image": "^1.11.13", "jsencrypt": "^3.3.2", "lint-staged": "^15.2.10", "lodash-es": "^4.17.21", "markdown-it": "^14.1.0", "md-editor-v3": "^5.6.1", "mitt": "^3.0.0", "mockjs": "^1.1.0", "nanoid": "^5.1.5", "nprogress": "^0.2.0", "pinia": "^2.0.16", "pinia-plugin-persistedstate": "^3.1.0", "qrcode": "^1.5.4", "qs": "^6.11.2", "query-string": "^9.0.0", "swiper": "^11.2.8", "tailwind-merge": "^3.3.0", "v-viewer": "^3.0.10", "viewerjs": "^1.11.6", "vite-plugin-vue-devtools": "^7.0.27", "vue": "^3.5.13", "vue-codemirror6": "^1.1.27", "vue-color-kit": "^1.0.5", "vue-cropper": "^1.1.1", "vue-demi": "^0.14.10", "vue-draggable-plus": "^0.3.5", "vue-echarts": "^6.5.5", "vue-json-pretty": "^2.4.0", "vue-router": "^4.3.3", "vue3-tree-org": "^4.2.2", "xe-utils": "^3.5.7", "xgplayer": "^2.31.6"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@eslint/js": "^9.25.0", "@tailwindcss/postcss": "^4.1.5", "@tailwindcss/typography": "^0.5.15", "@types/crypto-js": "^4.2.2", "@types/lodash-es": "^4.17.12", "@types/node": "^20.2.5", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "autoprefixer": "^10.4.21", "boxen": "^8.0.1", "code-inspector-plugin": "^0.20.10", "eslint": "^9.25.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-vue": "^10.0.0", "less": "^4.1.3", "less-loader": "^11.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.47", "prettier": "^3.5.3", "rollup-plugin-visualizer": "^5.14.0", "sass": "^1.62.1", "sass-loader": "^13.2.2", "swagger-typescript-api": "^13.1.3", "tailwindcss": "3.4.3", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1", "unplugin-auto-import": "^0.16.4", "unplugin-vue-components": "^0.25.1", "vite": "^6.3.2", "vite-plugin-mock": "^2.9.8", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^2.2.8"}, "pnpm": {"onlyBuiltDependencies": ["@vue-office/docx", "@vue-office/excel", "@vue-office/pdf", "core-js", "es5-ext", "esbuild", "vue-demi", "vue-echarts"]}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}}