<template>
  <div>
    <a-form ref="formRef" :model="form">
      <a-form-item field="type" label="字段类型">
        <a-radio-group v-model="form.type">
          <a-radio v-for="item in optionList" :key="item.value" :value="item.value">{{ item.label }}</a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item field="key" label="变量名称">
        <a-input v-model="form.key" placeholder="请输入" />
      </a-form-item>
      <a-form-item field="name" label="显示名称">
        <a-input v-model="form.name" placeholder="请输入" />
      </a-form-item>
      <template v-if="isStringInput">
        <a-form-item field="max_length" label="最大长度">
          <a-input-number v-model="form.max_length" placeholder="请输入" :min="1" :max="99999999" />
        </a-form-item>
      </template>
      <a-form-item v-if="isSelect" field="options" label="选项">
        <a-space direction="vertical">
          <div v-for="(op, index) in form.options" :key="op" class="flex justify-between items-center">
            <a-input :default-value="op" placeholder="请输入" @change="handleChange(index, $event)" />
            <a-button class="arco-btn-only-icon ml-2" type="text" @click="handleDelete(index)">
              <icon-delete />
            </a-button>
          </div>
          <div>
            <a-button long class="mt-1" @click="handleAddOption">添加选项</a-button>
          </div>
        </a-space>
      </a-form-item>
      <a-form-item field="required" label="必填">
        <a-switch v-model="form.required" type="round" />
      </a-form-item>
    </a-form>
  </div>
</template>
<script setup lang="ts">
import { OPTION_LIST } from '@/views/app/workflow/constant/configuration'
import { InputVarType } from '@/views/app/workflow/types/workflow'

const props = withDefaults(
  defineProps<{
    editItem?: any
  }>(),
  {
    editItem: () => ({})
  }
)
console.log('modal-props：', props)
const optionList = OPTION_LIST
const form = ref({ ...props.editItem })
console.log('form:', form)
const isStringInput = computed(() => {
  return form.value.type === InputVarType.textInput || form.value.type === InputVarType.paragraph
})
const isSelect = computed(() => {
  return form.value.type === InputVarType.select
})
const handleChange = (index, val) => {
  form.value.options[index] = val
}

const handleAddOption = () => {
  if (form.value.options) {
    form.value.options.push('')
  } else {
    form.value.options = ['']
  }
}
const handleDelete = (index) => {
  form.value.options.splice(index, 1)
}
// watch(
//   () => props.editItem,
//   () => {
//     form.value = { ...props.editItem }
//   }
// )
defineExpose({
  form
})
</script>
<style scoped lang="scss"></style>
