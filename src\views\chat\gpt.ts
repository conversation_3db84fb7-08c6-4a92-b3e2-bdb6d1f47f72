export const chat = async (messages, apiKey) => {
  // 定义一个名为 chat 的异步函数，接收消息数组 messages 和 API 密钥 apiKey 作为参数
  try {
    // 尝试执行以下代码
    const result = await fetch('https://api.302.ai/v1/chat/completions', {
      // 发送一个 fetch 请求
      method: 'POST',
      // 请求方法为 POST
      headers: {
        // 设置请求头
        'Content-Type': 'application/json',
        // 设置授权信息
        Authorization: `Bearer ${apiKey}`
      },
      body: JSON.stringify({
        // 将请求体转换为 JSON 字符串
        model: 'gpt-3.5-turbo',
        messages
      })
    })

    const data = await result.json()
    // 等待将响应解析为 JSON 格式
    console.log(data)
    // 打印响应数据
    return data.choices[0].message.content
    // 返回响应数据中 choices 数组的第一个元素的 message 对象的 content 属性值
  } catch (err) {
    // 如果上述操作过程中发生错误
    throw err
    // 抛出错误
  }
}
