import { ReasoningMode } from '@/views/app/workflow//types/node'
const nodeDefaultParameter: any = {
  defaultValue: {
    instruction: '',
    model: {
      provider: '',
      name: '',
      mode: 'chat',
      completion_params: {
        temperature: 0.7
      }
    },
    parameters: [],
    query: [],
    selected: true,
    reasoning_mode: ReasoningMode['Function/Tool Calling'],
    variables: [],
    vision: {
      enabled: false
    }
  }
}
export default nodeDefaultParameter
