<template>
  <div class="chunk-detail-container">
    <!-- 1. 页面顶部 -->
    <div class="detail-header">
      <div class="header-left">
        <a-button class="back-btn" size="mini" type="text" @click="goBack">
          <template #icon><icon-left /></template>
        </a-button>
        <h5 class="document-title">{{ documentDetail.name || '未命名文档' }}</h5>
      </div>
      <div class="header-right">
        <a-button class="action-btn" type="primary" size="mini" :disabled="isProcessing" @click="handleAddSegment">
          <template #icon><icon-plus /></template>
          添加分段
        </a-button>
        <a-switch
          v-model="documentEnabled"
          type="round"
          class="status-switch"
          :disabled="isProcessing"
          @change="toggleDocumentStatus"
        >
          <template #checked>可用</template>
          <template #unchecked>已禁用</template>
        </a-switch>
        <a-button size="mini" class="btn1" :disabled="isProcessing" @click="goToChunkSetting">
          <template #icon>
            <a-tooltip content="分段设置" position="top" mini>
              <icon-ordered-list />
            </a-tooltip>
          </template>
        </a-button>
        <a-dropdown trigger="click" :disabled="isProcessing">
          <a-button class="action-btn" size="mini" :disabled="isProcessing">
            <template #icon><icon-more /></template>
          </a-button>
          <template #content>
            <a-doption @click="handleArchiveDocument">{{ documentArchived ? '撤销归档' : '归档文档' }}</a-doption>
            <a-doption @click="handleRenameDocument">重命名</a-doption>
            <a-doption @click="handleDeleteDocument">删除文档</a-doption>
          </template>
        </a-dropdown>
        <a-button class="action-btn" size="mini" :disabled="isProcessing" @click="toggleFullscreen">
          <template #icon>
            <icon-fullscreen v-if="!isFullscreen" />
            <icon-fullscreen-exit v-else />
          </template>
        </a-button>
      </div>
    </div>

    <div class="detail-content" :class="{ 'fullscreen-mode': isFullscreen }">
      <!-- 2. 页面内容区左侧：分段列表 -->
      <div class="segments-list-container" :class="{ fullwidth: isFullscreen }">
        <!-- 处理中状态显示 -->
        <div v-if="isProcessing" class="processing-container">
          <div class="processing-spinner">
            <icon-loading />
          </div>
          <h3 class="processing-title">嵌入处理中...</h3>
          <div class="processing-progress">
            <div class="progress-info">
              <span>阶段：{{ processingProgress >= 100 ? '完成' : '分段' }}</span>
              <span>{{ processingProgress }}%</span>
            </div>
            <a-progress :percent="processingProgress" :show-text="false" status="normal" />
          </div>
          <div class="processing-info">
            <div class="info-row">
              <div class="info-label">分段格式</div>
              <div class="info-value">自定义</div>
            </div>
            <div class="info-row">
              <div class="info-label">最大分段长度</div>
              <div class="info-value">
                {{ documentDetail.document_process_rule?.rules?.segmentation?.max_tokens || 1024 }}
              </div>
            </div>
            <div class="info-row">
              <div class="info-label">文本预处理规则</div>
              <div class="info-value">移除连续换行符、移除行首和行尾空白</div>
            </div>
          </div>
        </div>

        <div v-else>
          <div class="segments-header">
            <div class="segments-count">
              <a-checkbox
                :model-value="isAllSelected"
                :indeterminate="isIndeterminate"
                style="margin-right: 8px"
                @change="toggleSelectAll"
              />
              {{ totalSegments }} {{ segmentsCountLabel }}
            </div>
            <div class="segments-btns">
              <div v-if="selectedSegments.length > 0" class="batch-operations">
                <div class="selected-count">
                  <span class="count-badge">{{ selectedSegments.length }}</span>
                  已选择
                </div>
                <a-button type="outline" size="mini" @click="batchEnableSegments">
                  <template #icon><icon-check-circle /></template>
                  启用
                </a-button>
                <a-button type="outline" size="mini" @click="batchDisableSegments">
                  <template #icon><icon-close-circle /></template>
                  禁用
                </a-button>
                <a-button type="outline" status="danger" size="mini" @click="showBatchDeleteConfirm">
                  <template #icon><icon-delete /></template>
                  删除
                </a-button>
                <a-button type="outline" size="mini" @click="clearSelection">取消</a-button>
              </div>
            </div>
            <div class="segments-actions">
              <a-select v-model="statusFilter" placeholder="全部" style="width: 100px" @change="handleSearch">
                <a-option value="all">全部</a-option>
                <a-option value="enabled">已启用</a-option>
                <a-option value="disabled">已禁用</a-option>
              </a-select>
              <a-input-search
                v-model="searchKeyword"
                placeholder="搜索"
                style="width: 200px"
                allow-clear
                @search="handleSearch"
                @change="handleSearch"
              />
              <a-button class="list-view-btn" @click="toggleListView">
                <template #icon>
                  <a-tooltip :content="!isExpandedView ? '展开分段' : '折叠分段'">
                    <icon-expand v-if="!isExpandedView" />
                    <icon-shrink v-else />
                  </a-tooltip>
                </template>
              </a-button>
            </div>
          </div>

          <div class="segments-list">
            <a-checkbox-group v-model="selectedSegments">
              <div
                v-for="segment in segmentsList"
                :key="segment.id"
                class="segment-item"
                :class="{ disabled: !segment.enabled }"
              >
                <div class="segment-checkbox">
                  <a-checkbox :value="segment.id" />
                </div>
                <div class="segment-content" @click="expandSegment(segment)">
                  <div class="segment-info">
                    <span class="segment-number">
                      <icon-unordered-list />
                      分段-{{ String(segment.position).padStart(2, '0') }}
                    </span>
                    <a-divider direction="vertical" />
                    <span class="segment-words">{{ segment.word_count }} 字符</span>
                    <a-divider direction="vertical" />
                    <span class="segment-hit">{{ segment.hit_count || 0 }} 召回次数</span>
                  </div>
                  <div class="segment-text" :class="{ expanded: isExpandedView }">{{ segment.content }}</div>
                  <div class="segment-tags">
                    <a-tag v-for="(tag, tagIndex) in segment.keywords" :key="tagIndex" size="small">#{{ tag }}</a-tag>
                  </div>
                </div>
                <div class="segment-status">
                  <a-tag :color="segment.enabled ? 'green' : 'gray'">{{ segment.enabled ? '已启用' : '已禁用' }}</a-tag>
                </div>
                <div class="hover-show">
                  <icon-edit class="icon" @click.stop="expandSegment(segment)" />
                  <icon-delete class="icon" @click.stop="deleteSegment(segment)" />
                  <a-switch
                    :model-value="segment.enabled"
                    class="icon"
                    type="round"
                    size="small"
                    @click.stop="toggleSegmentStatus(segment)"
                  />
                </div>
              </div>
            </a-checkbox-group>
          </div>

          <div class="pagination-container">
            <a-pagination
              v-model:current="currentPage"
              v-model:page-size="pageSize"
              :total="totalSegments"
              show-total
              show-jumper
              @change="handlePageChange"
            />
          </div>
        </div>
      </div>

      <!-- 3. 页面内容区右侧：元数据和文档信息 -->
      <div v-show="!isFullscreen" class="document-info-container">
        <!-- 元数据部分 -->
        <div class="metadata-section">
          <div class="section-header">
            <div class="title-row">
              <p>
                元数据
                <a-tooltip
                  v-if="isMetadataEditing"
                  content="元数据是关于文档的数据，用于描述文档的属性。元数据可以帮助您更好地组织和管理文档。"
                >
                  <icon-question-circle />
                </a-tooltip>
              </p>
              <span v-if="!isMetadataEditing">
                元数据是关于文档的数据，用于描述文档的属性。元数据可以帮助您更好地组织和管理文档。
              </span>
              <div v-if="isMetadataEditing" class="header-actions">
                <a-space>
                  <a-button size="mini" @click="cancelMetadataEdit">取消</a-button>
                  <a-button size="mini" type="primary" :loading="savingMetadata" @click="saveMetadata">保存</a-button>
                </a-space>
              </div>
            </div>

            <!-- 查看模式 -->
            <a-button v-if="!isMetadataEditing" type="primary" class="annotate-btn" @click="startAnnotation">
              开始标注
              <icon-arrow-right />
            </a-button>

            <!-- 编辑模式 -->
            <div v-else class="metadata-edit-content">
              <div class="metadata-list">
                <div v-for="(item, index) in documentMetadata" :key="index" class="metadata-item">
                  <div class="metadata-key">{{ item.key }}</div>
                  <div class="metadata-value">{{ item.value }}</div>
                  <a-button class="delete-btn" type="text" status="danger" @click="removeMetadata(index)">
                    <template #icon><icon-delete /></template>
                  </a-button>
                </div>
              </div>

              <a-button long type="dashed" size="mini" class="add-metadata-btn" @click="showAddMetadataModal">
                <template #icon><icon-plus /></template>
                添加元数据
              </a-button>
            </div>
          </div>
        </div>

        <!-- 文档信息部分 -->
        <div class="document-info-section">
          <h3>文档信息</h3>
          <div class="info-grid">
            <div class="info-item">
              <div class="info-label">原始文件名称</div>
              <div class="info-value">{{ documentDetail.name || '未命名文档' }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">原始文件大小</div>
              <div class="info-value">{{ formatFileSize(documentDetail.data_source_info?.upload_file?.size) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">上传日期</div>
              <div class="info-value">{{ formatDate(documentDetail.created_at) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">最后更新日期</div>
              <div class="info-value">{{ formatDate(documentDetail.updated_at) }}</div>
            </div>
            <div class="info-item">
              <div class="info-label">来源</div>
              <!-- <div class="info-value">{{ documentDetail.created_from || '文件上传' }}</div> -->
              <div class="info-value">
                {{
                  documentDetail.data_source_type === 'data_source_type' ? '文件上传' : documentDetail.data_source_type
                }}
              </div>
            </div>
          </div>
        </div>

        <!-- 技术参数部分 -->
        <div class="technical-params-section">
          <h3>技术参数</h3>
          <div class="params-grid">
            <div class="param-item">
              <div class="param-label">分段规则</div>
              <div class="param-value">自定义</div>
            </div>
            <div class="param-item">
              <div class="param-label">段落长度</div>
              <div class="param-value">
                {{ documentDetail.document_process_rule?.rules?.segmentation?.max_tokens || 500 }}
              </div>
            </div>
            <div class="param-item">
              <div class="param-label">平均段落长度</div>
              <div class="param-value">{{ documentDetail.average_segment_length || 0 }} characters</div>
            </div>
            <div class="param-item">
              <div class="param-label">段落数量</div>
              <div class="param-value">{{ totalSegments }} paragraphs</div>
            </div>
            <div class="param-item">
              <div class="param-label">召回次数</div>
              <div class="param-value">{{ calculateTotalHits() }}</div>
            </div>
            <div class="param-item">
              <div class="param-label">嵌入时间</div>
              <div class="param-value">{{ documentDetail.indexing_latency?.toFixed(2) || 0 }} sec</div>
            </div>
            <div class="param-item">
              <div class="param-label">嵌入花费</div>
              <div class="param-value">{{ documentDetail.tokens || 0 }} tokens</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 抽屉：添加分段 -->
    <a-drawer
      v-model:visible="addSegmentModalVisible"
      :width="drawerFullscreen ? '100%' : 680"
      :unmount-on-close="false"
      :mask-closable="false"
      :footer="false"
    >
      <template #header>
        <div style="display: flex; justify-content: space-between; width: 100%">
          <div class="custom-header">
            <h2>新增分段</h2>
          </div>
          <div>
            <a-button class="fullscreen-btn" type="text" size="mini" @click="toggleAddDrawerFullscreen">
              <template #icon>
                <icon-fullscreen v-if="!addDrawerFullscreen" />
                <icon-fullscreen-exit v-else />
              </template>
              <span style="margin-left: 4px">{{ addDrawerFullscreen ? '退出全屏' : '全屏' }}</span>
            </a-button>
            <icon-close @click="cancelAddSegment" />
          </div>
        </div>
      </template>

      <div class="add-segment-container">
        <div class="segment-info-header">
          <div class="segment-title">新分段</div>
          <a-divider direction="vertical" />
          <div class="segment-words">{{ newSegmentContent.length || 0 }} 字符</div>
        </div>

        <div class="segment-content-area">
          <a-textarea
            v-model="newSegmentContent"
            placeholder="在这里添加内容"
            :auto-size="{ minRows: 20, maxRows: 30 }"
            allow-clear
          />
        </div>

        <div class="segment-keywords-area">
          <div class="keywords-label">关键词</div>
          <a-input-tag v-model="newSegmentKeywords" placeholder="添加关键词" allow-clear />
        </div>

        <div class="drawer-footer">
          <div class="left-actions">
            <a-checkbox v-model="continuousAdd">连续新增</a-checkbox>
          </div>
          <div class="right-actions">
            <a-button @click="cancelAddSegment">取消 ESC</a-button>
            <a-button type="primary" :loading="addingSegment" @click="confirmAddSegment">
              保存
              <span class="shortcut-key">⌘ S</span>
            </a-button>
          </div>
        </div>
      </div>
    </a-drawer>

    <!-- 模态框：重命名文档 -->
    <a-modal
      v-model:visible="renameModalVisible"
      title="重命名文档"
      title-align="start"
      @ok="confirmRename"
      @cancel="cancelRename"
    >
      <a-input v-model="newDocumentName" placeholder="请输入新文档名称" />
    </a-modal>

    <!-- 模态框：确认删除 -->
    <a-modal
      v-model:visible="deleteConfirmVisible"
      title="确认删除"
      title-align="start"
      @ok="confirmDelete"
      @cancel="cancelDelete"
    >
      <p>确定要删除此文档吗？此操作不可逆。</p>
    </a-modal>

    <!-- 编辑分段抽屉 -->
    <a-drawer
      v-model:visible="editDrawerVisible"
      :width="drawerFullscreen ? '100%' : 500"
      title="编辑分段"
      :footer="false"
      unmount-on-close
      :header-style="{ padding: '16px 24px' }"
    >
      <template #header>
        <div style="display: flex; justify-content: space-between">
          <div class="custom-header">
            <h2>编辑分段</h2>
          </div>
          <div>
            <a-button class="fullscreen-btn" type="text" size="mini" @click="toggleDrawerFullscreen">
              <template #icon>
                <icon-fullscreen v-if="!drawerFullscreen" />
                <icon-fullscreen-exit v-else />
              </template>
              <span style="margin-left: 4px">{{ drawerFullscreen ? '退出全屏' : '全屏' }}</span>
            </a-button>
            <icon-close @click="cancelEdit" />
          </div>
        </div>
      </template>
      <div class="drawer-content" :class="{ fullscreen: drawerFullscreen }">
        <div class="segment-info-header">
          <div class="segment-id">
            <icon-unordered-list />
            {{ editingSegment.position ? `分段-${String(editingSegment.position).padStart(2, '0')}` : '' }}
          </div>
          <a-divider direction="vertical" />
          <div class="segment-words">{{ editingSegment.word_count || 0 }} 字符</div>
        </div>
        <a-form :model="editingSegment" layout="vertical" class="edit-form">
          <a-form-item label="分段内容">
            <a-textarea
              v-model="editingSegment.content"
              placeholder="请输入分段内容"
              :auto-size="{ minRows: 10, maxRows: 30 }"
            />
          </a-form-item>
          <a-form-item label="关键词">
            <div class="flex flex-wrap gap-3">
              <a-tag
                v-for="(tagItem, index) of editingSegment.keywords"
                :key="tagItem + '-' + index"
                :closable="true"
                @close="tagRemove(tagItem)"
              >
                {{ tagItem }}
              </a-tag>
              <a-input
                v-model="tagValue"
                class="add-tag w-[110px] border border-dashed border-divider-deep px-1.5 p-0"
                placeholder="添加关键词"
                allow-clear
                @keyup.enter="addTag"
              />
            </div>
          </a-form-item>
        </a-form>
        <div class="drawer-footer">
          <a-space>
            <a-button @click="cancelEdit">取消</a-button>
            <a-button type="primary" :loading="savingEdit" @click="saveEdit">
              保存
              <template #icon><icon-save /></template>
            </a-button>
          </a-space>
        </div>
      </div>
    </a-drawer>

    <!-- 添加元数据的模态框 -->
    <a-modal
      v-model:visible="addMetadataModalVisible"
      title="元数据"
      title-align="start"
      :footer="false"
      width="600px"
      @cancel="cancelAddMetadata"
    >
      <!-- 搜索框 -->
      <a-input-search
        v-model="metadataSearchKeyword"
        placeholder="搜索元数据"
        style="margin-bottom: 16px"
        allow-clear
      />

      <!-- 下方按钮区域 -->
      <div class="metadata-actions">
        <a-button type="primary" @click="showNewMetadataForm">
          <template #icon><icon-plus /></template>
          新建元数据
        </a-button>
        <a-button @click="goBack()">
          <template #icon><icon-settings /></template>
          管理
        </a-button>
      </div>

      <!-- 元数据列表 -->
      <div v-if="metadataOptions.length > 0" class="metadata-options-list">
        <div
          v-for="(option, index) in filteredMetadataOptions"
          :key="index"
          class="metadata-option-item"
          @click="selectMetadataOption(option)"
        >
          <span class="option-name">{{ option.key }}</span>
        </div>
      </div>
      <div v-else class="empty-metadata-options">
        <p>暂无元数据选项</p>
      </div>
    </a-modal>

    <!-- 新建元数据的表单 -->
    <a-modal
      v-model:visible="newMetadataFormVisible"
      title="新建元数据"
      title-align="start"
      :footer="false"
      width="500px"
      @cancel="cancelNewMetadataForm"
    >
      <div class="metadata-form-container">
        <!-- <div class="metadata-form-header">
          <a-button class="back-btn" @click="cancelNewMetadataForm">
            <template #icon><icon-left /></template>
            返回
          </a-button>
          <h3 class="form-title">新建元数据</h3>
        </div> -->

        <div class="metadata-form-content">
          <div class="form-section">
            <div class="section-label">类型</div>
            <div class="type-selection">
              <a-radio-group v-model="newMetadataType" type="button">
                <a-radio value="string">String</a-radio>
                <a-radio value="number">Number</a-radio>
                <a-radio value="time">Time</a-radio>
              </a-radio-group>
            </div>
          </div>

          <div class="form-section">
            <div class="section-label">名称</div>
            <a-input v-model="newMetadataKey" placeholder="添加元数据名称" />
          </div>
        </div>

        <div class="metadata-form-footer">
          <a-button @click="cancelNewMetadataForm">取消</a-button>
          <a-button type="primary" :disabled="!newMetadataKey.trim()" @click="confirmAddMetadata">保存</a-button>
        </div>
      </div>
    </a-modal>

    <!-- 添加确认删除分段的模态框 -->
    <a-modal
      v-model:visible="deleteSegmentModalVisible"
      title="删除这个分段？"
      title-align="start"
      modal-class="rename-modal"
      ok-text="我确认"
      @ok="handleDeleteSegment"
      @cancel="cancelDeleteSegment"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Message, Modal } from '@arco-design/web-vue'
import {
  getDatasetDetail,
  getDocumentDetail,
  getDocumentSegments,
  enableDocuments,
  disableDocuments,
  archiveDocuments,
  unArchiveDocuments,
  renameDocument,
  deleteDocument,
  enableDocumentSegment,
  disableDocumentSegment,
  createDocumentSegment,
  deleteDocumentSegment,
  updateDocumentSegment,
  batchEnableDocumentSegments,
  batchDisableDocumentSegments,
  batchDeleteDocumentSegments,
  getBatchProcessingStatus,
  getDatasetMetadata,
  createDatasetMetadata,
  updateDatasetMetadata,
  deleteDatasetMetadata,
  enableBuiltInMetadata,
  disableBuiltInMetadata,
  updateDocumentsMetadata
} from '@/apis/datasets'

// 路由相关
const router = useRouter()
const route = useRoute()
const datasetId = ref('')
const documentId = ref('')

// 文档详情
const documentDetail = ref<any>({})
const documentEnabled = ref(true)
const documentArchived = ref(false)

// 处理状态相关
const isProcessing = ref(false)
const processingProgress = ref(0)
const processingBatchId = ref('')
const processingTimer = ref<number | null>(null)

// 分段列表
const segmentsList = ref<any[]>([])
const totalSegments = ref(0)
const currentPage = ref(1)
const pageSize = ref(10)
const searchKeyword = ref('')
const statusFilter = ref('all')
const selectedSegments = ref<string[]>([])

// 模态框
const addSegmentModalVisible = ref(false)
const newSegmentContent = ref('')
const renameModalVisible = ref(false)
const newDocumentName = ref('')
const deleteConfirmVisible = ref(false)
const deleteSegmentModalVisible = ref(false)
const currentSegment = ref<any>(null)

// 全屏模式状态
const isFullscreen = ref(false)

// 展开视图状态（true: 展开视图，显示全部内容; false: 概要视图，仅显示两行）
const isExpandedView = ref(false)

// 抽屉相关状态
const editDrawerVisible = ref(false)
const drawerFullscreen = ref(false)
const editingSegment = ref<any>({
  id: '',
  position: 0,
  content: '',
  keywords: [],
  word_count: 0
})
const savingEdit = ref(false)

// 元数据相关状态
const isMetadataEditing = ref(false)
const documentMetadata = ref<Array<{ key: string; value: string }>>([])
const savingMetadata = ref(false)

// 添加元数据模态框
const addMetadataModalVisible = ref(false)
const metadataSearchKeyword = ref('')
const metadataOptions = ref<Array<{ key: string; value: string }>>([])
const newMetadataFormVisible = ref(false)
const newMetadataKey = ref('')
const newMetadataValue = ref('')
const newMetadataType = ref('string')

const tagValue = ref('')
const addTag = () => {
  if (!tagValue.value || editingSegment.value.keywords.includes(tagValue.value)) return
  const tag = JSON.parse(JSON.stringify(tagValue.value))
  editingSegment.value.keywords.push(tag)
  tagValue.value = ''
}
const tagRemove = (tagItem) => {
  editingSegment.value.keywords = editingSegment.value.keywords.filter((item) => item !== tagItem)
}
// 计算属性：过滤后的元数据选项
const filteredMetadataOptions = computed(() => {
  if (!metadataSearchKeyword.value) {
    return metadataOptions.value
  }
  return metadataOptions.value.filter((option) =>
    option.key.toLowerCase().includes(metadataSearchKeyword.value.toLowerCase())
  )
})

// 计算属性：分段结果标签
const segmentsCountLabel = computed(() => {
  // 如果有关键词搜索或状态筛选不是"全部"，则显示为"搜索结果"
  if (searchKeyword.value || statusFilter.value !== 'all') {
    return '搜索结果'
  }
  // 否则显示为"分段"
  return '分段'
})

// 添加全选计算属性和方法
const isAllSelected = computed(() => {
  if (segmentsList.value.length === 0) return false
  return segmentsList.value.every((segment) => selectedSegments.value.includes(segment.id))
})

// 添加半选中状态计算属性
const isIndeterminate = computed(() => {
  const selectedCount = selectedSegments.value.length
  return selectedCount > 0 && selectedCount < segmentsList.value.length
})

// 全选/取消全选方法
const toggleSelectAll = (checked: boolean) => {
  if (checked) {
    // 全选
    selectedSegments.value = segmentsList.value.map((segment) => segment.id)
  } else {
    // 取消全选
    selectedSegments.value = []
  }
}

// 组件挂载时初始化
onMounted(async () => {
  // 获取路由参数
  datasetId.value = route.params.datasetId as string
  documentId.value = route.params.documentId as string

  if (!datasetId.value) {
    Message.error('未能获取知识库ID')
    return
  }

  if (!documentId.value) {
    Message.error('未能获取文档ID')
    return
  }

  // 检查是否有正在处理的任务
  try {
    const processingInfo = localStorage.getItem(`document_processing_${datasetId.value}_${documentId.value}`)
    if (processingInfo) {
      const { batchId, timestamp } = JSON.parse(processingInfo)
      // 检查时间戳是否在24小时内
      const now = Date.now()
      const isRecent = now - timestamp < 24 * 60 * 60 * 1000

      if (isRecent && batchId) {
        isProcessing.value = true
        processingBatchId.value = batchId
        // 开始检查处理状态
        startProcessingStatusChecker(batchId)
        // 立即检查一次状态
        await checkProcessingStatus(batchId)

        // 如果处理状态已完成，则加载数据
        if (!isProcessing.value) {
          await loadData()
        } else {
          // 防止长时间处理超时
          setTimeout(
            () => {
              if (isProcessing.value) {
                isProcessing.value = false
                clearInterval(processingTimer.value as number)
                processingTimer.value = null
                Message.warning('处理超时，请稍后刷新页面')
                loadData()
              }
            },
            5 * 60 * 1000
          ) // 5分钟超时
        }
        return
      } else {
        // 清除过期的处理信息
        localStorage.removeItem(`document_processing_${datasetId.value}_${documentId.value}`)
      }
    }

    // 没有处理任务，正常加载数据
    await loadData()
  } catch (error) {
    console.error('检查处理状态失败:', error)
    await loadData()
  }
})

// 加载数据方法
const loadData = async () => {
  try {
    console.log('开始加载数据，documentId:', documentId.value, 'datasetId:', datasetId.value)

    if (!documentId.value || !datasetId.value) {
      console.error('文档ID或知识库ID为空，无法加载数据')
      Message.error('参数错误，请检查文档ID和知识库ID')
      return
    }

    // 并行请求以提高加载速度
    const [documentDetailResponse, documentMetadataResponse, segmentsResponse] = await Promise.all([
      // 1. 获取文档详情（不包含元数据）
      getDocumentDetail(datasetId.value, documentId.value, { metadata: 'without' }).catch((err) => {
        console.error('获取文档详情失败:', err)
        Message.error('获取文档详情失败')
        return {} // 返回空对象以防止解构错误
      }),

      // 2. 获取文档处理规则（仅元数据）
      getDocumentDetail(datasetId.value, documentId.value, { metadata: 'only' }).catch((err) => {
        console.error('获取文档元数据失败:', err)
        // 不向用户显示此错误，因为这是次要数据
        return { metadata: {} } // 返回默认结构以防止解构错误
      }),

      // 3. 获取文档分段数据
      getDocumentSegments(datasetId.value, documentId.value, {
        page: currentPage.value,
        limit: pageSize.value,
        keyword: searchKeyword.value,
        enabled: statusFilter.value !== 'all' ? (statusFilter.value === 'enabled' ? 'true' : 'false') : 'all'
      }).catch((err) => {
        console.error('获取文档分段失败:', err)
        Message.error('获取文档分段失败')
        return { data: [], total: 0 } // 返回默认结构以防止解构错误
      })
    ])

    // 处理文档详情响应
    if (documentDetailResponse && Object.keys(documentDetailResponse).length > 0) {
      documentDetail.value = documentDetailResponse
      documentEnabled.value = !!documentDetailResponse.enabled
      documentArchived.value = !!documentDetailResponse.archived
      newDocumentName.value = documentDetailResponse.name || ''
      console.log('文档详情加载成功:', documentDetail.value)
    } else {
      console.warn('文档详情返回为空')
    }

    // 处理元数据响应
    if (documentMetadataResponse && documentMetadataResponse.metadata) {
      // 将元数据转换为我们需要的格式
      documentMetadata.value = Object.entries(documentMetadataResponse.metadata).map(([key, value]) => ({
        key,
        value: String(value)
      }))
      console.log('文档元数据加载成功:', documentMetadata.value)
    } else {
      console.warn('文档元数据返回为空')
      documentMetadata.value = [] // 确保是空数组而不是undefined
    }

    // 处理分段响应
    if (segmentsResponse && segmentsResponse.data) {
      segmentsList.value = (segmentsResponse.data || []).map((segment) => ({
        ...segment,
        statusLoading: false // 添加状态加载标志
      }))
      totalSegments.value = segmentsResponse.total || 0
      console.log('文档分段加载成功:', segmentsList.value)
    } else {
      console.warn('文档分段返回为空')
      segmentsList.value = [] // 确保是空数组而不是undefined
      totalSegments.value = 0
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    Message.error('加载数据失败，请刷新页面重试')
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1 // 重置页码
  loadData()
}

// 分页
const handlePageChange = () => {
  loadData()
}

// 切换文档状态
const toggleDocumentStatus = async (value: boolean | string | number, ev: Event) => {
  try {
    const enabled = Boolean(value)
    if (enabled) {
      await enableDocuments(datasetId.value, documentId.value)
      Message.success('文档已启用')
    } else {
      await disableDocuments(datasetId.value, documentId.value)
      Message.success('文档已禁用')
    }

    // 刷新文档详情
    await loadData()
  } catch (error) {
    console.error('切换文档状态失败:', error)
    Message.error('切换文档状态失败')
    documentEnabled.value = !Boolean(value) // 恢复开关状态
  }
}

// 归档/取消归档文档
const handleArchiveDocument = async () => {
  try {
    if (documentArchived.value) {
      await unArchiveDocuments(datasetId.value, documentId.value)
      Message.success('文档已取消归档')
    } else {
      await archiveDocuments(datasetId.value, documentId.value)
      Message.success('文档已归档')
    }

    // 刷新文档详情
    await loadData()
  } catch (error) {
    console.error('归档/取消归档文档失败:', error)
    Message.error('归档/取消归档文档失败')
  }
}

// 重命名文档
const handleRenameDocument = () => {
  newDocumentName.value = documentDetail.value.name || ''
  renameModalVisible.value = true
}

const confirmRename = async () => {
  if (!newDocumentName.value.trim()) {
    Message.warning('文档名称不能为空')
    return
  }

  try {
    await renameDocument(datasetId.value, documentId.value, { name: newDocumentName.value.trim() })
    Message.success('文档重命名成功')
    renameModalVisible.value = false

    // 刷新文档详情
    await loadData()
  } catch (error) {
    console.error('文档重命名失败:', error)
    Message.error('文档重命名失败')
  }
}

const cancelRename = () => {
  renameModalVisible.value = false
}

// 删除文档
const handleDeleteDocument = () => {
  deleteConfirmVisible.value = true
}

const confirmDelete = async () => {
  try {
    await deleteDocument(datasetId.value, documentId.value)
    Message.success('文档删除成功')
    deleteConfirmVisible.value = false

    // 返回文档列表页面
    router.push({
      name: 'documents',
      params: { datasetId: datasetId.value }
    })
  } catch (error) {
    console.error('文档删除失败:', error)
    Message.error('文档删除失败')
  }
}

const cancelDelete = () => {
  deleteConfirmVisible.value = false
}

// 抽屉：添加分段
const addDrawerFullscreen = ref(false)
const newSegmentKeywords = ref<string[]>([])
const continuousAdd = ref(false)
const addingSegment = ref(false)

// 全屏切换方法
const toggleAddDrawerFullscreen = () => {
  addDrawerFullscreen.value = !addDrawerFullscreen.value
}

// 添加分段
const handleAddSegment = () => {
  newSegmentContent.value = ''
  newSegmentKeywords.value = []
  addingSegment.value = false
  addSegmentModalVisible.value = true
}

const confirmAddSegment = async () => {
  if (!newSegmentContent.value.trim()) {
    Message.warning('分段内容不能为空')
    return
  }

  addingSegment.value = true

  try {
    // 构建请求参数，仅当有关键词时才添加keywords字段
    const params: { content: string; keywords?: string[] } = {
      content: newSegmentContent.value.trim()
    }

    // 只有当关键词数组不为空时才添加到参数中
    if (newSegmentKeywords.value && newSegmentKeywords.value.length > 0) {
      params.keywords = newSegmentKeywords.value
    }

    // 调用接口
    const result = await createDocumentSegment(datasetId.value, documentId.value, params)

    // 成功提示
    Message.success('添加分段成功')

    // 根据是否连续添加决定下一步操作
    if (!continuousAdd.value) {
      // 非连续添加模式，关闭抽屉
      addSegmentModalVisible.value = false
    } else {
      // 连续添加模式，清空内容保持抽屉打开
      newSegmentContent.value = ''
      newSegmentKeywords.value = []

      // 聚焦文本输入框（可选）
      setTimeout(() => {
        const textarea = document.querySelector('.segment-content-area .arco-textarea-wrapper textarea')
        if (textarea) {
          ;(textarea as HTMLTextAreaElement).focus()
        }
      }, 100)
    }

    // 刷新分段列表
    await loadData()

    return result
  } catch (error) {
    console.error('添加分段失败:', error)
    Message.error('添加分段失败，请检查内容后重试')
  } finally {
    addingSegment.value = false
  }
}

const cancelAddSegment = () => {
  addSegmentModalVisible.value = false
}

// 跳转到分段设置页面
const goToChunkSetting = () => {
  router.push({
    name: 'documentsChunkSetting',
    params: { datasetId: datasetId.value },
    query: { documentId: documentId.value }
  })
}

// 检查文档处理状态
const checkProcessingStatus = async (batchId: string) => {
  try {
    // 调用索引状态API
    const response = await getBatchProcessingStatus(datasetId.value, batchId)

    // 检查返回结构，适配不同格式
    if (response.data && Array.isArray(response.data)) {
      // getDatasetIndexingStatus格式
      if (response.data.length === 0) {
        console.error('未找到索引状态数据')
        return
      }

      // 获取第一个状态记录
      const statusData = response.data[0]

      // 计算进度百分比
      const totalSegments = statusData.total_segments || 0
      const completedSegments = statusData.completed_segments || 0
      processingProgress.value = totalSegments > 0 ? Math.floor((completedSegments / totalSegments) * 100) : 0

      // 检查是否完成或失败
      const status = statusData.indexing_status
      const isCompleted = status === 'completed' || statusData.completed_at !== null
      const isFailed = status === 'failed' || statusData.error !== null

      if (isCompleted || isFailed) {
        clearInterval(processingTimer.value as number)
        processingTimer.value = null
        isProcessing.value = false

        // 刷新数据
        await loadData()

        if (isCompleted) {
          Message.success('文档处理完成')
        } else {
          Message.error('文档处理失败: ' + (statusData.error || '未知错误'))
        }
      }
    } else if ('status' in response) {
      // 旧的getBatchProcessingStatus格式
      // 更新处理进度
      const percentage = 'percentage' in response ? Number(response.percentage) : 0
      processingProgress.value = percentage

      // 如果处理完成，刷新数据并清除定时器
      if (response.status === 'completed' || response.status === 'failed') {
        clearInterval(processingTimer.value as number)
        processingTimer.value = null
        isProcessing.value = false

        // 刷新数据
        await loadData()

        if (response.status === 'completed') {
          Message.success('文档处理完成')
        } else {
          Message.error('文档处理失败')
        }
      }
    } else {
      console.error('未知的响应格式:', response)
    }
  } catch (error) {
    console.error('检查处理状态失败:', error)
  }
}

// 开始检查处理状态的定时器
const startProcessingStatusChecker = (batchId: string) => {
  // 清除可能存在的旧定时器
  if (processingTimer.value) {
    clearInterval(processingTimer.value)
  }

  // 设置新的定时器，每3秒检查一次
  processingTimer.value = setInterval(() => {
    checkProcessingStatus(batchId)
  }, 3000) as unknown as number
}

// 返回上一页
const goBack = () => {
  router.push({
    name: 'documents',
    params: { datasetId: datasetId.value }
  })
}

// 展开分段详情
const expandSegment = (segment: any) => {
  // 复制分段数据以避免直接修改原数据
  editingSegment.value = {
    id: segment.id,
    position: segment.position,
    content: segment.content,
    keywords: segment.keywords ? [...segment.keywords] : [],
    word_count: segment.word_count
  }

  // 打开抽屉
  editDrawerVisible.value = true
}

// 开始标注
const startAnnotation = async () => {
  try {
    // 获取知识库元数据和内置元数据状态
    const metadataInfo = await getDatasetMetadata(datasetId.value)
    console.log('知识库元数据信息:', metadataInfo)

    // // 获取文档详情，包含元数据
    // const documentWithMetadata = await getDocumentDetail(datasetId.value, documentId.value, { metadata: 'with' });

    // if (documentWithMetadata && documentWithMetadata.metadata) {
    //   // 转换元数据格式
    //   documentMetadata.value = Object.entries(documentWithMetadata.metadata).map(([key, value]) => ({
    //     key,
    //     value: String(value)
    //   }));
    //   console.log('文档元数据:', documentMetadata.value);
    // }

    isMetadataEditing.value = true
  } catch (error) {
    console.error('获取元数据失败:', error)
    Message.error('获取元数据失败')
    isMetadataEditing.value = true // 即使失败也打开编辑界面
  }
}

// 全屏切换
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
}

// 计算总召回次数
const calculateTotalHits = () => {
  const totalHits = segmentsList.value.reduce((sum, segment) => sum + (segment.hit_count || 0), 0)
  return `${totalHits} (${segmentsList.value.length > 0 ? ((totalHits / segmentsList.value.length) * 100).toFixed(2) : '0.00'}%)`
}

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (!size) return '0 B'

  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let i = 0
  let formattedSize = size

  while (formattedSize >= 1024 && i < units.length - 1) {
    formattedSize /= 1024
    i++
  }

  return `${formattedSize.toFixed(2)} ${units[i]}`
}

// 格式化日期
const formatDate = (timestamp: number) => {
  if (!timestamp) return '--'

  const date = new Date(timestamp * 1000)
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}`
}

// 切换列表视图模式
const toggleListView = () => {
  isExpandedView.value = !isExpandedView.value
}

// 添加保存和取消编辑的方法
const saveEdit = async () => {
  if (!editingSegment.value.content.trim()) {
    Message.warning('分段内容不能为空')
    return
  }

  savingEdit.value = true

  try {
    const params = {
      content: editingSegment.value.content.trim(),
      keywords: editingSegment.value.keywords
    }

    await updateDocumentSegment(datasetId.value, documentId.value, editingSegment.value.id, params)
    Message.success('分段更新成功')
    editDrawerVisible.value = false

    // 刷新分段列表
    await loadData()
  } catch (error) {
    console.error('更新分段失败:', error)
    Message.error('更新分段失败')
  } finally {
    savingEdit.value = false
  }
}

const cancelEdit = () => {
  editDrawerVisible.value = false
}

// 添加抽屉全屏切换方法
const toggleDrawerFullscreen = () => {
  drawerFullscreen.value = !drawerFullscreen.value
}

// 显示添加元数据的模态框
const showAddMetadataModal = async () => {
  try {
    // 从API获取可用的元数据选项
    const response = await getDatasetMetadata(datasetId.value)
    metadataOptions.value = (response.doc_metadata || []).map((item) => ({
      key: item.name,
      value: ''
    }))
    console.log('可用的元数据选项:', metadataOptions.value)
  } catch (error) {
    console.error('获取元数据选项失败:', error)
    // 如果API调用失败，使用默认空数组
    metadataOptions.value = []
  }

  metadataSearchKeyword.value = ''
  addMetadataModalVisible.value = true
}

// 显示新建元数据表单
const showNewMetadataForm = () => {
  newMetadataKey.value = ''
  newMetadataValue.value = ''
  newMetadataType.value = 'string' // 重置为默认值
  newMetadataFormVisible.value = true
}

// 取消新建元数据表单
const cancelNewMetadataForm = () => {
  newMetadataFormVisible.value = false
}

// 选择已有的元数据选项
const selectMetadataOption = (option: { key: string; value: string }) => {
  // newMetadataKey.value = option.key;
  // newMetadataValue.value = '';
  // newMetadataType.value = 'string'; // 重置为默认值
  // addMetadataModalVisible.value = false;
  // newMetadataFormVisible.value = true;
}

// 添加元数据验证函数
const validateMetadataName = (name: string) => {
  const regex = /^[a-z][a-z0-9_]*$/
  return regex.test(name)
}

// 确认添加元数据
const confirmAddMetadata = async () => {
  if (!newMetadataKey.value.trim()) {
    Message.warning('请输入元数据名称')
    return
  }

  if (!validateMetadataName(newMetadataKey.value.trim())) {
    Message.warning('元数据名称格式不正确，只能包含小写字母、数字和下划线，并且必须以小写字母开头')
    return // 直接返回，不关闭模态框
  }

  try {
    // 调用API创建元数据
    await createDatasetMetadata(datasetId.value, {
      type: newMetadataType.value as 'string' | 'number' | 'time',
      name: newMetadataKey.value.trim()
    })

    // 添加到本地元数据列表
    documentMetadata.value.push({
      key: newMetadataKey.value.trim(),
      value: newMetadataType.value
    })

    // 同时添加到元数据选项列表，以便在选择列表中显示
    metadataOptions.value.push({
      key: newMetadataKey.value.trim(),
      value: ''
    })

    // 关闭模态框
    newMetadataFormVisible.value = false
    addMetadataModalVisible.value = true // 返回到元数据选择列表

    // 显示成功消息
    Message.success('添加元数据成功')
  } catch (error) {
    console.error('添加元数据失败:', error)
    Message.error('添加元数据失败')
  }
}

// 取消添加元数据
const cancelAddMetadata = () => {
  addMetadataModalVisible.value = false
}

// 取消元数据编辑
const cancelMetadataEdit = () => {
  isMetadataEditing.value = false
  // 恢复原始数据
  // 这里可以重新加载元数据，如果需要的话
}

// 保存元数据
const saveMetadata = async () => {
  try {
    savingMetadata.value = true

    // 将元数据数组转换为API需要的格式
    const metadataList = documentMetadata.value.map((item) => ({
      key: item.key,
      value: item.value
    }))

    // 调用API保存元数据 - 使用批量保存接口
    await updateDocumentsMetadata(datasetId.value, {
      operation_data: [
        {
          document_id: documentId.value,
          metadata_list: metadataList
        }
      ]
    })

    Message.success('元数据保存成功')
    isMetadataEditing.value = false

    // 刷新文档数据
    await loadData()
  } catch (error) {
    console.error('保存元数据失败:', error)
    Message.error('保存元数据失败')
  } finally {
    savingMetadata.value = false
  }
}

// 删除元数据项
const removeMetadata = (index: number) => {
  documentMetadata.value.splice(index, 1)
  Message.success('元数据项已删除')
}

// 切换分段启用/禁用状态
const toggleSegmentStatus = async (segment: any) => {
  // 如果已经在加载中，不处理重复点击
  if (segment.statusLoading) return

  // 添加loading状态
  segment.statusLoading = true

  // 保存原始状态
  const originalStatus = segment.enabled

  // 先更新UI状态
  segment.enabled = !segment.enabled

  try {
    if (!segment.enabled) {
      // 禁用分段
      await disableDocumentSegment(datasetId.value, documentId.value, segment.id)
      Message.success('分段已禁用')
    } else {
      // 启用分段
      await enableDocumentSegment(datasetId.value, documentId.value, segment.id)
      Message.success('分段已启用')
    }

    // 成功后刷新分段列表，确保数据与后端同步
    await loadData()
  } catch (error) {
    console.error('切换分段状态失败:', error)
    Message.error('切换分段状态失败')

    // 如果API调用失败，恢复为原始状态
    segment.enabled = originalStatus
  } finally {
    segment.statusLoading = false
  }
}

// 确认删除分块
const deleteSegment = (segment: any) => {
  currentSegment.value = segment
  deleteSegmentModalVisible.value = true
}

// 取消删除分段
const cancelDeleteSegment = () => {
  deleteSegmentModalVisible.value = false
  currentSegment.value = null
}

// 删除分段
const handleDeleteSegment = async () => {
  if (!currentSegment.value) return

  try {
    // 修正调用方式，直接传递segmentId而不是包裹在params中
    await deleteDocumentSegment(datasetId.value, documentId.value, currentSegment.value.id)
    Message.success('分段删除成功')
    deleteSegmentModalVisible.value = false
    currentSegment.value = null

    // 刷新分段列表
    await loadData()
  } catch (error) {
    console.error('删除分段失败:', error)
    Message.error('删除分段失败')
  }
}

// 批量启用分段
const batchEnableSegments = async () => {
  if (selectedSegments.value.length === 0) {
    Message.warning('请至少选择一个分段')
    return
  }

  try {
    // 使用批量启用API
    await batchEnableDocumentSegments(datasetId.value, documentId.value, selectedSegments.value)

    Message.success(`已成功启用 ${selectedSegments.value.length} 个分段`)
    await loadData() // 刷新数据
  } catch (error) {
    console.error('批量启用分段失败:', error)
    Message.error('批量启用分段失败')
  }
}

// 批量禁用分段
const batchDisableSegments = async () => {
  if (selectedSegments.value.length === 0) {
    Message.warning('请至少选择一个分段')
    return
  }

  try {
    // 使用批量禁用API
    await batchDisableDocumentSegments(datasetId.value, documentId.value, selectedSegments.value)

    Message.success(`已成功禁用 ${selectedSegments.value.length} 个分段`)
    await loadData() // 刷新数据
  } catch (error) {
    console.error('批量禁用分段失败:', error)
    Message.error('批量禁用分段失败')
  }
}

// 显示批量删除确认
const showBatchDeleteConfirm = () => {
  if (selectedSegments.value.length === 0) {
    Message.warning('请至少选择一个分段')
    return
  }

  Modal.confirm({
    title: '确定删除吗？',
    titleAlign: 'start',
    content: `确定要删除选中的 ${selectedSegments.value.length} 个分段吗？如果您需要稍后恢复处理，您将从您离开的地方继续。`,
    okText: '确认删除',
    okButtonProps: {
      status: 'danger'
    },
    cancelText: '取消',
    onOk: batchDeleteSegments
  })
}

// 批量删除分段
const batchDeleteSegments = async () => {
  try {
    // 使用批量删除API
    await batchDeleteDocumentSegments(datasetId.value, documentId.value, selectedSegments.value)

    Message.success(`已成功删除 ${selectedSegments.value.length} 个分段`)
    selectedSegments.value = [] // 清空选择
    await loadData() // 刷新数据
  } catch (error) {
    console.error('批量删除分段失败:', error)
    Message.error('批量删除分段失败')
  }
}

// 清空选择
const clearSelection = () => {
  selectedSegments.value = []
}
</script>

<style scoped lang="scss">
.chunk-detail-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--color-border-2);
    background-color: #fff;

    .header-left {
      display: flex;
      align-items: center;

      .back-btn {
        margin-right: 16px;
      }

      .document-title {
        font-size: 18px;
        font-weight: 500;
        margin: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 400px;
      }
    }

    .header-right {
      display: flex;
      align-items: center;
      gap: 8px;

      .action-btn {
        margin: 0 2px;
      }

      .status-switch {
        margin: 0 6px;
      }
    }
  }

  .detail-content {
    display: flex;
    flex: 1;
    overflow: hidden;

    &.fullscreen-mode {
      .segments-list-container {
        width: 100%;
        border-right: none;
      }
    }

    .segments-list-container {
      width: 65%;
      border-right: 1px solid var(--color-border-2);
      display: flex;
      flex-direction: column;
      overflow: auto;
      height: 100%;
      transition: width 0.3s ease;

      &.fullwidth {
        width: 100%;
      }

      .segments-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid var(--color-border-2);
        background-color: var(--color-fill-1);

        .segments-count {
          font-size: 16px;
          font-weight: 500;
          display: flex;
          align-items: center;

          :deep(.arco-checkbox) {
            margin-right: 8px;
          }
        }

        .segments-btns {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;

          .batch-operations {
            display: flex;
            align-items: center;
            gap: 8px;
            background-color: var(--color-fill-2);
            border-radius: 10px;
            padding: 4px 10px;

            .selected-count {
              display: flex;
              align-items: center;
              margin-right: 8px;
              font-size: 14px;

              .count-badge {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                background-color: var(--color-primary);
                color: #155aef;
                font-size: 12px;
                font-weight: bold;
                margin-right: 4px;
              }
            }
          }
        }

        .segments-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }

      .segments-list {
        flex: 1;
        overflow-y: auto;
        padding: 0 16px;
        min-height: 0;

        :deep(.arco-checkbox-group) {
          width: 100%;
        }

        .segment-item {
          display: flex;
          margin: 16px 0;
          padding: 16px;
          border: 1px solid var(--color-border-2);
          border-radius: 8px;
          transition: all 0.2s;
          position: relative;

          &:hover {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            background-color: var(--color-fill-2);

            .hover-show {
              display: block;
            }

            .segment-status {
              display: none;
            }
          }

          &.disabled {
            opacity: 0.6;
            background-color: var(--color-fill-1);
          }

          .segment-checkbox {
            margin-right: 12px;
            display: flex;
            align-items: flex-start;
            padding-top: 4px;
          }

          .segment-content {
            flex: 1;
            min-width: 0;
            cursor: pointer;

            .segment-info {
              display: flex;
              // gap: 16px;
              margin-bottom: 8px;
              color: var(--color-text-3);
              font-size: 13px;

              .segment-number {
                font-weight: 500;
                color: var(--color-text-2);
              }
            }

            .segment-text {
              font-size: 14px;
              line-height: 1.6;
              margin-bottom: 12px;
              white-space: pre-wrap;
              word-break: break-word;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              text-overflow: ellipsis;
              transition: all 0.3s ease;

              &.expanded {
                -webkit-line-clamp: unset;
                max-height: none;
              }
            }

            .segment-tags {
              display: flex;
              flex-wrap: wrap;
              gap: 8px;
            }
          }

          .segment-status {
            margin-left: 12px;
            display: flex;
            align-items: flex-start;
            padding-top: 4px;
            flex-direction: column;
            display: block;
            position: absolute;
            right: 10px;
            top: 8px;
          }

          .hover-show {
            height: 34px;
            line-height: 34px;
            background: #fff;
            border-radius: 8px;
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 0 6px;
            display: none;
            cursor: pointer;
            position: absolute;
            right: 10px;
            top: 8px;

            .icon {
              width: 30px;
              margin-right: 10px;
            }
          }
        }
      }

      .pagination-container {
        padding: 16px;
        border-top: 1px solid var(--color-border-2);
        background-color: #fff;
        display: flex;
        justify-content: flex-end;
        position: sticky;
        bottom: 0;
        z-index: 1;
      }

      /* 处理中状态样式 */
      .processing-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 100%;
        padding: 40px;
        text-align: center;

        .processing-spinner {
          font-size: 36px;
          color: var(--color-primary);
          margin-bottom: 16px;
          animation: rotate 2s linear infinite;
        }

        .processing-title {
          font-size: 18px;
          font-weight: 500;
          margin-bottom: 24px;
        }

        .processing-progress {
          width: 100%;
          max-width: 400px;
          margin-bottom: 32px;

          .progress-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
            color: var(--color-text-3);
          }
        }

        .processing-info {
          display: flex;
          flex-direction: column;
          gap: 16px;
          width: 100%;
          max-width: 400px;
          background-color: var(--color-fill-1);
          padding: 16px;
          border-radius: 8px;

          .info-row {
            display: flex;
            justify-content: space-between;
            font-size: 14px;

            .info-label {
              color: var(--color-text-3);
            }

            .info-value {
              color: var(--color-text-1);
              font-weight: 500;
            }
          }
        }
      }

      @keyframes rotate {
        from {
          transform: rotate(0deg);
        }

        to {
          transform: rotate(360deg);
        }
      }
    }

    .document-info-container {
      width: 35%;
      padding: 16px;
      overflow-y: auto;
      transition:
        width 0.3s ease,
        opacity 0.3s ease;

      .metadata-section,
      .document-info-section,
      .technical-params-section {
        background-color: #fff;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

        h3 {
          font-size: 12px;
          font-weight: 600;
          margin: 0 0 16px;
          color: var(--color-text-1);
          line-height: 16px;
        }

        .section-desc {
          font-size: 14px;
          color: var(--color-text-3);
          margin-bottom: 16px;
          line-height: 1.6;
        }
      }

      .metadata-section {
        .section-header {
          margin-bottom: 16px;

          .title-row {
            position: relative;

            p {
              font-size: 14px;
              font-weight: 600;
            }

            span {
              font-size: 12px;
            }

            .header-actions {
              position: absolute;
              right: 10px;
              top: 0;
            }
          }

          .metadata-edit-content {
            .metadata-list {
              margin-top: 25px;
            }
          }
        }

        .annotate-btn {
          margin-top: 20px;
          width: 40%;
        }
      }

      .info-grid,
      .params-grid {
        display: grid;
        // grid-template-columns: repeat(2, 1fr);
        gap: 16px;

        .info-item,
        .param-item {
          display: flex;
          flex-direction: row;

          .info-label,
          .param-label {
            font-size: 12px;
            font-weight: 500;
            line-height: 16px;
            color: var(--color-text-3);
            margin-bottom: 4px;
            width: 128px;
          }

          .info-value,
          .param-value {
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            color: var(--color-text-1);
            word-break: break-word;
          }
        }
      }
    }
  }
}

.drawer-content {
  display: flex;
  flex-direction: column;
  height: 100%;

  &.fullscreen {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;

    .edit-form {
      .arco-textarea {
        font-size: 16px;
        line-height: 1.8;
        border: none;

        :deep(.arco-textarea-wrapper),
        :deep(.arco-input-tag) {
          border: none;
        }
      }
    }
  }

  .segment-info-header {
    display: flex;
    // gap: 16px;
    // margin-bottom: 16px;
    padding-bottom: 16px;
    // border-bottom: 1px solid var(--color-border-2);

    .segment-id {
      font-size: 16px;
      font-weight: 500;
    }

    .segment-words {
      font-size: 14px;
      color: var(--color-text-3);
    }
  }

  .edit-form {
    flex: 1;
    overflow-y: auto;
  }

  .drawer-footer {
    margin-top: 24px;
    display: flex;
    justify-content: flex-end;
  }
}

/* 元数据模态框样式 */
.metadata-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.metadata-options-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--color-border-2);
  border-radius: 4px;

  .metadata-option-item {
    padding: 10px 16px;
    border-bottom: 1px solid var(--color-border-2);
    cursor: pointer;
    transition: background-color 0.2s;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: var(--color-fill-2);
    }

    .option-name {
      font-size: 14px;
      color: var(--color-text-1);
    }
  }
}

.empty-metadata-options {
  padding: 40px;
  text-align: center;
  color: var(--color-text-3);
  background-color: var(--color-fill-1);
  border-radius: 4px;
}

/* 新建元数据表单样式 */
.metadata-form-container {
  display: flex;
  flex-direction: column;

  .metadata-form-header {
    display: flex;
    align-items: center;
    margin-bottom: 24px;

    .back-btn {
      margin-right: 16px;
    }

    .form-title {
      font-size: 18px;
      font-weight: 500;
      margin: 0;
    }
  }

  .metadata-form-content {
    margin-bottom: 32px;

    .form-section {
      margin-bottom: 20px;

      .section-label {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 12px;
      }

      .type-selection {
        :deep(.arco-radio-group) {
          display: flex;

          .arco-radio-button {
            flex: 1;
            text-align: center;
          }
        }
      }
    }
  }

  .metadata-form-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 16px;
    border-top: 1px solid var(--color-border-2);
  }
}

.rename-modal {
  :deep(.arco-modal) {
    border-radius: 16px;
    overflow: hidden;
  }

  .arco-modal-header {
    border-bottom: none;
  }

  .arco-modal-footer {
    border-top: none;
  }

  .arco-modal-header .arco-modal-title {
    font-size: 18px;
    font-weight: 600;
    line-height: 1.2;
  }

  .rename-content {
    p {
      margin-bottom: 12px;
      text-align: left;
      font-weight: 500;
      line-height: 21px;
    }

    .arco-input-wrapper {
      border-radius: 8px;
      background-color: var(--color-neutral-2);
    }
  }
}

:deep(.arco-textarea-wrapper),
:deep(.arco-input-tag) {
  border: none;
}

// 添加新的样式
:deep(.arco-drawer-header-title) {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .arco-drawer-title {
    flex: 1;
  }

  .arco-drawer-extra {
    flex-shrink: 0;
  }
}

.fullscreen-btn {
  padding: 4px 8px;
  border-radius: 4px;

  &:hover {
    background-color: var(--color-fill-2);
  }
}

.add-segment-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .segment-info-header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    .segment-title {
      font-weight: 500;
      font-size: 14px;
    }

    .segment-words {
      color: var(--color-text-3);
      font-size: 14px;
    }
  }

  .segment-content-area {
    flex: 1;
    margin-bottom: 16px;

    :deep(.arco-textarea) {
      height: 100%;
      min-height: 300px;
      font-size: 14px;
      line-height: 1.6;
    }
  }

  .segment-keywords-area {
    margin-bottom: 24px;

    .keywords-label {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 8px;
    }
  }

  .drawer-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 16px;
    margin-top: 16px;
    border-top: 1px solid var(--color-border-2);

    .right-actions {
      display: flex;
      gap: 8px;
    }

    .shortcut-key {
      opacity: 0.6;
      font-size: 12px;
    }
  }
}

.add-tag {
  padding: auto;

  :deep(.arco-input) {
    padding: 0;
  }
}
</style>
<style lang="scss">
:deep(.arco-drawer-header) {
  width: 100%;
  display: flex;
  justify-content: space-between;

  .custom-header {
    font-size: 14px;
    font-weight: 600;
  }
}
</style>
