<template>
  <div class="w-full">
    <a-upload
      class="upload-area"
      draggable
      :file-list="innerUploadedFiles"
      v-bind="uploadProps"
      :show-file-list="false"
      :custom-request="handleUpload"
    >
      <template #upload-button>
        <div class="upload-trigger">
          <div class="upload-icon">
            <icon-upload />
          </div>
          <div class="upload-text">
            <div class="content">点击或拖拽文件到此处上传</div>
            <div v-if="props.tips" class="tips">{{ props.tips }}</div>
          </div>
        </div>
      </template>
    </a-upload>
    <!-- 自定义上传文件列表 -->
    <div class="upload-files-list">
      <div v-if="fileListTitle && innerUploadedFiles?.length" class="upload-files-list-title mb-3">
        {{ fileListTitle }}
      </div>
      <div v-for="file in innerUploadedFiles" :key="file.uid" class="uploaded-file-item">
        <icon-file class="file-icon" />
        <div class="file-info">
          <div class="file-name">{{ file.name }}</div>
          <div class="file-size">{{ formatFileSize(file.size) }}</div>
        </div>
        <a-button type="text" status="danger" @click.stop="removeFile(file)">
          <icon-delete />
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { UploadProps } from './type'
import { omit } from 'lodash-es'

// 文件类型定义
interface UploadFile {
  uid: string
  name: string
  size: number
  status?: string
  response?: any
  [key: string]: any
}

interface Props extends UploadProps {
  fileListTitle?: string // 文件列表title
  inputsKey?: string // 自定义props
  method?: string // 上传文件的方法
  tips?: string // 上传文件的类型及大小等说明
  type?: string
  removeFileFun?: (fileItem?: UploadFile, key?: string) => void
}

defineOptions({
  name: 'AiFileUpload'
})

const props = defineProps<Props>()

// 业务组件定义上传文件调用后端方法，并将返回结果回传到当前实例，此方案为选中文件直接上传到后端
const emits = defineEmits(['uploadFile'])

const innerUploadedFiles = ref<any[]>([])

const handleUpload = async (options) => {
  const { fileItem, onProgress, onSuccess, onError } = options
  let file = fileItem.file
  try {
    // 验证文件对象存在且有效
    if (!file || typeof file !== 'object') {
      onError && onError(new Error('文件对象无效'))
      return
    }

    // 确保文件有size属性
    if (file.size === undefined) {
      onError && onError(new Error('文件对象缺少size属性'))
      return
    }

    try {
      await emits(
        'uploadFile',
        file,
        (percent) => {
          onProgress && onProgress(percent)
        },
        props.inputsKey,
        props.method,
        (res) => {
          const uploadItem = {
            uid: res.id,
            name: res.name,
            file: file,
            status: 'done',
            size: res.size,
            response: res
          }
          if (!props.multiple) {
            innerUploadedFiles.value = [uploadItem]
          } else {
            innerUploadedFiles.value.push(uploadItem)
          }
          onSuccess && onSuccess(res)
        },
        props.type
      )
    } catch (apiError: any) {
      Message.warning(`文件上传失败: ${apiError.message || '未知错误'}`)
      onError && onError(apiError)
    }
  } catch (error: any) {
    Message.warning(`文件上传错误: ${error.message || '未知错误'}`)
    onError && onError(error)
  }
}

const uploadProps = computed(() => {
  const baseProps = omit(props, [
    'fileListTitle',
    'customRequest',
    'inputsKey',
    'method',
    'tips',
    'showFileList',
    'removeFileFun'
  ])
  return { ...baseProps }
})
// 移除文件
const removeFile = (file: UploadFile) => {
  const fileIndex = innerUploadedFiles.value.findIndex((f) => f.uid === file.uid)
  if (props.removeFileFun) {
    props.removeFileFun(file, props.inputsKey)
  }
  if (fileIndex !== -1) {
    innerUploadedFiles.value.splice(fileIndex, 1)
  }
}

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + 'B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + 'KB'
  } else {
    return (size / (1024 * 1024)).toFixed(2) + 'MB'
  }
}
</script>

<style scoped lang="scss">
.upload-area {
  width: 100%;
  padding: 14px 0;
  :deep(.arco-upload) {
    width: 100%;
  }
  .upload-trigger {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 1px dashed var(--color-border-1);
    border-radius: 8px;
    background-color: var(--color-fill-1);
    padding: 8px 0;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: var(--color-primary-light-2);
      background-color: var(--color-fill-2);
    }

    .upload-icon {
      font-size: 20px;
      color: var(--color-text-3);
      margin-right: 16px;
    }

    .upload-text {
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;

      .content {
        font-size: 14px;
        color: var(--color-text-1);
      }

      .tips {
        font-size: 12px;
        color: var(--color-text-3);
        max-width: 500px;
      }
    }
  }
}

// 上传文件列表样式
.upload-files-list {
  width: 100%;
  .uploaded-file-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 8px 12px;
    border-radius: 8px;
    cursor: pointer;
    margin-bottom: 8px;
    border: 1px solid #dbdbdb;

    &:hover {
      background-color: var(--color-fill-2);
    }

    .file-icon {
      color: var(--color-text-3);
      font-size: 18px;
    }

    .file-info {
      flex: 1;
      font-size: 14px;
      color: var(--color-text-1);
      display: flex;
      align-items: center;
      .file-name {
        font-size: 14px;
        padding-right: 12px;
      }

      .file-size {
        font-size: 12px;
        color: var(--color-text-3);
      }
    }
  }
}
</style>
