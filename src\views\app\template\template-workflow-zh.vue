<template>
  <div class="w">
    <h1>工作流应用 API</h1>
    <p>工作流应用无会话支持，适合用于翻译/文章写作/总结 AI 等等。</p>
    <h3>基础 URL</h3>
    <CodeGroup title="Code">
      <pre>{{ baseUrl }}</pre>
    </CodeGroup>
    <h3>鉴权</h3>
    <p>
      Service API 使用
      <code>API-Key</code>
      进行鉴权。
      <i>
        <strong>
          强烈建议开发者把
          <code>API-Key</code>
          放在后端存储，而非分享或者放在客户端存储，以免
          <code>API-Key</code>
          泄露，导致财产损失。
        </strong>
      </i>
      所有 API 请求都应在
      <strong><code>Authorization</code></strong>
      HTTP Header 中包含您的
      <code>API-Key</code>
      ，如下所示：
    </p>
    <CodeGroup title="Code">
      <pre>Authorization: Bearer {API_KEY}</pre>
    </CodeGroup>
  </div>

  <hr />
  <div class="w">
    <Heading url="/workflows/run" method="POST" title="执行 workflow" name="#Execute-Workflow" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>执行 workflow，没有已发布的 workflow，不可执行。</p>
        <h3>Request Body</h3>
        <ul>
          <li>
            <code>inputs</code>
            (object) Required 允许传入 App 定义的各变量值。 inputs 参数包含了多组键值对（Key/Value
            pairs），每组的键对应一个特定变量，每组的值则是该变量的具体值。
          </li>
          <li>
            <code>response_mode</code>
            (string) Required 返回响应模式，支持：
            <ul>
              <li>
                <code>streaming</code>
                流式模式（推荐）。基于 SSE（
                <strong>
                  <a
                    href="https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events"
                  >
                    Server-Sent Events
                  </a>
                </strong>
                ）实现类似打字机输出方式的流式返回。
              </li>
              <li>
                <code>blocking</code>
                阻塞模式，等待执行完毕后返回结果。（请求若流程较长可能会被中断）。
              </li>
            </ul>
          </li>
          <li>
            <code>user</code>
            (string) Required 用户标识，用于定义终端用户的身份，方便检索、统计。
            由开发者定义规则，需保证用户标识在应用内唯一。
          </li>
          <li>
            <code>files</code>
            (array[object]) Optional 文件列表，适用于传入文件（图片）结合文本理解并回答问题，仅当模型支持 Vision
            能力时可用。
            <ul>
              <li>
                <code>type</code>
                (string) 支持类型：图片
                <code>image</code>
                （目前仅支持图片格式）。
              </li>
              <li>
                <code>transfer_method</code>
                (string) 传递方式，
                <code>remote_url</code>
                图片地址 /
                <code>local_file</code>
                上传文件
              </li>
              <li>
                <code>url</code>
                (string) 图片地址（仅当传递方式为
                <code>remote_url</code>
                时）
              </li>
              <li>
                <code>upload_file_id</code>
                (string) (string) 上传文件 ID（仅当传递方式为
                <code>local_file</code>
                时）
              </li>
            </ul>
          </li>
        </ul>
        <h3>Response</h3>
        <p>
          当
          <code>response_mode</code>
          为
          <code>blocking</code>
          时，返回 CompletionResponse object。 当
          <code>response_mode</code>
          为
          <code>streaming</code>
          时，返回 ChunkCompletionResponse object 流式序列。
        </p>
        <h3>CompletionResponse</h3>
        <p>
          返回完整的 App 结果，
          <code>Content-Type</code>
          为
          <code>application/json</code>
          。
        </p>
        <ul>
          <li>
            <code>workflow_run_id</code>
            (string) workflow 执行 ID
          </li>
          <li>
            <code>task_id</code>
            (string) 任务 ID，用于请求跟踪和下方的停止响应接口
          </li>
          <li>
            <code>data</code>
            (object) 详细内容
            <ul>
              <li>
                <code>id</code>
                (string) workflow 执行 ID
              </li>
              <li>
                <code>workflow_id</code>
                (string) 关联 Workflow ID
              </li>
              <li>
                <code>status</code>
                (string) 执行状态,
                <code>running</code>
                /
                <code>succeeded</code>
                /
                <code>failed</code>
                /
                <code>stopped</code>
              </li>
              <li>
                <code>outputs</code>
                (json) Optional 输出内容
              </li>
              <li>
                <code>error</code>
                (string) Optional 错误原因
              </li>
              <li>
                <code>elapsed_time</code>
                (float) Optional 耗时(s)
              </li>
              <li>
                <code>total_tokens</code>
                (int) Optional 总使用 tokens
              </li>
              <li>
                <code>total_steps</code>
                (int) 总步数（冗余），默认 0
              </li>
              <li>
                <code>created_at</code>
                (timestamp) 开始时间
              </li>
              <li>
                <code>finished_at</code>
                (timestamp) 结束时间
              </li>
            </ul>
          </li>
        </ul>
        <h3>ChunkCompletionResponse</h3>
        <p>
          返回 App 输出的流式块，
          <code>Content-Type</code>
          为
          <code>text/event-stream</code>
          。 每个流式块均为 data: 开头，块之间以
          <code>\n\n</code>
          即两个换行符分隔，如下所示：
        </p>
        <CodeGroup gap>
          <pre>
data: {"event": "message", "task_id": "900bbd43-dc0b-4383-a372-aa6e6c414227", "id": "663c5084-a254-4040-8ad3-51f2a3c1a77c", "answer": "Hi", "created_at": 1705398420}\n\n</pre
          >
        </CodeGroup>
        <p>
          流式块中根据
          <code>event</code>
          不同，结构也不同，包含以下类型：
        </p>
        <ul>
          <li>
            <code>event: workflow_started</code>
            workflow 开始执行
            <ul>
              <li>
                <code>task_id</code>
                (string) 任务 ID，用于请求跟踪和下方的停止响应接口
              </li>
              <li>
                <code>workflow_run_id</code>
                (string) workflow 执行 ID
              </li>
              <li>
                <code>event</code>
                (string) 固定为
                <code>workflow_started</code>
              </li>
              <li>
                <code>data</code>
                (object) 详细内容
                <ul>
                  <li>
                    <code>id</code>
                    (string) workflow 执行 ID
                  </li>
                  <li>
                    <code>workflow_id</code>
                    (string) 关联 Workflow ID
                  </li>
                  <li>
                    <code>sequence_number</code>
                    (int) 自增序号，App 内自增，从 1 开始
                  </li>
                  <li>
                    <code>created_at</code>
                    (timestamp) 开始时间
                  </li>
                </ul>
              </li>
            </ul>
          </li>
          <li>
            <code>event: node_started</code>
            node 开始执行
            <ul>
              <li>
                <code>task_id</code>
                (string) 任务 ID，用于请求跟踪和下方的停止响应接口
              </li>
              <li>
                <code>workflow_run_id</code>
                (string) workflow 执行 ID
              </li>
              <li>
                <code>event</code>
                (string) 固定为
                <code>node_started</code>
              </li>
              <li>
                <code>data</code>
                (object) 详细内容
                <ul>
                  <li>
                    <code>id</code>
                    (string) workflow 执行 ID
                  </li>
                  <li>
                    <code>node_id</code>
                    (string) 节点 ID
                  </li>
                  <li>
                    <code>node_type</code>
                    (string) 节点类型
                  </li>
                  <li>
                    <code>title</code>
                    (string) 节点名称
                  </li>
                  <li>
                    <code>index</code>
                    (int) 执行序号，用于展示 Tracing Node 顺序
                  </li>
                  <li>
                    <code>predecessor_node_id</code>
                    (string) 前置节点 ID，用于画布展示执行路径
                  </li>
                  <li>
                    <code>inputs</code>
                    (array[object]) 节点中所有使用到的前置节点变量内容
                  </li>
                  <li>
                    <code>created_at</code>
                    (timestamp) 开始时间
                  </li>
                </ul>
              </li>
            </ul>
          </li>
          <li>
            <code>event: node_finished</code>
            node 执行结束，成功失败同一事件中不同状态
            <ul>
              <li>
                <code>task_id</code>
                (string) 任务 ID，用于请求跟踪和下方的停止响应接口
              </li>
              <li>
                <code>workflow_run_id</code>
                (string) workflow 执行 ID
              </li>
              <li>
                <code>event</code>
                (string) 固定为
                <code>node_finished</code>
              </li>
              <li>
                <code>data</code>
                (object) 详细内容
                <ul>
                  <li>
                    <code>id</code>
                    (string) node 执行 ID
                  </li>
                  <li>
                    <code>node_id</code>
                    (string) 节点 ID
                  </li>
                  <li>
                    <code>index</code>
                    (int) 执行序号，用于展示 Tracing Node 顺序
                  </li>
                  <li>
                    <code>predecessor_node_id</code>
                    (string) optional 前置节点 ID，用于画布展示执行路径
                  </li>
                  <li>
                    <code>inputs</code>
                    (array[object]) 节点中所有使用到的前置节点变量内容
                  </li>
                  <li>
                    <code>process_data</code>
                    (json) Optional 节点过程数据
                  </li>
                  <li>
                    <code>outputs</code>
                    (json) Optional 输出内容
                  </li>
                  <li>
                    <code>status</code>
                    (string) 执行状态
                    <code>running</code>
                    /
                    <code>succeeded</code>
                    /
                    <code>failed</code>
                    /
                    <code>stopped</code>
                  </li>
                  <li>
                    <code>error</code>
                    (string) Optional 错误原因
                  </li>
                  <li>
                    <code>elapsed_time</code>
                    (float) Optional 耗时(s)
                  </li>
                  <li>
                    <code>execution_metadata</code>
                    (json) 元数据
                    <ul>
                      <li>
                        <code>total_tokens</code>
                        (int) optional 总使用 tokens
                      </li>
                      <li>
                        <code>total_price</code>
                        (decimal) optional 总费用
                      </li>
                      <li>
                        <code>currency</code>
                        (string) optional 货币，如
                        <code>USD</code>
                        /
                        <code>RMB</code>
                      </li>
                    </ul>
                  </li>
                  <li>
                    <code>created_at</code>
                    (timestamp) 开始时间
                  </li>
                </ul>
              </li>
            </ul>
          </li>
          <li>
            <code>event: workflow_finished</code>
            workflow 执行结束，成功失败同一事件中不同状态
            <ul>
              <li>
                <code>task_id</code>
                (string) 任务 ID，用于请求跟踪和下方的停止响应接口
              </li>
              <li>
                <code>workflow_run_id</code>
                (string) workflow 执行 ID
              </li>
              <li>
                <code>event</code>
                (string) 固定为
                <code>workflow_finished</code>
              </li>
              <li>
                <code>data</code>
                (object) 详细内容
                <ul>
                  <li>
                    <code>id</code>
                    (string) workflow 执行 ID
                  </li>
                  <li>
                    <code>workflow_id</code>
                    (string) 关联 Workflow ID
                  </li>
                  <li>
                    <code>status</code>
                    (string) 执行状态
                    <code>running</code>
                    /
                    <code>succeeded</code>
                    /
                    <code>failed</code>
                    /
                    <code>stopped</code>
                  </li>
                  <li>
                    <code>outputs</code>
                    (json) Optional 输出内容
                  </li>
                  <li>
                    <code>error</code>
                    (string) Optional 错误原因
                  </li>
                  <li>
                    <code>elapsed_time</code>
                    (float) Optional 耗时(s)
                  </li>
                  <li>
                    <code>total_tokens</code>
                    (int) Optional 总使用 tokens
                  </li>
                  <li>
                    <code>total_steps</code>
                    (int) 总步数（冗余），默认 0
                  </li>
                  <li>
                    <code>created_at</code>
                    (timestamp) 开始时间
                  </li>
                  <li>
                    <code>finished_at</code>
                    (timestamp) 结束时间
                  </li>
                </ul>
              </li>
            </ul>
          </li>
          <li>
            <code>event: ping</code>
            每 10s 一次的 ping 事件，保持连接存活。
          </li>
        </ul>
        <h3>Errors</h3>
        <ul>
          <li>
            400，
            <code>invalid_param</code>
            ，传入参数异常
          </li>
          <li>
            400，
            <code>app_unavailable</code>
            ，App 配置不可用
          </li>
          <li>
            400，
            <code>provider_not_initialize</code>
            ，无可用模型凭据配置
          </li>
          <li>
            400，
            <code>provider_quota_exceeded</code>
            ，模型调用额度不足
          </li>
          <li>
            400，
            <code>model_currently_not_support</code>
            ，当前模型不可用
          </li>
          <li>
            400，
            <code>workflow_request_error</code>
            ，workflow 执行失败
          </li>
          <li>500，服务内部异常</li>
        </ul>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="POST" label="/workflows/run">
          <pre>
curl -X POST 'http://192.168.1.59/v1/workflows/run' \
--header 'Authorization: Bearer {api_key}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "inputs": {},
    "response_mode": "streaming",
    "user": "abc-123"
}'
          </pre>
        </CodeGroup>
        <h3>Blocking Mode</h3>
        <CodeGroup title="Response">
          <pre>
{
    "workflow_run_id": "djflajgkldjgd",
    "task_id": "9da23599-e713-473b-982c-4328d4f5c78a",
    "data": {
        "id": "fdlsjfjejkghjda",
        "workflow_id": "fldjaslkfjlsda",
        "status": "succeeded",
        "outputs": {
          "text": "Nice to meet you."
        },
        "error": null,
        "elapsed_time": 0.875,
        "total_tokens": 3562,
        "total_steps": 8,
        "created_at": 1705407629,
        "finished_at": 1727807631
    }
}
          </pre>
        </CodeGroup>
        <h3>Streaming Mode</h3>
        <CodeGroup title="Response">
          <pre>
  data: {"event": "workflow_started", "task_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "workflow_run_id": "5ad498-f0c7-4085-b384-88cbe6290", "data": {"id": "5ad498-f0c7-4085-b384-88cbe6290", "workflow_id": "dfjasklfjdslag", "sequence_number": 1, "created_at": 1679586595}}
  data: {"event": "node_started", "task_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "workflow_run_id": "5ad498-f0c7-4085-b384-88cbe6290", "data": {"id": "5ad498-f0c7-4085-b384-88cbe6290", "node_id": "dfjasklfjdslag", "node_type": "start", "title": "Start", "index": 0, "predecessor_node_id": "fdljewklfklgejlglsd", "inputs": {}, "created_at": 1679586595}}
  data: {"event": "node_finished", "task_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "workflow_run_id": "5ad498-f0c7-4085-b384-88cbe6290", "data": {"id": "5ad498-f0c7-4085-b384-88cbe6290", "node_id": "dfjasklfjdslag", "node_type": "start", "title": "Start", "index": 0, "predecessor_node_id": "fdljewklfklgejlglsd", "inputs": {}, "outputs": {}, "status": "succeeded", "elapsed_time": 0.324, "execution_metadata": {"total_tokens": 63127864, "total_price": 2.378, "currency": "USD"},  "created_at": 1679586595}}
  data: {"event": "workflow_finished", "task_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "workflow_run_id": "5ad498-f0c7-4085-b384-88cbe6290", "data": {"id": "5ad498-f0c7-4085-b384-88cbe6290", "workflow_id": "dfjasklfjdslag", "outputs": {}, "status": "succeeded", "elapsed_time": 0.324, "total_tokens": 63127864, "total_steps": "1", "created_at": 1679586595, "finished_at": 1679976595}}
          </pre>
        </CodeGroup>
      </a-col>
    </a-row>
  </div>

  <hr />
  <div class="w">
    <Heading url="/workflows/:task_id/stop" method="POST" title="停止响应" name="#stop-generatebacks" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>仅支持流式模式。</p>
        <h3>Path</h3>
        <ul>
          <li>
            <code>task_id</code>
            (string) 任务 ID，可在流式返回 Chunk 中获取
          </li>
        </ul>
        <h3>Request Body</h3>
        <ul>
          <li>
            <code>user</code>
            (string) Required 用户标识，用于定义终端用户的身份，必须和发送消息接口传入 user 保持一致。
          </li>
        </ul>
        <h3>Response</h3>
        <ul>
          <li>
            <code>result</code>
            (string) 固定返回 "success"
          </li>
        </ul>
      </a-col>
      <a-col :span="11">
        <h3 style="margin-top: 0">Request Example</h3>
        <CodeGroup title="Request" tag="POST" label="/workflows/:task_id/stop">
          <pre>
curl -X POST 'http://192.168.1.59/v1/workflows/:task_id/stop' \
-H 'Authorization: Bearer {api_key}' \
-H 'Content-Type: application/json' \
--data-raw '{"user": "abc-123"}'
          </pre>
        </CodeGroup>
        <h3>Response Example</h3>
        <CodeGroup title="Response">
          <pre>
{
  "result": "success"
}
          </pre>
        </CodeGroup>
      </a-col>
    </a-row>
  </div>

  <hr />
  <div class="w">
    <Heading url="/parameters" method="GET" title="获取应用配置信息" name="#parameters" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>用于进入页面一开始，获取功能开关、输入参数名称、类型及默认值等使用。</p>
        <h3>Query</h3>
        <Property key="user" name="user" type="string" p>
          用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
        </Property>
        <h3>Response</h3>
        <ul>
          <li>
            <code>user_input_form</code>
            (array[object]) 用户输入表单配置
            <ul>
              <li>
                <code>text-input</code>
                (object) 文本输入控件
                <ul>
                  <li>
                    <code>label</code>
                    (string) 控件展示标签名
                  </li>
                  <li>
                    <code>variable</code>
                    (string) 控件 ID
                  </li>
                  <li>
                    <code>required</code>
                    (bool) 是否必填
                  </li>
                  <li>
                    <code>default</code>
                    (string) 默认值
                  </li>
                </ul>
              </li>
              <li>
                <code>paragraph</code>
                (object) 段落文本输入控件
                <ul>
                  <li>
                    <code>label</code>
                    (string) 控件展示标签名
                  </li>
                  <li>
                    <code>variable</code>
                    (string) 控件 ID
                  </li>
                  <li>
                    <code>required</code>
                    (bool) 是否必填
                  </li>
                  <li>
                    <code>default</code>
                    (string) 默认值
                  </li>
                </ul>
              </li>
              <li>
                <code>select</code>
                (object) 下拉控件
                <ul>
                  <li>
                    <code>label</code>
                    (string) 控件展示标签名
                  </li>
                  <li>
                    <code>variable</code>
                    (string) 控件 ID
                  </li>
                  <li>
                    <code>required</code>
                    (bool) 是否必填
                  </li>
                  <li>
                    <code>default</code>
                    (string) 默认值
                  </li>
                  <li>
                    <code>options</code>
                    (array[string]) 选项值
                  </li>
                </ul>
              </li>
            </ul>
          </li>
          <li>
            <code>file_upload</code>
            (object) 文件上传配置
            <ul>
              <li>
                <code>image</code>
                (object) 图片设置 当前仅支持图片类型：
                <code>png</code>
                ,
                <code>jpg</code>
                ,
                <code>jpeg</code>
                ,
                <code>webp</code>
                ,
                <code>gif</code>
                <ul>
                  <li>
                    <code>enabled</code>
                    (bool) 是否开启
                  </li>
                  <li>
                    <code>number_limits</code>
                    (int) 图片数量限制，默认 3
                  </li>
                  <li>
                    <code>transfer_methods</code>
                    (array[string]) 传递方式列表，remote_url , local_file，必选一个
                  </li>
                </ul>
              </li>
            </ul>
          </li>
          <li>
            <code>system_parameters</code>
            (object) 系统参数
            <ul>
              <li>
                <code>image_file_size_limit</code>
                (string) 图片文件上传大小限制（MB）
              </li>
            </ul>
          </li>
        </ul>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="GET" label="/parameters" gap>
          <pre>
curl -X POST 'http://192.168.1.59/v1/workflows/:task_id/stop' \
-H 'Authorization: Bearer {api_key}' \
-H 'Content-Type: application/json' \
--data-raw '{"user": "abc-123"}'
          </pre>
        </CodeGroup>
        <CodeGroup title="Response">
          <pre>
{
  "result": "success"
}
          </pre>
        </CodeGroup>
      </a-col>
    </a-row>
  </div>
  <!-- <hr />
  <div class="w">
    <a-row :gutter="[20, 0]">
      <a-col :span="13"></a-col>
      <a-col :span="11"></a-col>
    </a-row>
  </div> -->
  <!-- <h3>阻塞模式</h3>
        <CodeGroup title="Response">
          <pre>

          </pre>
        </CodeGroup> -->
</template>
<script setup lang="ts" name="template-workflow-zh">
import CodeGroup from './components/code-group.vue'
import Heading from './components/heading.vue'
import Property from './components/property.vue'

const props = withDefaults(
  defineProps<{
    baseUrl?: string
    inputs?: object
  }>(),
  {
    baseUrl: '',
    inputs: () => ({})
  }
)
</script>
