import {
  type CredentialFormSchemaRadio,
  type CredentialFormSchemaTextInput,
  FormTypeEnum,
  MODEL_TYPE_TEXT,
  ModelTypeEnum
} from '@/apis/model-mgmt/type'

export const sizeFormat = (size: number) => {
  if (!size) return ''
  const remainder = Math.floor(size / 1000)
  if (remainder < 1) return `${size}`
  else return `${remainder}K`
}
/**
 * 获取模型tag
 * @param modelType
 */
export const modelTypeFormat = (modelType) => {
  if (modelType === ModelTypeEnum.textEmbedding) return 'TEXT EMBEDDING'
  return modelType.toLocaleUpperCase()
}
/**
 * 获取模型名称
 * @param obj
 */
export const renderI18nName = (obj: Record<string, string>) => {
  if (!obj) return ''
  if (obj?.['zh_Hans']) return obj['zh_Hans']
  if (obj?.en_US) return obj.en_US
  return Object.values(obj)[0]
}

/**
 * 模型类型转换
 * @param modelTypes
 */
export const genModelTypeFormSchema = (modelTypes: ModelTypeEnum[]) => {
  return {
    type: FormTypeEnum.radio,
    label: {
      zh_Hans: '模型类型',
      en_US: 'Model Type'
    },
    variable: '__model_type',
    default: modelTypes[0],
    required: true,
    show_on: [],
    options: modelTypes.map((modelType: ModelTypeEnum) => {
      return {
        value: modelType,
        label: {
          zh_Hans: MODEL_TYPE_TEXT[modelType],
          en_US: MODEL_TYPE_TEXT[modelType]
        },
        show_on: []
      }
    })
  } as CredentialFormSchemaRadio
}

export const genModelNameFormSchema = (model?: Pick<CredentialFormSchemaTextInput, 'label' | 'placeholder'>) => {
  return {
    type: FormTypeEnum.textInput,
    label: model?.label || {
      zh_Hans: '模型名称',
      en_US: 'Model Name'
    },
    variable: '__model_name',
    required: true,
    show_on: [],
    placeholder: model?.placeholder || {
      zh_Hans: '请输入模型名称',
      en_US: 'Please enter model name'
    }
  } as CredentialFormSchemaTextInput
}
