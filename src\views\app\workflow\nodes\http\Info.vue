<template>
  <div class="field-info">
    <Fieldlist :list="fieldList" :nodeInfo="nodeInfo" />
    <AddFieldModal />
    <AddFieldModal ref="AddFieldModalRef" @save-success="onSave" />
  </div>
</template>

<script setup lang="ts">
import Fieldlist from './components/FieldList.vue'
import AddFieldModal from './components/AddFieldModal.vue'

interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
}

const AddFieldModalRef = ref<InstanceType<typeof AddFieldModal>>()
// 新增
const onAdd = () => {
  AddFieldModalRef.value?.onAdd()
}
const props = defineProps({
  popupContainer: String,
  nodeInfo: Object
})
const fieldList = ref<FieldType[]>([])

const onSave = (e) => {
  const field = {
    label: e.label,
    max_length: e.max_length,
    options: e.options,
    required: e.required && e.required.length > 0 ? e.required[0] : false,
    type: e.type,
    variable: e.variable
  }
  fieldList.value.push(field)
}
</script>
<style scoped lang="scss">
.field-info {
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-1);
}
</style>
