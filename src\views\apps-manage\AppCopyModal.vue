<template>
  <a-modal
    v-model:visible="visible"
    title="复制应用"
    title-align="start"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 500 ? 500 : '100%'"
    draggable
    @before-ok="save"
    @cancel="handleCancel"
    @close="reset"
  >
    <AiForm ref="formRef" v-model="form" layout="vertical" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { AiForm, type ColumnItem } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'
import { copyApp } from '@/apis'

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()

const visible = ref(false)
const formRef = ref<InstanceType<typeof AiForm>>()

const [form, resetForm] = useResetReactive({
  name: '' as string,
  mode: '',
  icon: '🤖',
  icon_background: '#FFEAD5',
  icon_type: 'emoji',
  id: ''
})

const columns = reactive<ColumnItem[]>([
  {
    label: '应用名称',
    field: 'name',
    type: 'input',
    span: 24,
    required: true,
    props: {
      maxLength: 100
    }
  }
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 处理取消
const handleCancel = () => {
  visible.value = false
  reset()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    await copyApp(form)
    Message.success('复制成功')
    visible.value = false
    emit('save-success')
    reset()
    return true
  } catch (error) {
    return false
  }
}
// 复制
const onCopy = async ({ id, name, mode, icon, icon_background, icon_type }) => {
  reset()
  visible.value = true
  const params = {
    id,
    name,
    mode,
    icon,
    icon_background,
    icon_type
  }
  Object.assign(form, params)
}
defineExpose({ onCopy })
</script>
