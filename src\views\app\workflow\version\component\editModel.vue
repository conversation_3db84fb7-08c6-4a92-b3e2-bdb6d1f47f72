<template lang="">
  <a-modal v-model:visible="visible" @cancel="handleCancel">
    <template #title>编辑信息</template>
    <a-form :model="form" layout="vertical">
      <a-form-item field="marked_name" label="标题">
        <a-input v-model="form.marked_name" placeholder="命名" />
      </a-form-item>
      <a-form-item field="marked_comment" label="发布说明">
        <a-textarea v-model="form.marked_comment" placeholder="请描述变更" allow-clear />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleOk">确定</a-button>
    </template>
  </a-modal>
</template>
<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { getversion } from '@/apis/workflow/index'
const props = defineProps<{
  editvisable?: boolean
  editData?: any
}>()
const route = useRoute()
const appId = route.params.appId as string
const visible = props.editvisable
const form = ref({
  marked_name: props.editData.marked_name,
  marked_comment: props.editData.marked_comment
})
const rootMethods = inject('rootMethods')
const emits = defineEmits(['hideEditModel'])
const handleCancel = () => {
  emits('hideEditModel')
}
const handleOk = () => {
  getversion(appId, props.editData.id, { ...form.value }).then((res) => {
    Message.success('编辑成功')
    handleCancel()
    rootMethods.versionhistory()
  })
}
</script>
<style scoped lang="scss"></style>
