<template>
  <div class="sub-model-list">
    <a-list v-if="subModelList?.length > 0" size="small" :hoverable="true">
      <!--<template #header>-->
      <!--  子模型list-->
      <!--</template>-->
      <a-list-item v-for="(item, index) in subModelList" :key="index">
        {{ item.model }}
        <a-space>
          <a-tag v-for="tag in item.provider?.supported_model_types" :key="tag">{{ tag }}</a-tag>
        </a-space>

        <template #actions>
          <template v-if="item.fetch_from === ConfigurationMethodEnum.customizableModel">
            <a-button @click="handleAddEditModel(item, 'edit')">配置</a-button>
          </template>
          <template
            v-else-if="!item.deprecated && [ModelStatusEnum.active, ModelStatusEnum.disabled].includes(item.status)"
          >
            <a-button @click="handleLoadBalancing(item)">设置负载均衡</a-button>
          </template>

          <a-switch
            :default-checked="item.status === ModelStatusEnum.active"
            :disabled="item.deprecated"
            type="round"
            @change="handleChangeStatus(item, $event)"
          >
            <!--:beforeChange="handleChangeIntercept(item)"-->
            <!--@change="handleChangeStatus(item,$event)"-->
          </a-switch>
        </template>
      </a-list-item>
    </a-list>

    <LoadBalancingModal
      v-if="loadBalancingVisible"
      :loadBalancingItem="loadBalancingItem"
      @closeLoadBalancingModal="closeLoadBalancingModal"
    />

    <!--编辑模型-->
    <AddEditModelModal
      v-if="addEditModelVisible"
      :providerItem="providerItem"
      :subModelItem="subModelItem"
      :formSchemas="formSchemas"
      :pageMode="pageMode"
      @closeAddEditModelModal="closeAddEditModelModal"
    />
  </div>
</template>

<script setup lang="ts">
import {
  ConfigurationMethodEnum,
  type CredentialFormSchema,
  type DefaultModelResponse,
  type ModelItem,
  type ModelProvider,
  ModelStatusEnum,
  type SubModelListItem
} from '@/apis/model-mgmt/type'
import { changeModelStatusHttp } from '@/apis/model-mgmt'
import LoadBalancingModal from '@/views/model-mgmt/components/LoadBalancingModal.vue'
import AddEditModelModal from '@/views/model-mgmt/components/AddEditModelModal.vue'
import { genModelNameFormSchema, genModelTypeFormSchema } from '@/views/app/workflow/utils/model'

defineOptions({ name: 'SubModelList' })

const props = defineProps<{
  subModelList: SubModelListItem[]
  providerItem: ModelProvider
}>()

/**
 * 切换子模型的状态
 * @param item
 * @param status
 */
const handleChangeStatus = async (item: SubModelListItem, status) => {
  const params = {
    model_type: item.model_type || '',
    model: item.model || ''
  }
  const res = await changeModelStatusHttp(params, item.provider?.provider, status ? 'enable' : 'disable')
}
const handleChangeIntercept = async (item, newVal) => {
  await new Promise((resolve) => setTimeout(resolve, 500))
  return true
}

// 设置负载均衡
const loadBalancingVisible = ref(false)
const loadBalancingItem = ref<SubModelListItem | any>({})
const handleLoadBalancing = (item) => {
  loadBalancingItem.value = item
  loadBalancingVisible.value = true
}
const closeLoadBalancingModal = () => {
  loadBalancingVisible.value = false
}
const handleEditModal = (item) => {}

const addEditModelVisible = ref(false)
const formSchemas = reactive<CredentialFormSchema[]>([])
const subModelItem = ref<SubModelListItem | {}>({})
let pageMode = ref('')
const handleAddEditModel = (item, mode: string) => {
  pageMode.value = mode
  subModelItem.value = item
  // 清空
  formSchemas.splice(0, formSchemas.length)

  // 组装form
  const provider = props.providerItem
  formSchemas.push(genModelTypeFormSchema(provider.supported_model_types))
  formSchemas.push(genModelNameFormSchema(provider.model_credential_schema?.model))
  formSchemas.push(...provider.model_credential_schema.credential_form_schemas)

  // 显示弹框
  addEditModelVisible.value = true
}
const closeAddEditModelModal = (type: string) => {
  addEditModelVisible.value = false
}
</script>

<style scoped lang="scss">
.sub-model-list {
  padding: var(--padding);
}

.arco-list-item {
  align-items: center;
}
</style>
