<template>
  <div class="output-var-by-code">
    <Field :fieldTitle="'输出变量'" :tooltip="''">
      <template #operation>
        <a-button type="text" @click="handleAdd">
          <icon-plus />
        </a-button>
      </template>

      <a-table :columns="columns" :data="varTable" :show-header="false" :pagination="false">
        <template #varKey="{ record }">
          <a-input v-model="record.varKey" @change="handleChange" />
        </template>
        <template #varValue="{ record }">
          <a-select v-model="record.varValue.type" @change="handleChange">
            <a-option v-for="option in TYPE_OPTIONS" :key="option.value" :value="option.value">
              {{ option.text }}
            </a-option>
          </a-select>
        </template>
        <template #operation="{ rowIndex }">
          <icon-delete :size="18" class="cursor-pointer" @click="deleteItem(rowIndex)" />
        </template>
      </a-table>
    </Field>
  </div>
</template>
<script setup lang="ts">
import { TYPE_OPTIONS } from '@/views/app/workflow/constant/common'
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'

const columns = [
  {
    title: 'Key',
    slotName: 'varKey',
    width: '150'
  },
  {
    title: 'Value',
    slotName: 'varValue',
    width: '100'
  },
  {
    title: 'Operation',
    slotName: 'operation',
    width: '40'
  }
]
const props = defineProps({
  nodeInfo: Object,
  default: () => ({})
})

const varTable = ref<any[]>([])
watch(
  () => props.nodeInfo?.outputs,
  () => {
    varTable.value = []
    let temp = props.nodeInfo?.outputs
    const arr = Object.keys(temp)
    arr.forEach((item) => {
      varTable.value.push({
        varKey: item,
        varValue: temp[item]
      })
    })
  },
  { immediate: true }
)

const deleteItem = (index) => {
  varTable.value.splice(index, 1)
  handleChange()
}
const handleChange = () => {
  const data = getData()
  props.nodeInfo!.outputs = data
}
const getData = () => {
  let obj = {}
  varTable.value.forEach((item) => {
    obj[item.varKey] = item.varValue
  })
  return obj
}
const getVarIndex = (list, index) => {
  for (let i = index; i < 10000; i++) {
    let flag = list.some((item) => item.varKey === 'var_' + i)
    if (!flag) {
      return i
      break
    }
    continue
  }
}
const handleAdd = () => {
  let leng = varTable.value.length + 1
  let newIndex = getVarIndex(varTable.value, leng)

  const newKey = 'var_' + newIndex

  varTable.value.push({
    varKey: newKey,
    varValue: { type: 'string', children: null }
  })
  handleChange()
}
</script>
<style scoped lang="scss"></style>
