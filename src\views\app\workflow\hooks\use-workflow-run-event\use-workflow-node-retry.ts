import { useVueFlow } from '@vue-flow/core'
import { useWorkflowStore } from '@/stores'
import type { NodeFinishedResponse } from '@/views/app/workflow/types/workflow'

export const useWorkflowNodeRetry = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowNodeRetry = (params: NodeFinishedResponse) => {
    const { data } = params
    const { workflowRunningData, setWorkflowRunningData } = workflowStore
    const { nodes, setNodes } = useVueFlow()

    const workflowData = workflowRunningData
    workflowData?.tracing.push(data)
    setWorkflowRunningData(workflowData)

    if (nodes.value?.length) {
      const newNodes = nodes.value.map((node) => {
        if (node.id === data.node_id) {
          node.data._retryIndex = data.retry_index
        }
        return node
      })
      setNodes(newNodes)
    }
  }

  return {
    handleWorkflowNodeRetry
  }
}
