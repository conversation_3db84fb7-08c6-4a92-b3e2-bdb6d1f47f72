<template>
  <div>
    <a-button class="mr-2 shrink-0" @click="setIsShowAgentSetting">
      <icon-settings class="mr-1 h-4 w-4 text-text-tertiary" />
      Agent 设置
    </a-button>

    <AgentSetting v-if="isShowAgentSetting" :agentConfig="agentConfig" @closeAgentSetting="closeAgentSetting" />
  </div>
</template>
<script setup lang="ts">
import AgentSetting from '@/views/app/configuration/config/agent/agent-setting/index.vue'

const props = defineProps({
  agentConfig: {
    type: Object
  }
})
const isShowAgentSetting = ref(false)
const setIsShowAgentSetting = () => {
  isShowAgentSetting.value = true
}
const closeAgentSetting = () => {
  isShowAgentSetting.value = false
}
</script>
<style scoped lang="scss"></style>
