import { type GraphNode, useVueFlow } from '@vue-flow/core'

// import { useAppManagerStore } from '@/store/app-manager';

import { IterationStartNode, LoopStartNode } from '../nodes/nodes'
import { NodeType } from '../types/node'

import { deepCopy } from '@/utils/index'
import { Message, Modal } from '@arco-design/web-vue'
import { useEnum } from '../plugins/enum'

const $enum = useEnum()

const state = {
  appId: null,
  instance: null,
  showNodeRun: ref(false)
}
export default function useNodeHook(instance?, appId?) {
  if (instance) state.instance = instance
  if (appId) state.appId = appId

  const { addNodes, updateNodeData, removeNodes, getEdges, getNodes, removeEdges, getSelectedNodes, findNode } =
    useVueFlow(state.appId!)

  // const appManagerStore = useAppManagerStore();

  /**
   * 添加节点
   */
  function addNodesFn(addList: { type: NodeType; [key: string]: any }[]): GraphNode[] {
    const nodes: GraphNode[] = []
    for (let a = 0; a < addList.length; a++) {
      const { type, ...config } = addList[a]
      const createFn = $enum.WORKFLOW_NODE_TYPE.$MATCH_create(type)
      const addNode: GraphNode = createFn(config)
      nodes.push(addNode)
      switch (addNode.data.type) {
        case NodeType.循环:
        case NodeType.迭代: {
          const { id: parentNode } = addNode
          addNode.data.start_node_id = `${parentNode}start`
          let childNodeClass
          if (addNode.data.type === NodeType.循环) {
            childNodeClass = LoopStartNode
          } else {
            childNodeClass = IterationStartNode
          }
          const addChildNode = new childNodeClass({
            parentNode
          }) as GraphNode
          nodes.push(addChildNode)
          break
        }
      }
    }

    addNodes(nodes)
    getSelectedNodes.value.forEach((node) => {
      updateNodeData(node.id, { selected: false })
      node.data = {
        ...node.data,
        selected: false
      }
    })
    nodes.forEach((node) => {
      if (node.selectable) {
        updateNodeData(node.id, { selected: node.selected })
        node.data = {
          ...node.data,
          selected: node.selected
        }
      }
    })
    return nodes
  }

  /**
   * 删除节点
   */
  async function removeNodesFn(removeList: GraphNode[]) {
    removeNodes(removeList)
  }

  function showConfirmRemoveModal(title: string) {
    return new Promise((resolve) => {
      Modal.confirm({
        title: `删除${title}节点？`,
        content: `删除${title}节点将删除所有子节点`,
        onOk() {
          resolve(true)
        },
        onCancel() {
          resolve(false)
        }
      })
    })
  }

  /**
   * 修改节点
   * @param {GraphNode} node 需要修改的节点
   * @param {NodeType} type 需要修改的类型
   */
  async function editNodeFn(node: GraphNode, type: NodeType, info?: any) {
    const removeEdgeList = getEdges.value
      .filter((edge) => {
        return edge.source === node.id || edge.target === node.id
      })
      .map((edge) => {
        edge.selected = true
        return edge
      })
    removeEdges(removeEdgeList)

    if ([NodeType.循环, NodeType.迭代].includes(node.data.type)) {
      const removeNodes = getNodes.value.filter((i) => i.parentNode === node.id)!
      removeNodesFn(removeNodes)
    }

    await nextTick()
    const createFn = $enum.WORKFLOW_NODE_TYPE.$MATCH_create(type)
    let config = {}
    if (type === NodeType.工具) {
      const { icon, label, name, provider_id, provider_name, provider_type } = info
      config = {
        type: NodeType.工具,
        label,
        name,
        icon,
        position: node.position,
        provider_id,
        provider_name,
        provider_type
      }
    }
    const { data, dimensions, type: vueFlowType, id } = createFn(config)
    Object.assign(node, {
      data,
      dimensions,
      type: vueFlowType,
      id
    })

    switch (type) {
      case NodeType.循环:
      case NodeType.迭代: {
        const { id: parentNode } = node
        let childNodeClass
        if (type === NodeType.循环) {
          childNodeClass = LoopStartNode
        } else {
          childNodeClass = IterationStartNode
        }
        const addChildNode = new childNodeClass({
          parentNode
        }) as GraphNode
        addNodes([addChildNode])
        break
      }
    }

    await nextTick()
    onNodeClick(node)
  }

  /**
   * 节点点击事件
   */
  async function onNodeClick(node: GraphNode | string) {
    if (typeof node === 'string') node = findNode(node)!
    if (node && node.selectable && !node.selected) {
      getSelectedNodes.value.forEach((n) => {
        n.selected = false
      })
      node.selected = true
      await nextTick()
      getEdges.value.forEach((edge) => {
        edge.selected = false
      })
    }
  }

  const showRunBtn = (type) => {
    return ![
      NodeType.开始,
      NodeType.条件分支,
      NodeType.直接回复,
      NodeType.文档提取器,
      NodeType.循环开始,
      NodeType.迭代开始,
      NodeType.结束,
      NodeType.变量聚合
    ].includes(type)
  }

  const showEditBrn = (type) => {
    return ![NodeType.循环, NodeType.迭代].includes(type)
  }

  const showMoreBtn = (type) => {
    return ![NodeType.开始, NodeType.循环开始, NodeType.迭代开始].includes(type)
  }

  async function nodeRunClick(node: GraphNode) {
    onNodeClick(node)
    await nextTick()
    switch (node.data.type) {
      case NodeType.大模型: {
        const { prompt_template, memory } = node.data
        if (!memory && (!Array.isArray(prompt_template) || !prompt_template.find((i) => !!i.text))) {
          return Message.error('提示词不能为空')
        } else if (memory && memory.query_prompt_template.indexOf(`{{#sys.query#}}`) === -1) {
          return Message.error('用户提示词 必须包含 sys.query')
        }
        break
      }
      case NodeType.知识检索: {
        const { dataset_ids, query_variable_selector } = node.data
        if (!Array.isArray(query_variable_selector) || !query_variable_selector.length) {
          return Message.error('查询变量不能为空')
        }

        if (!Array.isArray(dataset_ids) || dataset_ids.length === 0) {
          return Message.error('知识库不能为空')
        }
        break
      }
      case NodeType.问题分类: {
        const { classes, query_variable_selector } = node.data
        if (!Array.isArray(query_variable_selector) || query_variable_selector.length === 0) {
          return Message.error('输入变量不能为空')
        }

        if (!Array.isArray(classes) || classes.length === 0) {
          return Message.error('分类不能为空')
        } else if (classes.find((i) => !i.name)) {
          return Message.error('分类主题内容不能为空')
        }
        break
      }
      case NodeType.代码执行: {
        const { code, variables } = node.data
        if (variables.find((i) => !i.variable)) {
          return Message.error('变量名不能为空')
        } else if (variables.find((i) => !Array.isArray(i.value_selector) || i.value_selector.length === 0)) {
          return Message.error('变量值不能为空')
        }

        if (!code.trim()) {
          return Message.error('代码不能为空')
        }
        break
      }
      case NodeType.模板转换: {
        const { template, variables } = node.data
        if (variables.find((i) => !i.variable)) {
          return Message.error('变量名不能为空')
        } else if (variables.find((i) => !Array.isArray(i.value_selector) || i.value_selector.length === 0)) {
          return Message.error('变量值不能为空')
        }

        if (!template.trim()) {
          return Message.error('代码不能为空')
        }
        break
      }
      case NodeType.参数提取: {
        const { query, parameters } = node.data
        if (!Array.isArray(query) || query.length === 0) {
          return Message.error('输入变量不能为空')
        }

        if (!Array.isArray(parameters) || parameters.length === 0) {
          return Message.error('提取参数不能为空')
        }
        break
      }
      case NodeType.HTTP请求: {
        const { url } = node.data
        if (!url.trim()) {
          return Message.error('API不能为空')
        }
        break
      }
      case NodeType.循环: {
        const { break_conditions } = node.data
        if (!Array.isArray(break_conditions) || break_conditions.length === 0) break

        for (let a = 0; a < break_conditions.length; a++) {
          const condition = break_conditions[a]

          if (!Array.isArray(condition.variable_selector) || condition.variable_selector.length === 0) {
            return Message.error('循环终止条件变量名不能为空')
          }

          if (!condition.comparison_operator) {
            return Message.error('循环终止条件变量操作符不能为空')
          }

          if (
            !$enum.COMPARISON_OPERATOR.$RULE.OTHER_VALUE.includes(condition.comparison_operator) &&
            [null, undefined, ''].includes(condition.value)
          ) {
            return Message.error('循环终止条件变量值不能为空')
          }
        }
        break
      }
      // case NodeType.工具: {
      //   const fullToolList = computed(() => {
      //     const { builtinTools, customTools } = appManagerStore;
      //     let result: any[] = [];
      //     builtinTools.map((group) => {
      //       result = result.concat(group.tools);
      //     });
      //     customTools.map((group) => {
      //       result = result.concat(group.tools);
      //     });
      //     return result;
      //   });
      //   const { tool_name, tool_parameters } = node.data;
      //   const currentTool = fullToolList.value?.find((tool) => tool.name === tool_name);
      //   if (Array.isArray(currentTool?.parameters) && currentTool?.parameters.length > 0) {
      //     const required_parameters = currentTool?.parameters.filter((i) => i.required);
      //     required_parameters.forEach((e) => {
      //       if (!tool_parameters[e.name]?.value) {
      //         return Message.error(`${e.label.zh_Hans}不能为空`);
      //       }
      //     });
      //   }
      // }
    }

    await nextTick()
    state.showNodeRun.value = true
  }

  /**
   * 复制节点
   * @param {GraphNode} node 需要复制的节点
   */
  function nodeCopyClick(node: GraphNode, parentNode?: string) {
    const nodeId = node.id
    const copyNodes: GraphNode[] = []
    node.selected = false
    const copyNode = deepCopy(node)
    if (!parentNode) copyNode.position.y += copyNode.dimensions.height + 20
    copyNode.handleBounds = {
      source: [],
      target: []
    }
    copyNodes.push(copyNode)

    switch (copyNode.data.type) {
      case NodeType.问题分类: {
        copyNode.id = `${+new Date()}`
        const id = +new Date()
        copyNode.data.classes.forEach((item, index) => {
          item.id = `${id + index}-class`
        })
        break
      }
      case NodeType.循环:
      case NodeType.迭代: {
        copyNode.id = `${+new Date()}`
        const childrenNodes = getNodes.value.filter((n) => n.parentNode === nodeId)
        childrenNodes.forEach((n, index) => {
          const [copyChild] = nodeCopyClick(n, copyNode.id)!
          if (![NodeType.循环开始, NodeType.迭代开始].includes(copyChild.data.type)) {
            copyChild.id = `${+new Date() + index}`
          }
          copyNodes.push(copyChild)
        })

        break
      }
      case NodeType.循环开始:
      case NodeType.迭代开始: {
        copyNode.id = `${parentNode}start`
        break
      }
      default: {
        copyNode.id = `${+new Date()}`
        break
      }
    }

    if (parentNode) {
      copyNode.parentNode = parentNode
      return copyNodes
    } else {
      copyNode.selected = true
      addNodes(copyNodes)
    }
  }

  function onContextmenuClick(node: GraphNode) {
    getSelectedNodes.value.forEach((n) => {
      if (node.id !== n.id) {
        n.selected = false
      }
    })
  }

  function clearSelectedStatus() {
    getSelectedNodes.value.forEach((n) => {
      n.selected = false
    })
  }

  function handleNodeDrag(e) {
    const { node } = e

    if (node.parentNode) {
      const { x, y } = { ...node.position }
      // 限制 y 坐标不低于 68
      if (y < 68) {
        node.position.y = 68
      }
      // 限制 x 坐标不小于 24
      if (x < 24) {
        node.position.x = 24
      }
    }
  }
  /**
   * 检查是否存在循环连接或不允许的连接
   * @param sourceId 源节点ID
   * @param targetId 目标节点ID
   * @returns 如果存在不允许的连接返回true，否则返回false
   */
  function checkCyclicConnection({ source: sourceId, target: targetId, sourceHandle }: any) {
    // 0. 如果源节点和目标节点相同，则形成自循环
    if (sourceId === targetId) {
      console.warn('不能连接到自身节点')
      return true
    }

    // 1. 检查目标节点是否是源节点的子孙节点（向下查找）
    const isDescendant = (ancestorId: string, descendantId: string, sourceHandle): boolean => {
      // 直接子节点检查
      const directChildren = getEdges.value
        .filter((edge) => edge.source === ancestorId && edge.sourceHandle === sourceHandle)
        .map((edge) => edge.target)

      // 如果是直接子节点，允许连接
      if (directChildren.includes(descendantId)) {
        return false
      }

      // 检查是否是更深层次的子孙节点
      const visited = new Set<string>()
      const queue = [...directChildren]

      while (queue.length > 0) {
        const currentId = queue.shift()!

        if (visited.has(currentId)) continue
        visited.add(currentId)

        if (currentId === descendantId) {
          console.warn('不能连接到子孙节点（非直接子节点）')
          return true
        }

        // 添加当前节点的子节点到队列
        const childrenIds = getEdges.value.filter((edge) => edge.source === currentId).map((edge) => edge.target)

        queue.push(...childrenIds)
      }

      return false
    }

    if (isDescendant(sourceId, targetId, sourceHandle)) {
      return true
    }

    // 2. 检查从目标节点是否可以到达源节点（形成回路）,祖先查找
    const visited = new Set<string>()
    const queue = [targetId]

    while (queue.length > 0) {
      const currentId = queue.shift()!

      if (visited.has(currentId)) continue
      visited.add(currentId)

      // 查找从当前节点出发的所有边
      const outgoingEdges = getEdges.value.filter((edge) => edge.source === currentId)

      for (const edge of outgoingEdges) {
        if (edge.target === sourceId) {
          // 找到了回路
          console.warn('场景2: 检测到循环连接，不能形成回路')
          return true
        }
        queue.push(edge.target)
      }
    }
    // 3. 检查是否存在共同的父节点，形成环形结构, a->b, a->c, 则不允许 b->c
    // 查找所有指向 sourceId 的边的源节点
    const sourceParents = getEdges.value
      .filter((edge) => edge.target === sourceId && edge.sourceHandle === sourceHandle)
      .map((edge) => edge.source)

    // 检查这些源节点是否也指向 targetId
    const hasCommonParent = sourceParents.some((parentId) => {
      return getEdges.value.some((edge) => edge.source === parentId && edge.target === targetId)
    })

    if (hasCommonParent) {
      console.warn('场景3: 检测到环形连接，不能形成环形结构')
      return true
    }
    // 4. 检查是否存在共同的目标节点:
    // 4.1 如a->c, b->c, 则不允许 a->d
    // 4.2 如a->d, b->c, 则不允许 a->c
    // 4.3 如a->c, b->d, 则不允许 a->d
    // 4.4 如a->d, b->c, 则不允许 a->c
    if (checkComplexCyclicConnection(sourceId, targetId, sourceHandle)) {
      return true
    }
    return false
  }
  /**
   * 检查复杂环形连接
   * 场景：检查四种环形连接情况
   * @param sourceId 源节点ID
   * @param targetId 目标节点ID
   * @returns 如果存在不允许的连接返回true，否则返回false
   */
  function checkComplexCyclicConnection(sourceId: string, targetId: string, sourceHandle): boolean {
    // 获取源节点的所有出边目标
    const sourceOutTargets = getEdges.value
      .filter((edge) => edge.source === sourceId && edge.sourceHandle === sourceHandle)
      .map((edge) => edge.target)

    // 如果源节点没有出边，则允许连接
    if (sourceOutTargets.length === 0) {
      return false
    }

    // 获取所有节点
    const allNodes = getNodes.value.map((node) => node.id)

    // 对于每个节点，检查是否形成环形结构
    for (const nodeId of allNodes) {
      if (nodeId === sourceId || nodeId === targetId) continue

      // 获取该节点的所有出边目标
      const nodeOutTargets = getEdges.value.filter((edge) => edge.source === nodeId).map((edge) => edge.target)

      // 如果该节点没有出边，则跳过
      if (nodeOutTargets.length === 0) continue

      // 场景4.1：如a->c, b->c, 则不允许 a->d
      // 检查源节点和当前节点是否有共同的目标节点
      const commonTargets = sourceOutTargets.filter((target) => nodeOutTargets.includes(target))
      if (commonTargets.length > 0 && !sourceOutTargets.includes(targetId)) {
        console.warn('检测到环形连接：多个节点指向同一目标，不能形成环形结构（场景4.1）')
        return true
      }

      // 场景4.2：如a->d, b->c, 则不允许 a->c
      // 检查源节点是否已经有出边，且当前节点指向目标节点
      if (sourceOutTargets.length > 0 && nodeOutTargets.includes(targetId)) {
        console.warn('检测到环形连接：节点已有出边，不能连接到其他节点指向的目标（场景4.2）')
        return true
      }

      // 场景4.3：如a->c, b->d, 则不允许 a->d
      // 检查源节点是否指向当前节点的出边目标
      const isSourcePointToNodeTarget = sourceOutTargets.some((target) => nodeOutTargets.includes(target))
      if (isSourcePointToNodeTarget && nodeOutTargets.includes(targetId)) {
        console.warn('检测到环形连接：不能连接到已有关联节点的目标（场景4.3）')
        return true
      }

      // 场景4.4：如a->d, b->c, 则不允许 a->c
      // 检查源节点的出边目标是否包含当前节点的出边目标
      const isTargetNodeOutTarget = nodeOutTargets.includes(targetId)
      if (sourceOutTargets.length > 0 && isTargetNodeOutTarget) {
        console.warn('检测到环形连接：不能连接到其他节点的出边目标（场景4.4）')
        return true
      }
    }

    return false
  }
  return {
    onNodeClick,
    addNodesFn,
    editNodeFn,
    showRunBtn,
    showEditBrn,
    showMoreBtn,
    nodeRunClick,
    nodeCopyClick,
    removeNodesFn,
    onContextmenuClick,
    showNodeRun: state.showNodeRun,
    clearSelectedStatus,
    showConfirmRemoveModal,
    handleNodeDrag,
    checkCyclicConnection,
    checkComplexCyclicConnection
  }
}
