import type { FileResponse, HistoryWorkflowData, NodeTracing } from '@/views/app/workflow/types/workflow'
import { defineStore } from 'pinia'

export type WorkflowRunningData = {
  task_id?: string
  message_id?: string
  conversation_id?: string
  result: {
    sequence_number?: number
    workflow_id?: string
    inputs?: string
    process_data?: string
    outputs?: string
    status: string
    error?: string
    elapsed_time?: number
    total_tokens?: number
    created_at?: number
    created_by?: string
    finished_at?: number
    steps?: number
    showSteps?: boolean
    total_steps?: number
    files?: FileResponse[]
    exceptions_count?: number
  }
  tracing?: NodeTracing[]
}

type PreviewRunningData = WorkflowRunningData & {
  resultTabActive?: boolean
  resultText?: string
}

const storeSetup = () => {
  const iterParallelLogMap = ref<Map<string, Map<string, NodeTracing[]>>>()
  const workflowRunningData = ref<any | undefined>(undefined)
  const historyWorkflowData = ref<HistoryWorkflowData | undefined>()
  const iterTimes = ref<number>()

  const setIterParallelLogMap = (iterParallelLogMapData: Map<string, Map<string, NodeTracing[]>>) => {
    iterParallelLogMap.value = iterParallelLogMapData
  }
  // 保存运行时的数据
  const setWorkflowRunningData = (workflowData: PreviewRunningData) => {
    workflowRunningData.value = workflowData
  }

  const setHistoryWorkflowData = (historyData) => {
    historyWorkflowData.value = historyData
  }

  const setIterTimes = (time) => {
    iterTimes.value = time
  }

  return {
    iterParallelLogMap,
    workflowRunningData,
    historyWorkflowData,
    iterTimes,
    setIterParallelLogMap,
    setWorkflowRunningData,
    setHistoryWorkflowData,
    setIterTimes
  }
}

export const useWorkflowStore = defineStore('workflow', storeSetup, { persist: false })
