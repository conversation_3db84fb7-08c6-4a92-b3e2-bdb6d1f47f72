<template>
  <div class="step-content step2-content">
    <div class="split-container">
      <!-- 左侧 -->
      <div class="settings-panel">
        <!-- 分段设置 -->
        <chunk-setting
          :segment-mode="segmentMode"
          :normal-segment-config="normalSegmentConfig"
          :parent-child-config="parentChildConfig"
          :parent-mode="parentMode"
          :processing-rules="processingRules"
          :use-qa-splitting="useQaSplitting"
          :qa-language="qaLanguage"
          @segment-mode-change="handleSegmentModeChange"
          @parent-mode-change="handleParentModeChange"
          @normal-segment-config-change="handleNormalSegmentConfigChange"
          @parent-child-config-change="handleParentChildConfigChange"
          @processing-rules-change="handleProcessingRulesChange"
          @qa-splitting-change="handleQaSplittingChange"
          @qa-language-change="handleQaLanguageChange"
          @preview-blocks="previewBlocks"
        />
        <!-- 索引方式 - 添加文档模式时禁用 -->
        <div v-if="isAddingDocument" class="disabled-setting-card">
          <div class="disabled-overlay">
            <!-- <span class="disabled-text">添加文件模式下不可修改</span> -->
          </div>
          <index-type
            :indexing-method="indexingMethod"
            :segment-mode="segmentMode"
            :disabled="true"
            @indexing-method-change="handleIndexingMethodChange"
          />
        </div>
        <index-type
          v-else
          :indexing-method="indexingMethod"
          :segment-mode="segmentMode"
          @indexing-method-change="handleIndexingMethodChange"
        />

        <!-- Embedding模型 - 添加文档模式时禁用 -->
        <div v-if="isAddingDocument" class="disabled-setting-card">
          <div class="disabled-overlay">
            <!-- <span class="disabled-text">添加文件模式下不可修改</span> -->
          </div>
          <embedding-model
            :embedding-model="embeddingModel"
            :embedding-model-options="embeddingModelOptions"
            :disabled="true"
            @embedding-model-change="handleEmbeddingModelChange"
          />
        </div>
        <embedding-model
          v-else
          :embedding-model="embeddingModel"
          :embedding-model-options="embeddingModelOptions"
          @embedding-model-change="handleEmbeddingModelChange"
        />

        <!-- 检索设置 - 添加文档模式时禁用 -->
        <div v-if="isAddingDocument" class="disabled-setting-card">
          <div class="disabled-overlay">
            <!-- <span class="disabled-text">添加文件模式下不可修改</span> -->
          </div>
          <retrieval-setting
            :search-method="searchMethod"
            :vector-settings="vectorSettings"
            :full-text-settings="fullTextSettings"
            :hybrid-settings="hybridSettings"
            :rerank-models="rerankModels"
            :disabled="true"
            @search-method-change="handleSearchMethodChange"
            @vector-settings-change="handleVectorSettingsChange"
            @full-text-settings-change="handleFullTextSettingsChange"
            @hybrid-settings-change="handleHybridSettingsChange"
          />
        </div>
        <retrieval-setting
          v-else
          :search-method="searchMethod"
          :vector-settings="vectorSettings"
          :full-text-settings="fullTextSettings"
          :hybrid-settings="hybridSettings"
          :rerank-models="rerankModels"
          @search-method-change="handleSearchMethodChange"
          @vector-settings-change="handleVectorSettingsChange"
          @full-text-settings-change="handleFullTextSettingsChange"
          @hybrid-settings-change="handleHybridSettingsChange"
        />

        <!-- 步骤导航按钮 -->
        <div class="step-footer">
          <a-button v-if="props.isFrom !== 'DocumentsChunkSetting'" class="prev-btn" @click="goToPrevStep">
            上一步
          </a-button>
          <a-button type="primary" class="next-btn" @click="goToNextStep">保存并处理</a-button>
          <a-button
            v-if="props.isFrom === 'DocumentsChunkSetting'"
            type="primary"
            class="next-btn"
            @click="goToPrevStep"
          >
            取消
          </a-button>
        </div>
      </div>
      <!-- 右侧: 预览 -->
      <file-preview
        :selected-preview-file-id="props.selectedPreviewFileId"
        :uploaded-files="props.uploadedFiles"
        :total-segments="props.totalSegments"
        :preview-loading="props.previewLoading"
        :previewing-blocks="previewingBlocks"
        :blocks-preview-content="blocksPreviewContent"
        :blocks-preview-total="blocksPreviewTotal"
        :segment-mode="segmentMode"
        @update:selectedPreviewFileId="(fileId) => emit('update:selectedPreviewFileId', fileId)"
        @preview-blocks="previewBlocks"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { previewDocumentSegments, getEmbeddingModels, getRerankModels, getDefaultRerankModel } from '@/apis/datasets'
import FilePreview from '@/views/datasets/components/FilePreview.vue'
import ChunkSetting from '@/views/datasets/components/ChunkSetting.vue'
import IndexType from '@/views/datasets/components/IndexType.vue'
import EmbeddingModel from '@/views/datasets/components/EmbeddingModel.vue'
import RetrievalSetting from '@/views/datasets/components/RetrievalSetting.vue'

// Props定义
const props = defineProps({
  uploadedFiles: {
    type: Array as () => Array<{
      uid: string
      name: string
      size: number
      response?: {
        id: string
        [key: string]: any
      }
      [key: string]: any
    }>,
    default: () => []
  },
  selectedPreviewFileId: {
    type: String,
    default: ''
  },
  segmentPreview: {
    type: Array,
    default: () => []
  },
  totalSegments: {
    type: Number,
    default: 0
  },
  previewLoading: {
    type: Boolean,
    default: false
  },
  // 新增的属性，表示是否是添加文档模式
  isAddingDocument: {
    type: Boolean,
    default: false
  },
  // 新增的属性，表示目标知识库ID
  datasetId: {
    type: String,
    default: ''
  },
  // 新增的属性，标识组件来源
  isFrom: {
    type: String,
    default: '' // 默认为空，当从DocumentsChunkSetting进入时值为'DocumentsChunkSetting'
  }
})

// Emits定义
const emit = defineEmits([
  'update:selectedPreviewFileId',
  'next-step',
  'prev-step',
  'preview-blocks',
  'prev-fetch-dataset-detail'
])

// 加载状态
const loading = ref(false)

// 防止递归更新的标志
const isUpdating = ref(false)

// 分段模式
const segmentMode = ref('normal') // 'normal' 或 'parent-child'

// 通用分段配置
const normalSegmentConfig = ref({
  separator: '\\n\\n',
  maxLength: 1024,
  minLength: 50
})

// 父子分段配置
const parentChildConfig = ref({
  parent: {
    separator: '\\n\\n',
    maxLength: 1024
  },
  child: {
    separator: '\\n',
    maxLength: 512
  }
})

// 父块模式
const parentMode = ref('paragraph') // 'paragraph' 或 'fulltext'

const processingRules = ref({
  removeSpaces: true,
  removeUrls: false
})

const useQaSplitting = ref(false)
const qaLanguage = ref('zh_CN')

// 索引方法
const indexingMethod = ref<'high_quality' | 'economy'>('high_quality') // 'high_quality' 或 'economy'

// 检索设置
const searchMethod = ref('hybrid') // 'vector', 'fulltext', 'hybrid'

// 声明选项类型
interface ModelOption {
  value: string
  label: string
}

// Embedding模型选项
const embeddingModelOptions = ref<ModelOption[]>([])
const embeddingModel = ref('')

// 获取Embedding模型列表
const fetchEmbeddingModels = async () => {
  try {
    loading.value = true

    // 添加超时控制
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('API调用超时')), 10000) // 10秒超时
    })

    const response = (await Promise.race([getEmbeddingModels(), timeoutPromise])) as any

    const options: ModelOption[] = []

    // 处理响应数据，提取模型信息
    if (response?.data && Array.isArray(response.data)) {
      response.data.forEach((provider: any) => {
        if (provider?.models && Array.isArray(provider.models)) {
          provider.models.forEach((model: any) => {
            if (model?.model) {
              options.push({
                value: model.model,
                label: model.label?.zh_Hans || model.model
              })
            }
          })
        }
      })
    }

    embeddingModelOptions.value = options

    // 默认选择第一个模型
    if (options.length > 0) {
      embeddingModel.value = options[0].value
    } else {
      // 如果没有获取到模型，使用默认值
      embeddingModel.value = 'text-embedding-ada-002'
    }
  } catch (error) {
    console.error('获取Embedding模型失败:', error)
    // 设置默认模型，确保页面可以正常使用
    embeddingModel.value = 'text-embedding-ada-002'
    embeddingModelOptions.value = [{ value: 'text-embedding-ada-002', label: '默认模型' }]
  } finally {
    loading.value = false
  }
}

// 搜索方法计算属性
const searchMethods = computed(() => {
  // 根据当前选择的搜索方法，设置对应的值
  return {
    vector: searchMethod.value === 'vector' || searchMethod.value === 'hybrid',
    fulltext: searchMethod.value === 'fulltext' || searchMethod.value === 'hybrid',
    hybrid: searchMethod.value === 'hybrid'
  }
})

// 向量检索设置
const vectorSettings = ref({
  useRerank: true,
  rerankModel: 'netease-youdao/bce-reranker-base_v1',
  topK: 3,
  useScoreThreshold: false,
  scoreThreshold: 0.5
})

// 全文检索设置
const fullTextSettings = ref({
  useRerank: true,
  rerankModel: 'netease-youdao/bce-reranker-base_v1',
  topK: 3,
  useScoreThreshold: false,
  scoreThreshold: 0.5
})

// 混合检索设置类型定义
interface HybridSettings {
  mode: 'weight' | 'rerank'
  vectorWeight: number
  rerankModel: string
  topK: number
  useScoreThreshold: boolean
  scoreThreshold: number
}

// 混合检索设置
const hybridSettings = ref<HybridSettings>({
  mode: 'rerank' as 'weight' | 'rerank',
  vectorWeight: 0.5,
  rerankModel: 'netease-youdao/bce-reranker-base_v1',
  topK: 3,
  useScoreThreshold: false,
  scoreThreshold: 0.5
})

// 定义RerankModel接口
interface RerankModel {
  model: string
  label: {
    zh_Hans: string
    en_US: string
  }
  model_type: string
  model_properties: {
    context_size: number
  }
  status: string
  features: any
  fetch_from: string
  deprecated: boolean
  load_balancing_enabled: boolean
}

// Rerank模型列表
const rerankModels = ref<RerankModel[]>([
  {
    model: 'netease-youdao/bce-reranker-base_v1',
    label: { zh_Hans: 'Netease Youdao Reranker', en_US: 'Netease Youdao Reranker' },
    model_type: 'rerank',
    features: null,
    fetch_from: 'model',
    model_properties: { context_size: 512 },
    deprecated: false,
    status: 'active',
    load_balancing_enabled: false
  }
])

// 获取Rerank模型列表
const fetchRerankModels = async () => {
  try {
    // 添加超时控制
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('API调用超时')), 10000) // 10秒超时
    })

    // 获取模型列表
    const modelsResponse = (await Promise.race([getRerankModels(), timeoutPromise])) as any

    // 处理模型数据
    if (modelsResponse?.data && Array.isArray(modelsResponse.data)) {
      const allModels: RerankModel[] = []

      // 遍历所有提供商及其模型
      modelsResponse.data.forEach((provider: any) => {
        if (provider?.models && Array.isArray(provider.models)) {
          provider.models.forEach((model: any) => {
            if (model?.model) {
              // 创建新的RerankModel对象，直接提供默认值而不从model对象中获取可能不存在的属性
              const completeModel: RerankModel = {
                model: model.model || '',
                label: model.label || { zh_Hans: '', en_US: '' },
                model_type: model.model_type || '',
                model_properties: model.model_properties || { context_size: 0 },
                status: model.status || 'active',
                features: null, // 直接使用默认值
                fetch_from: 'model', // 直接使用默认值
                deprecated: false, // 直接使用默认值
                load_balancing_enabled: false // 直接使用默认值
              }
              allModels.push(completeModel)
            }
          })
        }
      })

      // 更新模型列表
      rerankModels.value = allModels
      console.log('成功获取Rerank模型列表:', allModels.length, '个模型')
      console.log('模型详情:', allModels)
    }

    // 获取默认模型
    try {
      const defaultModelResponse = (await Promise.race([getDefaultRerankModel(), timeoutPromise])) as any
      if (defaultModelResponse?.data && (defaultModelResponse.data as any).model) {
        // 更新所有使用Rerank的设置
        const defaultModel = (defaultModelResponse.data as any).model
        vectorSettings.value.rerankModel = defaultModel
        fullTextSettings.value.rerankModel = defaultModel
        hybridSettings.value.rerankModel = defaultModel
      }
    } catch (defaultModelError) {
      console.warn('获取默认Rerank模型失败，使用预设默认值:', defaultModelError)
      // 使用预设的默认模型
      const defaultModel = 'netease-youdao/bce-reranker-base_v1'
      vectorSettings.value.rerankModel = defaultModel
      fullTextSettings.value.rerankModel = defaultModel
      hybridSettings.value.rerankModel = defaultModel
    }
  } catch (error) {
    console.error('获取Rerank模型列表失败:', error)
    // 确保有默认值，页面可以正常使用
    const defaultModel = 'netease-youdao/bce-reranker-base_v1'
    vectorSettings.value.rerankModel = defaultModel
    fullTextSettings.value.rerankModel = defaultModel
    hybridSettings.value.rerankModel = defaultModel
  }
}

// 在组件挂载时获取Embedding和Rerank模型
onMounted(async () => {
  try {
    // 使用Promise.allSettled确保即使某个API失败也不会阻塞页面
    const results = await Promise.allSettled([fetchEmbeddingModels(), fetchRerankModels()])

    // 检查结果并记录错误
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        const apiName = index === 0 ? 'Embedding模型' : 'Rerank模型'
        console.warn(`获取${apiName}失败:`, result.reason)
      }
    })
  } catch (error) {
    console.error('初始化模型数据时发生错误:', error)
  }
})

// 处理QA语言变更
const handleQaLanguageChange = (lang: string) => {
  if (isUpdating.value) return
  qaLanguage.value = lang
}

// 前往上一步
const goToPrevStep = () => {
  emit('prev-step')
}

// 前往下一步
const goToNextStep = () => {
  // 检查文件是否已上传 - 只要有上传的文件就可以进入下一步
  if (props.uploadedFiles && props.uploadedFiles.length > 0) {
    // 如果是添加文档模式，在进入下一步前查询知识库详情
    if (props.isAddingDocument && props.datasetId) {
      emit('prev-fetch-dataset-detail', props.datasetId)
    }
    emit('next-step')
  } else {
    Message.warning('请先上传文件')
  }
}

// 处理分段模式变更 - 直接更新本地状态，不再有递归风险
const handleSegmentModeChange = (mode: string) => {
  console.log('收到分段模式变更:', mode)
  segmentMode.value = mode
}

// 处理父模式变更
const handleParentModeChange = (mode: string) => {
  console.log('收到父模式变更:', mode)
  parentMode.value = mode
}

// 处理分段配置变更 - 直接更新本地状态，不再有递归风险
const handleNormalSegmentConfigChange = (config: any) => {
  console.log('收到分段配置变更:', config)
  normalSegmentConfig.value = { ...config }
}

// 处理父子分段配置变更
const handleParentChildConfigChange = (config: any) => {
  console.log('收到父子分段配置变更:', config)
  parentChildConfig.value = { ...config }
}

// 处理处理规则变更
const handleProcessingRulesChange = (rules: any) => {
  console.log('收到处理规则变更:', rules)
  processingRules.value = { ...rules }
}

// 处理QA分段设置变更
const handleQaSplittingChange = (value: boolean) => {
  if (isUpdating.value) return
  useQaSplitting.value = value
}

// 处理索引方法变更
const handleIndexingMethodChange = (method: 'high_quality' | 'economy') => {
  if (isUpdating.value) return
  indexingMethod.value = method
}

// 处理Embedding模型变更
const handleEmbeddingModelChange = (model: string) => {
  if (isUpdating.value) return
  embeddingModel.value = model
}

// 处理搜索方法变更
const handleSearchMethodChange = (method: string) => {
  if (isUpdating.value) return
  searchMethod.value = method
}

// 处理向量设置变更 - 直接更新本地状态，不再有递归风险
const handleVectorSettingsChange = (settings: any) => {
  console.log('收到向量设置变更:', settings)
  vectorSettings.value = { ...settings }
}

// 处理全文设置变更
const handleFullTextSettingsChange = (settings: any) => {
  console.log('收到全文设置变更:', settings)
  fullTextSettings.value = { ...settings }
}

// 处理混合设置变更
const handleHybridSettingsChange = (settings: any) => {
  console.log('收到混合设置变更:', settings)
  hybridSettings.value = { ...settings }
}

// 预览相关状态
const previewingBlocks = ref(false)
const blocksPreviewError = ref(false)

// 声明分段预览内容类型
interface SegmentPreview {
  content: string
  child_chunks?: any[] | null
  [key: string]: any
}

// 预览分块相关状态
const blocksPreviewContent = ref<SegmentPreview[]>([])
const blocksPreviewTotal = ref(0)

// 预览块
const previewBlocks = async (mode: string) => {
  if (!props.selectedPreviewFileId && props.uploadedFiles.length === 0) {
    Message.warning('请先选择要预览的文件')
    return
  }

  // 如果没有选择文件ID但有上传文件，使用第一个文件
  const fileId =
    props.selectedPreviewFileId || (props.uploadedFiles[0]?.response?.id ? props.uploadedFiles[0].response.id : null)

  if (!fileId) {
    Message.warning('无有效文件可以预览')
    return
  }

  previewingBlocks.value = true
  blocksPreviewError.value = false
  blocksPreviewContent.value = []

  try {
    // 使用类型断言解决TypeScript报错
    let params: IndexingEstimateParams = {
      info_list: {
        data_source_type: 'upload_file',
        file_info_list: {
          file_ids: [fileId]
        }
      },
      indexing_technique: indexingMethod.value,
      doc_language: 'Chinese Simplified',
      process_rule: {
        rules: {
          pre_processing_rules: [
            {
              id: 'remove_extra_spaces',
              enabled: processingRules.value.removeSpaces
            },
            {
              id: 'remove_urls_emails',
              enabled: processingRules.value.removeUrls
            }
          ],
          segmentation: {
            separator: '',
            max_tokens: 0
          }
        },
        mode: 'custom'
      },
      doc_form: 'text_model'
    }

    // 添加dataset_id参数
    if (props.datasetId) {
      params.dataset_id = props.datasetId
    }

    if (mode === 'normal') {
      // 通用模式
      params.process_rule = {
        rules: {
          pre_processing_rules: [
            {
              id: 'remove_extra_spaces',
              enabled: processingRules.value.removeSpaces
            },
            {
              id: 'remove_urls_emails',
              enabled: processingRules.value.removeUrls
            }
          ],
          segmentation: {
            separator: normalSegmentConfig.value.separator,
            max_tokens: normalSegmentConfig.value.maxLength,
            chunk_overlap: normalSegmentConfig.value.minLength
          }
        },
        mode: 'custom'
      }
      params.doc_form = 'text_model'
    } else if (mode === 'parent-child') {
      // 父子分段模式
      params.process_rule = {
        rules: {
          pre_processing_rules: [
            {
              id: 'remove_extra_spaces',
              enabled: processingRules.value.removeSpaces
            },
            {
              id: 'remove_urls_emails',
              enabled: processingRules.value.removeUrls
            }
          ],
          segmentation: {
            separator: parentChildConfig.value.parent.separator,
            max_tokens: parentChildConfig.value.parent.maxLength
          },
          parent_mode: parentMode.value === 'fulltext' ? 'full-doc' : 'paragraph',
          subchunk_segmentation: {
            separator: parentChildConfig.value.child.separator,
            max_tokens: parentChildConfig.value.child.maxLength
          }
        },
        mode: 'hierarchical'
      }
      params.doc_form = 'hierarchical_model'
    }

    emit('preview-blocks', params)

    const response = await previewDocumentSegments(params)
    blocksPreviewContent.value = response.preview
    blocksPreviewTotal.value = response.total_segments

    // Message.success(`文档将被分成${blocksPreviewTotal.value}个块`)
  } catch (error) {
    console.error('获取块预览失败:', error)
    blocksPreviewError.value = true
    // Message.error('获取块预览失败')
  } finally {
    previewingBlocks.value = false
  }
}

// IndexingEstimateParams 接口定义
interface IndexingEstimateParams {
  info_list: {
    data_source_type: 'upload_file'
    file_info_list: {
      file_ids: string[]
    }
  }
  indexing_technique: 'high_quality' | 'economy'
  doc_language: string
  process_rule: {
    rules: {
      pre_processing_rules: Array<{
        id: string
        enabled: boolean
      }>
      segmentation: {
        separator: string
        max_tokens: number
        chunk_overlap?: number
      }
      parent_mode?: string
      subchunk_segmentation?: {
        separator: string
        max_tokens: number
        chunk_overlap?: number
      }
    }
    mode: 'custom' | 'hierarchical'
  }
  doc_form: 'text_model' | 'hierarchical_model'
  dataset_id?: string
}

// 向父组件暴露值和方法
defineExpose({
  segmentMode,
  normalSegmentConfig,
  parentChildConfig,
  parentMode,
  processingRules,
  useQaSplitting,
  qaLanguage,
  indexingMethod,
  searchMethod,
  embeddingModel,
  vectorSettings,
  fullTextSettings,
  hybridSettings,
  searchMethods,
  // 添加一个新方法，用于获取当前的所有设置
  getSettings: () => {
    return {
      segmentMode: segmentMode.value,
      normalSegmentConfig: normalSegmentConfig.value,
      parentChildConfig: parentChildConfig.value,
      parentMode: parentMode.value,
      processingRules: processingRules.value,
      useQaSplitting: useQaSplitting.value,
      qaLanguage: qaLanguage.value,
      indexingMethod: indexingMethod.value,
      searchMethod: searchMethod.value,
      embeddingModel: embeddingModel.value,
      vectorSettings: vectorSettings.value,
      fullTextSettings: fullTextSettings.value,
      hybridSettings: hybridSettings.value
    }
  }
})
</script>

<style lang="scss">
@use './create.scss';

// 添加对禁用卡片的样式支持
.disabled-setting-card {
  position: relative;
  margin-bottom: 16px;
  opacity: 0.8;
  pointer-events: none;

  .disabled-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.6);
    z-index: 10;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;

    .disabled-text {
      padding: 4px 12px;
      background-color: rgba(0, 0, 0, 0.6);
      color: white;
      border-radius: 4px;
      font-size: 14px;
    }
  }
}
</style>
