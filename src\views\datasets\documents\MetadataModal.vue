<template>
  <a-drawer
    :visible="visible"
    title="元数据"
    :footer="false"
    :mask-closable="true"
    :width="600"
    @cancel="emit('update:visible', false)"
    @close="emit('update:visible', false)"
  >
    <div class="metadata-container">
      <div class="metadata-description">
        元数据是关于文档的数据，用于描述文档的属性。元数据可以帮助您更好地组织和管理文档。
      </div>

      <a-button type="primary" class="add-btn" :loading="loading" @click="handleAddMetadata">
        <template #icon><icon-plus /></template>
        添加元数据
      </a-button>

      <div class="metadata-list">
        <!-- 自定义元数据项 -->
        <div v-if="loading" class="loading-container">
          <a-spin />
          <span class="loading-text">加载中...</span>
        </div>
        <template v-else>
          <div v-for="(item, index) in customMetadata" :key="item.id" class="metadata-item">
            <div class="metadata-icon">
              <component :is="getTypeIcon(item.type)" />
            </div>
            <div class="metadata-content">
              <div class="metadata-name">
                {{ item.name }}
                <span class="metadata-type">{{ item.type }}</span>
              </div>
              <div class="metadata-value">{{ item.count || 0 }} 个值</div>
            </div>
            <div class="metadata-actions">
              <a-button type="text" size="mini" @click="handleEditMetadata(item)">
                <template #icon><icon-edit /></template>
              </a-button>
              <a-button type="text" status="danger" size="mini" @click="handleDeleteMetadata(item)">
                <template #icon><icon-delete /></template>
              </a-button>
            </div>
          </div>
        </template>

        <!-- 内置元数据开关 -->
        <div class="builtin-toggle">
          <a-switch v-model="showBuiltinMetadata" :loading="builtinLoading" @change="handleBuiltinMetadataChange" />
          <span class="toggle-label">内置</span>
          <a-tooltip content="显示系统内置的元数据字段，开启后可以编辑内置元数据字段">
            <icon-question-circle class="help-icon" />
          </a-tooltip>
        </div>

        <!-- 内置元数据项 -->
        <div class="builtin-metadata-container" :class="{ 'disabled-container': !showBuiltinMetadata }">
          <div
            v-for="(item, index) in builtinMetadata"
            :key="index"
            class="metadata-item"
            :class="{ disabled: !showBuiltinMetadata }"
          >
            <div class="metadata-icon">
              <component :is="getTypeIcon(item.type)" />
            </div>
            <div class="metadata-content">
              <div class="metadata-name">
                {{ item.name }}
                <span class="metadata-type">{{ item.type }}</span>
              </div>
              <div v-if="showBuiltinMetadata" class="metadata-edit">
                <!-- <a-input v-model="item.value" placeholder="输入值" size="small" /> -->
              </div>
              <div v-else class="metadata-status">已禁用</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新建/编辑元数据模态框 -->
    <a-modal
      v-model:visible="metadataFormVisible"
      :title="isEditing ? '编辑元数据' : '添加元数据'"
      title-align="start"
      :ok-button-props="{ loading: formSubmitting }"
      @ok="confirmMetadataForm"
      @cancel="cancelMetadataForm"
    >
      <a-form :model="metadataForm" layout="vertical">
        <a-form-item v-if="!isEditing" label="类型">
          <a-radio-group v-model="metadataForm.type">
            <a-radio value="string">String</a-radio>
            <a-radio value="number">Number</a-radio>
            <a-radio value="time">Time</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="名称" required>
          <a-input v-model="metadataForm.name" placeholder="请输入元数据名称" />
          <a-tooltip content="元数据名称只能包含小写字母、数字和下划线，并且必须以小写字母开头." position="top" mini>
            <icon-exclamation-circle-fill />
          </a-tooltip>
        </a-form-item>
      </a-form>
    </a-modal>
  </a-drawer>
</template>

<script setup lang="ts">
// 项目使用auto-import，不需要手动导入Vue API
import { ref, watch, nextTick, onMounted } from 'vue'
import { Message, Modal } from '@arco-design/web-vue'
import {
  getDatasetMetadata,
  createDatasetMetadata,
  updateDatasetMetadata,
  deleteDatasetMetadata,
  enableBuiltInMetadata,
  disableBuiltInMetadata
} from '@/apis/datasets'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  datasetId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:visible', 'add-metadata', 'refresh'])

// 数据加载状态
const loading = ref(false)
const builtinLoading = ref(false)

// 自定义元数据
const customMetadata = ref<any[]>([])

// 内置元数据
const builtinMetadata = ref([
  { name: 'document_name', type: 'string', disabled: true, value: '' },
  { name: 'uploader', type: 'string', disabled: true, value: '' },
  { name: 'upload_date', type: 'time', disabled: true, value: '' },
  { name: 'last_update_date', type: 'time', disabled: true, value: '' },
  { name: 'source', type: 'string', disabled: true, value: '' }
])

// 切换内置元数据编辑状态
const showBuiltinMetadata = ref(false)

// 监听父组件的visible变化，加载数据
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      fetchMetadata()
    }
  }
)

// 初始化时加载数据
onMounted(() => {
  if (props.visible) {
    fetchMetadata()
  }
})

// 获取元数据列表
const fetchMetadata = async () => {
  if (!props.datasetId) return

  loading.value = true

  try {
    const response = await getDatasetMetadata(props.datasetId)
    customMetadata.value = response.doc_metadata || []
    showBuiltinMetadata.value = response.built_in_field_enabled || false
    console.log('获取元数据成功:', response)
  } catch (error) {
    console.error('获取元数据失败:', error)
    Message.error('获取元数据列表失败')
  } finally {
    loading.value = false
  }
}

// 处理内置元数据切换事件
const handleBuiltinMetadataChange = async (value) => {
  builtinLoading.value = true

  try {
    if (value) {
      // 启用内置元数据
      await enableBuiltInMetadata(props.datasetId)
      Message.success('已启用内置元数据')
    } else {
      // 禁用内置元数据
      await disableBuiltInMetadata(props.datasetId)
      Message.success('已禁用内置元数据')
    }

    // 刷新元数据列表，但保持用户选择的开关状态
    // await fetchMetadata(); // 注释掉，不刷新数据避免覆盖用户的选择
  } catch (error) {
    console.error('切换内置元数据状态失败:', error)
    Message.error('切换内置元数据状态失败')
    // 删除下面这行，不再恢复状态，保持用户的选择
    // showBuiltinMetadata.value = !value; // 恢复为原来的状态
  } finally {
    builtinLoading.value = false
  }
}

// 元数据表单相关
const metadataFormVisible = ref(false)
const formSubmitting = ref(false)
const isEditing = ref(false)
const metadataForm = ref({
  id: '',
  type: 'string',
  name: ''
})

// 打开添加元数据表单
const handleAddMetadata = () => {
  isEditing.value = false
  metadataForm.value = {
    id: '',
    type: 'string',
    name: ''
  }
  metadataFormVisible.value = true
}

// 打开编辑元数据表单
const handleEditMetadata = (item) => {
  isEditing.value = true
  metadataForm.value = {
    id: item.id,
    type: item.type,
    name: item.name
  }
  metadataFormVisible.value = true
}

// 处理删除元数据
const handleDeleteMetadata = (item) => {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除元数据 "${item.name}" 吗？`,
    okText: '确定',
    cancelText: '取消',
    okButtonProps: { status: 'danger' },
    onOk: async () => {
      try {
        await deleteDatasetMetadata(props.datasetId, item.id)
        Message.success('元数据删除成功')
        await fetchMetadata()
        emit('refresh')
      } catch (error) {
        console.error('删除元数据失败:', error)
        Message.error('删除元数据失败')
      }
    }
  })
}

// 验证元数据名称格式
const validateMetadataName = (name: string) => {
  const regex = /^[a-z][a-z0-9_]*$/
  return regex.test(name)
}

// 确认元数据表单
const confirmMetadataForm = async () => {
  if (!metadataForm.value.name.trim()) {
    Message.warning('请输入元数据名称')
    return // 直接返回，不关闭模态框
  }

  if (!validateMetadataName(metadataForm.value.name)) {
    Message.warning('元数据名称格式不正确，只能包含小写字母、数字和下划线，并且必须以小写字母开头')
    return // 直接返回，不关闭模态框
  }

  formSubmitting.value = true

  try {
    if (isEditing.value) {
      // 编辑元数据
      await updateDatasetMetadata(props.datasetId, metadataForm.value.id, {
        name: metadataForm.value.name
      })
      Message.success('元数据更新成功')
    } else {
      // 添加元数据
      await createDatasetMetadata(props.datasetId, {
        type: metadataForm.value.type as 'string' | 'number' | 'time',
        name: metadataForm.value.name
      })
      Message.success('元数据添加成功')
    }

    // 只有API调用成功才关闭模态框
    metadataFormVisible.value = false
    await fetchMetadata()
    emit('refresh')
  } catch (error) {
    console.error(isEditing.value ? '更新元数据失败:' : '添加元数据失败:', error)
    Message.error(isEditing.value ? '更新元数据失败' : '添加元数据失败')
    // API调用失败时，模态框保持打开状态，不需要额外操作
  } finally {
    formSubmitting.value = false
  }
}

// 取消元数据表单
const cancelMetadataForm = () => {
  metadataFormVisible.value = false
}

// 关闭弹窗
const handleCancel = () => {
  emit('update:visible', false)
}

// 根据类型获取图标
const getTypeIcon = (type: string) => {
  switch (type) {
    case 'time':
      return 'icon-calendar'
    case 'number':
      return 'icon-hash'
    case 'string':
    default:
      return 'icon-file'
  }
}
</script>

<style scoped lang="scss">
.metadata-container {
  display: flex;
  flex-direction: column;

  .metadata-description {
    font-size: 14px;
    color: var(--color-text-3);
    line-height: 32px;
    margin-bottom: 20px;
  }

  .add-btn {
    margin-bottom: 24px;
    align-self: flex-start;
  }

  .metadata-list {
    display: flex;
    flex-direction: column;
    gap: 12px;

    .loading-container {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 32px 0;
      color: var(--color-text-3);

      .loading-text {
        margin-left: 8px;
      }
    }

    .metadata-item {
      display: flex;
      align-items: center;
      padding: 12px 16px;
      border: 1px solid var(--color-border-2);
      border-radius: 8px;

      &.disabled {
        opacity: 0.7;
        background-color: var(--color-fill-1);
      }

      .metadata-icon {
        margin-right: 12px;
        color: var(--color-text-3);
      }

      .metadata-content {
        flex: 1;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;

        .metadata-name {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 4px;

          .metadata-type {
            color: var(--color-text-3);
            font-weight: normal;
            margin-left: 8px;
          }
        }

        .metadata-value,
        .metadata-status {
          font-size: 12px;
          color: var(--color-text-3);
        }

        .metadata-status {
          color: var(--color-text-4);
        }

        .metadata-edit {
          margin-top: 4px;
        }
      }

      .metadata-actions {
        display: flex;
        gap: 4px;
      }
    }

    .builtin-toggle {
      display: flex;
      align-items: center;
      padding: 12px 0;
      margin: 4px 0;

      .toggle-label {
        margin-left: 8px;
        font-size: 14px;
      }

      .help-icon {
        margin-left: 4px;
        color: var(--color-text-3);
        font-size: 14px;
      }
    }

    .builtin-metadata-container {
      border: none;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      gap: 12px 0;

      &.disabled-container {
        // background-color: #fafafa;
      }
    }

    .no-builtin-display {
      border: 1px solid #ff4d4f;
      border-radius: 8px;
      padding: 80px 16px;
      display: flex;
      justify-content: center;
      align-items: center;

      .empty-state {
        color: #ff4d4f;
        font-size: 16px;
      }
    }
  }
}

.form-tip {
  font-size: 12px;
  color: var(--color-text-3);
  margin-top: 4px;
}
</style>
