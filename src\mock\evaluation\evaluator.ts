import Mock from 'mockjs'
import { defineMock } from '../_base'
import { getDelayTime, resultSuccess } from '../_utils'

const data = Mock.mock({
  'list|10-20': [
    {
      id: '@id',
      name: '@ctitle(5, 15)',
      version: 'v@float(0, 1, 1, 1)',
      description: '@csentence(10, 30)',
      createUserString: '@cname',
      createTime: '@datetime',
      updateUserString: '@cname',
      updateTime: '@datetime'
    }
  ]
})

export default defineMock([
  {
    url: '/evaluator/list',
    method: 'get',
    timeout: getDelayTime(),
    response: ({ query }) => {
      const { name, current = 1, pageSize = 10 } = query
      let list = [...data.list]

      // 按名称筛选
      if (name) {
        list = list.filter((item) => item.name.includes(name))
      }

      // 分页
      const startIndex = (current - 1) * pageSize
      const endIndex = startIndex + Number(pageSize)
      const pageList = list.slice(startIndex, endIndex)

      return resultSuccess({
        list: pageList,
        total: list.length
      })
    }
  },
  {
    url: '/evaluator/:id',
    method: 'get',
    timeout: getDelayTime(),
    response: ({ query }) => {
      const id = query.id
      const item = data.list.find((item) => item.id === id) || data.list[0]
      return resultSuccess(item)
    }
  },
  {
    url: '/evaluator/create',
    method: 'post',
    timeout: getDelayTime(),
    response: ({ body }) => {
      return resultSuccess(true)
    }
  },
  {
    url: '/evaluator/:id',
    method: 'put',
    timeout: getDelayTime(),
    response: ({ query, body }) => {
      return resultSuccess(true)
    }
  },
  {
    url: '/evaluator/:id',
    method: 'delete',
    timeout: getDelayTime(),
    response: ({ query }) => {
      return resultSuccess(true)
    }
  },
  {
    url: '/evaluator/:id/copy',
    method: 'post',
    timeout: getDelayTime(),
    response: ({ query }) => {
      return resultSuccess(true)
    }
  }
])
