.fade-message-enter-from {
  opacity: 0;
  transform: translate3d(0, -100%, 0);
}

.fade-message-enter-to {
  opacity: 1;
  transform: translate3d(0, 0, 0);
}

.fade-message-enter-active,
.fade-message-appear-active {
  transition: opacity @transition-duration-3, transform @transition-duration-4;
}

.fade-message-leave-from {
  opacity: 1;
  transform: translate3d(0, 0, 0);
}

.fade-message-leave-to {
  opacity: 0;
  transform: translate3d(0, -100%, 0);
}

.fade-message-leave-active {
  position: absolute;
}
