<template>
  <div class="config-vision">
    <Field fieldTitle="视觉" :tooltip="'开启视觉功能将允许模型输入图片，并根据图像内容的理解回答用户问题'">
      <template #operation>
        <a-switch v-model="nodeInfo.vision.enabled" :disabled="true" type="round" />
      </template>
    </Field>
  </div>
</template>
<script setup lang="ts">
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'
const props = defineProps(['nodeInfo'])
</script>
<style scoped lang="scss"></style>
