<template>
  <div class="h-full overflow-scroll bg-chatbot-bg px-4 py-6 sm:px-12" style="border: 1px red">
    <div class="mb-3 flex items-center justify-between">
      <div class="system-xl-semibold flex items-center text-text-primary h-[41px]">监测</div>
      <!-- <div class="flex cursor-pointer items-center rounded-xl border border-effects-highlight bg-background-default-dodge p-2 shadow-xs hover:border-effects-highlight-lightmode-off hover:bg-background-default-lighter">
        <div class="w-6 flex items-center h-6 p-1 rounded-lg bg-primary-500 shadow-md">
          <icon-storage />
        </div>
        <div class="system-sm-semibold mx-2 text-text-secondary">追踪应用性能</div>
        <div class="flex items-center"></div>
        <div class="w-[1px] mx-2 bg-divider-regular h-3.5"></div>
        <div class="rounded-md p-1">
          <icon-double-down />
        </div>
      </div> -->
    </div>
    <div class="mb-6 grid w-full grid-cols-1 gap-6 xl:grid-cols-2">
      <div class="border-[0.5px] shadow-md w-full max-w-full rounded-xl border-effects-highlight">
        <div class="bg-background-default rounded-xl">
          <div
            class="flex w-full flex-col items-start justify-center gap-3 self-stretch border-b-[0.5px] border-divider-subtle p-3"
          >
            <div class="flex w-full items-center gap-3 self-stretch">
              <div class="flex grow items-center">
                <div class="mr-3 shrink-0">
                  <span
                    class="flex items-center justify-center relative rounded-lg grow-0 shrink-0 overflow-hidden w-9 h-9 text-[22px] border !border-primary-200 !bg-primary-100"
                  >
                    <icon-copy />
                  </span>
                </div>
                <div class="group w-full">
                  <div
                    class="system-md-semibold flex flex-row items-center text-text-secondary group-hover:text-text-primary"
                  >
                    <div class="min-w-0 overflow-hidden text-ellipsis break-normal">
                      {{ appInfo?.name ? appInfo?.name : '' }}
                    </div>
                  </div>
                  <div class="system-2xs-medium-uppercase text-text-tertiary">开箱即用的 AI WebApp</div>
                </div>
              </div>
              <div class="flex items-center gap-1">
                <div
                  class="w-2 h-2 border border-solid rounded-[3px] bg-components-badge-status-light-success-bg border-components-badge-status-light-success-border-inner shadow-status-indicator-green-shadow"
                ></div>
                <div class="text-text-success system-xs-semibold-uppercase">
                  {{ appInfo?.enable_site ? '运行中' : '已停用' }}
                </div>
              </div>
              <a-switch
                size="small"
                v-model="appInfo.enable_site"
                @change="handleAppStatusChange('enable_site', appInfo.enable_site)"
                type="round"
              />
            </div>
          </div>
          <div class="flex flex-col items-start justify-center self-stretch">
            <div class="system-xs-medium pb-1 pt-1 text-text-tertiary pl-5">公开访问 URL</div>
            <div
              style="background: #c8ceda40"
              class="inline-flex h-9 w-full items-center gap-0.5 rounded-lg bg-components-input-bg-normal p-1 pl-2"
            >
              <div class="flex pl-5 min-w-0 flex-1 items-center justify-between gap-2 px-1">
                <span>{{ baseUrl }}</span>
                <div>
                  <a-button type="text" size="small" class="copy-btn" @click="handleCopy(baseUrl)">
                    <template #icon>
                      <icon-copy />
                    </template>
                  </a-button>
                  <a-popover title="二维码" trigger="click">
                    <a-button type="text" @click="handlecode(baseUrl)" size="small" class="copy-btn">
                      <template #icon>
                        <icon-mosaic />
                      </template>
                    </a-button>
                    <template #content>
                      <img :src="imgurl" alt="" />
                    </template>
                  </a-popover>

                  <a-button type="text" size="small" class="copy-btn" @click="Regenerate">
                    <template #icon>
                      <icon-sync />
                    </template>
                  </a-button>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex items-center gap-1 self-stretch p-3">
          <!-- 底部按钮 -->
          <a-button class="mt-4">启动</a-button>
          <a-button class="mt-4" @click="settingModel">设置</a-button>
        </div>
      </div>

      <div class="border-[0.5px] shadow-md w-full max-w-full rounded-xl border-effects-highlight">
        <div class="bg-background-default rounded-xl">
          <div
            class="flex w-full flex-col items-start justify-center gap-3 self-stretch border-b-[0.5px] border-divider-subtle p-3"
          >
            <div class="flex w-full items-center gap-3 self-stretch">
              <div class="flex grow items-center">
                <div class="mr-3 shrink-0">
                  <span
                    class="flex items-center justify-center relative rounded-lg grow-0 shrink-0 overflow-hidden w-9 h-9 text-[22px] border !border-primary-200 !bg-primary-100"
                  >
                    <icon-copy />
                  </span>
                </div>
                <div class="group w-full">
                  <div
                    class="system-md-semibold flex flex-row items-center text-text-secondary group-hover:text-text-primary"
                  >
                    <div class="min-w-0 overflow-hidden text-ellipsis break-normal">后端服务 API</div>
                  </div>
                  <div class="system-2xs-medium-uppercase text-text-tertiary">可集成至你的应用的后端即服务</div>
                </div>
              </div>
              <div class="flex items-center gap-1">
                <div
                  class="w-2 h-2 border border-solid rounded-[3px] bg-components-badge-status-light-success-bg border-components-badge-status-light-success-border-inner shadow-status-indicator-green-shadow"
                ></div>
                <div class="text-text-success system-xs-semibold-uppercase">
                  {{ appInfo?.enable_api ? '运行中' : '已停用' }}
                </div>
              </div>
              <a-switch
                size="small"
                v-model="appInfo.enable_api"
                @change="handleAppStatusChange2('enable_api', appInfo.enable_api)"
                type="round"
              />
            </div>
          </div>
          <div class="flex flex-col items-start justify-center self-stretch">
            <div class="system-xs-medium pb-1 text-text-tertiary pt-1 pl-5">API 访问凭据</div>
            <div
              style="background: #c8ceda40"
              class="inline-flex h-9 w-full items-center gap-0.5 rounded-lg bg-components-input-bg-normal p-1 pl-2"
            >
              <div class="flex pl-5 min-w-0 flex-1 items-start justify-start gap-2 px-1">
                {{ appInfo?.api_base_url ? appInfo.api_base_url : '' }}
              </div>
            </div>
          </div>
        </div>
        <div class="flex items-center gap-1 self-stretch p-3">
          <a-button class="mt-4" @click="showApiKeyModal = true">API密钥</a-button>
          <a-button class="mt-4" @click="changeTabkey">查询API文档</a-button>
        </div>
      </div>
    </div>
    <div class="mb-3 flex items-center">
      <div class="system-xl-semibold flex items-center text-text-primary h-[41px]">分析</div>
      <a-select
        :style="{ width: '320px' }"
        v-model="datatype"
        style="margin-left: 20px"
        placeholder="Please select ..."
        @change="changeDateType"
      >
        <a-option>今天</a-option>
        <a-option>过去7天</a-option>
        <a-option>过去4周</a-option>
        <a-option>过去3月</a-option>
        <a-option>过去12月</a-option>
        <a-option>本月至今</a-option>
        <a-option>本季度至今</a-option>
        <a-option>本年至今</a-option>
      </a-select>
      <!-- <a-option>所有时间</a-option> -->
    </div>

    <div class="mb-6 grid w-full grid-cols-1 gap-6 xl:grid-cols-2">
      <div
        class="flex w-full flex-col rounded-xl bg-components-chart-bg border px-6 py-4 shadow-md flex-wrap"
        v-for="item in dateTypeList"
        :key="item.value"
      >
        <div class="mb-3">
          <div class="flex grow items-center">
            <div class="group w-full">
              <div
                class="system-md-semibold flex flex-row items-center text-text-secondary group-hover:text-text-primary"
              >
                <div class="min-w-0 overflow-hidden text-ellipsis break-normal">{{ item.label }}</div>
                <div class="w-4 ml-1">
                  <a-tooltip :content="item.explanation">
                    <icon-question-circle />
                  </a-tooltip>
                </div>
              </div>
              <div class="system-2xs-medium-uppercase text-text-tertiary">
                {{ datatype }}
              </div>
            </div>
          </div>
        </div>
        <div class="mb-4 flex-1">
          <div class="flex grow items-center">
            <div class="group w-full">
              <div
                class="system-md-semibold flex flex-row items-center text-text-secondary group-hover:text-text-primary !text-3xl !font-normal !text-text-quaternary"
              >
                <div class="min-w-0 overflow-hidden text-ellipsis break-normal">
                  {{ item.total }} {{ item.value == 'tokens-per-second' ? 'Token/秒' : '' }}
                </div>
              </div>
              <div class="system-2xs-medium-uppercase flex text-text-tertiary" v-if="item.value == 'token-costs'">
                耗费 Tokens(~${{ item.price }})
              </div>
            </div>
          </div>
        </div>
        <div class="echarts-for-react">
          <lineCharts :x-data="item.date" :y-data="item.number" :line-color="'#ff7300'"></lineCharts>
        </div>
      </div>
    </div>
    <!-- API密钥管理弹窗 -->
    <a-modal
      v-model:visible="showApiKeyModal"
      title="API 密钥"
      width="800px"
      :footer="false"
      @cancel="showApiKeyModal = false"
    >
      <div class="api-key-modal">
        <div class="modal-description">
          <a-alert type="warning" show-icon>
            <template #default>
              如果不想你的 API 被滥用，请保护好你的 API Key， 最佳实践是避免在前端代码中明文引用。
            </template>
          </a-alert>
        </div>

        <!-- 密钥列表 -->
        <div class="api-keys-section">
          <div v-if="loading" class="loading-container">
            <a-spin />
          </div>

          <div v-else-if="apiKeys.length === 0" class="empty-container">
            <a-empty description="暂无API密钥">
              <a-button type="primary" @click="handleCreateKey">创建第一个密钥</a-button>
            </a-empty>
          </div>

          <div v-else class="keys-table">
            <!-- 表头 -->
            <div class="table-header">
              <div class="header-cell key-cell">密钥</div>
              <div class="header-cell created-cell">创建时间</div>
              <div class="header-cell used-cell">最后使用</div>
              <div class="header-cell actions-cell">操作</div>
            </div>

            <!-- 表格内容 -->
            <div class="table-body">
              <div v-for="key in apiKeys" :key="key?.id || Math.random()" class="table-row">
                <div class="body-cell key-cell">
                  <span class="key-token">{{ formatToken(key?.token || '') }}</span>
                </div>
                <div class="body-cell created-cell">
                  {{ formatDateTime(key?.created_at) }}
                </div>
                <div class="body-cell used-cell">
                  {{ key?.last_used_at ? formatDateTime(key.last_used_at) : '从未' }}
                </div>
                <div class="body-cell actions-cell">
                  <a-space>
                    <a-button type="text" size="small" @click="handleCopy(key?.token || '')">
                      <template #icon>
                        <icon-copy />
                      </template>
                    </a-button>
                    <a-popconfirm
                      title="确定要删除这个API密钥吗？"
                      content="删除后无法恢复，正在使用的应用可能会受到影响。"
                      @ok="handleDeleteKey(key?.id || '')"
                    >
                      <a-button type="text" size="small" status="danger">
                        <template #icon>
                          <icon-delete />
                        </template>
                      </a-button>
                    </a-popconfirm>
                  </a-space>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 创建密钥按钮 - 只在有密钥时显示 -->
        <div v-if="apiKeys.length > 0" class="create-key-section">
          <a-button type="primary" :loading="createLoading" @click="handleCreateKey">
            <template #icon>
              <icon-plus />
            </template>
            创建密钥
          </a-button>
        </div>
      </div>
    </a-modal>

    <!-- 新密钥展示弹窗 -->
    <a-modal v-model:visible="showNewKeyModal" title="API密钥创建成功" :footer="false" :closable="false">
      <div class="new-key-modal">
        <a-alert type="success" show-icon>
          <template #title>密钥创建成功</template>
          <template #default>请将此密钥保存在安全且可访问的地方。关闭此窗口后将无法再次查看完整密钥。</template>
        </a-alert>

        <div class="new-key-display">
          <a-input :model-value="newApiKey" readonly class="new-key-input">
            <template #suffix>
              <a-button type="text" @click="handleCopy(newApiKey)">
                <icon-copy />
              </a-button>
            </template>
          </a-input>
        </div>

        <div class="modal-actions">
          <a-button type="primary" @click="handleCloseNewKeyModal">我已保存，关闭</a-button>
        </div>
      </div>
    </a-modal>
    <setting v-if="showSetModal" :visible="showSetModal"></setting>
  </div>
</template>

<script setup lang="ts">
import lineCharts from './lineCharts.vue'
import dayjs from 'dayjs'
import QRCode from 'qrcode'
import utc from 'dayjs/plugin/utc'
import { Message } from '@arco-design/web-vue'
import timezone from 'dayjs/plugin/timezone'
import setting from './setting.vue'
import {
  dailyConversations,
  dailyEndUsers,
  averageSessionInteractions,
  tokensPerSecond,
  userSatisfactionRate,
  tokenCosts,
  dailyMessages,
  siteEnable,
  apiEnable,
  workflowConversations,
  workflowTerminals,
  workflowCosts,
  workflowinteractions,
  accessTokenReset
} from '@/apis/workflow'
import { getApiKeys, createApiKey, deleteApiKey, type ApiKey } from '@/apis/apps'
import { copyToClipboard, dateFormat } from '@/utils'
import { Modal } from '@arco-design/web-vue'
const datatype = ref('过去7天')
const props = defineProps<{
  appInfo?: any
}>()

const route = useRoute()
const appId = route.params.appId as string
dayjs.extend(utc)
dayjs.extend(timezone)
const getStartTimeAndEndTime = () => {
  const now = new Date()
  let startDate: Date, endDate: Date

  switch (datatype.value) {
    case '今天':
      startDate = dayjs(now).startOf('day').toDate()
      endDate = dayjs(now).endOf('day').toDate()
      break
    case '过去7天':
      endDate = dayjs(now).endOf('day').toDate()
      startDate = dayjs(endDate).subtract(7, 'days').toDate()
      break
    case '过去4周':
      endDate = dayjs(now).endOf('day').toDate()
      startDate = dayjs(endDate).subtract(28, 'days').toDate()
      break
    case '过去3月':
      endDate = dayjs(now).endOf('day').toDate()
      startDate = dayjs(endDate).subtract(3, 'months').toDate()
      break
    case '过去12月':
      endDate = dayjs(now).endOf('day').toDate()
      startDate = dayjs(endDate).subtract(1, 'year').toDate()
      break
    case '本月至今':
      startDate = dayjs(now).startOf('month').toDate()
      endDate = dayjs(now).endOf('month').toDate()
      break
    case '本季度至今':
      startDate = dayjs(now).startOf('quarter').toDate()
      endDate = dayjs(now).endOf('month').toDate()
      break
    case '本年至今':
      startDate = dayjs(now).startOf('year').toDate()
      endDate = dayjs(now).endOf('year').toDate()
      break
    case '所有时间':
      startDate = dayjs(new Date(2000, 0, 1))
        .startOf('day')
        .toDate() // 2000-01-01
      endDate = dayjs(now).endOf('day').toDate()
      break
    default:
      startDate = now
      endDate = now
  }

  return {
    startTime: dayjs(startDate).format('YYYY-MM-DD HH:mm'),
    endTime: dayjs(endDate).format('YYYY-MM-DD HH:mm')
  }
}
// 数据状态
const loading = ref(false)
const createLoading = ref(false)
const apiKeys = ref<ApiKey[]>([])
const apiBaseUrl = ref('')

// 弹窗状态
const showApiKeyModal = ref(false)
const showNewKeyModal = ref(false)
const showSetModal = ref(false)
const newApiKey = ref('')
const imgurl = ref('')
// const qrCanvas = ref()
const handlecode = async (baseurl) => {
  try {
    const url = await QRCode.toDataURL(baseurl, {
      width: 150,
      height: 150,
      margin: 2
    })
    imgurl.value = url
  } catch (err) {
    console.error(err)
  }
}
// 加载API密钥列表
const loadApiKeys = async () => {
  try {
    loading.value = true
    const res = await getApiKeys(appId)
    // 确保数据格式正确
    if (res && res.data && Array.isArray(res.data)) {
      apiKeys.value = res.data
    } else {
      apiKeys.value = []
    }
  } catch (error) {
    apiKeys.value = []
  } finally {
    loading.value = false
  }
}
const emit = defineEmits(['tab-change'])
const changeTabkey = () => {
  emit('tab-change', 'develop')
}

const settingModel = () => {
  showSetModal.value = true
}
// 创建API密钥
const handleCreateKey = async () => {
  try {
    createLoading.value = true
    const response = await createApiKey(props.appId, {})
    // 处理不同的响应格式
    const token = response.data?.token || response.token
    if (token) {
      newApiKey.value = token
      // 不关闭密钥列表弹窗，只显示新密钥弹窗
      showNewKeyModal.value = true
      await loadApiKeys()
      Message.success('API密钥创建成功')
    } else {
      throw new Error('未获取到密钥信息')
    }
  } catch (error) {
    console.error('创建API密钥失败:', error)
    Message.error('创建API密钥失败')
  } finally {
    createLoading.value = false
  }
}
// 关闭新密钥弹窗
const handleCloseNewKeyModal = () => {
  showNewKeyModal.value = false
  newApiKey.value = ''
}

// 处理复制
const handleCopy = async (text: string) => {
  try {
    const success = await copyToClipboard(text)
    if (success) {
      Message.success('已复制到剪贴板')
    } else {
      Message.error('复制失败')
    }
  } catch (error) {
    Message.error('复制失败')
  }
}
const handleAppStatusChange = (type, choice) => {
  siteEnable(appId, {
    [type]: choice
  }).then((res) => {
    Message.success('修改成功')
  })
}
const handleAppStatusChange2 = (type, choice) => {
  apiEnable(appId, {
    [type]: choice
  }).then((res) => {
    Message.success('修改成功')
  })
}
const Regenerate = () => {
  Modal.confirm({
    title: '重新生成',
    content: `您是否要重新生成公开访问 URL？`,
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      accessTokenReset(appId).then((res) => {
        token.value = res.access_token
      })
    }
  })
}
// 生成 xData 方法
const generateXData = (range: string): string[] => {
  const now = dayjs()
  let dates: string[] = []

  switch (range) {
    case '今天':
      dates = [now.format('YYYY-MM-DD')]
      break

    case '过去7天':
      for (let i = 6; i >= 0; i--) {
        dates.push(now.subtract(i, 'day').format('YYYY-MM-DD'))
      }
      break

    case '过去4周':
      for (let i = 27; i >= 0; i--) {
        dates.push(now.subtract(i, 'day').format('YYYY-MM-DD'))
      }
      break

    case '过去3月':
      for (let i = 0; i < 3; i++) {
        dates.push(now.subtract(i, 'month').format('YYYY-MM'))
      }
      dates.reverse() // 最近的月份在最后
      break

    case '过去12月':
      for (let i = 0; i < 12; i++) {
        dates.push(now.subtract(i, 'month').format('YYYY-MM'))
      }
      dates.reverse()
      break

    case '本月至今':
      const startOfMonth = now.startOf('month')
      const daysInMonth = now.daysInMonth()
      for (let i = 0; i < daysInMonth; i++) {
        dates.push(startOfMonth.add(i, 'day').format('YYYY-MM-DD'))
      }
      break

    case '本季度至今':
      const startOfQuarter = now.startOf('quarter')
      const diff = now.diff(startOfQuarter, 'day')
      for (let i = 0; i <= diff; i++) {
        dates.push(startOfQuarter.add(i, 'day').format('YYYY-MM-DD'))
      }
      break

    case '本年至今':
      const startOfYear = now.startOf('year')
      const diffYear = now.diff(startOfYear, 'day')
      for (let i = 0; i <= diffYear; i++) {
        dates.push(startOfYear.add(i, 'day').format('YYYY-MM-DD'))
      }
      break

    default:
      dates = []
  }

  return dates
}

const changeDateType = () => {
  const { startTime, endTime } = getStartTimeAndEndTime()
  console.log(startTime, endTime)
  getEchartsData(startTime, endTime)
}

const dateTypeList = ref([])
const token = ref(props.appInfo?.site?.access_token || '')
const commonItems = {
  dailyMessages: {
    label: '全部消息数',
    value: 'daily-messages',
    explanation: '反映 AI 每天的互动总次数，每回答用户一个问题算一条 Message。',
    number: [],
    date: [],
    total: 0
  },
  dailyEndUsers: {
    label: '活跃用户数',
    value: 'daily-end-users',
    explanation: '与 AI 有效互动，即有一问一答以上的唯一用户数。提示词编排和调试的会话不计入。',
    number: [],
    date: [],
    total: 0
  },
  tokenCosts: {
    label: '费用消耗',
    value: 'token-costs',
    explanation: '反映每日该应用请求语言模型的 Tokens 花费，用于成本控制。',
    number: [],
    date: [],
    total: 0,
    price: 0
  }
}

const workflowItems = [
  {
    label: '全部消息数',
    value: 'workflowConversations',
    explanation: '反映 AI 每天的互动总次数，每回答用户一个问题算一条 Message。',
    number: [],
    date: [],
    total: 0
  },
  {
    label: '活跃用户数',
    value: 'workflowTerminals',
    explanation: '与 AI 有效互动，即有一问一答以上的唯一用户数。提示词编排和调试的会话不计入。',
    number: [],
    date: [],
    total: 0
  },
  {
    label: '费用消耗',
    value: 'workflowCosts',
    explanation: '反映每日该应用请求语言模型的 Tokens 花费，用于成本控制。',
    number: [],
    date: [],
    total: 0,
    price: 0
  },
  {
    label: '平均用户调用次数',
    value: 'workflowinteractions',
    explanation: '反映每天用户的使用次数。该指标反映了用户粘性。',
    number: [],
    date: [],
    total: 0
  }
]

const defaultItems = [
  {
    label: '全部会话数',
    value: 'daily-conversations',
    explanation: '反映 AI 每天的会话总次数，提示词编排和调试的消息不计入。',
    number: [],
    date: [],
    total: 0
  },
  commonItems.dailyEndUsers,
  {
    label: '平均会话互动数',
    value: 'average-session-interactions',
    explanation:
      '反映每个会话用户的持续沟通次数，如果用户与 AI 问答了 10 轮，即为 10。该指标反映了用户粘性。仅在对话型应用提供。',
    number: [],
    date: [],
    total: 0
  },
  {
    label: 'Token 输出速度',
    value: 'tokens-per-second',
    explanation: '衡量 LLM 的性能。统计 LLM 从请求开始到输出完毕这段期间的 Tokens 输出速度。',
    number: [],
    date: [],
    total: 0
  },
  {
    label: '用户满意度',
    value: 'user-satisfaction-rate',
    explanation: '每 1000 条消息的点赞数。反映了用户对回答十分满意的比例。',
    number: [],
    date: [],
    total: 0
  },
  commonItems.tokenCosts,
  commonItems.dailyMessages
]

const updateDateTypeList = (mode) => {
  dateTypeList.value = mode === 'workflow' ? workflowItems : defaultItems
}
const getEchartsData = async (startTime, endTime) => {
  if (!props.appInfo || !props.appInfo.mode) {
    return false
  }
  updateDateTypeList(props.appInfo.mode)
  if (props.appInfo.mode === 'workflow') {
    getworkflowConversations(startTime, endTime)
    getworkflowTerminals(startTime, endTime)
    getworkflowCosts(startTime, endTime)
    getworkflowinteractions(startTime, endTime)
  } else {
    getDailyConversations(startTime, endTime)
    getdailyEndUsers(startTime, endTime)
    getaverageSessionInteractions(startTime, endTime)
    gettokensPerSecond(startTime, endTime)
    getuserSatisfactionRate(startTime, endTime)
    gettokenCosts(startTime, endTime)
    getdailyMessages(startTime, endTime)
  }
}
const getworkflowConversations = async (startTime, endTime) => {
  workflowConversations(appId, {
    start: startTime,
    end: endTime
  }).then((res) => {
    const dailyItem = dateTypeList.value.find((item) => item.value === 'workflowConversations')
    if (dailyItem) {
      if (res.data.length > 0) {
        dailyItem.total = 0
        dailyItem.date = []
        dailyItem.number = []
        res.data.forEach((i) => {
          const count = Number(i.runs)
          dailyItem.total += count
          dailyItem.date.push(i.date)
          dailyItem.number.push(count)
        })
      } else {
        dailyItem.date = generateXData(datatype.value)
        dailyItem.number = new Array(dailyItem.date.length).fill(0)
        dailyItem.total = 0
      }
    }
  })
}
const getworkflowTerminals = async (startTime, endTime) => {
  workflowTerminals(appId, {
    start: startTime,
    end: endTime
  }).then((res) => {
    const dailyItem = dateTypeList.value.find((item) => item.value === 'workflowTerminals')
    if (dailyItem) {
      if (res.data.length > 0) {
        dailyItem.total = 0
        dailyItem.date = []
        dailyItem.number = []
        res.data.forEach((i) => {
          const count = Number(i.terminal_count)
          dailyItem.total += count
          dailyItem.date.push(i.date)
          dailyItem.number.push(count)
        })
      } else {
        dailyItem.date = generateXData(datatype.value)
        dailyItem.number = new Array(dailyItem.date.length).fill(0)
        dailyItem.total = 0
      }
    }
  })
}
const getworkflowCosts = async (startTime, endTime) => {
  workflowCosts(appId, {
    start: startTime,
    end: endTime
  }).then((res) => {
    const dailyItem = dateTypeList.value.find((item) => item.value === 'workflowCosts')
    if (dailyItem) {
      if (res.data.length > 0) {
        dailyItem.total = 0
        dailyItem.date = []
        dailyItem.number = []
        res.data.forEach((i) => {
          const count = Number(i.token_count)
          dailyItem.total += count
          dailyItem.date.push(i.date)
          dailyItem.number.push(count)
        })
      } else {
        dailyItem.date = generateXData(datatype.value)
        dailyItem.number = new Array(dailyItem.date.length).fill(0)
        dailyItem.total = 0
      }
    }
  })
}

const getworkflowinteractions = async (startTime, endTime) => {
  workflowinteractions(appId, {
    start: startTime,
    end: endTime
  }).then((res) => {
    const dailyItem = dateTypeList.value.find((item) => item.value === 'workflowinteractions')
    if (dailyItem) {
      if (res.data.length > 0) {
        dailyItem.total = 0
        dailyItem.date = []
        dailyItem.number = []
        res.data.forEach((i) => {
          const count = Number(i.interactions)
          dailyItem.total += count
          dailyItem.date.push(i.date)
          dailyItem.number.push(count)
        })
      } else {
        dailyItem.date = generateXData(datatype.value)
        dailyItem.number = new Array(dailyItem.date.length).fill(0)
        dailyItem.total = 0
      }
    }
  })
}
const getDailyConversations = async (startTime, endTime) => {
  dailyConversations(appId, {
    start: startTime,
    end: endTime
  }).then((res) => {
    const dailyItem = dateTypeList.value.find((item) => item.value === 'daily-conversations')
    if (dailyItem) {
      if (res.data.length > 0) {
        dailyItem.total = 0
        dailyItem.date = []
        dailyItem.number = []
        res.data.forEach((i) => {
          const count = Number(i.conversation_count)
          dailyItem.total += count
          dailyItem.date.push(i.date)
          dailyItem.number.push(count)
        })
      } else {
        dailyItem.date = generateXData(datatype.value)
        dailyItem.number = new Array(dailyItem.date.length).fill(0)
        dailyItem.total = 0
      }
    }
  })
}
const getdailyEndUsers = async (startTime, endTime) => {
  dailyEndUsers(appId, {
    start: startTime,
    end: endTime
  }).then((res) => {
    const dailyItem = dateTypeList.value.find((item) => item.value === 'daily-end-users')
    if (dailyItem) {
      if (res.data.length > 0) {
        dailyItem.total = 0
        dailyItem.date = []
        dailyItem.number = []
        res.data.forEach((i) => {
          const count = Number(i.terminal_count)
          dailyItem.total += count
          dailyItem.date.push(i.date)
          dailyItem.number.push(count)
        })
      } else {
        dailyItem.date = generateXData(datatype.value)
        dailyItem.number = new Array(dailyItem.date.length).fill(0)
        dailyItem.total = 0
      }
    }
  })
}
const getaverageSessionInteractions = async (startTime, endTime) => {
  averageSessionInteractions(appId, {
    start: startTime,
    end: endTime
  }).then((res) => {
    const dailyItem = dateTypeList.value.find((item) => item.value === 'average-session-interactions')
    if (dailyItem) {
      if (res.data.length > 0) {
        dailyItem.total = 0
        dailyItem.date = []
        dailyItem.number = []
        res.data.forEach((i) => {
          const count = Number(i.interactions)
          dailyItem.total += count
          dailyItem.date.push(i.date)
          dailyItem.number.push(count)
        })
      } else {
        dailyItem.date = generateXData(datatype.value)
        dailyItem.number = new Array(dailyItem.date.length).fill(0)
        dailyItem.total = 0
      }
    }
  })
}

const gettokensPerSecond = async (startTime, endTime) => {
  tokensPerSecond(appId, {
    start: startTime,
    end: endTime
  }).then((res) => {
    const dailyItem = dateTypeList.value.find((item) => item.value === 'tokens-per-second')
    if (dailyItem) {
      if (res.data.length > 0) {
        dailyItem.total = 0
        dailyItem.date = []
        dailyItem.number = []
        res.data.forEach((i) => {
          const count = Number(i.tps)
          dailyItem.total += count
          dailyItem.date.push(i.date)
          dailyItem.number.push(count)
        })
      } else {
        dailyItem.date = generateXData(datatype.value)
        dailyItem.number = new Array(dailyItem.date.length).fill(0)
        dailyItem.total = 0
      }
    }
  })
}

const getuserSatisfactionRate = async (startTime, endTime) => {
  userSatisfactionRate(appId, {
    start: startTime,
    end: endTime
  }).then((res) => {
    const dailyItem = dateTypeList.value.find((item) => item.value === 'user-satisfaction-rate')
    if (dailyItem) {
      if (res.data.length > 0) {
        dailyItem.total = 0
        dailyItem.date = []
        dailyItem.number = []
        res.data.forEach((i) => {
          const count = Number(i.rate)
          dailyItem.total += count
          dailyItem.date.push(i.date)
          dailyItem.number.push(count)
        })
      } else {
        dailyItem.date = generateXData(datatype.value)
        dailyItem.number = new Array(dailyItem.date.length).fill(0)
        dailyItem.total = 0
      }
    }
  })
}

const gettokenCosts = async (startTime, endTime) => {
  tokenCosts(appId, {
    start: startTime,
    end: endTime
  }).then((res) => {
    const dailyItem = dateTypeList.value.find((item) => item.value === 'token-costs')
    if (dailyItem) {
      if (res.data.length > 0) {
        dailyItem.total = 0
        dailyItem.date = []
        dailyItem.number = []
        res.data.forEach((i) => {
          const count = Number(i.token_count)
          dailyItem.total += count
          dailyItem.date.push(i.date)
          dailyItem.number.push(count)
          dailyItem.price = parseFloat(parseFloat(i.total_price || 0).toFixed(4))
        })
      } else {
        dailyItem.date = generateXData(datatype.value)
        dailyItem.number = new Array(dailyItem.date.length).fill(0)
        dailyItem.total = 0
        dailyItem.price = 0
      }
    }
  })
}

const getdailyMessages = async (startTime, endTime) => {
  dailyMessages(appId, {
    start: startTime,
    end: endTime
  }).then((res) => {
    const dailyItem = dateTypeList.value.find((item) => item.value === 'daily-messages')
    if (dailyItem) {
      if (res.data.length > 0) {
        dailyItem.total = 0
        dailyItem.date = []
        dailyItem.number = []
        res.data.forEach((i) => {
          const count = Number(i.message_count)
          dailyItem.total += count
          dailyItem.date.push(i.date)
          dailyItem.number.push(count)
        })
      } else {
        dailyItem.date = generateXData(datatype.value)
        dailyItem.number = new Array(dailyItem.date.length).fill(0)
        dailyItem.total = 0
      }
    }
  })
}

const baseUrl = computed(() => {
  if (!props.appInfo || !props.appInfo.site) {
    return ''
  }

  let app_base_url = props.appInfo.site.app_base_url || ''
  let mode = ''

  if (props.appInfo.mode) {
    if (props.appInfo.mode === 'workflow' || props.appInfo.mode === 'completion') {
      mode = props.appInfo.mode
    } else {
      mode = 'chat'
    }
  }

  return `${app_base_url}/${mode}/${token.value}`
})

watch(
  () => props.appInfo?.mode,
  (newMode) => {
    if (newMode) {
      const { startTime, endTime } = getStartTimeAndEndTime()
      getEchartsData(startTime, endTime)
    }
  },
  { immediate: true }
)
onMounted(async () => {
  const { startTime, endTime } = getStartTimeAndEndTime()
  getEchartsData(startTime, endTime)
  await loadApiKeys()
})
</script>

<style scoped lang="scss">
.echarts-for-react {
  height: 300px;
}
</style>
