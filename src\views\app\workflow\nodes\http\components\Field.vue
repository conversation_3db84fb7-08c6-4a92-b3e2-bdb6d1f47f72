<template>
  <div>
    <div v-if="$attrs.fieldTitle" class="flex items-center justify-between field-item">
      <div class="flex h-6 items-center">
        <a-space size="mini">
          <!--折叠icon-->
          <template v-if="$attrs.supportFold">
            <icon-right />
            <icon-down />
          </template>
          <!--标题-->
          <span class="field-title">{{ $attrs.fieldTitle }}</span>
          <template v-if="$attrs.tooltip">
            <a-tooltip :content="$attrs.tooltip">
              <icon-question-circle />
            </a-tooltip>
          </template>
        </a-space>
      </div>

      <!--操作按钮-->
      <slot name="operation" />
    </div>

    <!--插槽-->
    <slot />
  </div>
</template>

<script setup lang="ts">
const $attrs = useAttrs()
</script>

<style scoped lang="scss">
.field-item {
  margin-top: var(--margin);
  margin-bottom: 7px;

  .field-title {
    font-weight: 500;
  }
}
</style>
