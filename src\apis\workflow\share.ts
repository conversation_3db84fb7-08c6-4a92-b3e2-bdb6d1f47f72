import { getToken } from '@/utils/auth'
import http from '@/utils/http'
import type { FileUploadResponse } from '../datasets/type'
import { baseOptions, ContentType, type FetchOptionType, type IOnDataMoreInfo, type IOtherOptions } from './type'
import handleStream from '@/views/app/workflow/utils/handle-stream'

const BASE_URL = 'console/api'

// 获取app信息
export function getAppInfo(id: string) {
  return http.get(`${BASE_URL}/site`)
}

// 根据app获取params
export function getAppParams(id: string) {
  return http.get(`${BASE_URL}/installed-apps/${id}/parameters`)
}

// 上传文件
export function uploadFile(file: File, onProgress?: (percent: number) => void) {
  const formData = new FormData()
  formData.append('file', file)
  const headers = {
    Authorization: `Bearer ${getToken()}`,
    'Content-Type': 'multipart/form-data'
  }

  return http.post<FileUploadResponse>(`${BASE_URL}/files/upload`, formData, {
    headers: headers,
    onUploadProgress: (progressEvent: any) => {
      if (onProgress && progressEvent.total) {
        const percent = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(percent)
      }
    }
  })
}

export function shareRun(appId, fetchOptions: FetchOptionType, otherOptions?: IOtherOptions) {
  const {
    onData,
    onCompleted,
    onFile,
    onWorkflowStarted,
    onWorkflowFinished,
    onNodeStarted,
    onNodeFinished,
    onIterationStart,
    onIterationNext,
    onIterationFinish,
    onNodeRetry,
    onParallelBranchStarted,
    onParallelBranchFinished,
    onTextChunk,
    onTTSChunk,
    onTTSEnd,
    onTextReplace,
    onAgentLog,
    onError,
    getAbortController,
    onLoopStart,
    onLoopNext,
    onLoopFinish
  } = otherOptions!
  const abortController = new AbortController()
  const token = getToken()
  const options = Object.assign(
    {},
    baseOptions,
    {
      method: 'POST',
      signal: abortController.signal,
      headers: new Headers({
        Authorization: `Bearer ${token}`
      })
    } as RequestInit,
    fetchOptions
  )
  const contentType = (options.headers as Headers).get('Content-Type')
  if (!contentType) (options.headers as Headers).set('Content-Type', ContentType.json)
  const { body } = options
  if (body) options.body = JSON.stringify(body)
  fetch(`/console/api/installed-apps/${appId}/workflows/run`, options as RequestInit)
    .then((res) => {
      return handleStream(
        res,
        (str: string, isFirstMessage: boolean, moreInfo: IOnDataMoreInfo) => {
          if (moreInfo.errorMessage) {
            onError?.(moreInfo.errorMessage, moreInfo.errorCode)
            if (
              moreInfo.errorMessage !== 'AbortError: The user aborted a request.' &&
              !moreInfo.errorMessage.includes('TypeError: Cannot assign to read only property')
            )
              console.warn({ type: 'error', message: moreInfo.errorMessage })
            return
          }
          onData?.(str, isFirstMessage, moreInfo)
        },
        onCompleted,
        onFile,
        onWorkflowStarted,
        onWorkflowFinished,
        onNodeStarted,
        onNodeFinished,
        onIterationStart,
        onIterationNext,
        onIterationFinish,
        onLoopStart,
        onLoopNext,
        onLoopFinish,
        onNodeRetry,
        onParallelBranchStarted,
        onParallelBranchFinished,
        onTextChunk,
        onTTSChunk,
        onTTSEnd,
        onTextReplace,
        onAgentLog
      )
    })
    .catch((e) => {
      if (
        e.toString() !== 'AbortError: The user aborted a request.' &&
        !e.toString().errorMessage.includes('TypeError: Cannot assign to read only property')
      )
        console.log('err')
    })
}
