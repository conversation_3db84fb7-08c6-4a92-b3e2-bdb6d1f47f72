<template>
  <div class="custom-node" :style="{ width: '100%' }">
    <div class="truncate flex items-center h-[46px]">
      <div class="custom-node-icon" :style="{ backgroundColor: '#06aed4' }">
        <AiSvgIcon style="width: 14px; height: 14px" :name="`workflow-${type}`" />
      </div>
      <div class="custom-node-text overflow-ellipsis overflow-hidden whitespace-nowrap flex-1">
        {{ data.title }}
      </div>
      <Handle id="target" type="target" :position="Position.Left" />
    </div>

    <div v-for="(item, index) in data.cases" :key="item.id" class="custom-node-condition">
      <div class="flex">
        <span class="custom-node-condition-label">{{ item.label !== '' ? 'CASE' + (index + 1) : '' }}</span>
        <span class="custom-node-condition-sy">{{ index == 0 ? 'IF' : 'ELIF' }}</span>
        <NodeList
          class="custom-node-add"
          :popoverInstance="true"
          :nodeId="props.id"
          :node-props="props"
          :source-handle="`${item.id}`"
        >
          <Handle :id="item.id" type="source" class="custom-node-handle" :position="Position.Right">
            <icon-plus :style="{ pointerEvents: 'none' }" />
          </Handle>
        </NodeList>
      </div>
      <div v-if="item.conditions">
        <div v-for="condition in item.conditions" class="flex h-6 items-center rounded-md my-1 bg-slate-100 px-1">
          <AiSvgIcon name="ai-variable" />
          <div class="ml-0.5 shrink-[2] truncate text-xs font-medium max-w-[70px]">
            {{ condition.variable_selector ? condition.variable_selector[1] : '' }}
          </div>
          <div class="mx-1 shrink-0 text-xs font-medium text-text-primary">
            {{
              condition.varType == 'number'
                ? condition.comparison_operator
                : comparisonOperator[condition.comparison_operator]
            }}
          </div>
          <div class="shrink-[3] truncate text-xs text-text-secondary">{{ condition.value }}</div>
          <!-- <div class="absolute bottom-[-10px] right-1 z-10 text-[10px] font-medium uppercase leading-4 text-text-accent"  >and</div> -->
        </div>
      </div>
    </div>
    <div class="custom-node-condition">
      <div class="flex">
        <span class="custom-node-condition-label" />
        <span class="custom-node-condition-sy">ELSE</span>
        <NodeList class="custom-node-add" :popoverInstance="true" :nodeId="props.id" :node-props="props">
          <Handle
            id="false"
            :key="`${data.id}-else-source-handle`"
            type="source"
            class="custom-node-handle"
            :position="Position.Right"
          >
            <icon-plus :style="{ pointerEvents: 'none' }" />
          </Handle>
        </NodeList>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { Handle, Position } from '@vue-flow/core'
import type { NodeProps } from '@vue-flow/core'
import NodeList from '../node-list.vue'
import { nanoid } from 'nanoid'
import { comparisonOperator } from '@/views/app/workflow/types/workflow'
const props = defineProps<NodeProps>()
const conditionData = reactive<any[]>([
  { id: nanoid(), label: 'CASE ', name: 'IF' },
  { id: nanoid(), label: '', name: 'ELSE' }
])
console.log(props.data)
const add = () => {
  conditionData.splice(conditionData.length - 1, 0, { id: nanoid(), label: 'CASE', name: 'ELIF' })
}
</script>
<style scoped lang="scss">
:deep(.vue-flow__handle-left) {
  top: auto;
}

.custom-node {
  min-width: 240px;
  display: flex;
  flex-direction: column;
  padding: 4px 14px;
  // min-height: 100px;
  width: 240px;
  background-color: var(--color-bg-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 12px;

  &-info {
    display: flex;
    align-items: center;
  }

  &-icon {
    margin-right: 8px;
    font-size: 14px;
    height: 24px;
    width: 24px;
    border-radius: 8px;
    background-color: var(--color-fill-3);
    text-align: center;
    line-height: 24px;
    color: var(--color-text-1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-text {
    font-size: 18px;
    font-weight: 500;
    color: var(--color-text-2);
  }

  &-handle {
    background: rgb(var(--primary-6));
    height: 10px;
    width: 2px;
    border-radius: 0;
    border: none;
    min-width: 2px;

    .arco-icon {
      display: none;
    }
  }

  &:hover {
    .custom-node-handle {
      background-color: rgb(var(--primary-6));
      border-radius: 50%;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
      cursor: pointer;
    }

    .arco-icon {
      display: inline-block;
      width: 14px;
      height: 14px;
      color: var(--color-white);
    }
  }

  &-add {
    position: absolute;
    right: -14px;
    top: 10px;
    pointer-events: none;
  }

  &-condition {
    position: relative;
    padding: 2px 0;

    &-label {
      flex: 1;
      font-size: 12px;
    }

    &-sy {
      font-weight: 600;
    }
  }
}
</style>
