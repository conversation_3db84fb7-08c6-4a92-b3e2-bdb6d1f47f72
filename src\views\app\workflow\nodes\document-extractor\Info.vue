<template>
  <div class="field-info">
    <a-form size="small" :model="form" layout="vertical">
      <a-form-item field="variable_selector" label="输入变量">
        <a-select v-model="form.variable_selector" class="flex-1 overflow-hidden" placeholder="设置变量值">
          <template #label="{ data }">
            <span>
              开始 /
              <span style="color: rgb(var(--primary-6))">{{ data?.value }}</span>
              / String
            </span>
          </template>
          <a-option v-for="(item, index) in defaultList" :key="index" :value="item.variable">
            <div class="flex h-8 cursor-pointer items-center justify-between rounded-lg">
              <div class="flex w-0 grow items-center space-x-1">
                <AiSvgIcon name="workflow-ai-variable" />
                <div class="system-xs-regular max-w-[130px] shrink-0 truncate font-medium text-text-secondary">
                  {{ item.variable }}
                </div>
              </div>
              <div class="ml-2 flex shrink-0 items-center">
                <div class="mr-2 text-xs font-normal text-text-tertiary">
                  <span>{{ item.type }}</span>
                </div>
              </div>
            </div>
          </a-option>
        </a-select>
      </a-form-item>
    </a-form>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    node: any
  }>(),
  {
    node: {}
  }
)
const defaultList = ref([
  {
    type: 'array[file]',
    variable: 'sys.files',
    value: ['sys', 'files']
  }
])
interface FieldType {
  value_selector: string[]
  variable: string
  selector: string
}
const change = (e, item) => {
  item.value_selector = e.split('.')
}
const form = ref({
  variable_selector: ''
})
form.value = props.node
</script>
<style scoped lang="scss">
.field-info {
  display: flex;
  flex-direction: column;
  background-color: var(--color-bg-1);
}

:deep(.arco-select-option-content) {
  width: 100%;
}
</style>
