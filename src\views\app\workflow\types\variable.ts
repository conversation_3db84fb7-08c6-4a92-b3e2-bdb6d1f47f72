import type {
  InputVarType,
  SupportUploadFileTypes,
  ValueSelector,
  VarType
} from '@/views/app/workflow/types/workflow.ts'
import { BlockEnum } from '@/views/app/workflow/types/workflow'
import type { TransferMethod } from '@/views/app/workflow/types/app.ts'
export const SUPPORT_OUTPUT_VARS_NODE = [
  BlockEnum.Start,
  BlockEnum.LLM,
  BlockEnum.KnowledgeRetrieval,
  BlockEnum.Code,
  BlockEnum.TemplateTransform,
  BlockEnum.HttpRequest,
  BlockEnum.Tool,
  BlockEnum.VariableAssigner,
  BlockEnum.VariableAggregator,
  BlockEnum.QuestionClassifier,
  BlockEnum.ParameterExtractor,
  BlockEnum.Iteration,
  BlockEnum.Loop,
  BlockEnum.DocExtractor,
  BlockEnum.ListFilter,
  BlockEnum.Agent
]
export type SchemaEnumType = string[] | number[]
export type Field = {
  type: Type
  properties?: {
    // Object has properties
    [key: string]: Field
  }
  required?: string[] // Key of required properties in object
  description?: string
  items?: ArrayItems // Array has items. Define the item type
  enum?: SchemaEnumType // Enum values
  additionalProperties?: false // Required in object by api. Just set false
}
type ArrayItemType = Exclude<Type, Type.array>
export type ArrayItems = Omit<Field, 'type'> & { type: ArrayItemType }
export enum Type {
  string = 'string',
  number = 'number',
  boolean = 'boolean',
  object = 'object',
  array = 'array',
  arrayString = 'array[string]',
  arrayNumber = 'array[number]',
  arrayObject = 'array[object]'
}
export type SchemaRoot = {
  type: Type.object
  properties: Record<string, Field>
  required?: string[]
  additionalProperties: false
}
export type StructuredOutput = {
  schema: SchemaRoot
}

export type Var = {
  variable: string
  type: VarType
  children?: Var[] | StructuredOutput // if type is obj, has the children struct
  isParagraph?: boolean
  isSelect?: boolean
  options?: string[]
  required?: boolean
  des?: string
  isException?: boolean
  isLoopVariable?: boolean
  nodeId?: string
}
export type Branch = {
  id: string
  name: string
}
export enum NodeRunningStatus {
  NotStart = 'not-start',
  Waiting = 'waiting',
  Running = 'running',
  Succeeded = 'succeeded',
  Failed = 'failed',
  Exception = 'exception',
  Retry = 'retry'
}

export enum ErrorHandleTypeEnum {
  none = 'none',
  failBranch = 'fail-branch',
  defaultValue = 'default-value'
}
export type WorkflowRetryConfig = {
  max_retries: number
  retry_interval: number
  retry_enabled: boolean
}

export type DefaultValueForm = {
  key: string
  type: VarType
  value?: any
}
export type ToolDefaultValue = {
  provider_id: string
  provider_type: string
  provider_name: string
  tool_name: string
  tool_label: string
  tool_description: string
  title: string
  is_team_authorization: boolean
  params: Record<string, any>
  paramSchemas: Record<string, any>[]
  output_schema: Record<string, any>
}

export type CommonNodeType<T = object> = {
  _connectedSourceHandleIds?: string[]
  _connectedTargetHandleIds?: string[]
  _targetBranches?: Branch[]
  _isSingleRun?: boolean
  _runningStatus?: NodeRunningStatus
  _runningBranchId?: string
  _singleRunningStatus?: NodeRunningStatus
  _isCandidate?: boolean
  _isBundled?: boolean
  _children?: { nodeId: string; nodeType: BlockEnum }[]
  _isEntering?: boolean
  _showAddVariablePopup?: boolean
  _holdAddVariablePopup?: boolean
  _iterationLength?: number
  _iterationIndex?: number
  _inParallelHovering?: boolean
  _waitingRun?: boolean
  _retryIndex?: number
  isInIteration?: boolean
  iteration_id?: string
  selected?: boolean
  title: string
  desc: string
  type: BlockEnum
  width?: number
  height?: number
  _loopLength?: number
  _loopIndex?: number
  isInLoop?: boolean
  loop_id?: string
  error_strategy?: ErrorHandleTypeEnum
  retry_config?: WorkflowRetryConfig
  default_value?: DefaultValueForm[]
} & T &
  Partial<Pick<ToolDefaultValue, 'provider_id' | 'provider_type' | 'provider_name' | 'tool_name'>>
export type UploadFileSetting = {
  allowed_file_upload_methods: TransferMethod[]
  allowed_file_types: SupportUploadFileTypes[]
  allowed_file_extensions?: string[]
  max_length: number
  number_limits?: number
}

export type InputVar = {
  type: InputVarType
  label:
    | string
    | {
        nodeType: BlockEnum
        nodeName: string
        variable: string
        isChatVar?: boolean
      }
  variable: string
  max_length?: number
  default?: string
  required: boolean
  hint?: string
  options?: string[]
  value_selector?: ValueSelector
} & Partial<UploadFileSetting>
export type StartNodeType = CommonNodeType & {
  variables: InputVar[]
}
export type Variable = {
  variable: string
  label?:
    | string
    | {
        nodeType: BlockEnum
        nodeName: string
        variable: string
      }
  value_selector: ValueSelector
  variable_type?: VarType
  value?: string
  options?: string[]
  required?: boolean
  isParagraph?: boolean
}
export enum CodeLanguage {
  python3 = 'python3',
  javascript = 'javascript',
  json = 'json'
}
export type OutputVar = Record<
  string,
  {
    type: VarType
    children: null // support nest in the future,
  }
>

export type CodeNodeType = CommonNodeType & {
  variables: Variable[]
  code_language: CodeLanguage
  code: string
  outputs: OutputVar
}
export type VarGroupItem = {
  output_type: VarType
  variables: ValueSelector[]
}
export type VariableAssignerNodeType = CommonNodeType &
  VarGroupItem & {
    advanced_settings: {
      group_enabled: boolean
      groups: ({
        group_name: string
        groupId: string
      } & VarGroupItem)[]
    }
  }
export type ToolVarInputs = Record<
  string,
  {
    type: VarType
    value?: string | ValueSelector | any
  }
>

export enum CollectionType {
  all = 'all',
  builtIn = 'builtin',
  custom = 'api',
  model = 'model',
  workflow = 'workflow'
}

export type ToolNodeType = CommonNodeType & {
  provider_id: string
  provider_type: CollectionType
  provider_name: string
  tool_name: string
  tool_label: string
  tool_parameters: ToolVarInputs
  tool_configurations: Record<string, any>
  output_schema: Record<string, any>
  paramSchemas?: Record<string, any>[]
}

export type ModelConfig = {
  provider: string
  name: string
  mode: string
  completion_params: Record<string, any>
}

export enum ReasoningModeType {
  prompt = 'prompt',
  functionCall = 'function_call'
}
export enum ParamType {
  string = 'string',
  number = 'number',
  bool = 'bool',
  select = 'select',
  arrayString = 'array[string]',
  arrayNumber = 'array[number]',
  arrayObject = 'array[object]'
}

export type Param = {
  name: string
  type: ParamType
  options?: string[]
  description: string
  required?: boolean
}

export type RolePrefix = {
  user: string
  assistant: string
}

export type Memory = {
  role_prefix?: RolePrefix
  window: {
    enabled: boolean
    size: number | string | null
  }
  query_prompt_template: string
}
export enum Resolution {
  low = 'low',
  high = 'high'
}

export type VisionSetting = {
  variable_selector: ValueSelector
  detail: Resolution
}

export type ParameterExtractorNodeType = CommonNodeType & {
  model: ModelConfig
  query: ValueSelector
  reasoning_mode: ReasoningModeType
  parameters: Param[]
  instruction: string
  memory?: Memory
  vision: {
    enabled: boolean
    configs?: VisionSetting
  }
}

export enum ErrorHandleMode {
  Terminated = 'terminated',
  ContinueOnError = 'continue-on-error',
  RemoveAbnormalOutput = 'remove-abnormal-output'
}
export type IterationNodeType = CommonNodeType & {
  startNodeType?: BlockEnum
  start_node_id: string // start node id in the iteration
  iteration_id?: string
  iterator_selector: ValueSelector
  output_selector: ValueSelector
  output_type: VarType // output type.
  is_parallel: boolean // open the parallel mode or not
  parallel_nums: number // the numbers of parallel
  error_handle_mode: ErrorHandleMode // how to handle error in the iteration
  _isShowTips: boolean // when answer node in parallel mode iteration show tips
}

export enum LogicalOperator {
  and = 'and',
  or = 'or'
}
export enum ComparisonOperator {
  contains = 'contains',
  notContains = 'not contains',
  startWith = 'start with',
  endWith = 'end with',
  is = 'is',
  isNot = 'is not',
  empty = 'empty',
  notEmpty = 'not empty',
  equal = '=',
  notEqual = '≠',
  largerThan = '>',
  lessThan = '<',
  largerThanOrEqual = '≥',
  lessThanOrEqual = '≤',
  isNull = 'is null',
  isNotNull = 'is not null',
  in = 'in',
  notIn = 'not in',
  allOf = 'all of',
  exists = 'exists',
  notExists = 'not exists'
}
export type CaseItem = {
  logical_operator: LogicalOperator
  conditions: Condition[]
}
export type Condition = {
  id: string
  varType: VarType
  variable_selector?: ValueSelector
  key?: string // sub variable key
  comparison_operator?: ComparisonOperator
  value: string | string[]
  numberVarType?: VarType
  sub_variable_condition?: CaseItem
}
export enum ValueType {
  variable = 'variable',
  constant = 'constant'
}

export type LoopVariable = {
  id: string
  label: string
  var_type: VarType
  value_type: ValueType
  value: any
}
export type LoopNodeType = CommonNodeType & {
  startNodeType?: BlockEnum
  start_node_id: string
  loop_id?: string
  logical_operator?: LogicalOperator
  break_conditions?: Condition[]
  loop_count: number
  error_handle_mode: ErrorHandleMode // how to handle error in the iteration
  loop_variables?: LoopVariable[]
}
export type DocExtractorNodeType = CommonNodeType & {
  variable_selector: ValueSelector
  is_array_file: boolean
}

export enum OrderBy {
  ASC = 'asc',
  DESC = 'desc'
}

export type Limit = {
  enabled: boolean
  size?: number
}

export type ListFilterNodeType = CommonNodeType & {
  variable: ValueSelector
  var_type: VarType // Cache for the type of output variable
  item_var_type: VarType // Cache for the type of output variable
  filter_by: {
    enabled: boolean
    conditions: Condition[]
  }
  extract_by: {
    enabled: boolean
    serial?: string
  }
  order_by: {
    enabled: boolean
    key: ValueSelector | string
    value: OrderBy
  }
  limit: Limit
}

export type AgentNodeType = CommonNodeType & {
  agent_strategy_provider_name?: string
  agent_strategy_name?: string
  agent_strategy_label?: string
  agent_parameters?: ToolVarInputs
  output_schema: Record<string, any>
  plugin_unique_identifier?: string
  memory?: Memory
}

export enum AgentFeature {
  HISTORY_MESSAGES = 'history-messages'
}
