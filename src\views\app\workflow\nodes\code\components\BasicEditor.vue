<template>
  <div>
    <code-mirror
      v-model="nodeInfo.code"
      basic
      class="basic-editor"
      :style="{ width, height }"
      :indent-with-tab="true"
      :tab-size="config.tabSize"
      :extensions="extensions"
    />
  </div>
</template>
<script setup lang="ts">
import CodeMirror from 'vue-codemirror6'
// CodeMirror extensions
import { javascript } from '@codemirror/lang-javascript'
import { json } from '@codemirror/lang-json'
import { vue } from '@codemirror/lang-vue'

// 定义 props
const props = defineProps({
  readonly: {
    type: Boolean,
    default: false
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '200px'
  },
  placeholder: {
    type: String,
    default: 'SELECT * FROM log_table WHERE id > ${id}'
  },
  nodeInfo: {
    type: Object,
    default: () => {}
  }
})
const defaultConfig = {
  tabSize: 2,
  // basic: true,
  dark: true,
  readonly: true
}
const config = defaultConfig
const value = ref('Cozy lummox gives smart squid who asks for job pen.')
// const lang: LanguageSupport = md();
const extensions = ref([javascript(), json(), vue()])
</script>
<style scoped lang="scss">
.basic-editor {
  overflow: auto;
  :deep(.cm-editor) {
    height: 100%;

    .cm-scroller {
      height: 100%;
      overflow: auto;
    }
  }
  .cm-editor {
  }
}
</style>
