<template>
  <div class="flexRowStarCen info">
    <a-tag :color="tagColor">{{ props.method }}</a-tag>
    <small>{{ props.url }}</small>
  </div>
  <h2>
    {{ props.title }}
  </h2>
</template>
<script setup name="" lang="ts">
import { computed } from 'vue'
const props = withDefaults(
  defineProps<{
    url?: string
    title?: string
    method?: 'PUT' | 'DELETE' | 'GET' | 'POST'
  }>(),
  {
    url: '',
    title: '',
    method: 'GET'
  }
)
const tagColor = computed(() => {
  let color = ''
  switch (props.method) {
    case 'GET':
      color = 'green'
      break
    case 'DELETE':
      color = 'red'

      break
    case 'POST':
      color = 'blue'

      break
    case 'PUT':
      color = 'orange'
      break
  }
  return color
})
</script>
<style lang="less" scoped>
.info {
  small {
    font-size: 12px;
    color: var(--color-text-3);
    padding-left: 10px;
  }
}
</style>
