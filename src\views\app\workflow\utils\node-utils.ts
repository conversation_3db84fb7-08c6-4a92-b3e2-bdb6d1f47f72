import { useVueFlow, type Node } from '@vue-flow/core'
import { nanoid } from 'nanoid'

const layoutConfig = {
  offsetX: 80, // 水平偏移
  offsetY: 40 // 垂直偏移
}
export interface NodeElement extends Node {
  dimensions?: {
    width?: number
    height?: number
  }
}
export default function nodeUtils(flowId?: string) {
  const { nodes, edges, addNodes, addEdges } = useVueFlow(flowId || '')
  // 计算位置
  const calculateHorizontalPosition = (nodeId) => {
    // 获取当前点击的节点信息
    const currentNode = nodes.value?.filter((i) => i.id === nodeId)?.[0]
    // 判断当前节点连接数
    const currentNodeConnectLength = edges.value?.filter((i) => i.source === nodeId)?.length
    // 初始化坐标
    const currentNodePosition = {
      x: currentNode.position.x + currentNode.dimensions.width! + layoutConfig.offsetX,
      y: currentNode.position.y
    }
    if (currentNodeConnectLength === 0) {
      return {
        x: currentNodePosition.x,
        y: currentNodePosition.y
      }
    } else {
      const maxHeightNode = getMaxPositionNode(nodeId, 'y') as NodeElement
      return {
        x: maxHeightNode?.position.x,
        y: maxHeightNode?.position.y! + maxHeightNode?.dimensions?.height! + layoutConfig.offsetY
      }
    }
  }

  // 当源节点关联多个节点时，获取positionY最大的节点
  const getMaxPositionNode = (nodeId, position?: string) => {
    // 获取关联边
    const connectEdges = edges.value.filter((edge) => edge.source === nodeId)
    // 获取关联节点
    const connectNodes = connectEdges.map((edge) => nodes.value.find((node) => node.id === edge.target))
    return connectNodes.sort(
      (perv, next) => (next?.position[position!] as never) - (perv?.position[position!] as never)
    )?.[0]
  }

  // 新增节点
  const addNodeToflow = async (nodeProps, newNode, sourceHandle?: string) => {
    // 计算坐标
    const { x, y } = await calculateHorizontalPosition(nodeProps.id)
    // 生成id
    const generateId = `node_${nanoid()}`
    const node = {
      id: generateId,
      type: newNode.type as string,
      position: {
        x,
        y
      },
      data: newNode
    }
    // 当为子组件时，需要添加以下属性
    if (nodeProps?.parentNodeId) {
      node['parentNode'] = nodeProps.parentNodeId
      node['extent'] = 'parent'
      node['expandParent'] = true
    }
    await addNodes(node)
    const generateEdgeId = `edge${nanoid()}`
    const edge = {
      id: generateEdgeId,
      source: nodeProps.id,
      target: generateId,
      type: 'custom',
      sourceHandle: sourceHandle || undefined
    }
    addEdges([edge])
    return {
      newNodeProps: node
    }
  }
  
  //工具栏新增节点
  const newaddNodeToflow = async (nodeProps, newNode, sourceHandle?: string) => {
    // 如果没有源节点，则使用默认起始位置
    let x = 100
    let y = 100
  
    if (nodeProps?.id) {
      const { x: sourceX, y: sourceY } = await calculateHorizontalPosition(nodeProps.id)
      x = sourceX
      y = sourceY
    }
  
    const generateId = `node_${nanoid()}`
    const node = {
      id: generateId,
      type: newNode.type as string,
      position: { x, y },
      data: newNode
    }
  
    // 当为子组件时，需要添加以下属性
    if (nodeProps?.parentNodeId) {
      node['parentNode'] = nodeProps.parentNodeId
      node['extent'] = 'parent'
      node['expandParent'] = true
    }
  
    await addNodes(node)
  
    if (nodeProps?.id) {
      const generateEdgeId = `edge${nanoid()}`
      const edge = {
        id: generateEdgeId,
        source: nodeProps.id,
        target: generateId,
        type: 'custom',
        sourceHandle: sourceHandle || undefined
      }
      addEdges([edge])
    }
  
    return {
      newNodeProps: node
    }
  }
  return {
    addNodeToflow,newaddNodeToflow
  }
}
