<template>
  <div class="setting-card">
    <div class="setting-card-header">
      <div class="setting-title">检索设置</div>
      <span style="font-size: 10px">
        <a-link style="font-size: 10px" href="link">了解更多</a-link>
        关于检索方法，您可以随时在知识库设置中更改此设置。
      </span>
    </div>
    <div class="retrieval-settings-container">
      <!-- 向量检索 -->
      <div class="retrieval-option" :class="{ active: searchMethod === 'vector' }">
        <div class="option-header" @click="searchMethod = 'vector'">
          <div class="option-icon">
            <icon-apps class="icon" />
          </div>
          <div class="option-info">
            <div class="option-title">向量检索</div>
            <div class="option-desc">通过生成查询嵌入并查询与其向量表示最相似的文本分段</div>
          </div>
          <a-radio :model-value="searchMethod === 'vector'" />
        </div>
        <div v-show="searchMethod === 'vector'" class="option-content">
          <div class="content-section">
            <div class="rerank-settings">
              <div class="rerank-header">
                <a-checkbox v-model="vectorSettings.useRerank">Rerank 模型</a-checkbox>
                <a-tooltip position="top">
                  <template #content>
                    <div class="tooltip-content">
                      重排序模型将根据候选文档列表与用户问题语义匹配度进行重新排序，从而改进语义排序的结果
                    </div>
                  </template>
                  <icon-question-circle class="hint-icon" />
                </a-tooltip>
              </div>
              <div>
                <a-select v-model="vectorSettings.rerankModel" class="model-select" placeholder="选择 Rerank 模型">
                  <a-option v-for="model in rerankModels" :key="model.model" :value="model.model">
                    {{ model.label.zh_Hans }}
                  </a-option>
                </a-select>
                <div class="params-config">
                  <div class="param-item">
                    <div class="param-label">
                      Top K
                      <a-tooltip position="top">
                        <template #content>
                          <div class="tooltip-content">
                            用于筛选与用户问题相似度最高的文本片段。系统同时会根据选用模型上下文窗口大小动态调整分段数量。
                          </div>
                        </template>
                        <icon-question-circle class="hint-icon" />
                      </a-tooltip>
                    </div>
                    <a-input-number v-model="vectorSettings.topK" :min="1" :max="20" style="width: 80px" />
                  </div>
                  <div class="param-item">
                    <div class="param-label1">
                      <a-checkbox v-model="vectorSettings.useScoreThreshold">Score 阈值</a-checkbox>
                      <a-tooltip position="top">
                        <template #content>
                          <div class="tooltip-content">用于设置文本片段筛选的相似度阈值。</div>
                        </template>
                        <icon-question-circle class="hint-icon" style="font-size: 16px" />
                      </a-tooltip>
                    </div>
                  </div>
                  <div class="param-item">
                    <a-input-number
                      v-model="vectorSettings.scoreThreshold"
                      :min="0"
                      :max="1"
                      :step="0.1"
                      style="width: 80px"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 全文检索 -->
      <div class="retrieval-option" :class="{ active: searchMethod === 'fulltext' }">
        <div class="option-header" @click="searchMethod = 'fulltext'">
          <div class="option-icon">
            <icon-file class="icon" />
          </div>
          <div class="option-info">
            <div class="option-title">全文检索</div>
            <div class="option-desc">索引文档中的所有词汇，从而允许用户查询任意词汇，并返回包含这些词汇的文本片段</div>
          </div>
          <a-radio :model-value="searchMethod === 'fulltext'" />
        </div>
        <div v-show="searchMethod === 'fulltext'" class="option-content">
          <div class="content-section">
            <div class="rerank-settings">
              <div class="rerank-header">
                <a-checkbox v-model="fullTextSettings.useRerank">Rerank 模型</a-checkbox>
                <a-tooltip position="top">
                  <template #content>
                    <div class="tooltip-content">
                      重排序模型将根据候选文档列表与用户问题语义匹配度进行重新排序，从而改进语义排序的结果
                    </div>
                  </template>
                  <icon-question-circle class="hint-icon" />
                </a-tooltip>
              </div>
              <div v-if="fullTextSettings.useRerank">
                <a-select v-model="fullTextSettings.rerankModel" class="model-select" placeholder="选择 Rerank 模型">
                  <a-option v-for="model in rerankModels" :key="model.model" :value="model.model">
                    {{ model.label.zh_Hans }}
                  </a-option>
                </a-select>
                <div class="params-config">
                  <div class="param-item">
                    <div class="param-label">
                      Top K
                      <a-tooltip position="top">
                        <template #content>
                          <div class="tooltip-content">
                            用于筛选与用户问题相似度最高的文本片段。系统同时会根据选用模型上下文窗口大小动态调整分段数量。
                          </div>
                        </template>
                        <icon-question-circle class="hint-icon" />
                      </a-tooltip>
                    </div>
                    <a-input-number v-model="fullTextSettings.topK" :min="1" :max="20" style="width: 80px" />
                  </div>
                  <div class="param-item">
                    <div class="param-label1">
                      <a-checkbox v-model="fullTextSettings.useScoreThreshold">Score 阈值</a-checkbox>
                      <a-tooltip position="top">
                        <template #content>
                          <div class="tooltip-content">用于设置文本片段筛选的相似度阈值。</div>
                        </template>
                        <icon-question-circle class="hint-icon" style="font-size: 16px" />
                      </a-tooltip>
                    </div>
                  </div>
                  <div class="param-item">
                    <a-input-number
                      v-model="fullTextSettings.scoreThreshold"
                      :min="0"
                      :max="1"
                      :step="0.1"
                      style="width: 80px"
                      :disabled="!fullTextSettings.useScoreThreshold"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 混合检索 -->
      <div class="retrieval-option" :class="{ active: searchMethod === 'hybrid' }">
        <div class="option-header" @click="searchMethod = 'hybrid'">
          <div class="option-icon">
            <icon-relation class="icon" />
          </div>
          <div class="option-info">
            <div class="option-title">
              混合检索
              <a-tag size="small" color="arcoblue">推荐</a-tag>
            </div>
            <div class="option-desc">
              同时执行全文检索和向量检索，并应用重排序步骤，从两类查询结果中选择匹配用户问题的最佳结果，用户可以选择设置权重或配置重新排序模型。
            </div>
          </div>
          <a-radio :model-value="searchMethod === 'hybrid'" />
        </div>
        <div v-show="searchMethod === 'hybrid'" class="option-content">
          <div class="content-section">
            <div class="hybrid-modes">
              <div
                class="hybrid-mode-option"
                :class="{ selected: hybridSettings.mode === 'weight' }"
                @click="hybridSettings.mode = 'weight'"
              >
                <div class="mode-icon">
                  <icon-arrow-rise />
                </div>
                <div class="mode-info">
                  <div class="mode-name">权重设置</div>
                  <div class="mode-desc">通过调整分配的权重，重新排序策略确定是优先进行语义匹配还是关键字匹配</div>
                </div>
                <a-radio :model-value="hybridSettings.mode === 'weight'" />
              </div>
              <div
                class="hybrid-mode-option"
                :class="{ selected: hybridSettings.mode === 'rerank' }"
                @click="hybridSettings.mode = 'rerank'"
              >
                <div class="mode-icon">
                  <icon-sort />
                </div>
                <div class="mode-info">
                  <div class="mode-name">Rerank 模型</div>
                  <div class="mode-desc">
                    重排序模型将根据候选文本列表与用户问题的语义匹配度进行排序，从而改进语义排序的结果
                  </div>
                </div>
                <a-radio :model-value="hybridSettings.mode === 'rerank'" />
              </div>
            </div>
            <div v-if="hybridSettings.mode === 'weight'" class="weight-settings">
              <div class="weight-setting-item">
                <span class="weight-value">语义: {{ hybridSettings.vectorWeight }}</span>
                <a-slider v-model="hybridSettings.vectorWeight" :step="0.1" :min="0" :max="1" />
                <span class="weight-note">关键词: {{ (1 - hybridSettings.vectorWeight).toFixed(1) }}</span>
              </div>
            </div>
            <div class="rerank-settings">
              <a-select
                v-if="hybridSettings.mode === 'rerank'"
                v-model="hybridSettings.rerankModel"
                class="model-select"
              >
                <a-option v-for="model in rerankModels" :key="model.model" :value="model.model">
                  {{ model.label.zh_Hans }}
                </a-option>
              </a-select>
              <div class="params-config">
                <div class="param-item">
                  <div class="param-label">
                    Top K
                    <a-tooltip position="top">
                      <template #content>
                        <div class="tooltip-content">
                          用于筛选与用户问题相似度最高的文本片段。系统同时会根据选用模型上下文窗口大小动态调整分段数量。
                        </div>
                      </template>
                      <icon-question-circle class="hint-icon" />
                    </a-tooltip>
                  </div>
                  <a-input-number v-model="hybridSettings.topK" :min="1" :max="20" style="width: 80px" />
                </div>
                <div class="param-item">
                  <div class="param-label1">
                    <a-checkbox v-model="hybridSettings.useScoreThreshold">Score 阈值</a-checkbox>
                    <a-tooltip position="top">
                      <template #content>
                        <div class="tooltip-content">用于设置文本片段筛选的相似度阈值。</div>
                      </template>
                      <icon-question-circle class="hint-icon" style="font-size: 16px" />
                    </a-tooltip>
                  </div>
                </div>
                <div class="param-item">
                  <a-input-number
                    v-model="hybridSettings.scoreThreshold"
                    :min="0"
                    :max="1"
                    :step="0.1"
                    style="width: 80px"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'

// 定义RerankModel接口
interface RerankModel {
  model: string
  label: {
    zh_Hans: string
    en_US: string
  }
  model_type: string
  features: any
  fetch_from: string
  model_properties: {
    context_size: number
  }
  deprecated: boolean
  status: string
  load_balancing_enabled: boolean
}

// 定义设置接口
interface VectorSettings {
  useRerank: boolean
  rerankModel: string
  topK: number
  useScoreThreshold: boolean
  scoreThreshold: number
}

interface FullTextSettings {
  useRerank: boolean
  rerankModel: string
  topK: number
  useScoreThreshold: boolean
  scoreThreshold: number
}

interface HybridSettings {
  mode: 'weight' | 'rerank'
  vectorWeight: number
  rerankModel: string
  topK: number
  useScoreThreshold: boolean
  scoreThreshold: number
}

// 接收props
const props = defineProps({
  searchMethod: {
    type: String,
    default: 'hybrid'
  },
  vectorSettings: {
    type: Object as () => VectorSettings,
    default: () => ({
      useRerank: true,
      rerankModel: 'netease-youdao/bce-reranker-base_v1',
      topK: 3,
      useScoreThreshold: false,
      scoreThreshold: 0.5
    })
  },
  fullTextSettings: {
    type: Object as () => FullTextSettings,
    default: () => ({
      useRerank: true,
      rerankModel: 'netease-youdao/bce-reranker-base_v1',
      topK: 3,
      useScoreThreshold: false,
      scoreThreshold: 0.5
    })
  },
  hybridSettings: {
    type: Object as () => HybridSettings,
    default: () => ({
      mode: 'rerank',
      vectorWeight: 0.5,
      rerankModel: 'netease-youdao/bce-reranker-base_v1',
      topK: 3,
      useScoreThreshold: false,
      scoreThreshold: 0.5
    })
  },
  rerankModels: {
    type: Array as () => RerankModel[],
    default: () => []
  }
})

// 定义事件
const emit = defineEmits([
  'search-method-change',
  'vector-settings-change',
  'full-text-settings-change',
  'hybrid-settings-change'
])

// 本地响应式变量 - 使用props的初始值，但不再监听props变化
const searchMethod = ref(props.searchMethod)
const vectorSettings = ref({ ...props.vectorSettings })
const fullTextSettings = ref({ ...props.fullTextSettings })
const hybridSettings = ref({ ...props.hybridSettings })

// 本地Rerank模型列表 - 需要响应props变化以获取真实数据
const rerankModels = ref<RerankModel[]>([...props.rerankModels])

// 初始化标志，确保只在组件初始化时同步一次props
const isInitialized = ref(false)

// 监听rerankModels props的变化，因为这是从API异步获取的数据
watch(
  () => props.rerankModels,
  (newModels) => {
    console.log('收到新的Rerank模型数据:', newModels)
    rerankModels.value = [...newModels]
  },
  { deep: true, immediate: true }
)

// 只在组件挂载时同步一次props，之后完全由子组件自己管理状态
onMounted(() => {
  if (!isInitialized.value) {
    searchMethod.value = props.searchMethod
    vectorSettings.value = { ...props.vectorSettings }
    fullTextSettings.value = { ...props.fullTextSettings }
    hybridSettings.value = { ...props.hybridSettings }
    rerankModels.value = [...props.rerankModels]
    isInitialized.value = true
  }
})

// 监听本地变量变化并触发事件 - 移除防递归逻辑，因为不再有双向绑定
watch(searchMethod, (newValue) => {
  if (isInitialized.value) {
    emit('search-method-change', newValue)
  }
})

watch(
  vectorSettings,
  (newValue) => {
    if (isInitialized.value) {
      emit('vector-settings-change', newValue)
    }
  },
  { deep: true }
)

watch(
  fullTextSettings,
  (newValue) => {
    if (isInitialized.value) {
      emit('full-text-settings-change', newValue)
    }
  },
  { deep: true }
)

watch(
  hybridSettings,
  (newValue) => {
    if (isInitialized.value) {
      emit('hybrid-settings-change', newValue)
    }
  },
  { deep: true }
)
</script>
