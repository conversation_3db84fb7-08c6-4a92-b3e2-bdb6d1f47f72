import { computed } from 'vue'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import { VarType } from '../types/workflow'

export interface AvailableVar {
  nodeId: string
  nodeName: string
  variable: string
  type: VarType
  description?: string
}

export function useAvailableVars(currentNodeId: string) {
  const nodesStore = useNodesStore()

  // 获取可用变量
  const availableVars = computed<AvailableVar[]>(() => {
    const vars: AvailableVar[] = []
    const nodes = nodesStore.nodes

    // 遍历所有节点，收集可用变量
    nodes.forEach((node) => {
      // 跳过当前节点和当前节点之后的节点
      if (node.id === currentNodeId) return

      // 根据节点类型添加相应的输出变量
      switch (node.data.type) {
        case 'start':
          // 开始节点的变量
          if (node.data.variables && Array.isArray(node.data.variables)) {
            node.data.variables.forEach((variable: any) => {
              vars.push({
                nodeId: node.id,
                nodeName: node.data.title || '开始',
                variable: variable.variable,
                type: getVarTypeFromFieldType(variable.type),
                description: variable.label
              })
            })
          }

          // 系统变量
          vars.push(
            {
              nodeId: 'sys',
              nodeName: '系统',
              variable: 'user_id',
              type: VarType.string,
              description: '用户ID'
            },
            {
              nodeId: 'sys',
              nodeName: '系统',
              variable: 'conversation_id',
              type: VarType.string,
              description: '对话ID'
            },
            {
              nodeId: 'sys',
              nodeName: '系统',
              variable: 'files',
              type: VarType.arrayFile,
              description: '上传文件'
            }
          )
          break

        case 'llm':
          // 大模型节点输出
          vars.push({
            nodeId: node.id,
            nodeName: node.data.title || '大模型',
            variable: 'text',
            type: VarType.string,
            description: '生成的文本'
          })
          break

        case 'knowledge-retrieval':
          // 知识检索节点输出
          vars.push(
            {
              nodeId: node.id,
              nodeName: node.data.title || '知识检索',
              variable: 'result',
              type: VarType.string,
              description: '检索结果'
            },
            {
              nodeId: node.id,
              nodeName: node.data.title || '知识检索',
              variable: 'metadata',
              type: VarType.object,
              description: '元数据'
            }
          )
          break

        case 'code':
          // 代码执行节点输出
          vars.push({
            nodeId: node.id,
            nodeName: node.data.title || '代码执行',
            variable: 'result',
            type: VarType.string,
            description: '执行结果'
          })
          break

        case 'http-request':
          // HTTP请求节点输出
          vars.push(
            {
              nodeId: node.id,
              nodeName: node.data.title || 'HTTP请求',
              variable: 'body',
              type: VarType.object,
              description: '响应体'
            },
            {
              nodeId: node.id,
              nodeName: node.data.title || 'HTTP请求',
              variable: 'status_code',
              type: VarType.number,
              description: '状态码'
            },
            {
              nodeId: node.id,
              nodeName: node.data.title || 'HTTP请求',
              variable: 'headers',
              type: VarType.object,
              description: '响应头'
            }
          )
          break

        case 'if-else':
          // 条件分支节点输出
          vars.push({
            nodeId: node.id,
            nodeName: node.data.title || '条件分支',
            variable: 'result',
            type: VarType.boolean,
            description: '条件结果'
          })
          break

        case 'variable-aggregator':
          // 变量聚合节点输出
          vars.push({
            nodeId: node.id,
            nodeName: node.data.title || '变量聚合',
            variable: 'output',
            type: VarType.object,
            description: '聚合结果'
          })
          break

        case 'parameter-extractor':
          // 参数提取节点输出
          if (node.data.parameters && Array.isArray(node.data.parameters)) {
            node.data.parameters.forEach((param: any) => {
              vars.push({
                nodeId: node.id,
                nodeName: node.data.title || '参数提取',
                variable: param.name,
                type: getVarTypeFromFieldType(param.type),
                description: param.description
              })
            })
          }
          break

        default:
          // 其他节点的通用输出
          vars.push({
            nodeId: node.id,
            nodeName: node.data.title || node.data.type,
            variable: 'output',
            type: VarType.string,
            description: '节点输出'
          })
          break
      }
    })

    return vars
  })

  // 按节点分组的变量
  const availableVarsByNode = computed(() => {
    const grouped: Record<string, AvailableVar[]> = {}

    availableVars.value.forEach((variable) => {
      if (!grouped[variable.nodeId]) {
        grouped[variable.nodeId] = []
      }
      grouped[variable.nodeId].push(variable)
    })

    return grouped
  })

  return {
    availableVars,
    availableVarsByNode
  }
}

// 将字段类型转换为变量类型
function getVarTypeFromFieldType(fieldType: string): VarType {
  switch (fieldType) {
    case 'text-input':
    case 'paragraph':
      return VarType.string
    case 'number':
      return VarType.number
    case 'select':
      return VarType.string
    case 'file':
      return VarType.file
    case 'file-list':
      return VarType.arrayFile
    default:
      return VarType.string
  }
}
