import type { UploadInstance } from '@arco-design/web-vue'

export interface UploadProps {
  fileList?: UploadInstance['$props']['fileList']
  defaultFileList?: UploadInstance['$props']['defaultFileList']
  accept?: UploadInstance['$props']['accept']
  action?: UploadInstance['$props']['action']
  disabled?: UploadInstance['$props']['disabled']
  multiple?: UploadInstance['$props']['multiple']
  directory?: UploadInstance['$props']['directory']
  draggable?: UploadInstance['$props']['draggable']
  tip?: UploadInstance['$props']['tip']
  headers?: UploadInstance['$props']['headers']
  data?: UploadInstance['$props']['data']
  name?: UploadInstance['$props']['name']
  withCredentials?: UploadInstance['$props']['withCredentials']
  customRequest?: UploadInstance['$props']['customRequest']
  limit?: UploadInstance['$props']['limit']
  autoUpload?: UploadInstance['$props']['autoUpload']
  showFileList?: UploadInstance['$props']['showFileList']
  showRemoveButton?: UploadInstance['$props']['showRemoveButton']
  showRetryButton?: UploadInstance['$props']['showRetryButton']
  showCancelButton?: UploadInstance['$props']['showCancelButton']
  showUploadButton?: UploadInstance['$props']['showUploadButton']
  showPreviewButton?: UploadInstance['$props']['showPreviewButton']
  download?: UploadInstance['$props']['download']
  showLink?: UploadInstance['$props']['showLink']
  imageLoading?: UploadInstance['$props']['imageLoading']
  listType?: UploadInstance['$props']['listType']
  responseUrlKey?: UploadInstance['$props']['responseUrlKey']
  customIcon?: UploadInstance['$props']['customIcon']
  imagePreview?: UploadInstance['$props']['imagePreview']
  onBeforeUpload?: UploadInstance['$props']['onBeforeUpload']
  onBeforeRemove?: UploadInstance['$props']['onBeforeRemove']
  onButtonClick?: UploadInstance['$props']['onButtonClick']
}
