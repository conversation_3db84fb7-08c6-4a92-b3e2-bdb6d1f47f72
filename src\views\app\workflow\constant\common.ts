import { ArrayType, Type } from '@/views/app/workflow/nodes/code/types'
import { type Var, VarType } from '@/views/app/workflow/types/workflow'

export const DOMAIN_NAME = import.meta.env.VITE_API_BASE_URL

export const TYPE_OPTIONS = [
  { value: Type.string, text: 'string' },
  { value: Type.number, text: 'number' },
  // { value: Type.boolean, text: 'boolean' },
  { value: Type.object, text: 'object' },
  { value: ArrayType.string, text: 'array[string]' },
  { value: ArrayType.number, text: 'array[number]' },
  // { value: ArrayType.boolean, text: 'array[boolean]' },
  { value: ArrayType.object, text: 'array[object]' }
]

export const MAXIMUM_DEPTH_TYPE_OPTIONS = [
  { value: Type.string, text: 'string' },
  { value: Type.number, text: 'number' },
  // { value: Type.boolean, text: 'boolean' },
  { value: ArrayType.string, text: 'array[string]' },
  { value: ArrayType.number, text: 'array[number]' }
  // { value: ArrayType.boolean, text: 'array[boolean]' },
]
export const LLM_OUTPUT_STRUCT: Var[] = [
  {
    variable: 'text',
    type: VarType.string
  }
]
export const KNOWLEDGE_RETRIEVAL_OUTPUT_STRUCT: Var[] = [
  {
    variable: 'result',
    type: VarType.arrayObject
  }
]

export const TEMPLATE_TRANSFORM_OUTPUT_STRUCT: Var[] = [
  {
    variable: 'output',
    type: VarType.string
  }
]

export const QUESTION_CLASSIFIER_OUTPUT_STRUCT = [
  {
    variable: 'class_name',
    type: VarType.string
  }
]
export const HTTP_REQUEST_OUTPUT_STRUCT: Var[] = [
  {
    variable: 'body',
    type: VarType.string
  },
  {
    variable: 'status_code',
    type: VarType.number
  },
  {
    variable: 'headers',
    type: VarType.object
  },
  {
    variable: 'files',
    type: VarType.arrayFile
  }
]

export const TOOL_OUTPUT_STRUCT: Var[] = [
  {
    variable: 'text',
    type: VarType.string
  },
  {
    variable: 'files',
    type: VarType.arrayFile
  },
  {
    variable: 'json',
    type: VarType.arrayObject
  }
]
export const PARAMETER_EXTRACTOR_COMMON_STRUCT: Var[] = [
  {
    variable: '__is_success',
    type: VarType.number
  },
  {
    variable: '__reason',
    type: VarType.string
  }
]
export const SUB_VARIABLES = ['type', 'size', 'name', 'url', 'extension', 'mime_type', 'transfer_method', 'related_id']
export const OUTPUT_FILE_SUB_VARIABLES = SUB_VARIABLES.filter((key) => key !== 'transfer_method')
export enum AuthorizationType {
  none = 'no-auth',
  apiKey = 'api-key'
}
export enum APIType {
  basic = 'basic',
  bearer = 'bearer',
  custom = 'custom'
}
