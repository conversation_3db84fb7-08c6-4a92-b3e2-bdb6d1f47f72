<template>
  <div id="parentNode" ref="inputRef" class="select-var">
    <!--单选变量-->
    <a-popover
      position="left"
      trigger="click"
      class="ks-popover"
      content-class="ks-popover2"
      popup-container="#parentNode"
    >
      <!-- @click="showPop(item)"-->
      <div class="var-item-value cursor-pointer">
        <!--变量名字-->
        <template v-if="getName">
          <div class="flex justify-between items-center cursor-pointer">
            <a-tag>{{ getName }}</a-tag>
            <icon-close @click="handleClearValue(varItem, $event)" />
          </div>
        </template>
        <template v-else>
          <span class="text-gray-500">{{ placeholder }}</span>
        </template>
      </div>
      <template #content>
        <!--下拉弹框-->
        <!--<div class="var-list-container">
          <div class="w-full" v-for="(groupItem,groupIndex) in varList" :key="groupIndex">
            <a-typography-text> {{ groupItem.title }}</a-typography-text>
            <div class="pop-var-item-container">
              <a-space direction="vertical" fill>
                &lt;!&ndash;hover:bg-state-base-hover&ndash;&gt;
                <div
                  class="pop-var-item flex justify-between pr-[18px] relative h-6 w-full cursor-pointer items-center rounded-md pl-3"
                  v-for="(option,optionIndex) in groupItem.vars" :key="optionIndex"
                  @click="selectValue(groupItem, option)">
                  <span>{{ option.variable }}</span>
                  <span>{{ option.type }}</span>
                </div>
              </a-space>
            </div>
          </div>
        </div>-->

        <VarList @handleSelectVar="handleSelectVar" />
      </template>
    </a-popover>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import VarList from '@/views/app/workflow/nodes/http/components/rich-text/VarList.vue'

const props = withDefaults(
  defineProps<{
    nodeInfo?: any
    varItem?: any
    varCode?: string // 变量的code可能是多种的。
    placeholder?: string //
  }>(),
  {
    nodeInfo: () => ({}),
    varItem: () => ({}),
    varCode: 'value_selector', // llm:variable_selector,http:value_selector
    placeholder: '设置变量' //
  }
)

// 不清楚是否会兼容其他组件使用，后续可以导出值，自己更新？？
const emits = defineEmits(['change'])
// 获取变量的名字:名字要加上分组的数据。因为获取到的数据，没有做处理，所以在这里做一下 处理吧。（todo可以在获取数据后，处理后再渲染）
const getName = computed(() => {
  const varItem = props.varItem
  let name = ''
  if (varItem[props.varCode]) {
    const value = varItem[props.varCode][0] == 'sys' ? varItem[props.varCode].join('.') : varItem[props.varCode][1]
    const groupCategory =
      varItem[props.varCode][0] == 'sys'
        ? varList.value.filter((v) => v.isStartNode)
        : varList.value.filter((v) => v.nodeId === varItem[props.varCode][0])

    if (groupCategory.length > 0) {
      groupCategory[0].vars.forEach((item) => {
        if (item.variable == value) {
          name = groupCategory[0].title + '/' + item.variable
        }
      })
    }
  }
  return name
})

// 变量下拉
const varList = ref<any[]>([])
const nodesStore = useNodesStore()
onMounted(() => {
  varList.value = nodesStore.parentNodesVarList
})

// 选择变量后，更新选中的值
const handleSelectVar = (groupItem, option) => {
  console.log('选择：', groupItem, option)
  // return false

  // 处理sys和非sys的变量：TODO-todo：结构怎么处理会更好？
  let groupId = ''
  let varId = ''
  if (option.variable.startsWith('sys.')) {
    groupId = 'sys'
    varId = option.variable.replace('sys.', '')
  } else {
    groupId = groupItem.nodeId
    varId = option.variable
  }
  // props.varItem[props.varCode] = [groupItem.nodeId, option.variable]
  // props.varItem[props.varCode] = [groupId, varId]
  emits('change', [groupId, varId])
}

// 删除变量的值
const handleClearValue = (varItem, e) => {
  e.stopPropagation()
  // varItem[props.varCode] = []
  emits('change', [])
}

const inputRef = ref()
</script>
<style scoped lang="scss">
.var-item-value {
  border: 1px solid var(--color-border-3);
  height: 36px;
  //line-height: 32px;
  border-radius: 4px;
  padding: 5px 12px;
}
</style>
