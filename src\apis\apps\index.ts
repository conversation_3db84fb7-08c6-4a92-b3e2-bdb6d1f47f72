import type * as T from './type'
import http from '@/utils/http'

export type * from './type'

const BASE_URL = 'console/api'

/** @desc 应用列表 */
export function getAppList(params: Record<string, any>) {
  return http.get<T.AppListResponse>(`${BASE_URL}/apps`, params)
}
/** @desc 新建应用 */
export function createApp(params: T.createAppType) {
  return http.post<T.AppDetailResponse>(`${BASE_URL}/apps`, params)
}
/** @desc 删除应用 */
export function deleteApp(id: Record<string, any>) {
  return http.del(`${BASE_URL}/apps/${id}`)
}
/** @desc 导出应用 */
export function exportAppConfig({ id, include = false }) {
  return http.get<T.ExportApp>(`${BASE_URL}/apps/${id}/export?include_secret=${include}`)
}

/** @desc 复制应用 */
export function copyApp(params: T.CopyAppResp) {
  return http.post(`${BASE_URL}/apps/${params.id}/copy`, params)
}

/** @desc 标签列表 type=app */
export function getTags(params: Record<string, any>) {
  return http.get<T.TagsResponse>(`${BASE_URL}/tags`, params)
}
/** @desc 删除标签 type=app */
export function removeTags(params: Record<string, any>) {
  return http.post<T.TagsResponse>(`${BASE_URL}/tag-bindings/remove`, params)
}
/** @desc 应用详情 */
export function getAppConfig({ appId }) {
  return http.get<T.AppConfigResponse>(`${BASE_URL}/apps/${appId}`)
}
/** @desc 获取密钥列表 */
export function getApiKeys(appId: string) {
  return http.get<T.ApiKeyResponse>(`${BASE_URL}/apps/${appId}/api-keys`)
}

/** @desc 创建密钥 */
export function createApiKey(appId: string, data: { name?: string }) {
  return http.post(`${BASE_URL}/apps/${appId}/api-keys`, data)
}
/** @desc 删除密钥 */
export function deleteApiKey(appId: string, keyId: string) {
  return http.del(`${BASE_URL}/apps/${appId}/api-keys/${keyId}`)
}

/** @desc 获取ApiBaseUrl */
export function getAppApiBaseUrl(appId: string) {
  return http.get(`${BASE_URL}/apps/${appId}/api-base-url`)
}

/** @desc 获取对话流/智能会话日志列表 */
export function getChatConversations(appId: string, params?: T.LogQueryParams) {
  return http.get<T.LogListResponse>(`${BASE_URL}/apps/${appId}/chat-conversations`, params)
}

/** @desc 获取工作流日志列表 */
export function getWorkflowConversations(appId: string, params?: T.LogQueryParams) {
  return http.get<T.LogListResponse>(`${BASE_URL}/apps/${appId}/workflow-app-logs`, params)
}

/** @desc 获取对话消息列表 */
export function getChatMessages(appId: string, params?: T.ChatMessageParams) {
  return http.get<T.ChatMessageResponse>(`${BASE_URL}/apps/${appId}/chat-messages`, params)
}

/** @desc 获取日志详情 */
export function getLogDetail(appId: string, logId: string) {
  return http.get<T.LogDetailResponse>(`${BASE_URL}/apps/${appId}/logs/${logId}`)
}

/** @desc 更新日志反馈 */
export function updateLogFeedback(appId: string, messageId: string, feedback: T.LogFeedback) {
  return http.post(`${BASE_URL}/apps/${appId}/feedbacks`, { message_id: messageId, ...feedback })
}

/** @desc 更新日志注释 */
export function updateLogAnnotation(appId: string, messageId: string, content: string) {
  return http.post(`${BASE_URL}/apps/${appId}/annotations`, { message_id: messageId, content })
}
