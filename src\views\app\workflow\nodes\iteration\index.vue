<template>
  <div :style="{ width: data.width, height: '100%' }">
    <NodeResizer
      :min-width="400"
      :min-height="300"
      handleClassName="custom-node-resizer-handle"
      lineClassName="custom-node-resizer-line"
    />
    <div class="custom-node">
      <div class="truncate flex items-center h-[46px]">
        <div class="custom-node-icon" :style="{ backgroundColor: '#06aed4' }">
          <AiSvgIcon style="width: 18px; height: 18px" :name="`workflow-${type}`" />
        </div>
        <div class="custom-node-text overflow-ellipsis overflow-hidden whitespace-nowrap flex-1">
          {{ data.title }}
        </div>
        <Handle id="target" type="target" :position="Position.Left" />
        <NodeList class="custom-node-add" :popoverInstance="true" :nodeId="props.id" :nodeProps="props">
          <Handle id="source" type="source" class="custom-node-handle" :position="Position.Right">
            <icon-plus :style="{ pointerEvents: 'none' }" />
          </Handle>
        </NodeList>
      </div>
    </div>
    <div class="vue-flow-children">
      <svg
        class="vue-flow__background !z-0 rounded-2xl"
        data-testid="rf__background"
        style="width: 100%; height: 100%; background: #f5f7fd"
      >
        <pattern
          id="pattern-1iteration-background-1746807947440"
          x="-8.68507888546128"
          y="-12.316692813859333"
          width="14"
          height="14"
          patternUnits="userSpaceOnUse"
          patternTransform="translate(-1,-1)"
        >
          <circle cx="1" cy="1" r="1" fill="#eaeaea" />
        </pattern>
        <rect x="0" y="0" width="100%" height="100%" fill="url(#pattern-1iteration-background-1746807947440)" />
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Handle, Position, useVueFlow, type NodeProps } from '@vue-flow/core'
import { NodeResizer } from '@vue-flow/node-resizer'
import '@vue-flow/node-resizer/dist/style.css'
import NodeList from '../node-list.vue'

const props = defineProps<NodeProps>()
const { nodes, edges } = useVueFlow()
</script>

<style scoped lang="scss">
:deep(.vue-flow__handle-left) {
  top: auto;
}

.custom-node {
  min-width: 240px;
  padding: 4px 14px;
  background-color: var(--color-bg-1);
  border-radius: 12px;

  &-icon {
    margin-right: 8px;
    height: 24px;
    width: 24px;
    border-radius: 8px;
    background-color: var(--color-fill-3);
    text-align: center;
    line-height: 24px;
    color: var(--color-text-1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-text {
    font-size: 18px;
    font-weight: 500;
    color: var(--color-text-2);
  }

  &-handle {
    background: rgb(var(--primary-6));
    height: 10px;
    width: 2px;
    border-radius: 0;
    border: none;
    min-width: 2px;

    .arco-icon {
      display: none;
    }
  }

  &:hover {
    .custom-node-handle-right {
      background-color: rgb(var(--primary-6));
      border-radius: 50%;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
      cursor: pointer;
    }

    .arco-icon {
      display: inline-block;
      width: 14px;
      height: 14px;
      color: var(--color-white);
    }
  }

  &-add {
    position: absolute;
    right: 0;
    pointer-events: none;
  }
}

.vue-flow-children {
  width: 100%;
  height: calc(100% - 60px);
  padding: 0 14px;
}
</style>
<style lang="scss">
.vue-flow__node-iteration {
  min-height: 250px;
  min-width: 400px;
  background-color: var(--color-bg-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 12px;

  .custom-node-resizer {
    &-handle {
      display: none;
      width: 10px;
      height: 10px;
      border: none;
      border-right: 2px solid;
      border-bottom: 2px solid;
      border-color: #3367d9;
      background-color: transparent;
      border-radius: 0 0 10px 0;
      transform: translate(-120%, -120%);
    }

    &-line {
      display: none;
    }

    + .right {
      border-color: red;
    }
  }

  .custom-node-resizer-handle + .bottom + .right {
    display: block;
  }
}
</style>
