<template>
  <div class="space-y-1">
    <div
      v-for="(item, index) in processedList"
      :key="item.variable"
      class="field-item text-primary flex h-8 cursor-pointer items-center justify-between rounded-lg border px-2.5 shadow-xs hover:shadow-md transition-all duration-200"
      @mouseenter="hoveredIndex = index"
      @mouseleave="hoveredIndex = -1"
    >
      <div class="flex w-0 grow items-center space-x-1">
        <AiSvgIcon name="workflow-ai-variable" />
        <div class="system-xs-regular max-w-[130px] shrink-0 truncate font-medium text-text-secondary">
          {{ item.variable }}.{{ item.label }}
        </div>
      </div>
      <div class="ml-2 flex shrink-0 items-center">
        <!-- 系统字段显示类型 -->
        <div v-if="item.readonly || isNode" class="mr-2 text-xs font-normal text-text-tertiary">
          <span>{{ item.type }}</span>
        </div>
        <!-- 自定义字段显示必填状态或操作按钮 -->
        <div v-else class="flex items-center">
          <!-- 非hover状态显示必填标识 -->
          <div v-if="hoveredIndex !== index" class="mr-2 text-xs font-normal text-text-tertiary">
            <span v-if="item.required">必填</span>
            <span v-else class="text-text-quaternary">可选</span>
          </div>
          <!-- hover状态显示操作按钮 -->
          <div v-else class="flex items-center space-x-1">
            <!-- 编辑按钮 -->
            <a-button type="text" size="mini" class="edit-btn" @click.stop="handleEdit(item)">
              <template #icon>
                <icon-edit class="w-3 h-3" />
              </template>
            </a-button>
            <!-- 删除按钮 -->
            <a-button type="text" size="mini" status="danger" class="delete-btn" @click.stop="handleDelete(item)">
              <template #icon>
                <icon-delete class="w-3 h-3" />
              </template>
            </a-button>
          </div>
        </div>
        <!-- 系统字段的标识图标 -->
        <svg
          v-if="item.readonly"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
          width="24"
          height="24"
          fill="currentColor"
          class="remixicon h-3.5 w-3.5 text-text-tertiary"
        >
          <path
            d="M7.78428 14L8.2047 10H4V8H8.41491L8.94043 3H10.9514L10.4259 8H14.4149L14.9404 3H16.9514L16.4259 8H20V10H16.2157L15.7953 14H20V16H15.5851L15.0596 21H13.0486L13.5741 16H9.58509L9.05957 21H7.04855L7.57407 16H4V14H7.78428ZM9.7953 14H13.7843L14.2047 10H10.2157L9.7953 14Z"
          />
        </svg>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from 'vue'
import { Modal } from '@arco-design/web-vue'

interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
  min_value?: number
  max_value?: number
  file_types?: string[]
  max_file_size?: number
}

const props = defineProps<{
  list: FieldType[]
  isChatMode?: false
  isNode?: boolean | false
}>()

const emit = defineEmits<{
  (e: 'edit', item: FieldType, index: number): void
  (e: 'delete', index: number): void
}>()

// hover状态管理
const hoveredIndex = ref(-1)

// 处理编辑操作
const handleEdit = (item: any) => {
  // 只编辑自定义字段，过滤掉系统字段
  if (!item.readonly) {
    const customIndex = props.list.findIndex((field) => field.variable === item.variable)
    if (customIndex !== -1) {
      emit('edit', props.list[customIndex], customIndex)
    }
  }
}

// 处理删除操作
const handleDelete = (item: any) => {
  if (!item.readonly) {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除变量 "${item.variable}" 吗？`,
      okText: '删除',
      cancelText: '取消',
      onOk: () => {
        // 只删除自定义字段，过滤掉系统字段
        const customIndex = props.list.findIndex((field) => field.variable === item.variable)
        if (customIndex !== -1) {
          emit('delete', customIndex)
        }
      }
    })
  }
}
const defaultList = [
  {
    label: '',
    required: false,
    readonly: true,
    type: 'string',
    variable: 'sys.user_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'array[file]',
    variable: 'sys.files'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.app_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_run_id'
  }
]
const isChatModeDefaultList = [
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.query'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.dialogue_count'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.conversation_id'
  }
]
const processedList = computed(() => {
  if (props.isNode) {
    return props.list?.map((item) => {
      const processed = {
        ...item,
        readonly: false
      }
      return processed
    })
  } else {
    return [
      ...props.list?.map((item) => {
        const processed = {
          ...item,
          readonly: false
        }
        return processed
      }),
      ...defaultList,
      ...(props.isChatMode ? isChatModeDefaultList : [])
    ]
  }
})
onMounted(() => {
  console.log(
    props.list.map((e) => {
      console.log(e)
    })
  )
})
</script>
<style scoped lang="scss">
.field-item {
  &:hover {
    .edit-btn,
    .delete-btn {
      opacity: 1;
      transform: scale(1);
    }
  }
}

.edit-btn,
.delete-btn {
  opacity: 0.7;
  transform: scale(0.9);
  transition: all 0.2s ease;

  &:hover {
    opacity: 1;
    transform: scale(1.1);
  }
}

.edit-btn {
  color: #1890ff;

  &:hover {
    background-color: rgba(24, 144, 255, 0.1);
  }
}

.delete-btn {
  color: #ff4d4f;

  &:hover {
    background-color: rgba(255, 77, 79, 0.1);
  }
}
</style>
