import http from '@/utils/http'

const BASE_URL = 'console/api'

// /workspaces/current/models/model-types/${type}
export const getModelListHttp = (type: any) => {
  return http.get<any>(`${BASE_URL}/workspaces/current/models/model-types/${type}`)
}
// /workspaces/current/default-model?model_type=${type}
export const getDefaultModelHttp = (type: any) => {
  return http.get<any>(`${BASE_URL}/workspaces/current/default-model?model_type=${type}`)
}

// model tool  {provider:collection.name}
export const fetchModelToolList = (params) => {
  return http.get<any>(`${BASE_URL}/workspaces/current/tool-provider/model/tools`, params)
}
// builtIn collection.name
export const fetchBuiltInToolList = (collectionName) => {
  return http.get<any>(`${BASE_URL}/workspaces/current/tool-provider/builtin/${collectionName}/tools`)
}
// workflow collection.id
export const fetchWorkflowToolList = (appID) => {
  return http.get<any>(`${BASE_URL}/workspaces/current/tool-provider/workflow/tools?workflow_tool_id=${appID}`)
}
// custom {provider:collection.name} else
export const fetchCustomToolList = (params) => {
  return http.get<any>(`${BASE_URL}/workspaces/current/tool-provider/api/tools`, params)
}
