import js from '@eslint/js'
import tseslint from 'typescript-eslint'
import pluginVue from 'eslint-plugin-vue'
import * as parserVue from 'vue-eslint-parser'
import configPrettier from 'eslint-config-prettier'
import pluginPrettier from 'eslint-plugin-prettier'
import { defineConfig, globalIgnores } from 'eslint/config'

export default defineConfig([
  // 全局忽略的文件和目录
  globalIgnores(['**/.*', 'dist/*', '*.d.ts', 'public/*', 'src/assets/**', 'node_modules']),
  {
    // 基础 JavaScript 推荐配置
    ...js.configs.recommended,
    plugins: {
      prettier: pluginPrettier
    },
    rules: {
      // 合并 Prettier 规则
      ...configPrettier.rules,
      ...pluginPrettier.configs.recommended.rules,
      // 关闭 debugger 检查
      'no-debugger': 'off',
      // 未使用变量规则配置，忽略以 _ 开头的变量和参数
      'no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_'
        }
      ],
      // Prettier 格式化规则配置
      'prettier/prettier': [
        'error',
        {
          endOfLine: 'auto'
        }
      ]
    }
  },
  // TypeScript ESLint 配置
  ...tseslint.config({
    extends: [...tseslint.configs.recommended],
    // 应用于 TypeScript 文件
    files: ['**/*.?([cm])ts', '**/*.?([cm])tsx'],
    rules: {
      // 禁止重复声明
      '@typescript-eslint/no-redeclare': 'error',
      // 允许使用 @ts-comment
      '@typescript-eslint/ban-ts-comment': 'off',
      // 允许使用 any 类型
      '@typescript-eslint/no-explicit-any': 'off',
      // 建议使用 as const
      '@typescript-eslint/prefer-as-const': 'warn',
      // 允许空函数
      '@typescript-eslint/no-empty-function': 'off',
      // 允许非空断言
      '@typescript-eslint/no-non-null-assertion': 'off',
      // 允许未使用的表达式
      '@typescript-eslint/no-unused-expressions': 'off',
      // 允许不安全的函数类型
      '@typescript-eslint/no-unsafe-function-type': 'off',
      // 禁止类型导入的副作用
      '@typescript-eslint/no-import-type-side-effects': 'error',
      // 不强制模块边界类型
      '@typescript-eslint/explicit-module-boundary-types': 'off',
      // 强制使用一致的类型导入
      '@typescript-eslint/consistent-type-imports': [
        'error',
        { disallowTypeAnnotations: false, fixStyle: 'inline-type-imports' }
      ],
      // 强制枚举成员使用字面量
      '@typescript-eslint/prefer-literal-enum-member': ['error', { allowBitwiseExpressions: true }],
      // TypeScript 未使用变量规则配置，忽略以 _ 开头的变量和参数
      '@typescript-eslint/no-unused-vars': [
        'error',
        {
          argsIgnorePattern: '^_',
          varsIgnorePattern: '^_'
        }
      ]
    }
  }),
  {
    // 针对 .d.ts 声明文件的特殊规则
    files: ['**/*.d.ts'],
    rules: {
      // 关闭 eslint 注释无限禁用检查
      'eslint-comments/no-unlimited-disable': 'off',
      // 关闭导入重复检查
      'import/no-duplicates': 'off',
      // 关闭受限语法检查
      'no-restricted-syntax': 'off',
      // 关闭未使用导入检查
      'unused-imports/no-unused-vars': 'off'
    }
  },
  {
    // 针对 JavaScript 文件的特殊规则
    files: ['**/*.?([cm])js'],
    rules: {
      // 允许使用 require 导入
      '@typescript-eslint/no-require-imports': 'off'
    }
  },
  {
    // Vue 文件配置
    files: ['**/*.vue'],
    languageOptions: {
      // 使用 Vue ESLint 解析器
      parser: parserVue,
      parserOptions: {
        ecmaFeatures: {
          jsx: true
        },
        extraFileExtensions: ['.vue'],
        parser: tseslint.parser,
        sourceType: 'module'
      }
    },
    plugins: {
      '@typescript-eslint': tseslint.plugin,
      vue: pluginVue
    },
    // 使用 Vue 处理器处理 .vue 文件
    processor: pluginVue.processors['.vue'],
    rules: {
      // 合并 Vue 基础、必要和推荐规则
      ...pluginVue.configs.base.rules,
      ...pluginVue.configs.essential.rules,
      ...pluginVue.configs.recommended.rules,
      // 关闭未定义变量检查
      'no-undef': 'off',
      // 关闭未使用变量检查
      'no-unused-vars': 'off',
      // 允许使用 v-html
      'vue/no-v-html': 'off',
      // 不要求 prop 有默认值
      'vue/require-default-prop': 'off',
      // 不要求显式声明 emits
      'vue/require-explicit-emits': 'off',
      // 允许单个单词的组件名
      'vue/multi-word-component-names': 'off',
      // 关闭 setup props 响应性丢失检查
      'vue/no-setup-props-reactivity-loss': 'off',
      // HTML 自闭合标签配置
      'vue/html-self-closing': [
        'error',
        {
          html: {
            void: 'always',
            normal: 'always',
            component: 'always'
          },
          svg: 'always',
          math: 'always'
        }
      ]
    }
  }
])
