<template>
  <a-modal :visible="visible" @ok="handleOk" @cancel="handleCancel">
    <template #title>
      {{ dialogTitle }}
    </template>

    <div>
      <a-typography-title :heading="6">
        {{ loadBalancingItem.label.zh_<PERSON> }}
      </a-typography-title>
      <a-space>
        <a-tag v-for="tag in loadBalancingItem.provider?.supported_model_types" :key="tag">{{ tag }}</a-tag>

        <a-tag>{{ modelTypeFormat(loadBalancingItem.model_type) }}</a-tag>
        <a-tag v-if="loadBalancingItem.model_properties.context_size">
          {{ sizeFormat(loadBalancingItem.model_properties.context_size as number) }}
        </a-tag>
      </a-space>

      <a-card :bordered="true">
        <a-card-meta>
          <template #avatar>
            <div :style="{ display: 'flex', alignItems: 'center', color: '#1D2129' }">
              <a-avatar :size="36" :style="{ marginRight: '8px' }">
                <img :src="DOMAIN_NAME + loadBalancingItem.provider?.icon_small.zh_Hans" alt="" />
              </a-avatar>
              <a-space direction="vertical" size="mini">
                <a-typography-text>由模型供应商管理</a-typography-text>
                <a-typography-text type="secondary">使用模型供应商提供的单组凭据</a-typography-text>
              </a-space>
            </div>
          </template>
        </a-card-meta>
      </a-card>
      <a-card :bordered="true">
        <a-card-meta>
          <template #avatar>
            <div :style="{ display: 'flex', alignItems: 'center', color: '#1D2129' }">
              <a-avatar :size="36" :style="{ marginRight: '8px' }">
                <icon-book :size="36" />
              </a-avatar>
              <a-space direction="vertical" size="mini">
                <a-typography-text>负载均衡</a-typography-text>
                <a-typography-text type="secondary">
                  为了减轻单组凭据的压力，您可以为模型调用配置多组凭据。
                </a-typography-text>
              </a-space>
            </div>
          </template>
        </a-card-meta>
      </a-card>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { DOMAIN_NAME } from '@/views/app/workflow/constant/common'

defineOptions({ name: 'LoadBalancingModal' })
import { ref } from 'vue'
import { modelTypeFormat, sizeFormat } from '@/views/app/workflow/utils/model'
import type { SubModelListItem } from '@/apis/model-mgmt/type.ts'

const visible = ref(true)
const dialogTitle = ref('设置负载均衡')
const emits = defineEmits(['closeLoadBalancingModal'])
const props = defineProps<{
  loadBalancingItem: SubModelListItem
}>()

onMounted(() => {
  console.log('loadBalancingItem:', props.loadBalancingItem)
})
const handleOk = async () => {
  emits('closeLoadBalancingModal')
}
const handleCancel = () => {
  emits('closeLoadBalancingModal')
}
</script>

<style scoped lang="scss">
.arco-card + .arco-card {
}

.arco-card {
  margin-top: var(--margin);

  :deep(.arco-card-meta-footer:last-child) {
    margin-top: 0;
  }
}

.arco-card:hover {
  border-color: $color-primary;
}
</style>
