/** 知识库标签类型 */
export interface DatasetTag {
  id: string
  name: string
  type: string
  binding_count?: string | number
}

/** 知识库重排序模型 */
export interface RerankingModel {
  reranking_provider_name: string
  reranking_model_name: string
}

/** 知识库检索模型配置 */
export interface RetrievalModelDict {
  search_method: string
  reranking_enable: boolean
  reranking_mode: string | null
  reranking_model: RerankingModel
  weights: any
  top_k: number
  score_threshold_enabled: boolean
  score_threshold: number | null
}

/** 知识库外部信息 */
export interface ExternalKnowledgeInfo {
  external_knowledge_id: string | null
  external_knowledge_api_id: string | null
  external_knowledge_api_name: string | null
  external_knowledge_api_endpoint: string | null
}

/** 知识库外部检索模型 */
export interface ExternalRetrievalModel {
  top_k: number
  score_threshold: number
  score_threshold_enabled: boolean | null
}

/** 知识库详情类型 */
export interface Dataset {
  id: string
  name: string
  description: string
  provider: string
  permission: string
  data_source_type: string
  indexing_technique: string
  app_count: number
  document_count: number
  word_count: number
  created_by: string
  created_at: number
  updated_by: string | null
  updated_at: number
  embedding_model: string
  embedding_model_provider: string
  embedding_available: boolean
  retrieval_model_dict: RetrievalModelDict
  tags: DatasetTag[]
  doc_form: string
  external_knowledge_info: ExternalKnowledgeInfo
  external_retrieval_model: ExternalRetrievalModel
  doc_metadata: any[]
  built_in_field_enabled: boolean
  partial_member_list: any[]
}

/** 知识库列表请求参数 */
export interface DatasetListParams {
  page?: number
  limit?: number
  include_all?: boolean
  tag_ids?: string | string[]
  keyword?: string
  ids?: string[]
  status?: string
}

/** 知识库列表响应类型 */
export interface DatasetListResponse {
  data: Dataset[]
  has_more: boolean
  limit: number
  total: number
  page: number
}

/** 文件上传响应类型 */
export interface FileUploadResponse {
  id: string
  name: string
  size: number
  extension: string
  mime_type: string
  created_by: string
  created_at: number
  preview_url: string | null
}

/** 预处理规则类型 */
export interface PreProcessingRule {
  id: string
  enabled: boolean
}

/** 分段规则类型 */
export interface SegmentationRule {
  separator: string
  max_tokens: number
  chunk_overlap?: number
}

/** 创建知识库请求参数类型 */
export interface CreateDatasetParams {
  name: string
  description?: string
  tag_ids?: string[]
  indexing_technique?: 'high_quality' | 'economy'
  process_rule?: {
    rules: {
      pre_processing_rules: PreProcessingRule[]
      segmentation: SegmentationRule
      parent_mode?: string
      subchunk_segmentation?: SegmentationRule
    }
    mode: 'custom' | 'hierarchical'
  }
  doc_form?: 'text_model' | 'hierarchical_model'
  doc_language?: string
  info_list?: {
    data_source_type: 'upload_file'
    file_info_list: {
      file_ids: string[]
    }
  }
  embedding_model?: string
}

/** 索引预估请求参数类型 */
export interface IndexingEstimateParams {
  info_list: {
    data_source_type: 'upload_file'
    file_info_list: {
      file_ids: string[]
    }
  }
  indexing_technique: 'high_quality' | 'economy'
  process_rule: {
    rules: {
      pre_processing_rules: PreProcessingRule[]
      segmentation: SegmentationRule
      parent_mode?: string
      subchunk_segmentation?: SegmentationRule
    }
    mode: 'custom' | 'hierarchical'
  }
  doc_form: 'text_model' | 'hierarchical_model'
  doc_language: string
}

/** 索引预估结果片段类型 */
export interface SegmentPreview {
  content: string
  child_chunks: any[] | null
}

/** 索引预估响应类型 */
export interface IndexingEstimateResponse {
  total_segments: number
  preview: SegmentPreview[]
  qa_preview: any | null
}

/** Embedding模型信息类型 */
export interface EmbeddingModel {
  model: string
  label: {
    zh_Hans: string
    en_US: string
  }
  model_type: string
  features: any | null
  fetch_from: string
  model_properties: {
    context_size: number
    max_chunks: number
  }
  deprecated: boolean
  status: string
  load_balancing_enabled: boolean
}

/** Embedding模型提供商类型 */
export interface EmbeddingModelProvider {
  tenant_id: string
  provider: string
  label: {
    zh_Hans: string
    en_US: string
  }
  icon_small: {
    zh_Hans: string
    en_US: string
  }
  icon_large: {
    zh_Hans: string
    en_US: string
  }
  status: string
  models: EmbeddingModel[]
}

/** Embedding模型列表响应类型 */
export interface EmbeddingModelsResponse {
  data: EmbeddingModelProvider[]
}

/** @desc 获取知识库文档响应类型 */
export interface DatasetDocument {
  id: string
  dataset_id: string
  name: string
  size: number
  extension: string
  mime_type: string
  segment_count: number
  word_count: number
  created_by: string
  created_at: number
  updated_at: number
  status: string
}

export interface DatasetDocumentsResponse {
  data: DatasetDocument[]
  has_more: boolean
  limit: number
  total: number
  page: number
}

/** @desc Rerank模型响应 */
export interface RerankModelsResponse {
  data: {
    tenant_id: string
    provider: string
    label: {
      zh_Hans: string
      en_US: string
    }
    icon_small: {
      zh_Hans: string
      en_US: string
    }
    icon_large: {
      zh_Hans: string
      en_US: string
    }
    status: string
    models: {
      model: string
      label: {
        zh_Hans: string
        en_US: string
      }
      model_type: string
      features: any
      fetch_from: string
      model_properties: {
        context_size: number
      }
      deprecated: boolean
      status: string
      load_balancing_enabled: boolean
    }[]
  }[]
}

/** @desc 默认Rerank模型响应 */
export interface DefaultRerankModelResponse {
  data: {
    model: string
    model_type: string
    provider: {
      provider: string
      label: {
        zh_Hans: string
        en_US: string
      }
      icon_small: {
        zh_Hans: string
        en_US: string
      }
      icon_large: {
        zh_Hans: string
        en_US: string
      }
      supported_model_types: string[]
      models: any[]
      tenant_id: string
    }
  }
}

/** @desc 知识库处理规则参数 */
export interface DatasetProcessRuleParams {
  rules: {
    pre_processing_rules: {
      id: string
      enabled: boolean
    }[]
    segmentation: {
      separator: string
      max_tokens: number
      chunk_overlap?: number
    }
    parent_mode?: 'paragraph' | 'full-doc'
    subchunk_segmentation?: {
      separator: string
      max_tokens: number
      chunk_overlap?: number
    }
  }
  mode: 'custom' | 'hierarchical'
}

/** @desc 知识库处理规则响应 */
export interface DatasetProcessRuleResponse {
  mode: string
  rules: {
    pre_processing_rules: {
      id: string
      enabled: boolean
    }[]
    segmentation: {
      separator: string
      max_tokens: number
      chunk_overlap: number
    }
    parent_mode?: string
    subchunk_segmentation?: {
      separator: string
      max_tokens: number
      chunk_overlap: number
    }
  }
  limits: {
    indexing_max_segmentation_tokens_length: number
  }
}

/** @desc 知识库索引状态响应 */
export interface DatasetIndexingStatusResponse {
  data: {
    id: string
    indexing_status: string
    processing_started_at: number
    parsing_completed_at: number | null
    cleaning_completed_at: number | null
    splitting_completed_at: number | null
    completed_at: number | null
    paused_at: number | null
    error: string | null
    stopped_at: number | null
    completed_segments: number
    total_segments: number
  }[]
}

/** @desc 知识库初始化参数 */
export interface DatasetInitParams {
  data_source: {
    type: string
    info_list: {
      data_source_type: string
      file_info_list: {
        file_ids: string[]
      }
    }
  }
  indexing_technique: string
  process_rule: DatasetProcessRuleParams
  doc_form: string
  doc_language: string
  retrieval_model: {
    search_method: string
    reranking_enable: boolean
    reranking_model: {
      reranking_provider_name: string
      reranking_model_name: string
    }
    top_k: number
    score_threshold_enabled: boolean
    score_threshold: number
    reranking_mode: string
    weights?: {
      weight_type: string
      vector_setting: {
        vector_weight: number
        embedding_provider_name: string
        embedding_model_name: string
      }
      keyword_setting: {
        keyword_weight: number
      }
    }
  }
  embedding_model: string
  embedding_model_provider: string
}

/** @desc 知识库初始化响应 */
export interface DatasetInitResponse {
  dataset: {
    id: string
    name: string
    description: string
    permission: string
    data_source_type: string
    indexing_technique: string
    created_by: string
    created_at: number
  }
  documents: DocumentResponse[]
  batch: string
}

/** @desc 文档响应 */
export interface DocumentResponse {
  id: string
  position: number
  data_source_type: string
  data_source_info: {
    upload_file_id: string
  }
  data_source_detail_dict: {
    upload_file: {
      id: string
      name: string
      size: number
      extension: string
      mime_type: string
      created_by: string
      created_at: number
    }
  }
  dataset_process_rule_id: string
  name: string
  created_from: string
  created_by: string
  created_at: number
  tokens: number
  indexing_status: string
  error: string | null
  enabled: boolean
  disabled_at: number | null
  disabled_by: string | null
  archived: boolean
  display_status: string
  word_count: number
  hit_count: number
  doc_form: string
  doc_metadata: any
}
