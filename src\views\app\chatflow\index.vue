<template>
  <div class="chatflow-container h-full flex">
    <!-- 左侧会话列表 -->
    <div class="sidebar w-80 bg-gray-50 border-r border-gray-200 flex flex-col">
      <!-- 头部信息 -->
      <div class="p-4 border-b border-gray-200">
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center space-x-3">
            <div
              class="w-10 h-10 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center"
            >
              <icon-robot class="text-white text-lg" />
            </div>
            <div>
              <h3 class="font-semibold text-gray-900">{{ appInfo?.app?.name || 'Chatflow 应用' }}</h3>
              <p class="text-sm text-gray-500">{{ appInfo?.app?.description || '智能对话流应用' }}</p>
            </div>
          </div>
          <a-button size="small" :disabled="!hasRequiredFields" @click="showVariableModal = true">
            <icon-settings />
          </a-button>
        </div>
        <a-button type="primary" class="w-full" @click="startNewConversation">
          <icon-plus class="mr-2" />
          开启新对话
        </a-button>
      </div>

      <!-- 会话列表 -->
      <div class="conversations flex-1 overflow-y-auto p-4">
        <div v-if="conversations.length === 0" class="text-center text-gray-500 mt-8">
          <icon-message class="text-4xl mb-2" />
          <p>暂无对话记录</p>
        </div>
        <div v-else class="space-y-2">
          <div
            v-for="conversation in conversations"
            :key="conversation.id"
            class="conversation-item p-3 bg-white rounded-lg border border-gray-200 cursor-pointer hover:bg-blue-50 hover:border-blue-300 transition-colors"
            :class="{ 'bg-blue-50 border-blue-300': currentConversationId === conversation.id }"
            @click="loadConversation(conversation.id)"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1 min-w-0">
                <h3 class="text-sm font-medium text-gray-900 truncate">{{ conversation.title }}</h3>
                <p class="text-xs text-gray-500 mt-1">{{ formatTime(conversation.created_at) }}</p>
              </div>
              <a-dropdown @select="handleConversationAction">
                <a-button type="text" size="mini" @click.stop>
                  <icon-more />
                </a-button>
                <template #content>
                  <a-doption :value="`pin-${conversation.id}`">
                    <icon-pushpin class="mr-2" />
                    {{ conversation.is_pinned ? '取消置顶' : '置顶' }}
                  </a-doption>
                  <a-doption :value="`rename-${conversation.id}`">
                    <icon-edit class="mr-2" />
                    重命名
                  </a-doption>
                  <a-doption :value="`delete-${conversation.id}`">
                    <icon-delete class="mr-2" />
                    删除
                  </a-doption>
                </template>
              </a-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧聊天区域 -->
    <div class="chat-area flex-1 flex flex-col">
      <!-- 聊天头部 -->
      <div class="chat-header p-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2">
            <icon-message class="text-gray-600" />
            <span class="font-medium text-gray-900">对话</span>
          </div>
          <div class="flex items-center space-x-2">
            <a-button v-if="isExecuting" size="small" status="danger" :loading="stopLoading" @click="stopExecution">
              <template #icon>
                <icon-stop />
              </template>
              停止
            </a-button>
            <a-button size="small" @click="clearConversation">
              <template #icon>
                <icon-refresh />
              </template>
              清空
            </a-button>
          </div>
        </div>
      </div>

      <!-- 聊天消息区域 -->
      <div ref="messagesContainer" class="chat-messages flex-1 overflow-y-auto p-4">
        <!-- <div v-if="messages.length === 0" class="text-center text-gray-500 mt-16">
          <icon-robot class="text-6xl mb-4 text-gray-300" />
          <p class="text-lg">欢迎使用 Chatflow</p>
          <p class="text-sm">请先填写左侧的输入变量，然后开始对话</p>
        </div> -->

        <ChatflowRoom
          :app-id="appId"
          :variables="variables"
          :is-running="isExecuting"
          :show-variable-form="false"
          :form-data="formData"
          @start-execution="handleStartExecution"
          @stop-execution="handleStopExecution"
          @show-detail="handleShowDetail"
        />
      </div>
    </div>

    <!-- 变量配置弹窗 -->
    <a-modal
      v-model:visible="showVariableModal"
      title="配置应用变量"
      :width="500"
      @ok="saveVariables"
      @cancel="showVariableModal = false"
    >
      <div class="space-y-4">
        <div v-for="variable in variables" :key="variable.key" class="form-item">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ variable.name || variable.key }}
            <span v-if="variable.required" class="text-red-500">*</span>
          </label>
          <a-input
            v-if="variable.type === 'text-input'"
            v-model="formData[variable.key]"
            :placeholder="`请输入${variable.name || variable.key}`"
            :required="variable.required"
          />
          <a-textarea
            v-else-if="variable.type === 'paragraph'"
            v-model="formData[variable.key]"
            :placeholder="`请输入${variable.name || variable.key}`"
            :required="variable.required"
            :auto-size="{ minRows: 3, maxRows: 6 }"
          />
          <a-select
            v-else-if="variable.type === 'select'"
            v-model="formData[variable.key]"
            :placeholder="`请选择${variable.name || variable.key}`"
            :required="variable.required"
          >
            <a-option v-for="option in variable.options" :key="option" :value="option" :label="option" />
          </a-select>
        </div>
      </div>
    </a-modal>

    <!-- 重命名对话弹窗 -->
    <a-modal v-model:visible="showRenameModal" title="重命名对话" @ok="confirmRename" @cancel="showRenameModal = false">
      <a-input v-model="newConversationTitle" placeholder="输入新的对话标题" @keyup.enter="confirmRename" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { chatflowRunFetch } from '@/apis/workflow/chatflow'
import { getAppConfig } from '@/apis'
import { Message } from '@arco-design/web-vue'
import { useAppStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { useRoute } from 'vue-router'
import ChatflowRoom from '../workflow/run/chatflow-room.vue'
import { useAppShare } from '@/stores'
import { getAppParams, shareRun } from '@/apis/workflow/share'
import type { SiteInfo } from '@/views/app/workflow/types/share'
import { Resolution, TransferMethod, type VisionFile, type VisionSettings } from '@/views/app/workflow/types/app'
import type { PromptConfig } from '@/views/app/workflow/types/debug'
import { userInputsFormToPromptVariables } from '@/views/app/workflow/utils/model-config'
import type { MoreLikeThisConfig, TextToSpeechConfig } from '@/views/workspace/ai/share/type'

const { installedApps } = useAppShare()

defineOptions({ name: 'ChatflowPage' })

const route = useRoute()
const appStore = useAppStore()
const { currentAppInfo } = storeToRefs(appStore)

// 从路由获取应用ID
const appId = computed(() => route.params.appId as string)

const isExecuting = ref(false)
const isStarting = ref(false)
const stopLoading = ref(false)
const conversationStarted = ref(false)
const messagesContainer = ref()
const variableValues = ref<Record<string, any>>({})
const siteInfo = ref<SiteInfo | null>(null)
const promptConfig = ref<PromptConfig | null>(null)
const moreLikeThisConfig = ref<MoreLikeThisConfig | null>(null)
const textToSpeechConfig = ref<TextToSpeechConfig | null>(null)
const visionConfig = ref<VisionSettings>({
  enabled: false,
  number_limits: 2,
  detail: Resolution.low,
  transfer_methods: [TransferMethod.local_file]
})

// 新增的状态变量
const conversations = ref([])
const currentConversationId = ref('')
const showVariableModal = ref(false)
const showRenameModal = ref(false)
const newConversationTitle = ref('')
const formData = ref<Record<string, any>>({})

// 获取应用信息和变量
const variables = computed(() => {
  return promptConfig.value?.prompt_variables || []
})

const messages = computed(() => [])

// 检查是否有必填字段
const hasRequiredFields = computed(() => {
  return variables.value.some((v) => v.required)
})

// 检查是否可以开始对话
const canStart = computed(() => {
  if (variables.value.length === 0) return true

  for (const variable of variables.value) {
    if (variable.required && !formData.value[variable.key]) {
      return false
    }
  }
  return true
})

// 当前对话
const currentConversation = computed(() => {
  return conversations.value.find((c) => c.id === currentConversationId.value)
})

// 初始化应用信息和变量默认值
onMounted(async () => {
  // 获取应用配置
  try {
    const appConfig = await getAppConfig({ appId: appId.value })
    appStore.setCurrentAppInfo(appConfig)
  } catch (error) {
    console.error('获取应用配置失败:', error)
    Message.error('获取应用配置失败')
  }

  // 初始化变量默认值
  variables.value.forEach((variable) => {
    if (variable.default) {
      formData.value[variable.key] = variable.default
    }
  })
})

const startConversation = async () => {
  if (!canStart.value) {
    Message.error('请填写所有必填变量')
    return
  }

  isStarting.value = true

  try {
    conversationStarted.value = true
    isExecuting.value = false
  } catch (error) {
    console.error('启动对话失败:', error)
    Message.error('启动对话失败')
  } finally {
    isStarting.value = false
  }
}

const handleStartExecution = () => {
  isExecuting.value = true
}

const handleStopExecution = () => {
  isExecuting.value = false
}

const stopExecution = async () => {
  stopLoading.value = true

  try {
    isExecuting.value = false
  } catch (error) {
    console.error('停止执行失败:', error)
  } finally {
    stopLoading.value = false
  }
}
const appInfo = ref()

onMounted(async () => {
  const appParams = await getAppParams((route.params.appId as string) || '')
  const currentApp = installedApps.find((ele) => ele.id === route.params.appId)
  appInfo.value = currentApp
  const appData = {
    app_id: currentApp?.id || '',
    site: {
      title: currentApp?.app.name || '',
      prompt_public: false,
      copyright: '',
      icon: currentApp?.app.icon,
      icon_background: currentApp?.app.icon_background
    },
    plan: 'basic'
  }
  if (currentApp) {
    const { app_id, site } = appData
    siteInfo.value = site

    const { user_input_form, more_like_this, file_upload, text_to_speech }: any = appParams

    visionConfig.value = {
      ...file_upload,
      transfer_methods: file_upload.allowed_file_upload_methods || file_upload.allowed_upload_methods,
      image_file_size_limit: appParams?.system_parameters?.image_file_size_limit,
      fileUploadConfig: appParams?.system_parameters
    }

    const prompt_variables = userInputsFormToPromptVariables(user_input_form)

    promptConfig.value = {
      prompt_template: '',
      prompt_variables
    } as PromptConfig

    moreLikeThisConfig.value = more_like_this
    textToSpeechConfig.value = text_to_speech
  }
})

const clearConversation = () => {
  conversationStarted.value = false
  isExecuting.value = false
}

// 新增方法
const startNewConversation = () => {
  currentConversationId.value = ''
  conversationStarted.value = true
  // 生成新的对话ID
  const newConversation = {
    id: `conv_${Date.now()}`,
    title: `新对话 ${new Date().toLocaleString()}`,
    created_at: Date.now(),
    is_pinned: false
  }
  conversations.value.unshift(newConversation)
  currentConversationId.value = newConversation.id
}

const loadConversation = (conversationId: string) => {
  currentConversationId.value = conversationId
  conversationStarted.value = true
}

const formatTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString()
}

const handleConversationAction = (value: string) => {
  const [action, id] = value.split('-')
  const conversation = conversations.value.find((c) => c.id === id)

  if (!conversation) return

  switch (action) {
    case 'pin':
      conversation.is_pinned = !conversation.is_pinned
      Message.success(conversation.is_pinned ? '已置顶' : '已取消置顶')
      break
    case 'rename':
      newConversationTitle.value = conversation.title
      showRenameModal.value = true
      break
    case 'delete':
      const index = conversations.value.findIndex((c) => c.id === id)
      if (index > -1) {
        conversations.value.splice(index, 1)
        if (currentConversationId.value === id) {
          currentConversationId.value = ''
        }
        Message.success('已删除')
      }
      break
  }
}

const confirmRename = () => {
  const conversation = conversations.value.find((c) => c.id === currentConversationId.value)
  if (conversation && newConversationTitle.value.trim()) {
    conversation.title = newConversationTitle.value.trim()
    showRenameModal.value = false
    Message.success('重命名成功')
  }
}

const saveVariables = () => {
  showVariableModal.value = false
  Message.success('变量配置已保存')
}

const handleShowDetail = (message: any) => {
  console.log('显示详情:', message)
}
</script>

<style scoped lang="scss">
.chatflow-container {
  height: 100%;

  .input-panel {
    min-width: 384px;
    max-width: 384px;
  }
}
</style>
