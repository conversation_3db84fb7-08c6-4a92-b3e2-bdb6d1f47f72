<template>
  <div class="api-input">
    <a-row :gutter="16">
      <a-col :span="8">
        <a-select v-model="nodeInfo.method">
          <a-option v-for="option in MethodOptions" :key="option.value" :label="option.label" :value="option.value" />
        </a-select>
      </a-col>
      <a-col :span="16">
        <a-input v-model="nodeInfo.url" class="w-full" placeholder="请输入" />
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { Method } from '@/views/app/workflow/nodes/http/types'

const props = defineProps(['nodeInfo'])
const MethodOptions = [
  { label: 'GET', value: Method.get },
  { label: 'POST', value: Method.post },
  { label: 'HEAD', value: Method.head },
  { label: 'PATCH', value: Method.patch },
  { label: 'PUT', value: Method.put },
  { label: 'DELETE', value: Method.delete }
]
</script>

<style scoped lang="scss">
.api-input {
}
</style>
