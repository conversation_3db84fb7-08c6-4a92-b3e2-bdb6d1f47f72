<template>
  <a-modal
    v-model:visible="visible"
    title="添加变量"
    title-align="start"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 500 ? 500 : '100%'"
    :body-style="{ maxHeight: '70vh', overflowY: 'auto', padding: '20px' }"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <div class="modal-form-container">
      <AiForm ref="formRef" v-model="form" layout="vertical" :columns="dynamicColumns" />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { AiForm, type ColumnItem } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'

interface FormData {
  label: string
  max_length: number
  options: string[]
  required: string[]
  type: string
  variable: string
  min_value?: number
  max_value?: number
  file_types: string[]
  max_file_size: number
}

const emit = defineEmits<{
  (e: 'save-success', form: FormData): void
}>()

const { width } = useWindowSize()

const visible = ref(false)
const formRef = ref<InstanceType<typeof AiForm>>()

const [form, resetForm] = useResetReactive({
  label: '',
  max_length: 48,
  options: [],
  required: [],
  type: 'text-input',
  variable: '',
  min_value: undefined,
  max_value: undefined,
  file_types: [],
  max_file_size: 10
})
// 基础字段配置
const baseColumns: ColumnItem[] = [
  {
    label: '字段类型',
    field: 'type',
    type: 'radio-group',
    required: true,
    span: 24,
    cascader: ['name'],
    props: {
      type: 'radio',
      options: [
        { label: '文本', value: 'text-input' },
        { label: '段落', value: 'paragraph' },
        { label: '下拉选项', value: 'select' },
        { label: '数字', value: 'number' },
        { label: '单文件', value: 'file' },
        { label: '文件列表', value: 'file-list' }
      ]
    }
  },
  {
    label: '变量名称',
    field: 'variable',
    type: 'input',
    span: 24,
    required: true,
    props: {
      maxLength: 100
    }
  },
  {
    label: '显示名称',
    field: 'label',
    type: 'input',
    span: 24,
    required: true,
    props: {
      maxLength: 100
    }
  }
]

// 动态字段配置
const dynamicColumns = computed<ColumnItem[]>(() => {
  const columns = [...baseColumns]

  // 根据字段类型添加相应的配置字段
  switch (form.type) {
    case 'text-input':
    case 'paragraph':
      // 文本和段落类型显示最大长度
      columns.push({
        label: '最大长度',
        field: 'max_length',
        type: 'input-number',
        span: 24,
        required: true,
        props: {
          min: 1,
          max: form.type === 'text-input' ? 256 : 4000,
          placeholder: '请输入最大长度'
        }
      })
      break

    case 'select':
      // 下拉选项类型显示选项配置
      columns.push({
        label: '选项配置',
        field: 'options',
        type: 'input-tag',
        span: 24,
        required: true,
        props: {
          placeholder: '请输入选项，按回车添加',
          allowClear: true,
          uniqueValue: true
        }
      })
      break

    case 'number':
      // 数字类型显示数值范围
      columns.push(
        {
          label: '最小值',
          field: 'min_value',
          type: 'input-number',
          span: 12,
          props: {
            placeholder: '请输入最小值'
          }
        },
        {
          label: '最大值',
          field: 'max_value',
          type: 'input-number',
          span: 12,
          props: {
            placeholder: '请输入最大值'
          }
        }
      )
      break

    case 'file':
    case 'file-list':
      // 文件类型显示文件配置
      columns.push(
        {
          label: '文件类型',
          field: 'file_types',
          type: 'checkbox-group',
          span: 24,
          props: {
            options: [
              { label: '图片', value: 'image' },
              { label: '文档', value: 'document' },
              { label: '音频', value: 'audio' },
              { label: '视频', value: 'video' }
            ]
          }
        },
        {
          label: '最大文件大小(MB)',
          field: 'max_file_size',
          type: 'input-number',
          span: 24,
          props: {
            min: 1,
            max: 100,
            placeholder: '请输入最大文件大小'
          }
        }
      )

      if (form.type === 'file-list') {
        columns.push({
          label: '最大文件数量',
          field: 'max_length',
          type: 'input-number',
          span: 24,
          props: {
            min: 1,
            max: 10,
            placeholder: '请输入最大文件数量'
          }
        })
      }
      break
  }

  // 添加必填选项
  columns.push({
    label: '',
    field: 'required',
    type: 'checkbox-group',
    span: 24,
    props: {
      type: 'checkbox',
      options: [{ label: '必填', value: 'false' }]
    }
  })

  return columns
})

// 监听字段类型变化，重置相关字段
watch(
  () => form.type,
  (newType, oldType) => {
    if (newType !== oldType) {
      // 重置类型相关的字段
      switch (oldType) {
        case 'text-input':
        case 'paragraph':
          form.max_length = 48
          break
        case 'select':
          form.options = []
          break
        case 'number':
          form.min_value = undefined
          form.max_value = undefined
          break
        case 'file':
        case 'file-list':
          form.file_types = []
          form.max_file_size = 10
          if (oldType === 'file-list') {
            form.max_length = 48
          }
          break
      }

      // 设置新类型的默认值
      switch (newType) {
        case 'text-input':
          form.max_length = 48
          break
        case 'paragraph':
          form.max_length = 1000
          break
        case 'select':
          form.options = []
          break
        case 'number':
          form.min_value = undefined
          form.max_value = undefined
          break
        case 'file':
          form.file_types = []
          form.max_file_size = 10
          break
        case 'file-list':
          form.file_types = []
          form.max_file_size = 10
          form.max_length = 5
          break
      }
    }
  }
)

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 验证表单数据
const validateForm = () => {
  // 基础验证
  if (!form.variable.trim()) {
    Message.error('请输入变量名称')
    return false
  }

  if (!form.label.trim()) {
    Message.error('请输入显示名称')
    return false
  }

  // 根据类型进行特殊验证
  switch (form.type) {
    case 'text-input':
    case 'paragraph':
      if (!form.max_length || form.max_length <= 0) {
        Message.error('请输入有效的最大长度')
        return false
      }
      break

    case 'select':
      if (!form.options || form.options.length === 0) {
        Message.error('请至少添加一个选项')
        return false
      }
      // 检查选项是否有重复
      const uniqueOptions = new Set(form.options)
      if (uniqueOptions.size !== form.options.length) {
        Message.error('选项不能重复')
        return false
      }
      break

    case 'number':
      if (form.min_value !== undefined && form.max_value !== undefined && form.min_value >= form.max_value) {
        Message.error('最小值必须小于最大值')
        return false
      }
      break

    case 'file':
    case 'file-list':
      if (!form.file_types || form.file_types.length === 0) {
        Message.error('请至少选择一种文件类型')
        return false
      }
      if (!form.max_file_size || form.max_file_size <= 0) {
        Message.error('请输入有效的最大文件大小')
        return false
      }
      if (form.type === 'file-list' && (!form.max_length || form.max_length <= 0)) {
        Message.error('请输入有效的最大文件数量')
        return false
      }
      break
  }

  return true
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false

    // 自定义验证
    if (!validateForm()) return false

    console.log('保存的表单数据:', form)
    Message.success('新建成功')
    emit('save-success', form)
    return true
  } catch (error) {
    console.error('保存失败:', error)
    Message.error('保存失败，请重试')
    return false
  }
}

// 新增
const onAdd = () => {
  reset()
  visible.value = true
}

defineExpose({ onAdd })
</script>

<style scoped lang="scss">
.modal-form-container {
  // 确保表单容器有足够的空间
  min-height: 200px;

  // 优化表单项间距
  :deep(.arco-form-item) {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  // 优化网格布局
  :deep(.arco-grid) {
    gap: 8px;
  }

  // 优化输入框样式
  :deep(.arco-input),
  :deep(.arco-input-number),
  :deep(.arco-select),
  :deep(.arco-textarea) {
    width: 100%;
  }

  // 优化单选框组样式
  :deep(.arco-radio-group) {
    width: 100%;

    .arco-radio {
      margin-right: 16px;
      margin-bottom: 8px;
    }
  }

  // 优化复选框组样式
  :deep(.arco-checkbox-group) {
    width: 100%;

    .arco-checkbox {
      margin-right: 16px;
      margin-bottom: 8px;
    }
  }

  // 优化标签输入框样式
  :deep(.arco-input-tag) {
    width: 100%;
    min-height: 40px;
  }
}

// 自定义滚动条样式
:deep(.arco-modal-body) {
  // Webkit浏览器滚动条样式
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }

  // Firefox滚动条样式
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}
</style>
