<template>
  <div class="status-box">
    <div v-if="isRunning && !outputs" class="start-header">
      <a-alert type="normal">
        工作流
        <template #icon>
          <a-spin>
            <template #icon>
              <icon-sync />
            </template>
          </a-spin>
        </template>
      </a-alert>
    </div>
    <div v-else>
      <a-alert v-if="status === 'succeeded'" type="success">工作流</a-alert>
      <a-alert v-else-if="status === 'failed'" type="error">工作流</a-alert>
      <a-alert v-else type="warning">工作流</a-alert>
    </div>
    <div v-if="resultTabActive">
      <Markdown
        :resultText="resultText"
        :inputs="JSON.stringify(outputs || {})"
        :outputs="JSON.stringify(outputs || {})"
      />
    </div>
    <div v-else>
      <Code v-if="outputs" :title="'输出'" :inputs="JSON.stringify(outputs || {})" />
    </div>

    <div v-if="!isRunning && error" class="mt-4 flex-1 w-full">
      <a-alert :show-icon="false" type="error">
        <div class="p-3">{{ props.error }}</div>
      </a-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import Code from './code.vue'
import Markdown from './markdown.vue'
defineOptions({
  name: 'ResultText'
})
const props = withDefaults(
  defineProps<{
    isRunning?: boolean
    outputs?: any
    error?: string
    onClick?: () => void
    allFiles?: any[]
    status: string
    resultText: string
    resultTabActive?: boolean
  }>(),
  {}
)
</script>

<style scoped lang="scss">
.status-box {
  max-width: 650px;
  padding: 12px;
}

:deep(.arco-spin) {
  display: block;

  .arco-spin-icon {
    display: flex;

    svg {
      color: #5856d6 !important;
    }
  }
}

:deep(.arco-alert-with-title) {
  align-items: center;
}

.arco-alert-icon {
  display: flex;
}

:deep(.arco-alert) {
  padding: 5px;
  border-radius: 10px;
}
</style>
