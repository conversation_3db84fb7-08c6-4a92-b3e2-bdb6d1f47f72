<template>
  <AiPageLayout>
    <a-tabs v-model:active-key="tabsKey" justify size="large" @change="tableChange">
      <template #extra>
        <a-space>
          <a-checkbox v-model="params.is_created_by_me" @change="search">我创建的</a-checkbox>
          <a-select
            v-model="tagValue"
            style="min-width: 160px"
            placeholder="全部标签"
            multiple
            :max-tag-count="1"
            @change="tagChange"
          >
            <template #label="{ data }">
              <span>
                <icon-tag style="margin-right: 4px" />
                {{ data?.label }}
              </span>
            </template>
            <a-option v-for="item in tags" :key="item.id" :value="item.id">{{ item.name }}</a-option>
          </a-select>
          <a-input-search v-model="params.name" placeholder="搜索名称/描述" allow-clear @search="search" />
          <a-button v-if="tabsKey === '1'" type="primary" @click="onAdd()">
            <template #icon><icon-plus /></template>
            <template #default>新建应用</template>
          </a-button>
        </a-space>
      </template>
      <a-tab-pane key="1" title="我的">
        <AppCard :tags="tags" :appListData="appListData" @operate-success="getAppListFn" />
      </a-tab-pane>
      <a-tab-pane key="2" title="分享">
        <AppCard />
      </a-tab-pane>
    </a-tabs>
    <AppAddModal ref="AppAddModalRef" @save-success="search" />
  </AiPageLayout>
</template>

<script setup lang="ts">
import AppCard from './AppCard.vue'
import AppAddModal from './AppAddModal.vue'

import { getAppList as getAppListApi, getTags } from '@/apis'

defineOptions({ name: 'AppsManage' })

const router = useRouter()

const tabsKey = ref<string>('1')
const appListData = ref()
const tags = ref()
const tagValue = ref()
const params = reactive<any>({
  page: '1',
  limit: '30',
  name: '',
  is_created_by_me: false
})
const getAppListFn = async () => {
  const res = await getAppListApi(params)
  appListData.value = res
}
const tagsParams = reactive<any>({
  type: 'app'
})
const getTagsFn = async () => {
  const res = await getTags(tagsParams)
  tags.value = res
}

onMounted(() => {
  getAppListFn()
  getTagsFn()
})

// 搜索
const search = () => {
  getAppListFn()
}
const tagChange = (e) => {
  if (e && e.length > 0) {
    params.tag_ids = e.join(',')
  } else {
    delete params.tag_ids
  }
  getAppListFn()
}

const tableChange = (key) => {
  router.replace({ path: '/appsManage/index', query: { tab: key } })
}
const AppAddModalRef = ref<InstanceType<typeof AppAddModal>>()
// 新增
const onAdd = () => {
  AppAddModalRef.value?.onAdd()
}
</script>

<style scoped lang="scss">
:deep(.arco-select-view-tag) {
  border: none !important;
  background-color: var(--color-fill-2) !important;
  border-radius: 5px !important;
}
</style>
