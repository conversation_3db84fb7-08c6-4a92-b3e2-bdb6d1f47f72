import { useVueFlow } from '@vue-flow/core'
import { useWorkflowStore } from '@/stores'
import { NodeRunningStatus, type LoopStartedResponse } from '@/views/app/workflow/types/workflow'

export const useWorkflowNodeLoopStarted = () => {
  const workflowStore = useWorkflowStore()
  const { nodes, setNodes, edges, setEdges } = useVueFlow()

  const handleWorkflowNodeLoopStarted = (params: LoopStartedResponse) => {
    const { data } = params
    const { workflowRunningData, setWorkflowRunningData } = workflowStore

    const workflowData = workflowRunningData
    workflowData?.tracing.push({
      ...data,
      status: NodeRunningStatus.Running
    })
    setWorkflowRunningData(workflowData)

    if (nodes.value?.length) {
      const newNodes = nodes.value.map((node) => {
        if (node.id === data.node_id) {
          node.data._runningStatus = NodeRunningStatus.Running
          node.data._loopLength = data.metadata.loop_length
          node.data._waitingRun = false
        }
        return node
      })
      setNodes(newNodes)
    }

    if (edges.value) {
      const newEdges = edges.value.map((edge) => {
        if (edge.target === data.node_id) {
          edge.data = {
            ...edge.data,
            _sourceRunningStatus: nodes.value?.find((node) => node.id === edge.source)!.data._runningStatus,
            _targetRunningStatus: NodeRunningStatus.Running,
            _waitingRun: false
          }
        }
        return edge
      })

      setEdges(newEdges)
    }
  }

  return {
    handleWorkflowNodeLoopStarted
  }
}
