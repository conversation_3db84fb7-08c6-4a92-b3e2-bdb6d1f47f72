<template>
  <div class="space-y-1">
    <!--输入变量-->
    <InputVar :nodeInfo="nodeInfo" :nodeId="nodeId" />

    <a-divider />

    <!--代码编辑器-->
    <Field>
      <div>
        <!--语言：切换语言的时候，还会转格式?暂时没有做切换-->
        <div class="flex items-center justify-between pl-3 pr-2 pt-1">
          <div class="system-xs-semibold-uppercase text-text-secondary hover:bg-state-base-hover text-lg">
            {{ lang }}
          </div>
        </div>
        <BasicEditor :nodeInfo="nodeInfo" />
      </div>
    </Field>

    <a-divider />

    <!--输出变量-->
    <!--<OutputVarCustom :nodeInfo="nodeInfo"></OutputVarCustom>-->
    <OutputVarByCode :nodeInfo="nodeInfo" />

    <a-divider />

    <!--retryConfig-->
    <RetryConfig :nodeInfo="nodeInfo" />

    <!--异常处理-->
    <ErrorStrategy :nodeInfo="nodeInfo" />
  </div>
</template>

<script setup lang="ts">
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'
import OutputVar from '@/views/app/workflow/nodes/http/components/OutputVar.vue'
import OutputVarCustom from '@/views/app/workflow/nodes/http/components/OutputVarCustom.vue'
import ErrorStrategy from '@/views/app/workflow/nodes/http/components/ErrorStrategy.vue'
import RetryConfig from '@/views/app/workflow/nodes/http/components/RetryConfig.vue'
import InputVar from '@/views/app/workflow/nodes/code/components/InputVar.vue'
import BasicEditor from '@/views/app/workflow/nodes/code/components/BasicEditor.vue'
import OutputVarByCode from '@/views/app/workflow/nodes/code/components/OutputVarByCode.vue'

interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
}

const props = defineProps<{
  list: FieldType[]
  isChatMode?: false
  nodeInfo?: any
  nodeId: string
}>()
// const props = defineProps({
//   list: FieldType[],
//   isChatMode: false
// })
const defaultList = [
  {
    label: '',
    required: false,
    readonly: true,
    type: 'string',
    variable: 'sys.user_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'array[file]',
    variable: 'sys.files'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.app_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_run_id'
  }
]
const isChatModeDefaultList = [
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.query'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.dialogue_count'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.conversation_id'
  }
]
const processedList = computed(() => {
  if (props.isChatMode) {
  }
  return [
    ...props.list.map((item) => {
      const processed = {
        ...item,
        readonly: false
      }
      return processed
    }),
    ...defaultList,
    ...(props.isChatMode ? isChatModeDefaultList : [])
  ]
})

const lang = computed(() => {
  const lang = props.nodeInfo.code_language
  return lang.toUpperCase()
})

onMounted(() => {
  console.log(
    props.list.map((e) => {
      console.log(e)
    })
  )
})
</script>
<style scoped lang="scss"></style>
