<template>
  <a-popover :content-style="{ width: '520px' }" trigger="click" position="lt">
    <!--触发元素-->
    <div v-if="hasTriggerSlot">
      <slot name="trigger" />
    </div>
    <!--默认trigger-->
    <div v-else>
      <Trigger />
    </div>
    <template #content>
      <Field fieldTitle="模型">
        <ModelSelector />
      </Field>

      <Field fieldTitle="参数">
        <LlmParams :paramsList="paramsList" :completion_params="completion_params" :customForm="customForm" />
      </Field>
    </template>
  </a-popover>
</template>
<script setup lang="ts">
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'
import { useProviderStore } from '@/stores/modules/workflow/provider'
import Trigger from '@/views/app/configuration/components/model-parameter-modal/Trigger.vue'
import ModelSelector from '@/views/app/configuration/components/model-selector/ModelSelector.vue'
import LlmParams from '@/views/app/configuration/components/model-selector/LlmParams.vue'

const slots = useSlots()

// 判断slot是否存在
const hasTriggerSlot = !!slots.trigger

const props = withDefaults(
  defineProps<{
    provider?: string // provider,eg:langgenius/openai/openai
    modelId?: string // model,eg:gpt-3.5-turbo
  }>(),
  {
    provider: '',
    modelId: ''
  }
)

const providerStore = useProviderStore()
const paramsList = computed(() => {
  return providerStore.provider.paramsList
})
const completion_params = computed(() => {
  return providerStore.provider.completion_params
})
const customForm = computed(() => {
  const paramsList = providerStore.provider.paramsList
  const completion_params = providerStore.provider.completion_params
  Object.keys(completion_params).forEach((key) => {
    if (paramsList.some((v) => v.name === key)) {
      const current = paramsList.find((v) => v.name === key)
      current.isChecked = true
    }
  })
  return paramsList
})
onMounted(() => {

})
</script>
<style scoped lang="scss">

</style>
