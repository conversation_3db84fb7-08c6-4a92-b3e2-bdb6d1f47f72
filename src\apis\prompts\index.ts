import http from '@/utils/http'
import type * as DataContracts from './type'

// prompt
/**
 * @name saveUsingPost
 * @summary 新增提示词
 * @request POST:/prompt
 */
export function saveUsingPost(params: DataContracts.PromptSaveDTO) {
  const url = `/mock/prompt`

  return http.post<DataContracts.RPrompt>(url, params)
}

/**
 * @name updateUsingPut
 * @summary 修改提示词
 * @request PUT:/prompt
 */
export function updateUsingPut(params: DataContracts.PromptUpdateDTO) {
  const url = `/mock/prompt`

  return http.put<DataContracts.RPrompt>(url, params)
}

/**
 * @name deleteUsingDelete
 * @summary 删除提示词
 * @request DELETE:/prompt
 */
export function deleteUsingDelete(params: string[]) {
  const url = `/mock/prompt`

  return http.del<DataContracts.RBoolean>(url, params)
}

/**
 * @name updateAllUsingPut
 * @summary 修改所有字段
 * @request PUT:/prompt/all
 */
export function updateAllUsingPut(params: DataContracts.Prompt) {
  const url = `/mock/prompt/all`

  return http.put<DataContracts.RPrompt>(url, params)
}

/**
 * @name pageUsingPost
 * @summary 分页列表查询
 * @request POST:/prompt/page
 */
export function pageUsingPost(params: DataContracts.PageParamsPromptPageQuery) {
  const url = `/mock/prompt/page`

  return http.post<DataContracts.RIPagePrompt>(url, params)
}

/**
 * @name queryUsingPost
 * @summary 批量查询
 * @request POST:/prompt/query
 */
export function queryUsingPost(params: DataContracts.Prompt) {
  const url = `/mock/prompt/query`

  return http.post<DataContracts.RListPrompt>(url, params)
}

/**
 * @name getUsingGet
 * @summary 单体查询
 * @request GET:/prompt/{id}
 */
export function getUsingGet(id: string) {
  const url = `/mock/prompt/${id}`

  return http.get<DataContracts.RPrompt>(url)
}
