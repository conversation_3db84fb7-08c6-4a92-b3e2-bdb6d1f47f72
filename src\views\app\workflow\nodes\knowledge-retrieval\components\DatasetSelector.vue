<template>
  <div class="dataset-selector">
    <!-- 搜索框 -->
    <div class="search-section">
      <a-input v-model="searchText" placeholder="搜索知识库" allow-clear>
        <template #prefix>
          <icon-search />
        </template>
      </a-input>
    </div>

    <!-- 知识库列表 -->
    <div class="dataset-list">
      <div v-if="loading" class="loading-state">
        <a-spin />
        <span>加载中...</span>
      </div>

      <div v-else-if="filteredDatasets.length === 0" class="empty-state">
        <div class="empty-text">没有找到知识库</div>
        <div class="empty-desc">请尝试调整搜索条件</div>
      </div>

      <div v-else class="dataset-items">
        <div
          v-for="dataset in filteredDatasets"
          :key="dataset.id"
          class="dataset-item"
          :class="{
            selected: selectedDatasets.includes(dataset.id),
            disabled: excludedIds.includes(dataset.id)
          }"
          @click="toggleDataset(dataset.id)"
        >
          <div class="dataset-checkbox">
            <a-checkbox
              :model-value="selectedDatasets.includes(dataset.id)"
              :disabled="excludedIds.includes(dataset.id)"
              @change="() => toggleDataset(dataset.id)"
            />
          </div>

          <div class="dataset-icon">
            <icon-book />
          </div>

          <div class="dataset-info">
            <div class="dataset-header">
              <div class="dataset-name">{{ dataset.name }}</div>
              <div class="dataset-status">
                <a-tag :color="getStatusColor(dataset.status)" size="small">
                  {{ getStatusText(dataset.status) }}
                </a-tag>
              </div>
            </div>

            <div class="dataset-description">
              {{ dataset.description || '暂无描述' }}
            </div>

            <div class="dataset-meta">
              <span class="meta-item">
                <icon-file />
                {{ dataset.document_count || 0 }} 个文档
              </span>
              <span class="meta-item">
                <icon-clock-circle />
                {{ formatDate(dataset.updated_at) }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 选择统计 -->
    <div v-if="selectedDatasets.length > 0" class="selection-summary">
      已选择 {{ selectedDatasets.length }} 个知识库
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { getDatasetList } from '@/apis'

const props = withDefaults(
  defineProps<{
    modelValue?: string[]
    excludedIds?: string[]
  }>(),
  {
    modelValue: () => [],
    excludedIds: () => []
  }
)

const emit = defineEmits<{
  (e: 'update:modelValue', value: string[]): void
  (e: 'change', value: string[]): void
}>()

const loading = ref(false)
const searchText = ref('')
const datasets = ref([])
const selectedDatasets = ref<string[]>([])

const filteredDatasets = computed(() => {
  if (!searchText.value) return datasets.value

  const searchLower = searchText.value.toLowerCase()
  return datasets.value.filter(
    (dataset) =>
      dataset.name.toLowerCase().includes(searchLower) ||
      (dataset.description && dataset.description.toLowerCase().includes(searchLower))
  )
})

onMounted(async () => {
  selectedDatasets.value = [...props.modelValue]
  await loadDatasets()
})

const loadDatasets = async () => {
  try {
    loading.value = true
    // 调用真实的API获取知识库列表
    const response = await getDatasetList({
      page: 1,
      limit: 100,
      status: 'available' // 只加载可用的知识库
    })
    datasets.value = response.data || []
  } catch (error) {
    Message.error('加载知识库列表失败')
    // 如果API失败，提供模拟数据作为后备
    datasets.value = [
      {
        id: '1',
        name: '产品知识库',
        description: '包含产品相关的所有文档和说明',
        status: 'available',
        document_count: 156,
        updated_at: '2024-01-15T10:30:00Z',
        doc_metadata: [
          { name: 'category', type: 'string', description: '文档分类' },
          { name: 'priority', type: 'number', description: '优先级' }
        ]
      },
      {
        id: '2',
        name: '技术文档库',
        description: '技术开发相关的文档和API说明',
        status: 'available',
        document_count: 89,
        updated_at: '2024-01-14T15:20:00Z',
        doc_metadata: [
          { name: 'version', type: 'string', description: '版本号' },
          { name: 'module', type: 'string', description: '模块名称' }
        ]
      },
      {
        id: '3',
        name: '客服FAQ',
        description: '常见问题和解答',
        status: 'processing',
        document_count: 45,
        updated_at: '2024-01-13T09:15:00Z',
        doc_metadata: [
          { name: 'topic', type: 'string', description: '话题' },
          { name: 'difficulty', type: 'number', description: '难度等级' }
        ]
      }
    ]
  } finally {
    loading.value = false
  }
}

const toggleDataset = (datasetId: string) => {
  if (props.excludedIds.includes(datasetId)) return

  const index = selectedDatasets.value.indexOf(datasetId)
  if (index > -1) {
    selectedDatasets.value.splice(index, 1)
  } else {
    selectedDatasets.value.push(datasetId)
  }

  emit('update:modelValue', selectedDatasets.value)
  emit('change', selectedDatasets.value)
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'available':
      return 'green'
    case 'processing':
      return 'blue'
    case 'error':
      return 'red'
    default:
      return 'gray'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'available':
      return '可用'
    case 'processing':
      return '处理中'
    case 'error':
      return '错误'
    default:
      return '未知'
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return '未知'

  const date = new Date(dateString)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (days === 0) return '今天'
  if (days === 1) return '昨天'
  if (days < 7) return `${days}天前`
  if (days < 30) return `${Math.floor(days / 7)}周前`
  if (days < 365) return `${Math.floor(days / 30)}个月前`
  return `${Math.floor(days / 365)}年前`
}
</script>

<style scoped lang="scss">
.dataset-selector {
  .search-section {
    margin-bottom: 16px;
  }

  .dataset-list {
    max-height: 400px;
    overflow-y: auto;

    .loading-state {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px;
      color: var(--color-text-3);

      .arco-spin {
        margin-right: 8px;
      }
    }

    .empty-state {
      text-align: center;
      padding: 40px;
      color: var(--color-text-3);

      .empty-text {
        font-size: 14px;
        margin-bottom: 4px;
      }

      .empty-desc {
        font-size: 12px;
      }
    }

    .dataset-items {
      .dataset-item {
        display: flex;
        align-items: flex-start;
        padding: 12px;
        margin-bottom: 8px;
        border: 1px solid var(--color-border-2);
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;

        &:hover:not(.disabled) {
          border-color: var(--color-primary-light-4);
          background-color: var(--color-primary-light-1);
        }

        &.selected {
          border-color: var(--color-primary);
          background-color: var(--color-primary-light-1);
        }

        &.disabled {
          opacity: 0.5;
          cursor: not-allowed;
          background-color: var(--color-bg-3);
        }

        .dataset-checkbox {
          margin-right: 12px;
          margin-top: 2px;
        }

        .dataset-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          background-color: var(--color-primary-light-1);
          border-radius: 6px;
          margin-right: 12px;
          margin-top: 2px;

          .arco-icon {
            color: var(--color-primary);
            font-size: 16px;
          }
        }

        .dataset-info {
          flex: 1;

          .dataset-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;

            .dataset-name {
              font-size: 14px;
              font-weight: 500;
              color: var(--color-text-1);
            }

            .dataset-status {
              .arco-tag {
                margin: 0;
              }
            }
          }

          .dataset-description {
            font-size: 12px;
            color: var(--color-text-3);
            line-height: 1.4;
            margin-bottom: 8px;
          }

          .dataset-meta {
            display: flex;
            gap: 16px;

            .meta-item {
              display: flex;
              align-items: center;
              font-size: 11px;
              color: var(--color-text-4);

              .arco-icon {
                margin-right: 4px;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }

  .selection-summary {
    margin-top: 16px;
    padding: 8px 12px;
    background-color: var(--color-primary-light-1);
    border: 1px solid var(--color-primary-light-4);
    border-radius: 4px;
    font-size: 12px;
    color: var(--color-primary);
    text-align: center;
  }
}
</style>
