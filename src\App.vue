<template>
  <a-config-provider update-at-scroll>
    <template #loading>
      <AiSvgIcon name="common-loading-bold" :size="30" class="loading-icon" spin />
    </template>
    <a-watermark
      :content="appStore.isOpenWatermark ? appStore.watermark || `${userStore.userInfo.name}` : ''"
      class="admin-ui-main"
    >
      <router-view />
    </a-watermark>
  </a-config-provider>
</template>

<script setup lang="ts">
import { useAppStore, useUserStore } from '@/stores'

defineOptions({ name: 'App' })
const userStore = useUserStore()
const appStore = useAppStore()
appStore.initTheme()
appStore.initSiteConfig()
</script>

<style scoped lang="scss">
.loading-icon {
  animation: arco-loading-circle 1s infinite cubic-bezier(0, 0, 1, 1);
}

:deep(.arco-empty-image) img {
  height: 60px;
}

.admin-ui-main {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}
</style>
