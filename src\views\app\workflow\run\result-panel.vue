<template>
  <div class="bg-components-panel-bg">
    <div>
      <StatusPanel
        :status="props.status"
        :time="props.elapsed_time"
        :tokens="props.total_tokens"
        :error="props.error"
        :exceptionCounts="props.exceptionCounts"
      />
      <Code v-if="props.inputs" class="mb-4 mt-4" :title="'输入'" :inputs="JSON.stringify(props.inputs || {})" />
      <Code v-if="props.inputs" class="mb-4" :title="'输出'" :inputs="JSON.stringify(props.outputs || {})" />
      <StatusInfo
        :status="props.status"
        :executor="props.created_by"
        :startTime="props.created_at"
        :time="props.elapsed_time"
        :showSteps="props.showSteps"
        :tokens="props.total_tokens"
        :error="props.error"
        :steps="props.steps"
        :exceptionCounts="props.exceptionCounts"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useWorkflowStore } from '@/stores'
import StatusPanel from './status.vue'
import StatusInfo from './status-info.vue'
import Code from './code.vue'

import type { AgentLogItemWithChildren, NodeTracing } from '@/views/app/workflow/types/workflow'
defineOptions({
  name: 'ResultPanel'
})

const props = defineProps<{
  nodeInfo?: NodeTracing
  inputs?: object
  process_data?: string
  outputs?: any
  status: string
  error?: string
  elapsed_time?: number
  total_tokens?: number
  created_at?: number
  created_by?: string
  finished_at?: number
  steps?: number
  showSteps?: boolean
  exceptionCounts?: number
  execution_metadata?: any
  handleShowIterationResultList?: (detail: NodeTracing[][], iterDurationMap: any) => void
  handleShowLoopResultList?: (detail: NodeTracing[][], loopDurationMap: any) => void
  onShowRetryDetail?: (detail: NodeTracing[]) => void
  handleShowAgentOrToolLog?: (detail?: AgentLogItemWithChildren) => void
}>()

const workflow = useWorkflowStore()
const { workflowRunningData } = workflow
</script>

<style scoped lang="scss"></style>
