<template>
  <div class="dataset-tab">
    <AiTable
      row-key="id"
      :data="dataList || []"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%' }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['name']"
      @refresh="search"
    >
      <template #toolbar-left>
        <a-space>
          <a-input-search
            v-model="queryForm.name"
            placeholder="搜索名称"
            allow-clear
            @search="search"
            @press-enter="search"
          />
          <a-select v-model="queryForm.type" placeholder="应用类型" allow-clear @change="search">
            <a-option :value="1">对话类</a-option>
            <a-option :value="2">工作流类</a-option>
          </a-select>
          <a-select v-model="queryForm.status" placeholder="状态" allow-clear @change="search">
            <a-option :value="1">草稿</a-option>
            <a-option :value="2">已发布</a-option>
          </a-select>
          <a-button @click="reset">
            <template #icon><icon-refresh /></template>
            <template #default>重置</template>
          </a-button>
        </a-space>
      </template>
      <template #toolbar-right>
        <a-dropdown :popup-max-height="false" @select="addEvaluation">
          <a-button type="primary">
            <a-space>
              <icon-plus />
              创建
              <icon-down />
            </a-space>
          </a-button>
          <template #content>
            <a-doption :value="2">工作流类</a-doption>
            <a-doption :value="1">对话类</a-doption>
          </template>
        </a-dropdown>
      </template>
      <template #type="{ record }">
        <span>{{ record?.type === 1 ? '对话类' : '工作流类' }}</span>
      </template>
      <template #status="{ record }">
        <a-tag v-if="record.status === 1" color="orange">草稿</a-tag>
        <a-tag v-else color="green">已发布</a-tag>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link v-if="record.status === 1" title="发布" @click="onPublish(record)">发布</a-link>
          <a-link title="修改" @click="onDetail(record)">查看</a-link>
          <a-link status="danger" title="删除" @click="onDelete(record)">删除</a-link>
        </a-space>
      </template>
    </AiTable>
    <AddModal
      v-if="modalVisible"
      :modalVisible="modalVisible"
      :evaluationType="evaluationType"
      @save-success="search"
      @close="close"
    />
    <DetailDrawer ref="DatasetDetailDrawerRef" :evaluationId="evaluationId" />
  </div>
</template>

<script setup lang="ts">
import type { TableInstance } from '@arco-design/web-vue'
import { Message } from '@arco-design/web-vue'
import { useTable } from '@/hooks'
import AddModal from './AddModal.vue'
import DetailDrawer from './DetailDrawer.vue'
import { getEvaluationSetsPage, deleteEvaluationSets } from '@/apis/evaluation/evaluation-sets'
import { EvalDatasetPageQuery, RIPageEvalDataset } from '@/apis/evaluation/evaluation-sets-type'

defineOptions({ name: 'EvaluationList' })

const queryForm = reactive<EvalDatasetPageQuery>({})
const evaluationType = ref<number>()
const modalVisible = ref<boolean>(false)
const evaluationId = ref<string>('')
const {
  tableData: dataList,
  loading,
  pagination,
  search,
  handleDelete
} = useTable<RIPageEvalDataset>((page) => getEvaluationSetsPage({ ...page, model: queryForm }), { immediate: true })

const columns: TableInstance['columns'] = [
  { title: '评测集名称', dataIndex: 'name', ellipsis: true, tooltip: true },
  { title: '评测集类型', dataIndex: 'type', slotName: 'type', width: 120 },
  { title: '状态', dataIndex: 'status', slotName: 'status', width: 100 },
  { title: '创建时间', dataIndex: 'createTime', width: 180 },
  // { title: '创建人', dataIndex: 'createdBy', ellipsis: true, tooltip: true },
  { title: '描述', dataIndex: 'description', ellipsis: true, tooltip: true },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 120,
    align: 'left'
  }
]

// 重置
const reset = () => {
  queryForm.name = undefined
  queryForm.type = undefined
  queryForm.status = undefined
  search()
}

// 删除
const onDelete = (record) => {
  return handleDelete(() => deleteEvaluationSets([record.id]), {
    content: '是否确定删除评测集【' + record.name + '】？',
    showModal: true
  })
}

// 发布
const onPublish = async (record) => {
  try {
    Message.success('发布成功')
  } catch (error) {
    // 错误处理
  }
}

// 新增
const addEvaluation = (value: number) => {
  evaluationType.value = value
  modalVisible.value = true
}

// 关闭
const close = () => {
  modalVisible.value = false
  evaluationType.value = undefined
}

// 修改
const onUpdate = (record) => {}

const DatasetDetailDrawerRef = ref<InstanceType<typeof DetailDrawer>>()
// 查看
const onDetail = (record) => {
  evaluationId.value = record.id
  nextTick(() => {
    DatasetDetailDrawerRef.value?.onOpen(record.id)
  })
}
</script>

<style scoped lang="scss">
.dataset-tab {
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
</style>
