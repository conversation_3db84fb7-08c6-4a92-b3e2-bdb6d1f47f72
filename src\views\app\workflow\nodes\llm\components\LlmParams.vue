<template>
  <div>
    <div v-for="(paramsItem, index) in paramsList" :key="index" class="retry-item">
      <div class="retry-label">
        <a-switch
          :default-checked="Object.keys(nodeInfo.model.completion_params).some((v) => v === paramsItem.name)"
          :size="'small'"
          type="round"
          @change="handleChangeLlmParams(paramsItem, index, $event)"
        />
        {{ paramsItem?.label?.zh_Hans }}

        <a-tooltip :content="paramsItem.help?.zh_<PERSON>">
          <icon-info-circle />
        </a-tooltip>
      </div>
      <div class="retry-value">
        <template v-if="Object.keys(nodeInfo.model.completion_params).some((v) => v === paramsItem.name)">
          <template v-if="paramsItem.type === 'int' || paramsItem.type === 'float'">
            <a-input-number
              v-model="nodeInfo.model.completion_params[paramsItem.name]"
              :precision="paramsItem.precision"
              :step="paramsItem.max ? (Math.floor(paramsItem.max / 10) > 1 ? 1 : 0.1) : 1"
              :min="paramsItem.min ? paramsItem.min : -Infinity"
              :max="paramsItem.max ? paramsItem.max : Infinity"
              placeholder="请输入"
              style="width: 100px"
            >
              <!--<template #suffix>毫秒</template>-->
            </a-input-number>
          </template>

          <template v-if="paramsItem.type === 'string'">
            <a-select v-model="nodeInfo.model.completion_params[paramsItem.name]" style="width: 100px">
              <a-option v-for="item in paramsItem.options" :key="item" :value="item">{{ item }}</a-option>
            </a-select>
          </template>
        </template>
        <template v-else>
          <template v-if="paramsItem.type === 'int' || paramsItem.type === 'float'">
            <a-input-number
              v-model="paramsItem.default"
              :precision="paramsItem.precision"
              :step="paramsItem.max ? (Math.floor(paramsItem.max / 10) > 1 ? 1 : 0.1) : 1"
              :min="paramsItem.min ? paramsItem.min : -Infinity"
              :max="paramsItem.max ? paramsItem.max : Infinity"
              placeholder="请输入"
              style="width: 100px"
            >
              <!--<template #suffix>毫秒</template>-->
            </a-input-number>
          </template>

          <template v-if="paramsItem.type === 'string'">
            <a-select v-model="paramsItem.default" style="width: 100px">
              <a-option v-for="item in paramsItem.options" :key="item" :value="item">{{ item }}</a-option>
            </a-select>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps(['paramsList', 'nodeInfo'])

const handleChangeLlmParams = (item, index, event) => {
  if (event) {
    props.nodeInfo.model.completion_params[item.name] = item.default
  } else {
    delete props.nodeInfo.model.completion_params[item.name]
  }
}
</script>

<style scoped lang="scss">
.retry-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
}
</style>
