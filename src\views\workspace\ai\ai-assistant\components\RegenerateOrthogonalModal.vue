<template>
  <a-modal
    v-model:visible="visible"
    title="重新生成正交组合"
    :width="500"
    :mask-closable="false"
    :esc-to-close="false"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form :model="form" layout="vertical">
      <a-form-item field="categories" label="类别" required>
        <a-select v-model="form.categories" multiple placeholder="请选择类别" style="width: 100%">
          <a-option v-for="category in availableCategories" :key="category" :label="category" :value="category" />
        </a-select>
      </a-form-item>
      <a-form-item label="重新生成全部">
        <a-switch v-model="form.regenerateAll" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { regenerateOrthogonal, type RegenerateParams } from '@/apis/ai-assistant'

// Props 定义
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  taskId: {
    type: String,
    default: ''
  },
  categories: {
    type: Array as () => string[],
    default: () => []
  },
  errorCategories: {
    type: Array as () => string[],
    default: () => []
  }
})

// Emits 定义
const emit = defineEmits(['update:modelValue', 'success'])

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 可用类别
const availableCategories = computed(() => props.categories)

// 表单数据
const form = reactive<RegenerateParams>({
  categories: [],
  regenerateAll: false
})

// 提交状态
const submitting = ref(false)

// 重置表单
const resetForm = () => {
  form.categories = []
  form.regenerateAll = false
}

// 初始化表单数据
const initializeForm = () => {
  form.categories = [...props.errorCategories]
  form.regenerateAll = false
}

// 处理取消
const handleCancel = () => {
  visible.value = false
  resetForm()
}

// 处理提交
const handleSubmit = async () => {
  if (!props.taskId) {
    Message.error('任务ID不能为空')
    return
  }

  try {
    submitting.value = true
    await regenerateOrthogonal(props.taskId, form)
    Message.success('重新生成任务已创建')
    visible.value = false
    emit('success')
    resetForm()
  } catch (error) {
    console.error('重新生成正交组合失败', error)
    Message.error('重新生成正交组合失败')
  } finally {
    submitting.value = false
  }
}

// 监听弹窗显示状态，初始化表单
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue) {
      initializeForm()
    }
  }
)

// 监听错误类别变化
watch(
  () => props.errorCategories,
  () => {
    if (props.modelValue) {
      initializeForm()
    }
  },
  { deep: true }
)
</script>

<style scoped>
:deep(.arco-modal-header) {
  border-bottom: none;
  padding: 16px 20px;
}

:deep(.arco-modal-body) {
  padding: 0 20px 20px;
}

:deep(.arco-modal-footer) {
  border-top: none;
  padding: 0 20px 20px;
}

:deep(.arco-form-item-label-col) {
  font-weight: normal;
}
</style>
