<template>
  <a-drawer
    popup-container="#parentNode"
    :visible="visible"
    :footer="false"
    :mask="false"
    :width="450"
    style="left: auto; right: 5px; top: 10px; bottom: 10px"
    :drawerStyle="{ borderRadius: '8px', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <template #header>
      <div class="flex items-center justify-between" style="width: 100%">
        <span class="overflow-ellipsis overflow-hidden whitespace-nowrap">{{ nodeInfo.title }}</span>
        <a-space class="button-list">
          <a-button type="text">
            <template #icon><icon-play-arrow /></template>
          </a-button>
          <a-button type="text">
            <template #icon>
              <IconRefresh />
            </template>
          </a-button>
          <a-dropdown @select="handleSelect">
            <a-button type="text">
              <template #icon>
                <icon-more />
              </template>
            </a-button>
            <template #content>
              <a-doption>运行此步骤</a-doption>
              <a-doption>拷贝</a-doption>
              <a-doption>复制</a-doption>
            </template>
          </a-dropdown>
          <a-divider direction="vertical" />
          <a-button type="text" @click="handleCancel">
            <template #icon>
              <IconClose />
            </template>
          </a-button>
        </a-space>
      </div>
    </template>
    <template #title>Title</template>
    <div style="height: 100%">
      <Start v-if="nodeInfo.type == 'start'" :node="nodeInfo" />
      <End v-else-if="nodeInfo.type == 'end'" :nodeId="nodeId" :node="nodeInfo" />
      <Answer v-else-if="nodeInfo.type == 'answer'" :nodeId="nodeId" :node="nodeInfo" @change="formChang" />
      <Iteration v-else-if="nodeInfo.type == 'iteration'" :nodeId="nodeId" :node="nodeInfo" />
      <Http v-else-if="nodeInfo.type == 'http-request'" :nodeInfo="nodeInfo" />
      <Llm v-else-if="nodeInfo.type == 'llm'" :nodeId="nodeId" :nodeInfo="nodeInfo" />
      <DocumenExtractor v-else-if="nodeInfo.type == 'document-extractor'" :node="nodeInfo" />
      <Code v-else-if="nodeInfo.type == 'code'" :nodeId="nodeId" :nodeInfo="nodeInfo" />
      <IfElse v-else-if="nodeInfo.type == 'if-else'" :nodeId="nodeId" :nodeInfo="nodeInfo" />
      <Tool v-else-if="nodeInfo.type == 'tool'" :nodeInfo="nodeInfo" />
      <VariableAggregator v-else-if="nodeInfo.type == 'variable-aggregator'" :nodeId="nodeId" :nodeInfo="nodeInfo" />
      <knowledgeRetrieval
        v-else-if="nodeInfo.type == 'knowledge-retrieval'"
        v-model:nodeInfo="nodeInfo"
        :nodeId="nodeId"
        @change="formChang"
      />
      <ParameterExtractor v-else-if="nodeInfo.type == 'parameter-extractor'" :nodeId="nodeId" :nodeInfo="nodeInfo" />
    </div>
  </a-drawer>
</template>
<script setup lang="ts">
import { ref } from 'vue'
import Start from './start/Info.vue'
import End from './end/Info.vue'
import Answer from './answer/Info.vue'
import Iteration from './iteration/Info.vue'
import Http from './http/Info.vue'
import Llm from './llm/Info.vue'
import DocumenExtractor from './document-extractor/Info.vue'
import Code from './code/Info.vue'
import IfElse from './if-else/Info.vue'
import Tool from './tool/Info.vue'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import VariableAggregator from './variable-aggregator/Info.vue'
import knowledgeRetrieval from './knowledge-retrieval/Info.vue'
import ParameterExtractor from './parameter-extractor/Info.vue'

const visible = ref(false)
const nodeInfo = ref<any>({})
const nodeId = ref<string>('')
const emit = defineEmits<{
  (e: 'change', value: any): void
}>()
const nodesStore = useNodesStore()

const handleClick = (event, node) => {
  visible.value = true
  nodeInfo.value = node.data
  nodeId.value = node.id
  nodesStore.setCurrentNode(node)
}
const formChang = (e) => {
  emit('change', e)
}
const handleOk = () => {
  visible.value = false
}
const handleCancel = () => {
  visible.value = false
}
const handleSelect = (value) => {
  console.log(value)
}
defineExpose({ handleClick })
</script>
<style scoped lang="scss">
:deep {
  .arco-drawer-header {
    padding: 0 12px;
    height: 86px;
    line-height: 56px;
    font-size: 16px;
    font-weight: 500;
    color: #000;
  }
}

.button-list {
  :deep(.arco-btn-text) {
    color: var(--color-text-2);
  }
}
</style>
