<template>
  <div :style="{ width: data.width, height: data.height }" class="custom-node">
    <div class="truncate flex items-center h-[46px]">
      <div class="custom-node-icon" :style="{ backgroundColor: '#2e90fa' }">
        <AiSvgIcon style="width: 18px; height: 18px" :name="`workflow-${type}`" />
      </div>
      <div class="custom-node-text overflow-ellipsis overflow-hidden whitespace-nowrap flex-1">
        {{ data.title }}
      </div>
      <Handle id="target" type="target" :position="Position.Left" />
      <NodeList class="custom-node-add" :popoverInstance="true" :nodeId="props.id" :nodeProps="props">
        <Handle id="source" type="source" class="custom-node-handle" :position="Position.Right">
          <icon-plus :style="{ pointerEvents: 'none' }" />
        </Handle>
      </NodeList>
    </div>
    <div />
  </div>
</template>

<script setup lang="ts">
import { Handle, Position } from '@vue-flow/core'
import type { NodeProps } from '@vue-flow/core'
import NodeList from '../node-list.vue'

const props = defineProps<NodeProps>()
</script>
<style scoped lang="scss">
:deep(.vue-flow__handle-left) {
  top: auto;
}

.custom-node {
  min-width: 240px;
  padding: 4px 14px;
  background-color: var(--color-bg-1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 12px;

  &-icon {
    margin-right: 8px;
    height: 24px;
    width: 24px;
    border-radius: 8px;
    background-color: var(--color-fill-3);
    text-align: center;
    line-height: 24px;
    color: var(--color-text-1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-text {
    font-size: 18px;
    font-weight: 500;
    color: var(--color-text-2);
  }

  &-handle {
    background: rgb(var(--primary-6));
    height: 10px;
    width: 2px;
    border-radius: 0;
    border: none;
    min-width: 2px;

    .arco-icon {
      display: none;
    }
  }

  &:hover {
    .custom-node-handle {
      background-color: rgb(var(--primary-6));
      border-radius: 50%;
      width: 16px;
      height: 16px;
      text-align: center;
      line-height: 16px;
      cursor: pointer;
    }

    .arco-icon {
      display: inline-block;
      width: 14px;
      height: 14px;
      color: var(--color-white);
    }
  }

  &-add {
    position: absolute;
    right: 0;
    pointer-events: none;
  }
}
</style>
