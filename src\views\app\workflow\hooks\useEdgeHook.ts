import { type Connection, type Edge, type GraphEdge, useVueFlow } from '@vue-flow/core'

import { ChartEdge } from '../edge/edges'
import { NodeType } from '../types/node'

const state = {
  appId: null,
  instance: null
}

export default function useEdgeHook(instance?, appId?) {
  if (instance) state.instance = instance
  if (appId) state.appId = appId

  const { findEdge, addEdges, updateEdge, findNode } = useVueFlow(state.appId!)

  /**
   * 数标移入
   */
  function onHoverEdge(edge, type = 'enter') {
    switch (type) {
      case 'enter': {
        edge.data.hover = true
        break
      }
      case 'leave': {
        edge.data.hover = false
      }
    }
  }

  /**
   * 根据边 Id 获取边信息
   * @param {string} id 节点Id
   */
  function getEdgeInfoById(id) {
    return findEdge(id)
  }

  function addEdgesFn(
    addList: {
      source: string
      target: string
      sourceHandle?: string
      targetHandle?: string
      id?: string
      zIndex?: number
      selectable?: boolean
    }[]
  ) {
    const edges: Edge[] = []
    for (let a = 0; a < addList.length; a++) {
      const { source, target, id, sourceHandle, targetHandle, zIndex = 0, selectable = true } = addList[a]

      let sHandle = sourceHandle
      if (!sourceHandle) {
        const sourceNode = findNode(source)
        if (sourceNode?.data?.type === NodeType.问题分类) {
          const { classes } = sourceNode.data
          sHandle = classes[0].id
        } else {
          sHandle = `${source}-source-handle`
        }
      }

      edges.push(
        new ChartEdge({
          id,
          source,
          target,
          sourceHandle: sHandle,
          targetHandle: targetHandle || `${target}-target-handle`,
          zIndex,
          selectable
        })
      )
    }
    addEdges(edges)
    return edges
  }

  function updateEdgeFn(oldEdge: GraphEdge, newInfo: Connection) {
    updateEdge(oldEdge, newInfo, true)
  }

  function onEdgeHoverStart(...args) {
    console.log('[ args ] >', args)
  }

  function onEdgeHoverEnd(...args) {
    console.log('[ args ] >', args)
  }

  return {
    getEdgeInfoById,
    addEdgesFn,
    updateEdgeFn,
    onHoverEdge,
    onEdgeHoverStart,
    onEdgeHoverEnd
  }
}
