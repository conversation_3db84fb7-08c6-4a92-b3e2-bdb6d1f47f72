<template>
  <a-drawer
    popup-container="#parentNode"
    :visible="visible"
    :footer="false"
    :mask="false"
    class="run-panel-drawer"
    :width="400"
    unmountOnClose
    :drawerStyle="{ borderRadius: '8px', boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)' }"
    :body-style="{ padding: 0 }"
    @cancel="handleCancel"
  >
    <template #title>Test Run#{{ historyData.sequence_number }}</template>
    <div>
      <a-tabs v-model:active-key="tabsKey" @tab-click="tabclick">
        <!-- <a-tab-pane v-if="variables.length > 0" key="inputs" title="输入">
					<InputsPanel ref="inputsPanel" :variables="variables" @switchTab="switchTab" />
				</a-tab-pane> -->
        <a-tab-pane key="result" :disabled="!historyData" title="结果">
          <ResultText
            :isRunning="historyData?.status === 'running' || !historyData"
            :outputs="historyData?.outputs"
            :allFiles="historyData?.files"
            :error="historyData?.error"
          />
        </a-tab-pane>
        <a-tab-pane key="detail" :disabled="!historyData" title="详情">
          <ResultPanel
            :inputs="historyData?.inputs"
            :outputs="historyData?.outputs"
            :status="historyData?.status || ''"
            :error="historyData?.error"
            :elapsed_time="historyData?.elapsed_time"
            :total_tokens="historyData?.total_tokens"
            :created_at="historyData?.created_at"
            :created_by="(historyData?.created_by as any)?.name"
            :steps="historyData?.total_steps"
            :exceptionCounts="historyData?.exceptions_count"
          />
        </a-tab-pane>
        <a-tab-pane key="tracing" :disabled="!historyData" title="追踪">
          <TracingPanel :list="tracinglist || []" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import InputsPanel from './inputs-panel.vue'
import ResultText from './result-text.vue'
import ResultPanel from './result-panel.vue'
import TracingPanel from './tracing-panel.vue'

import { useVueFlow } from '@vue-flow/core'
import { useWorkflowStore } from '@/stores'
import { storeToRefs } from 'pinia'
import { workflownodeexecutions } from '@/apis'
defineOptions({ name: 'RunPanel' })
const emits = defineEmits(['hideRunPanel'])
const inputsPanel = ref()
const { nodes } = useVueFlow()
// 获取开始节点配置的变量
const variables = ref([])
const tracinglist = ref([])
const tabsKey = ref<string>('result')
const visible = ref(false)
// const { historyData } = storeToRefs(useWorkflowStore())
const props = defineProps<{
  historyData?: any
}>()
const route = useRoute()
const tabclick = (key) => {
  console.log(key)
  if (key == 'tracing') {
    workflownodeexecutions(route.params.appId, props.historyData.id).then((res) => {
      console.log(res)
      tracinglist.value = res.data
    })
  }
}
onMounted(() => {
  variables.value = nodes.value.find((e) => e.type === 'start')?.data?.variables || []
})

const handleCancel = () => {
  visible.value = false
  emits('hideRunPanel')
}

const switchTab = (v: string) => {
  tabsKey.value = v
}
defineExpose({
  visible
})
</script>
<style lang="scss">
.run-panel-drawer {
  left: auto;
  right: 5px;
  top: 10px;
  bottom: 10px;

  .arco-drawer-header {
    height: 46px;
    border-bottom: 0;
    padding: 0 16px;
  }

  .arco-tabs-content {
    padding: 16px;
  }
}
</style>
