import type * as T from '@/apis/workflow/type'
import type { VisionFile } from '@/views/app/workflow/types/app'
import type {
  AgentLogResponse,
  IterationFinishedResponse,
  IterationNextResponse,
  IterationStartedResponse,
  LoopFinishedResponse,
  LoopNextResponse,
  LoopStartedResponse,
  NodeFinishedResponse,
  NodeStartedResponse,
  ParallelBranchFinishedResponse,
  ParallelBranchStartedResponse,
  TextChunkResponse,
  TextReplaceResponse,
  WorkflowFinishedResponse,
  WorkflowStartedResponse,
  Thought,
  MessageEnd,
  MessageReplace
} from '@/views/app/workflow/types/workflow'

function unicodeToChar(text: string) {
  if (!text) return ''

  return text.replace(/\\u[0-9a-f]{4}/g, (_match, p1) => {
    return String.fromCharCode(Number.parseInt(p1, 16))
  })
}

export const handleStream = (
  response: Response,
  onData: T.IOnData,
  onCompleted?: T.IOnCompleted,
  onFile?: T.IOnFile,
  onThought?: T.IOnThought,
  onMessageEnd?: T.IOnMessageEnd,
  onMessageReplace?: T.IOnMessageReplace,
  onWorkflowStarted?: T.IOnWorkflowStarted,
  onWorkflowFinished?: T.IOnWorkflowFinished,
  onNodeStarted?: T.IOnNodeStarted,
  onNodeFinished?: T.IOnNodeFinished,
  onIterationStart?: T.IOnIterationStarted,
  onIterationNext?: T.IOnIterationNext,
  onIterationFinish?: T.IOnIterationFinished,
  onLoopStart?: T.IOnLoopStarted,
  onLoopNext?: T.IOnLoopNext,
  onLoopFinish?: T.IOnLoopFinished,
  onNodeRetry?: T.IOnNodeRetry,
  onParallelBranchStarted?: T.IOnParallelBranchStarted,
  onParallelBranchFinished?: T.IOnParallelBranchFinished,
  onTextChunk?: T.IOnTextChunk,
  onTTSChunk?: T.IOnTTSChunk,
  onTTSEnd?: T.IOnTTSEnd,
  onTextReplace?: T.IOnTextReplace,
  onAgentLog?: T.IOnAgentLog
) => {
  if (!response) throw new Error('Network response was not ok')

  const reader = response.body?.getReader()
  const decoder = new TextDecoder('utf-8')
  let buffer = ''
  let bufferObj: Record<string, any>
  let isFirstMessage = true
  function read() {
    let hasError = false
    reader?.read().then((result: any) => {
      if (result.done) {
        onCompleted && onCompleted()
        return
      }
      buffer += decoder.decode(result.value, { stream: true })
      const lines = buffer.split('\n')
      try {
        lines.forEach((message) => {
          if (message.startsWith('data: ')) {
            // check if it starts with data:
            try {
              bufferObj = JSON.parse(message.substring(6)) as Record<string, any> // remove data: and parse as json
              // console.log(bufferObj, 'bufferObj')
            } catch {
              // mute handle message cut off
              onData('', isFirstMessage, {
                conversationId: bufferObj?.conversation_id,
                messageId: bufferObj?.message_id
              })
              return
            }
            if (bufferObj.status === 400 || !bufferObj.event) {
              onData('', false, {
                conversationId: undefined,
                messageId: '',
                errorMessage: bufferObj?.message,
                errorCode: bufferObj?.code
              })
              hasError = true
              onCompleted?.(true, bufferObj?.message)
              return
            }
            if (bufferObj.event === 'message' || bufferObj.event === 'agent_message') {
              // can not use format here. Because message is splitted.
              onData(unicodeToChar(bufferObj.answer), isFirstMessage, {
                conversationId: bufferObj.conversation_id,
                taskId: bufferObj.task_id,
                messageId: bufferObj.id
              })
              isFirstMessage = false
            } else if (bufferObj.event === 'agent_thought') {
              onThought?.(bufferObj as Thought)
            } else if (bufferObj.event === 'message_file') {
              onFile?.(bufferObj as VisionFile)
            } else if (bufferObj.event === 'message_end') {
              onMessageEnd?.(bufferObj as MessageEnd)
            } else if (bufferObj.event === 'message_replace') {
              onMessageReplace?.(bufferObj as MessageReplace)
            } else if (bufferObj.event === 'workflow_started') {
              onWorkflowStarted?.(bufferObj as WorkflowStartedResponse)
            } else if (bufferObj.event === 'workflow_finished') {
              onWorkflowFinished?.(bufferObj as WorkflowFinishedResponse)
            } else if (bufferObj.event === 'node_started') {
              onNodeStarted?.(bufferObj as NodeStartedResponse)
            } else if (bufferObj.event === 'node_finished') {
              onNodeFinished?.(bufferObj as NodeFinishedResponse)
            } else if (bufferObj.event === 'iteration_started') {
              onIterationStart?.(bufferObj as IterationStartedResponse)
            } else if (bufferObj.event === 'iteration_next') {
              onIterationNext?.(bufferObj as IterationNextResponse)
            } else if (bufferObj.event === 'iteration_completed') {
              onIterationFinish?.(bufferObj as IterationFinishedResponse)
            } else if (bufferObj.event === 'loop_started') {
              onLoopStart?.(bufferObj as LoopStartedResponse)
            } else if (bufferObj.event === 'loop_next') {
              onLoopNext?.(bufferObj as LoopNextResponse)
            } else if (bufferObj.event === 'loop_completed') {
              onLoopFinish?.(bufferObj as LoopFinishedResponse)
            } else if (bufferObj.event === 'node_retry') {
              onNodeRetry?.(bufferObj as NodeFinishedResponse)
            } else if (bufferObj.event === 'parallel_branch_started') {
              onParallelBranchStarted?.(bufferObj as ParallelBranchStartedResponse)
            } else if (bufferObj.event === 'parallel_branch_finished') {
              onParallelBranchFinished?.(bufferObj as ParallelBranchFinishedResponse)
            } else if (bufferObj.event === 'text_chunk') {
              onTextChunk?.(bufferObj as TextChunkResponse)
            } else if (bufferObj.event === 'text_replace') {
              onTextReplace?.(bufferObj as TextReplaceResponse)
            } else if (bufferObj.event === 'agent_log') {
              onAgentLog?.(bufferObj as AgentLogResponse)
            } else if (bufferObj.event === 'tts_message') {
              onTTSChunk?.(bufferObj.message_id, bufferObj.audio, bufferObj.audio_type)
            } else if (bufferObj.event === 'tts_message_end') {
              onTTSEnd?.(bufferObj.message_id, bufferObj.audio)
            }
          }
        })

        buffer = lines[lines.length - 1]
      } catch (e) {
        onData('', false, {
          conversationId: undefined,
          messageId: '',
          errorMessage: `${e}`
        })
        hasError = true
        onCompleted?.(true, e as string)
        return
      }
      if (!hasError) read()
    })
  }
  read()
}

export default handleStream
