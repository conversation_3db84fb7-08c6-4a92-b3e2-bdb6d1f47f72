/**
 * EvalDataset
 * 评测集主表
 */
export interface EvalDataset {
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string
  /** 创建人ID */
  createdBy?: string
  /**
   * 描述
   * @minLength 0
   * @maxLength 65535
   */
  description?: string
  echoMap?: object
  /** 主键 */
  id?: string
  /**
   * 评测集名称
   * @minLength 0
   * @maxLength 255
   */
  name?: string
  /**
   * 状态：1-草稿，2-已发布
   * @format int32
   */
  status?: number
  /**
   * 应用类型：1-对话类，2-工作流类
   * @format int32
   */
  type?: number
  /**
   * 最后修改时间
   * @format date-time
   */
  updateTime?: string
  /** 最后修改人ID */
  updatedBy?: string
  /**
   * 版本号
   * @minLength 0
   * @maxLength 50
   */
  version?: string
}

/**
 * EvalDatasetPageQuery
 * 评测集主表
 */
export interface EvalDatasetPageQuery {
  /** 描述 */
  description?: string
  /** 评测集名称 */
  name?: string
  /**
   * 状态：1-草稿，2-已发布
   * @format int32
   */
  status?: number
  /**
   * 应用类型：1-对话类，2-工作流类
   * @format int32
   */
  type?: number
  /** 版本号 */
  version?: string
}

/**
 * EvalDatasetSaveDTO
 * 评测集主表
 */
export interface EvalDatasetSaveDTO {
  /**
   * 描述
   * @minLength 0
   * @maxLength 65535
   */
  description?: string
  /**
   * 评测集名称
   * @minLength 0
   * @maxLength 255
   */
  name?: string
  /**
   * 状态：1-草稿，2-已发布
   * @format int32
   */
  status?: number
  /**
   * 应用类型：1-对话类，2-工作流类
   * @format int32
   */
  type?: number
  /**
   * 版本号
   * @minLength 0
   * @maxLength 50
   */
  version?: string
}

/**
 * EvalDatasetUpdateDTO
 * 评测集主表
 */
export interface EvalDatasetUpdateDTO {
  /**
   * 描述
   * @minLength 0
   * @maxLength 65535
   */
  description?: string
  /** 主键 */
  id?: string
  /**
   * 评测集名称
   * @minLength 0
   * @maxLength 255
   */
  name?: string
  /**
   * 状态：1-草稿，2-已发布
   * @format int32
   */
  status?: number
  /**
   * 应用类型：1-对话类，2-工作流类
   * @format int32
   */
  type?: number
  /**
   * 版本号
   * @minLength 0
   * @maxLength 50
   */
  version?: string
}

/** IPage«EvalDataset» */
export interface IPageEvalDataset {
  /** @format int64 */
  current?: number
  /** @format int64 */
  pages?: number
  records?: EvalDataset[]
  /** @format int64 */
  size?: number
  /** @format int64 */
  total?: number
}

/**
 * PageParams«EvalDatasetPageQuery»
 * 分页参数
 */
export interface PageParamsEvalDatasetPageQuery {
  /**
   * 当前页
   * @format int64
   * @example 1
   */
  current?: number
  /** 扩展参数 */
  extra?: object
  /** 查询参数 */
  model: EvalDatasetPageQuery
  /**
   * 排序规则, 默认descending
   * @example "descending"
   */
  order?: 'descending' | 'ascending'
  /**
   * 页面大小
   * @format int64
   * @example 10
   */
  size?: number
  /**
   * 排序,默认createTime
   * @example "id"
   */
  sort?: 'id' | 'createTime' | 'updateTime'
}

/** R«EvalDataset» */
export interface REvalDataset {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: EvalDataset
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}

/** R«IPage«EvalDataset»» */
export interface RIPageEvalDataset {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: IPageEvalDataset
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}

/** R«List«EvalDataset»» */
export interface RListEvalDataset {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: EvalDataset[]
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}

/** R«boolean» */
export interface RBoolean {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: boolean
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}
