<template>
  <div class="documents-page">
    <!-- 左侧导航栏 -->
    <div class="left-sidebar">
      <div class="dataset-info">
        <div class="dataset-icon">
          <icon-folder size="32" />
        </div>
        <div class="dataset-name">{{ datasetInfo?.name || '加载中...' }}</div>
        <div class="dataset-desc">
          {{ datasetInfo?.description || 'Useful for when you want to answer queries about the documents' }}
        </div>
      </div>

      <div class="sidebar-menu">
        <div class="menu-item" :class="{ active: activeTab === 'documents' }" @click="switchTab('documents')">
          <icon-file />
          <span>文档</span>
        </div>
        <div class="menu-item" :class="{ active: activeTab === 'hit-testing' }" @click="switchTab('hit-testing')">
          <icon-search />
          <span>召回测试</span>
        </div>
        <div class="menu-item" :class="{ active: activeTab === 'settings' }" @click="switchTab('settings')">
          <icon-settings />
          <span>设置</span>
        </div>
      </div>
    </div>

    <!-- 主内容区 -->
    <div class="main-content">
      <!-- 文档管理内容 -->
      <DocumentsContent
        v-if="activeTab === 'documents'"
        ref="documentsContentRef"
        :datasetId="datasetId"
        @refresh-dataset="fetchDatasetInfo"
      />

      <!-- 召回测试内容 -->
      <HitTestingContent v-if="activeTab === 'hit-testing'" :datasetId="datasetId" />

      <!-- 设置内容 -->
      <SettingsContent v-if="activeTab === 'settings'" :datasetId="datasetId" @dataset-deleted="handleDatasetDeleted" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useRoute, useRouter } from 'vue-router'
import { getDatasetDetail } from '@/apis/datasets'
import DocumentsContent from './DocumentsContent.vue'
import HitTestingContent from './HitTestingContent.vue'
import SettingsContent from './SettingsContent.vue'

// 获取路由参数和路由器
const route = useRoute()
const router = useRouter()
const datasetId = ref('')
const datasetInfo = ref<any>(null)

// 定义DocumentsContent组件暴露的方法接口
interface DocumentsContentInstance {
  fetchDocumentsList: () => Promise<void>
}

// 子组件引用
const documentsContentRef = ref<DocumentsContentInstance | null>(null)

// 当前激活的标签页
const activeTab = ref('documents')
const dataLoaded = ref(false)

// 生命周期钩子
onMounted(() => {
  datasetId.value = route.params.datasetId as string
  // 检查URL中是否有指定的标签页
  const tab = route.query.tab as string
  if (tab && ['documents', 'hit-testing', 'settings'].includes(tab)) {
    activeTab.value = tab
  }

  if (datasetId.value) {
    fetchDatasetInfo()
  } else {
    Message.error('未能获取知识库ID')
  }
})

// 监听datasetId变化
watch(datasetId, (newVal) => {
  if (newVal && activeTab.value === 'documents') {
    // 当datasetId改变且当前是文档标签页时，刷新文档列表
    refreshDocumentsList()
  }
})

// 监听dataLoaded和activeTab
watch([dataLoaded, activeTab], ([newDataLoaded, newActiveTab]) => {
  if (newDataLoaded && newActiveTab === 'documents') {
    // 当数据加载完成且当前是文档标签页时，刷新文档列表
    refreshDocumentsList()
  }
})

// 刷新文档列表
const refreshDocumentsList = () => {
  if (documentsContentRef.value) {
    // 使用setTimeout确保组件已经渲染完成
    setTimeout(() => {
      if (documentsContentRef.value) {
        documentsContentRef.value.fetchDocumentsList()
      }
    }, 0)
  }
}

// 获取知识库信息
const fetchDatasetInfo = async () => {
  try {
    const response = await getDatasetDetail(datasetId.value)
    datasetInfo.value = response
    console.log('知识库信息:', datasetInfo.value)
    dataLoaded.value = true
  } catch (error) {
    console.error('获取知识库信息失败:', error)
    Message.error('获取知识库信息失败')
  }
}

// 切换标签页
const switchTab = (tab: string) => {
  activeTab.value = tab
  // 更新URL，但不触发新的导航
  router.replace({
    query: {
      ...route.query,
      tab
    }
  })
}

// 处理知识库删除
const handleDatasetDeleted = () => {
  // 跳转到知识库列表页面
  router.push('/datasets/index')
}
</script>

<style scoped lang="scss">
.documents-page {
  display: flex;
  height: 100vh;
  overflow: hidden;
  margin: 14px;

  .left-sidebar {
    width: 240px;
    background-color: #fff;
    border-right: 1px solid var(--color-border-2);
    display: flex;
    flex-direction: column;
    padding: 20px 0;

    .dataset-info {
      padding: 0 20px 20px;
      border-bottom: 1px solid var(--color-border-2);

      .dataset-icon {
        margin-bottom: 12px;
        color: rgb(var(--primary-6));
      }

      .dataset-name {
        font-weight: 600;
        font-size: 16px;
        margin-bottom: 8px;
        word-break: break-word;
      }

      .dataset-desc {
        font-size: 12px;
        color: var(--color-text-3);
        line-height: 1.5;
        margin-bottom: 16px;
        word-break: break-word;
      }
    }

    .sidebar-menu {
      margin-top: 20px;

      .menu-item {
        padding: 12px 20px;
        display: flex;
        align-items: center;
        cursor: pointer;

        &:hover {
          background-color: var(--color-fill-2);
        }

        &.active {
          background-color: var(--color-fill-2);
          font-weight: 500;
          color: rgb(var(--primary-6));
          border-right: 2px solid rgb(var(--primary-6));
        }

        .arco-icon {
          margin-right: 12px;
        }
      }
    }
  }

  .main-content {
    flex: 1;
    overflow-y: auto;
    background-color: var(--color-bg-1);
    padding: 24px;
  }
}
</style>
