<template>
  <AiPageLayout class="model-mgmt-container">
    <div class="model-mgmt">
      <!--在js分组还是在template中分组？-->
      <div class="model-list model-list-config">
        <a-row justify="space-between" align="center" class="panel-title">
          <a-col :span="12">
            <a-typography-title :heading="6" class="agent-title">模型列表</a-typography-title>
          </a-col>
          <a-col :span="12" class="text-right">
            <a-button @click="handleSystemModelConfig">系统模型设置</a-button>
          </a-col>
        </a-row>

        <a-card
          v-for="(item, index) in modelListConfig"
          :key="index"
          :bordered="true"
          :body-style="{ 'border-radius': '16px', padding: '0px' }"
        >
          <div class="card-content">
            <a-space direction="vertical" fill>
              <div class="flex justify-between">
                <a-space direction="vertical">
                  <!--标题-->
                  <a-space :align="'center'">
                    <a-avatar>
                      <img
                        alt="avatar"
                        :src="`${DOMAIN_NAME}/console/api/workspaces/${item.tenant_id}/model-providers/${item.provider}/icon_small/zh_Hans`"
                      />
                    </a-avatar>

                    <a-typography-title :heading="6" class="agent-title">
                      {{ renderName(item.label) }}
                    </a-typography-title>
                  </a-space>
                  <!--标签-->
                  <a-space>
                    <a-tag v-for="modelType in item.supported_model_types" :key="modelType">
                      {{ modelTypeFormat(modelType) }}
                    </a-tag>
                  </a-space>
                </a-space>
                <!--右侧AIP-KEY配置-->
                <a-card v-if="item.provider_credential_schema" :bordered="true">
                  <a-space direction="vertical">
                    <a-space :align="'center'">
                      <div>API-KEY</div>
                      <span class="api-key-status" />
                    </a-space>
                    <a-button long @click="handleConfigApiKey(item, true)">设置</a-button>
                  </a-space>
                </a-card>
              </div>
            </a-space>
          </div>

          <div class="flex card-footer">
            <a-button type="text" status="normal" :loading="item.isLoadingSubModelList" @click="handleShowModel(item)">
              {{ item.isCollapsed ? '显示模型' : '隐藏模型' }}
              <icon-right v-if="item.isCollapsed" />
              <icon-down v-else />
              <!--<component :is=""></component>-->
            </a-button>
            <a-button type="text" status="normal" @click="handleAddEditModel(item, 'add')">
              <icon-plus-circle-fill />
              添加模型
            </a-button>
          </div>
          <!--子模型list-->
          <SubModelList v-if="!item.isCollapsed" :providerItem="item" :subModelList="item.subModelList" />
        </a-card>
      </div>
      <div class="model-list model-list-not-config">
        <a-row justify="space-between" align="center" class="panel-title">
          <a-col :span="12">
            <a-typography-title :heading="6" class="agent-title">待配置</a-typography-title>
          </a-col>
          <a-col :span="12" class="text-right">
            <!--<a-button>系统模型设置</a-button>-->
          </a-col>
        </a-row>

        <a-card
          v-for="(item, index) in modelListNotConfig"
          :key="index"
          :bordered="true"
          :body-style="{ 'border-radius': '16px', padding: '0px' }"
        >
          <div class="card-content">
            <div class="flex justify-between">
              <a-space direction="vertical">
                <!--标题-->
                <a-space :align="'center'">
                  <a-avatar>
                    <img
                      alt="avatar"
                      :src="`${DOMAIN_NAME}/console/api/workspaces/${item.tenant_id}/model-providers/${item.provider}/icon_small/zh_Hans`"
                    />
                  </a-avatar>

                  <a-typography-title :heading="6" class="agent-title">{{ renderName(item.label) }}</a-typography-title>
                </a-space>

                <!--标签-->
                <a-space>
                  <a-tag v-for="modelType in item.supported_model_types" :key="modelType">
                    {{ modelTypeFormat(modelType) }}
                  </a-tag>
                </a-space>
              </a-space>

              <!--右侧AIP-KEY配置-->
              <a-card v-if="item.provider_credential_schema" :bordered="true">
                <a-space direction="vertical">
                  <a-space :align="'center'">
                    <div>API-KEY</div>
                    <span class="api-key-status error-status" />
                  </a-space>
                  <a-button long @click="handleConfigApiKey(item, false)">设置</a-button>
                </a-space>
              </a-card>
            </div>
          </div>

          <div class="flex card-footer">
            <span>
              <icon-exclamation-circle-fill />
              请配置 API 密钥，添加模型。
            </span>
            <a-button type="text" status="normal" @click="handleAddEditModel(item, 'add')">
              <icon-plus-circle-fill :size="24" />
              添加模型
            </a-button>
          </div>
        </a-card>
      </div>
    </div>

    <SystemModelConfigDialog v-if="configVisible" @closeConfigDialog="closeConfigDialog" />

    <!--添加和编辑模型-->
    <AddEditModelModal
      v-if="addEditModelVisible"
      :providerItem="providerItem"
      :formSchemas="formSchemas"
      :subModelItem="subModelItem"
      pageMode="add"
      @closeAddEditModelModal="closeAddEditModelModal"
    />

    <ConfigApiKeyModal
      v-if="apiKeyVisible"
      :providerItem="providerItem"
      :apiKeySchemas="apiKeySchemas"
      :isConfigured="isConfigured"
      @closeConfigApiKeyModal="closeConfigApiKeyModal"
    />
  </AiPageLayout>
</template>

<script setup lang="ts">
import SubModelList from './components/SubModelList.vue'
import SystemModelConfigDialog from './components/SystemModelConfigDialog.vue'

import { getModelProvidersListHttp, getSubProvidersListHttp } from '@/apis/model-mgmt'
import {
  type CredentialFormSchema,
  CustomConfigurationStatusEnum,
  type ModelProvider,
  ModelTypeEnum,
  type SubModelListItem
} from '@/apis/model-mgmt/type'
import { genModelNameFormSchema, genModelTypeFormSchema, modelTypeFormat } from '@/views/app/workflow/utils/model'
import AddEditModelModal from '@/views/model-mgmt/components/AddEditModelModal.vue'
import { DOMAIN_NAME } from '@/views/app/workflow/constant/common'
import ConfigApiKeyModal from '@/views/model-mgmt/components/ConfigApiKeyModal.vue'
// 模型list
const modelList = ref<ModelProvider[]>([])
// 已配置
const modelListConfig = ref<ModelProvider[]>([])
// 未配置
const modelListNotConfig = ref<ModelProvider[]>([])

/**
 * 获取模型供应商list
 */
const getModelProviderList = async () => {
  const params = {}
  const res = await getModelProvidersListHttp()

  modelList.value = res.data || []

  modelListConfig.value = []
  modelListNotConfig.value = []

  // 已配置和未配置要分组==> react版本是分组的，那也暂时分组吧
  modelList.value.forEach((provider) => {
    if (
      provider.custom_configuration.status === CustomConfigurationStatusEnum.active ||
      (provider.system_configuration.enabled === true &&
        provider.system_configuration.quota_configurations.find(
          (item) => item.quota_type === provider.system_configuration.current_quota_type
        ))
    ) {
      modelListConfig.value.push({
        ...provider,
        subModelList: [],
        isCollapsed: true,
        isLoadingSubModelList: false
      })
    } else {
      modelListNotConfig.value.push(provider)
    }
  })
}

getModelProviderList()

/**
 * 获取模型名称
 * @param obj
 */
const renderName = (obj: Record<string, string>) => {
  if (!obj) return ''
  if (obj?.['zh_Hans']) return obj['zh_Hans']
  if (obj?.en_US) return obj.en_US
  return Object.values(obj)[0]
}

/**
 * 展开 查看细分模型
 */
const handleShowModel = async (item) => {
  item.isCollapsed = !item.isCollapsed
  // 如果有子模型，那么就不请求接口了。  检查下逻辑是否OK。
  if (item.subModelList.length === 0) {
    item.isLoadingSubModelList = true
    try {
      const res = await getSubProvidersListHttp(item.provider)
      const subModelList = res.data || []
      item.subModelList.push(...subModelList)
    } catch (error) {
      console.log(error)
    } finally {
      item.isLoadingSubModelList = false
    }
  }
}

/**
 * 系统模型配置
 */
let configVisible = ref(false)
const handleSystemModelConfig = () => {
  configVisible.value = true
}
const closeConfigDialog = (type: string) => {
  configVisible.value = false
}

/**
 * 添加和编辑模型
 */
const addEditModelVisible = ref(false)
const providerItem = ref<ModelProvider | {}>({})
const formSchemas = reactive<CredentialFormSchema[]>([])
const subModelItem = ref<SubModelListItem | {}>({})
const handleAddEditModel = (provider: ModelProvider, type: string) => {
  addEditModelVisible.value = true

  providerItem.value = provider

  formSchemas.splice(0, formSchemas.length)
  // 模型类型
  formSchemas.push(genModelTypeFormSchema(provider.supported_model_types))
  // 模型名称
  formSchemas.push(genModelNameFormSchema(provider.model_credential_schema?.model))
  // 自定义表单
  formSchemas.push(...provider.model_credential_schema.credential_form_schemas)
}
const closeAddEditModelModal = (type: string) => {
  addEditModelVisible.value = false
}

const apiKeyVisible = ref(false)
const apiKeySchemas = ref()
const isConfigured = ref(false)
const handleConfigApiKey = (item, isConfig) => {
  providerItem.value = item

  isConfigured.value = isConfig

  apiKeySchemas.value = item.provider_credential_schema.credential_form_schemas
  apiKeyVisible.value = true
}
const closeConfigApiKeyModal = (type) => {
  console.log(type)
  if (type === 'ok') {
    getModelProviderList()
  }
  apiKeyVisible.value = false
}
</script>

<style scoped lang="scss">
.model-mgmt {
  overflow: auto;

  .model-list + .model-list {
    margin-top: var(--margin);
  }
}

.panel-title {
  margin-bottom: var(--padding);
}

.agent-title {
  margin: 0;
}

.flex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  padding: var(--padding);
}

.card-footer {
  padding: 6px var(--padding);

  border-top: 1px solid var(--color-border-2);
}

.arco-card-bordered {
  border-radius: 8px;
  .api-key-status {
    width: 8px;
    height: 8px;
    background: $color-success;
    border-radius: 2px;

    &.error-status {
      background: $color-danger;
    }
  }
}

.arco-card + .arco-card {
  margin-top: var(--margin);
}
</style>
