<template>
  <div class="add-edit-model-modal">
    <!--TODO-todo:1.使用v-model控制弹框的显示和隐藏；-->
    <a-modal :visible="visible" :mask-closable="false" @ok="handleOk" @cancel="handleCancel">
      <template #title>
        {{ dialogTitle }}
      </template>

      <div>
        <CustomForms
          ref="customFormRef"
          :providerItem="providerItem"
          :formSchemas="formSchemas"
          :pageMode="pageMode"
          :form="form"
        />
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import type { DefaultFormValues, ModelItem, ModelProvider, SubModelListItem } from '@/apis/model-mgmt/type.ts'

defineOptions({ name: 'AddEditModelModal' })
const props = defineProps<{
  providerItem: ModelProvider | any // 模型分类
  subModelItem: SubModelListItem | any // 子模型细分类(新增的时候没用。编辑的时候回显部分字段的值)
  formSchemas: any // FormList 结构
  pageMode: string // add | edit
}>()
const emits = defineEmits(['closeAddEditModelModal'])
import { ref } from 'vue'
import { addModelHttp, getModelDefaultValueHttp } from '@/apis/model-mgmt'
import CustomForms from '@/views/model-mgmt/components/CustomForms.vue'

const visible = ref(true)
const dialogTitle = ref('添加模型')

// 表单的form：新增的时候，直接取key，然后给default
const form = reactive({})
const keys = props.formSchemas.map((v) => {
  form[v.variable] = v.default || ''
})
// 编辑时候的form 默认值
let modelDefaultValue = ref<any>({
  credentials: {
    __model_name: '',
    __model_type: ''
  },
  load_balancing: {}
})
const getModelDefaultValue = async () => {
  const res = await getModelDefaultValueHttp(
    props.providerItem.provider,
    props.subModelItem.model,
    props.subModelItem.model_type
  )

  modelDefaultValue.value = res
  modelDefaultValue.value.credentials.__model_name = props.subModelItem?.model
  modelDefaultValue.value.credentials.__model_type = props.subModelItem?.model_type

  Object.assign(form, modelDefaultValue.value.credentials)
}
if (props.pageMode === 'edit') {
  getModelDefaultValue()
}

const customFormRef = ref()
const handleOk = async () => {
  const valid = await customFormRef.value.validForm()
  if (!valid) {
    const { __model_name, __model_type, ...credentials } = form as any
    const loadBalancing = {
      enabled: false,
      configs: []
    }
    const params = {
      model: __model_name,
      model_type: __model_type,
      credentials,
      load_balancing: loadBalancing
    }

    try {
      const res = await addModelHttp(props.providerItem.provider, params)
    } catch (error) {
      console.log(error)
    }
  }
}
const handleCancel = () => {
  emits('closeAddEditModelModal')
}
</script>

<style scoped lang="scss"></style>
