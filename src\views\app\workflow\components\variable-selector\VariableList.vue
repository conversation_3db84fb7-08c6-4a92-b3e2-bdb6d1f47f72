<template>
  <div class="variable-list">
    <div class="variable-list-search">
      <a-input v-model="searchText" placeholder="搜索变量" allow-clear @keydown="handleKeyDown">
        <template #prefix>
          <icon-search />
        </template>
      </a-input>
    </div>

    <div class="variable-list-content">
      <template v-if="filteredVars.length > 0">
        <div class="variable-list-groups">
          <div v-for="(group, groupIndex) in filteredVars" :key="groupIndex" class="variable-list-group">
            <div class="variable-list-group-title">
              {{ group.title }}
            </div>
            <div class="variable-list-items">
              <div
                v-for="(item, itemIndex) in group.vars"
                :key="itemIndex"
                class="variable-list-item"
                @click="handleItemClick(group, item)"
              >
                <div class="variable-list-item-name">
                  {{ item.variable }}
                </div>
                <div class="variable-list-item-type">
                  {{ item.type }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="variable-list-empty">没有找到变量</div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { VarType } from '@/views/app/workflow/types/workflow'

const props = defineProps<{
  vars: any[]
  varType?: VarType
  filterVar?: (v: any) => boolean
}>()

const emit = defineEmits<{
  (e: 'select', valueSelector: string[], varItem: any): void
  (e: 'close'): void
}>()

const searchText = ref('')

// Filter variables based on search text and var type
const filteredVars = computed(() => {
  if (!props.vars || props.vars.length === 0) return []

  return props.vars
    .map((group) => {
      // Filter vars by search text and type
      const filteredVars = group.vars.filter((v) => {
        const matchesSearch =
          searchText.value === '' || v.variable.toLowerCase().includes(searchText.value.toLowerCase())

        const matchesType = !props.filterVar || props.filterVar(v)

        return matchesSearch && matchesType
      })

      // Return a new group with filtered vars
      return {
        ...group,
        vars: filteredVars
      }
    })
    .filter((group) => group.vars.length > 0)
})

// Event handlers
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === 'Escape') {
    e.preventDefault()
    emit('close')
  }
}

const handleItemClick = (group: any, item: any) => {
  // const valueSelector = group.isStartNode || group.nodeId === 'sys' || group.nodeId === 'env' || group.nodeId === 'conversation'
  //   // ? [item.variable]
  //   ? [group.nodeId, item.variable]
  //   : [group.nodeId, item.variable]

  emit('select', group, item)
}
</script>
<style scoped lang="scss">
.variable-list {
  width: 300px;
  max-height: 350px;
  display: flex;
  flex-direction: column;

  &-search {
    padding: 8px;
    border-bottom: 1px solid var(--color-border-2);
  }

  &-content {
    flex: 1;
    overflow-y: auto;
    max-height: 200px;
  }

  &-groups {
    padding: 8px;
  }

  &-group {
    margin-bottom: 12px;

    &:last-child {
      margin-bottom: 0;
    }

    &-title {
      font-size: 12px;
      font-weight: 500;
      color: var(--color-text-3);
      text-transform: uppercase;
      margin-bottom: 4px;
      padding: 0 8px;
    }
  }

  &-items {
    display: flex;
    flex-direction: column;
  }

  &-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background-color: var(--color-fill-2);
    }

    &-name {
      font-size: 12px;
      color: var(--color-text-1);
    }

    &-type {
      font-size: 12px;
      color: var(--color-text-3);
    }
  }

  &-empty {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    color: var(--color-text-3);
  }
}
</style>
