<template>
  <div class="debug">
    <AiMarkdown :content="answer" />

    <div>src/views/app/workflow/run/chatflow-room.vue</div>
  </div>

</template>
<script setup lang="ts">
import { getToken } from '@/utils/auth'

const message = ref({
  content: '123'
})

const answer = ref('<details  data-think="true"><blockquote> 123445</blockquote></details>')
const getAnswer = () => {
  const token = getToken()
  fetch(
    '/console/api/installed-apps/d11fb198-d367-4237-9aca-3524976a1e76/chat-messages',
    {
      method: 'POST',
      headers: new Headers({
        Authorization: `Bear<PERSON> ${token}`,
        'Content-Type': 'application/json'
      }),

      // TODO-todo: mock参数
      body: JSON.stringify({
        'response_mode': 'streaming',
        'conversation_id': '',
        'files': [],
        'query': 'tet',
        'inputs': { 'a1': null },
        'parent_message_id': null
      })
    }
  )
    .then(async response => {
      const reader = response.body?.getReader()
      const decoder = new TextDecoder('utf-8')

      while (true) {
        const { done, value } = await reader.read()
        if (done) {
          //全部获取流式数据结束后的操作
          // end()
          break
        }
        const message = decoder.decode(value, { stream: true })

        // 可能每次取得内容很多条，所以要处理一下list
        const lines = message.split('\n')

        lines.forEach((line) => {
          if (line.startsWith('data: ')) {
            try {
              const content = JSON.parse(line.substring(6))
              answer.value += content.answer || ''
            } catch (err) {
            }
          }
        })
      }
      console.log('answer:', answer.value)
    })
}
onMounted(() => {
  getAnswer()
})
</script>
<style scoped lang="scss">
.debug {
  height: 400px;
  overflow: auto;
}
</style>
