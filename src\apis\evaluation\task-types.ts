/**
 * EvalTask
 * 评测任务表
 */
export interface EvalTask {
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string
  /** 创建人ID */
  createdBy?: string
  echoMap?: object
  /**
   * 完成时间
   * @format date-time
   */
  endTime?: string
  /**
   * 评测助手ID
   * @minLength 0
   * @maxLength 36
   */
  evalAppId?: string
  /**
   * 评测助手版本号
   * @minLength 0
   * @maxLength 20
   */
  evalAppVersion?: string
  /**
   * 新增评测规则
   * @minLength 0
   * @maxLength 255
   */
  evalRuleAdd?: string
  /**
   * 评测规则提示词
   * @minLength 0
   * @maxLength 65535
   */
  evalRulePromptWord?: string
  /**
   * 内置评测规则
   * @minLength 0
   * @maxLength 255
   */
  evalRuleSystem?: string
  /** 关联的智能助手数据 */
  evalTaskApp?: EvalTaskApp
  /** 关联的评测集数据（自动评测） */
  evalTaskDataset?: EvalTaskDataset
  /** 关联的评测数据列表（人工评测） */
  evalTaskTestData?: EvalTaskTestData
  /**
   * 执行耗时（秒）
   * @format int32
   */
  executionDuration?: number
  /** 主键 */
  id?: string
  /**
   * 意图
   * @minLength 0
   * @maxLength 200
   */
  intention?: string
  /**
   * 输出格式
   * @minLength 0
   * @maxLength 20
   */
  outputFormat?: string
  /** 执行进度百分比 */
  progress?: number
  /**
   * 质量要求
   * @minLength 0
   * @maxLength 200
   */
  qualityRequirement?: string
  /**
   * 开始执行时间
   * @format date-time
   */
  startTime?: string
  /**
   * 任务状态：0-待执行，1-执行中，2-已完成，3-失败，4-已终止
   * @format int32
   */
  status?: number
  /**
   * 任务名称
   * @minLength 0
   * @maxLength 100
   */
  taskName?: string
  /**
   * 任务类型：1-自动评测，2-人工评测
   * @format int32
   */
  taskType?: number
  /**
   * 测试分析方法
   * @minLength 0
   * @maxLength 100
   */
  testMethod?: string
  /**
   * 最后修改时间
   * @format date-time
   */
  updateTime?: string
  /** 最后修改人ID */
  updatedBy?: string
}

/**
 * EvalTaskApp
 * 评测任务与智能助手关系表
 */
export interface EvalTaskApp {
  /**
   * 智能助手ID
   * @minLength 0
   * @maxLength 36
   */
  appId?: string
  /**
   * 智能助手名称
   * @minLength 0
   * @maxLength 255
   */
  appName?: string
  /**
   * 智能助手输出结果文件Id
   * @minLength 0
   * @maxLength 36
   */
  appResultFile?: string
  /**
   * 智能助手输出结果文件名称
   * @minLength 0
   * @maxLength 255
   */
  appResultFileName?: string
  /** 智能助手输出结果Json */
  appResultJson?: string
  /**
   * 智能助手输出结果Text
   * @minLength 0
   * @maxLength 65535
   */
  appResultText?: string
  /**
   * 智能助手版本号
   * @minLength 0
   * @maxLength 20
   */
  appVersion?: string
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string
  /** 创建人ID */
  createdBy?: string
  /** Dify任务ID */
  difyTaskId?: string
  echoMap?: object
  /** 主键 */
  id?: string
  /**
   * 评测任务ID
   * @format int64
   */
  taskId?: number
}

/**
 * EvalTaskAppSaveDTO
 * 评测任务与智能助手关系表
 */
export interface EvalTaskAppSaveDTO {
  /**
   * 智能助手ID
   * @minLength 0
   * @maxLength 36
   */
  appId?: string
  /**
   * 智能助手名称
   * @minLength 0
   * @maxLength 255
   */
  appName?: string
  /**
   * 智能助手输出结果文件Id
   * @minLength 0
   * @maxLength 36
   */
  appResultFile?: string
  /**
   * 智能助手输出结果文件名称
   * @minLength 0
   * @maxLength 255
   */
  appResultFileName?: string
  /** 智能助手输出结果Json */
  appResultJson?: string
  /**
   * 智能助手输出结果Text
   * @minLength 0
   * @maxLength 65535
   */
  appResultText?: string
  /**
   * 智能助手版本号
   * @minLength 0
   * @maxLength 20
   */
  appVersion?: string
  /**
   * 评测任务ID
   * @format int64
   */
  taskId?: number
}

/**
 * EvalTaskDataset
 * 自动评测任务和评测集关联表
 */
export interface EvalTaskDataset {
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string
  /**
   * 创建人ID
   * @format int64
   */
  createdBy?: number
  /**
   * 评测集ID
   * @format int64
   */
  datasetId?: number
  echoMap?: object
  /**
   * 评测任务ID
   * @format int64
   */
  taskId?: number
}

/**
 * EvalTaskDatasetSaveDTO
 * 自动评测任务和评测集关联表
 */
export interface EvalTaskDatasetSaveDTO {
  /**
   * 评测集ID
   * @format int64
   */
  datasetId?: string
  /**
   * 评测任务ID
   * @format int64
   */
  taskId?: string
}

/**
 * EvalTaskPageQuery
 * 评测任务表
 */
export interface EvalTaskPageQuery {
  /**
   * 完成时间
   * @format date-time
   */
  endTime?: string
  /** 评测助手ID */
  evalAppId?: string
  /** 评测助手版本号 */
  evalAppVersion?: string
  /** 新增评测规则 */
  evalRuleAdd?: string
  /** 评测规则提示词 */
  evalRulePromptWord?: string
  /** 内置评测规则 */
  evalRuleSystem?: string
  /**
   * 执行耗时（秒）
   * @format int32
   */
  executionDuration?: number
  /** 意图 */
  intention?: string
  /** 输出格式 */
  outputFormat?: string
  /** 执行进度百分比 */
  progress?: number
  /** 质量要求 */
  qualityRequirement?: string
  /**
   * 开始执行时间
   * @format date-time
   */
  startTime?: string
  /**
   * 任务状态：0-待执行，1-执行中，2-已完成，3-失败，4-已终止
   * @format int32
   */
  status?: number
  /** 任务名称 */
  taskName?: string
  /**
   * 任务类型：1-自动评测，2-人工评测
   * @format int32
   */
  taskType?: number
  /** 测试分析方法 */
  testMethod?: string
}

/**
 * EvalTaskSaveDTO
 * 评测任务表
 */
export interface EvalTaskSaveDTO {
  /** 智能助手关联数据 */
  appData?: EvalTaskAppSaveDTO
  /** 评测集关联数据（自动评测使用） */
  datasetData?: EvalTaskDatasetSaveDTO
  /**
   * 完成时间
   * @format date-time
   */
  endTime?: string
  /**
   * 评测助手ID
   * @minLength 0
   * @maxLength 36
   */
  evalAppId?: string
  /**
   * 评测助手版本号
   * @minLength 0
   * @maxLength 20
   */
  evalAppVersion?: string
  /**
   * 新增评测规则
   * @minLength 0
   * @maxLength 255
   */
  evalRuleAdd?: string
  /**
   * 评测规则提示词
   * @minLength 0
   * @maxLength 65535
   */
  evalRulePromptWord?: string
  /**
   * 内置评测规则
   * @minLength 0
   * @maxLength 255
   */
  evalRuleSystem?: string
  /**
   * 执行耗时（秒）
   * @format int32
   */
  executionDuration?: number
  /**
   * 意图
   * @minLength 0
   * @maxLength 200
   */
  intention?: string
  /**
   * 输出格式
   * @minLength 0
   * @maxLength 20
   */
  outputFormat?: string
  /** 执行进度百分比 */
  progress?: number
  /**
   * 质量要求
   * @minLength 0
   * @maxLength 200
   */
  qualityRequirement?: string
  /**
   * 开始执行时间
   * @format date-time
   */
  startTime?: string
  /**
   * 任务状态：0-待执行，1-执行中，2-已完成，3-失败，4-已终止
   * @format int32
   */
  status?: number
  /**
   * 任务名称
   * @minLength 0
   * @maxLength 100
   */
  taskName?: string
  /**
   * 任务类型：1-自动评测，2-人工评测
   * @format int32
   */
  taskType?: number
  /** 评测数据列表（人工评测使用） */
  testData?: EvalTaskTestDataSaveDTO
  /**
   * 测试分析方法
   * @minLength 0
   * @maxLength 100
   */
  testMethod?: string
}

/**
 * EvalTaskTestData
 * 人工评测任务和评测数据表
 */
export interface EvalTaskTestData {
  /**
   * 创建时间
   * @format date-time
   */
  createTime?: string
  /**
   * 创建人ID
   * @format int64
   */
  createdBy?: number
  echoMap?: object
  /**
   * 评测数据文件ID
   * @minLength 0
   * @maxLength 36
   */
  fileId?: string
  /**
   * 评测数据文件名称
   * @minLength 0
   * @maxLength 255
   */
  fileName?: string
  /**
   * 评测数据文件类型
   * @minLength 0
   * @maxLength 20
   */
  fileType?: string
  /**
   * 评测任务ID
   * @format int64
   */
  taskId?: number
}

/**
 * EvalTaskTestDataSaveDTO
 * 人工评测任务和评测数据表
 */
export interface EvalTaskTestDataSaveDTO {
  /**
   * 评测数据文件ID
   * @minLength 0
   * @maxLength 36
   */
  fileId?: string
  /**
   * 评测数据文件名称
   * @minLength 0
   * @maxLength 255
   */
  fileName?: string
  /**
   * 评测数据文件类型
   * @minLength 0
   * @maxLength 20
   */
  fileType?: string
  /**
   * 评测任务ID
   * @format int64
   */
  taskId?: number
}

/**
 * EvalTaskUpdateDTO
 * 评测任务表
 */
export interface EvalTaskUpdateDTO {
  /**
   * 完成时间
   * @format date-time
   */
  endTime?: string
  /**
   * 评测助手ID
   * @minLength 0
   * @maxLength 36
   */
  evalAppId?: string
  /**
   * 评测助手版本号
   * @minLength 0
   * @maxLength 20
   */
  evalAppVersion?: string
  /**
   * 新增评测规则
   * @minLength 0
   * @maxLength 255
   */
  evalRuleAdd?: string
  /**
   * 评测规则提示词
   * @minLength 0
   * @maxLength 65535
   */
  evalRulePromptWord?: string
  /**
   * 内置评测规则
   * @minLength 0
   * @maxLength 255
   */
  evalRuleSystem?: string
  /**
   * 执行耗时（秒）
   * @format int32
   */
  executionDuration?: number
  /** 主键 */
  id?: string
  /**
   * 意图
   * @minLength 0
   * @maxLength 200
   */
  intention?: string
  /**
   * 输出格式
   * @minLength 0
   * @maxLength 20
   */
  outputFormat?: string
  /** 执行进度百分比 */
  progress?: number
  /**
   * 质量要求
   * @minLength 0
   * @maxLength 200
   */
  qualityRequirement?: string
  /**
   * 开始执行时间
   * @format date-time
   */
  startTime?: string
  /**
   * 任务状态：0-待执行，1-执行中，2-已完成，3-失败，4-已终止
   * @format int32
   */
  status?: number
  /**
   * 任务名称
   * @minLength 0
   * @maxLength 100
   */
  taskName?: string
  /**
   * 任务类型：1-自动评测，2-人工评测
   * @format int32
   */
  taskType?: number
  /**
   * 测试分析方法
   * @minLength 0
   * @maxLength 100
   */
  testMethod?: string
}

/** IPage«EvalTask» */
export interface IPageEvalTask {
  /** @format int64 */
  current?: number
  /** @format int64 */
  pages?: number
  records?: EvalTask[]
  /** @format int64 */
  size?: number
  /** @format int64 */
  total?: number
}

/**
 * PageParams«EvalTaskPageQuery»
 * 分页参数
 */
export interface PageParamsEvalTaskPageQuery {
  /**
   * 当前页
   * @format int64
   * @example 1
   */
  current?: number
  /** 扩展参数 */
  extra?: object
  /** 查询参数 */
  model: EvalTaskPageQuery
  /**
   * 排序规则, 默认descending
   * @example "descending"
   */
  order?: 'descending' | 'ascending'
  /**
   * 页面大小
   * @format int64
   * @example 10
   */
  size?: number
  /**
   * 排序,默认createTime
   * @example "id"
   */
  sort?: 'id' | 'createTime' | 'updateTime'
}

/** R«EvalTask» */
export interface REvalTask {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: EvalTask
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}

/** R«IPage«EvalTask»» */
export interface RIPageEvalTask {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: IPageEvalTask
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}

/** R«List«EvalTask»» */
export interface RListEvalTask {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: EvalTask[]
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}

/** R«boolean» */
export interface RBoolean {
  /**
   * 响应编码:0/200-请求处理成功
   * @format int32
   */
  code?: number
  /** 响应数据 */
  data?: boolean
  /** 异常消息 */
  errorMsg?: string
  /** 附加数据 */
  extra?: object
  isSuccess?: boolean
  /** 提示消息 */
  msg?: string
  /** 请求路径 */
  path?: string
  /**
   * 响应时间戳
   * @format int64
   */
  timestamp?: number
}
