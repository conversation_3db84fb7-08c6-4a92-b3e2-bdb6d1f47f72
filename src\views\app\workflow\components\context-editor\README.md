
### 基本用法

```vue
<template>
  <ContextEditor
    v-model:value="content"
    :app-id="appId"
    :node="nodeData"
    :mode="'workflow'"
    placeholder="请输入内容"
    @change="handleChange"
  />
</template>

<script setup>
import ContextEditor from '@/views/app/workflow/components/context-editor/index.vue'

const content = ref('')
const appId = 'your-app-id'
const nodeData = {
  id: 'node-id',
  type: 'answer',
  position: { x: 0, y: 0 },
  data: {}
}

const handleChange = (value) => {
  console.log('Content changed:', value)
}
</script>
```

### 高级用法（全功能）

```vue
<template>
  <div class="min-item-box" :class="{ 'max-item-box': isMaximized }">
    <ContextEditor
      v-model:value="content"
      v-model:edition-type="editionType"
      :app-id="appId"
      :node="nodeData"
      :mode="'workflow'"
      :show-jinja="true"
      :show-remove="true"
      :filter-type="['string', 'number']"
      placeholder="请输入内容，支持变量插入和Jinja2模板"
      warn="这是一个警告信息"
      @change="handleChange"
      @edition-type-change="handleEditionTypeChange"
      @remove-click="handleRemove"
      @maximize-click="handleMaximize"
      @jinja-inset-var="handleJinjaInsertVar"
    >
      <template #toolbarLeft>
        <span class="editor-title">自定义标题</span>
      </template>
    </ContextEditor>
  </div>
</template>
```

## API 文档

### Props

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| value | string | '' | 编辑器内容（支持 v-model） |
| appId | string | - | 应用ID（必需） |
| editionType | EditionType | EditionType.basic | 编辑模式（支持 v-model） |
| filterType | string \| Array<string> | undefined | 变量类型过滤 |
| node | Node | - | 节点数据（必需） |
| customOutputs | any[] | undefined | 自定义输出变量 |
| showRemove | boolean | false | 显示删除按钮 |
| showJinja | boolean | false | 显示Jinja模式开关 |
| placeholder | string | '请输入' | 占位符文本 |
| warn | string | '' | 警告信息 |
| mode | string | - | 模式（必需） |

### Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| update:value | value: string | 内容更新 |
| update:editionType | type: EditionType | 编辑模式更新 |
| editionTypeChange | checked: boolean | 编辑模式切换 |
| removeClick | - | 删除按钮点击 |
| maximizeClick | - | 最大化按钮点击 |
| change | value: string | 内容变化 |
| blur | - | 失去焦点 |
| input | event | 输入事件 |
| jinjaInsetVar | - | Jinja模式插入变量 |

### Slots

| 插槽名 | 说明 |
|--------|------|
| toolbarLeft | 工具栏左侧内容 |
| toolbarRight | 工具栏右侧内容 |
| error | 错误信息内容 |
