<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    title-align="start"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 500 ? 500 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <AiForm ref="formRef" v-model="form" layout="vertical" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { AiForm, type ColumnItem } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'
import { publishToll, updatePublishToll, getPublishToll, getAppConfig } from '@/apis'

const props = defineProps<{
  workflowInfo: any
  flowData: any
}>()
const emit = defineEmits<{
  (e: 'save-success'): void
}>()
const { width } = useWindowSize()

const dataId = ref('')
const visible = ref(false)
const isUpdate = computed(() => !!dataId.value)
const formRef = ref<InstanceType<typeof AiForm>>()
const isPublish = ref(false)
const title = computed(() => (isPublish.value ? '发布为工具' : `发布为工具`)) //暂定

import { useVueFlow } from '@vue-flow/core'
const { nodes } = useVueFlow()
const route = useRoute()
const appId = route.params.appId as string
const [form, resetForm] = useResetReactive({
  name: '' as string,
  description: '' as string,
  icon: { content: '+1', background: '#FFEAD5' },
  label: '' as string,
  privacy_policy: '',
  workflow_app_id: appId,
  workflow_tool_id: '' as string
})

const variables = ref<any>([])

const columns = reactive<ColumnItem[]>([
  {
    label: '应用名称',
    field: 'label',
    type: 'input',
    span: 24,
    required: true,
    props: {
      maxLength: 100
    }
  },
  {
    label: '工具调试名称',
    field: 'name',
    type: 'input',
    span: 24,
    required: true,
    props: {
      maxLength: 100
    }
  },
  {
    label: '应用描述',
    field: 'description',
    type: 'textarea',
    span: 24
  }
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}
interface WorkflowToolProviderRequest {
  name: string
  icon: string
  description: string
  label: string
  parameters: {
    name: string
    form: string
    description: string
    required?: boolean
    type?: string
  }[]
  labels: string[]
  privacy_policy: string
}

const getWorkflowInfos = async () => {
  form.label = props.workflowInfo.name
  const app = await getAppConfig({ appId: appId })
  if (!props.flowData?.features?.tool_published) return
  const res = await getPublishToll({ workflow_app_id: appId })
  form.workflow_tool_id = res.workflow_tool_id
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    console.log(variables.value)
    const parameters = variables.value.map((e) => {
      return {
        description: e.description,
        form: 'llm',
        name: e.variable
      }
    })
    const params = {
      ...form,
      parameters: parameters
    }

    if (!props.flowData?.features?.tool_published) {
      await publishToll(params)
    } else {
      await updatePublishToll(params)
    }
    Message.success('发布成功')
    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

// 新增
const onAdd = () => {
  variables.value = nodes.value.find((e) => e.type === 'start')?.data?.variables || []
  reset()
  getWorkflowInfos()
  visible.value = true
  isPublish.value = props.flowData?.features?.tool_published
}

onMounted(() => {
  // getWorkflowInfos()
})

defineExpose({ onAdd })
</script>
