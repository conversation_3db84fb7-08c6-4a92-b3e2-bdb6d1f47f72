<template>
  <AiPageLayout :margin="false">
    <a-space style="padding-bottom: 10px">
      <a-input-search v-model="search.keyword" placeholder="搜索名称/描述" allow-clear @search="getAppListFn" />
    </a-space>
    <a-scrollbar outer-style="height: calc(100% - 60px)" style="height: 100%; overflow-y: auto">
      <div style="height: 100%; padding: 7px">
        <a-row :gutter="[14, 14]">
          <a-col
            v-for="(item, index) in appsList"
            :key="index"
            :xs="24"
            :sm="24"
            :md="12"
            :lg="12"
            :xl="8"
            :xxl="8"
            class="card-col"
          >
            <a-card :bordered="true" hoverable>
              <a-card-meta>
                <template #title>
                  <a-space :size="20">
                    <a-avatar class="badgetyle" shape="square">
                      <span>
                        <AiSvgIcon :name="`workflow-ai-${item?.app?.mode}`" />
                      </span>
                    </a-avatar>
                    <div>
                      <a-typography-paragraph
                        :style="{ fontSize: '16px' }"
                        :ellipsis="{
                          rows: 1,
                          showTooltip: true,
                          css: true
                        }"
                      >
                        {{ item?.app?.name }}
                      </a-typography-paragraph>
                      <a-typography-text
                        :style="{ fontSize: '12px' }"
                        :ellipsis="{
                          rows: 1,
                          showTooltip: true,
                          css: true
                        }"
                        type="secondary"
                      >
                        {{ flowType[item?.app?.mode] }}
                      </a-typography-text>
                    </div>
                  </a-space>
                </template>
                <template #description>
                  <a-typography-paragraph
                    :style="{ height: '44px', marginTop: '10px' }"
                    :ellipsis="{
                      rows: 2,
                      showTooltip: true,
                      css: true
                    }"
                  >
                    <a-typography-text type="secondary">
                      {{ item.description }}
                    </a-typography-text>
                  </a-typography-paragraph>
                </template>

                <template #avatar>
                  <div :style="{ width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }">
                    <span class="button-hover">
                      <a-button size="small" type="primary" @click="onAdd(item)">
                        <template #icon><icon-plus /></template>
                        <template #default>添加至我的工作流空间</template>
                      </a-button>
                    </span>
                  </div>
                </template>
              </a-card-meta>
            </a-card>
          </a-col>
        </a-row>
      </div>
    </a-scrollbar>
    <AppAddModal ref="AppAddModalRef" />
  </AiPageLayout>
</template>

<script setup lang="ts">
import { getAppsList } from '@/apis'

import AppAddModal from './AppAddModal.vue'
interface AppResp {
  alias: string
  name: string
  owner: string
  desc: string
  logo: string
  url: string
  status: string
  statusColor: string
}
const flowType = {
  workflow: '工作流',
  'agent-chat': 'AGENT',
  chat: '聊天助手',
  'advanced-chat': 'CHATFLOW',
  completion: '文本生成'
}

const search = reactive({
  keyword: '',
  tag: ''
})
const appsListLoading = ref(false)
const app: { list: any[]; categories: any[] } = reactive({
  list: [],
  categories: []
})
const appListData = ref()
const getAppListFn = async () => {
  const res = await getAppsList()
  app.list = res.recommended_apps
}
const appsList = computed(() => {
  if (!search.keyword) return app.list
  return app.list.filter((res) => {
    const app = res?.app || {}
    const SEARCH_KEYWORD = search.keyword.toLowerCase()
    const SEARCH_TAG = search.tag
    // 关键字
    if (SEARCH_KEYWORD && app.name.toLowerCase().indexOf(SEARCH_KEYWORD) < 0) return false
    // 标签
    if (SEARCH_TAG && res.category != SEARCH_TAG) return false
    return true
  })
})

onMounted(() => {
  getAppListFn()
})

const AppAddModalRef = ref<InstanceType<typeof AppAddModal>>()
// 搜索
const searchFn = (name: any) => {
  console.log(name)
}
// 新增
const onAdd = (record: AppResp) => {
  AppAddModalRef.value?.onAdd(record)
}
</script>

<style scoped lang="scss">
.badgetyle {
  height: 36px;
  width: 36px;
  border-radius: 4px;
  background: linear-gradient(rgb(var(--primary-6)) 0%, rgb(var(--primary-4)) 100%);
}

:deep(.arco-card-bordered) {
  border-radius: 10px;

  &:hover {
    border-color: rgb(var(--primary-6));

    .button-hover .arco-btn {
      display: block;
    }
  }
}

:deep(.arco-card-meta-avatar) {
  width: 100%;
}

:deep(.arco-card-body) {
  position: relative;
  overflow: hidden;
}

.button-hover {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 24px;
  // border-radius: 50%;

  .arco-btn {
    color: #ffffff;
    display: none;
    background-color: rgb(var(--primary-6));
  }
}
</style>
