<template>
  <div class="relative h-full">
    <!--Header-->
    <div class="bg-default-subtle h-14 w-full">
      <div class="flex h-14 items-center justify-between px-6">
        <div class="flex items-center">
          <div class="system-xl-semibold text-text-primary">编排</div>
          <div class="ml-2">
            <a-button @click="handleSave">发布</a-button>
          </div>
          <div class="flex h-[14px] items-center space-x-1 text-xs">
            <div
              v-if="isAdvancedMode"
              class="system-xs-medium-uppercase ml-1 flex h-5 items-center rounded-md border border-components-button-secondary-border px-1.5 uppercase text-text-tertiary"
            >
              专家模式
            </div>
          </div>
        </div>
        <div class="flex items-center">
          <!--agent 设置-->
          <AgentSettingButton :agentConfig="modelConfig.agentConfig" />
          <!--模型选择-->
          <ModelParameterModal :provider="modelConfig.provider" :modelId="modelConfig.model_id" :completionParams="{}">
            <!--<template #trigger>
              <a-button>模型</a-button>
            </template>-->
          </ModelParameterModal>
        </div>
      </div>
    </div>
    <div class="flex h-full">
      <div class="flex h-full w-full shrink-0 flex-col sm:w-1/2">
        <Config
          :modelConfig="modelConfig"
          :datasetConfigs="datasetConfigs"
          :collectionList="collectionList"
          @setModelConfig="setModelConfig"
        />
      </div>
      <div class="relative flex h-full w-1/2 grow flex-col overflow-y-auto">
        <Debug class="h-fulll overflow-y-auto" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { PromptMode, PromptVariable } from '@/views/app/workflow/types/debug'
import AgentSettingButton from '@/views/app/configuration/config/AgentSettingButton.vue'
import { ModelModeType } from '@/views/app/workflow/types/app'
import Debug from '@/views/app/configuration/debug/index.vue'
import {
  ANNOTATION_DEFAULT,
  DATASET_DEFAULT,
  DEFAULT_AGENT_SETTING,
  RETRIEVE_TYPE
} from '@/views/app/workflow/constant/configuration'
import { getAppConfig, getDatasetList } from '@/apis'
import {
  correctModelProvider,
  correctToolProvider,
  getMultipleRetrievalConfig,
  useModelListAndDefaultModelAndCurrentProviderAndModel,
  useTextGenerationCurrentProviderAndModelAndModelList
} from '@/views/app/workflow/utils/configuration'
import { userInputsFormToPromptVariables } from '@/views/app/workflow/utils/model-config'
import { fetchCollectionList } from '@/apis/workflow/tools'
import ModelParameterModal from '@/views/app/configuration/components/model-parameter-modal/index.vue'
import { useProviderStore } from '@/stores/modules/workflow/provider'
import { ModelTypeEnum } from '@/apis/model-mgmt/type'
import Config from '@/views/app/configuration/config/index.vue'

const isAdvancedMode = computed(() => {
  return promptMode.value === PromptMode.advanced
})
const currentModel = ref()
const currentProvider = ref()
const modelConfig = reactive<any>({
  // provider: 'langgenius/openai/openai',
  provider: 'langgenius/siliconflow/siliconflow',
  model_id: 'deepseek-ai/DeepSeek-R1',
  // model_id: 'gpt-3.5-turbo',
  mode: ModelModeType.unset,
  configs: {
    prompt_template: '',
    prompt_variables: [] as PromptVariable[]
  },
  more_like_this: null,
  opening_statement: '',
  suggested_questions: [],
  sensitive_word_avoidance: null,
  speech_to_text: null,
  text_to_speech: null,
  file_upload: null,
  suggested_questions_after_answer: null,
  retriever_resource: null,
  annotation_reply: null,
  dataSets: [],
  agentConfig: DEFAULT_AGENT_SETTING
})
const $route = useRoute()
const appId = $route.params.appId || ''

const datasetConfigs = ref({
  retrieval_model: RETRIEVE_TYPE.multiWay,
  reranking_model: {
    reranking_provider_name: '',
    reranking_model_name: ''
  },
  top_k: DATASET_DEFAULT.top_k,
  score_threshold_enabled: false,
  score_threshold: DATASET_DEFAULT.score_threshold,
  datasets: {
    datasets: []
  }
})

const collectionList = ref([])
const getCollectionList = async () => {
  // /workspaces/current/tool-providers
  const res = await fetchCollectionList()
  console.log('工具函数热搜：', res)
  collectionList.value = res || []
  const basePath = '' // TODO-todo: 修改
  if (basePath) {
    collectionList.value.forEach((item) => {
      if (typeof item.icon == 'string' && !item.icon.includes(basePath)) item.icon = `${basePath}${item.icon}`
    })
  }
}
const mode = ref('')
const promptMode = ref(PromptMode.simple)
const dataSets = ref([])
const introduction = ref('')
const suggestedQuestions = ref([])
const moreLikeThisConfig = ref({
  enabled: false
})
const suggestedQuestionsAfterAnswerConfig = ref({
  enabled: false
})

const speechToTextConfig = ref({
  enabled: false
})
const textToSpeechConfig = ref({
  enabled: false,
  voice: '',
  language: ''
})
const citationConfig = ref({
  enabled: false
})

const annotationConfig = ref({
  id: '',
  enabled: false,
  score_threshold: ANNOTATION_DEFAULT.score_threshold,
  embedding_model: {
    embedding_provider_name: '',
    embedding_model_name: ''
  }
})
const moderationConfig = ref({
  enabled: false
})
const externalDataToolsConfig = ref([])
const getAppDetail = async () => {
  await getCollectionList()
  const res = await getAppConfig({ appId })
  console.log('应用详情res：', res)
  mode.value = res.mode
  const modelConfig = res.model_config
  const promptModeTemp = modelConfig.prompt_type === PromptMode.advanced ? PromptMode.advanced : PromptMode.simple
  promptMode.value = promptModeTemp
  if (promptModeTemp === PromptMode.advanced) {
    // TODO-todo: xxx
  }

  const model = res.model_config.model
  let datasets: any = null
  // old dataset struct
  if (modelConfig.agent_mode?.tools?.find(({ dataset }: any) => dataset?.enabled))
    datasets = modelConfig.agent_mode?.tools.filter(({ dataset }: any) => dataset?.enabled)
  // new dataset struct
  else if (modelConfig.dataset_configs.datasets?.datasets?.length > 0)
    datasets = modelConfig.dataset_configs?.datasets?.datasets
  //
  if (dataSets && datasets?.length && datasets?.length > 0) {
    const params = {
      page: 1,
      ids: datasets.map(({ dataset }: any) => dataset.id)
    }
    const { data: dataSetsWithDetail } = await getDatasetList(params)
    console.log('知识库res：', dataSetsWithDetail)
    datasets = dataSetsWithDetail
    dataSets.value = datasets
  }

  introduction.value = modelConfig.opening_statement
  suggestedQuestions.value = modelConfig.suggested_questions || []
  if (modelConfig.more_like_this) {
    moreLikeThisConfig.value = modelConfig.more_like_this
  }
  if (modelConfig.suggested_questions_after_answer)
    suggestedQuestionsAfterAnswerConfig.value = modelConfig.suggested_questions_after_answer

  if (modelConfig.speech_to_text) speechToTextConfig.value = modelConfig.speech_to_text
  if (modelConfig.text_to_speech) textToSpeechConfig.value = modelConfig.text_to_speech
  if (modelConfig.retriever_resource) citationConfig.value = modelConfig.retriever_resource

  if (modelConfig.annotation_reply) {
    let annotationConfig = modelConfig.annotation_reply
    if (modelConfig.annotation_reply.enabled) {
      // TODO-todo: xxxx
    }
    setAnnotationConfig(annotationConfig, true)
  }
  if (modelConfig.sensitive_word_avoidance) moderationConfig.value = modelConfig.sensitive_word_avoidance
  if (modelConfig.external_data_tools) externalDataToolsConfig.value = modelConfig.external_data_tools

  const config = {
    modelConfig: {
      provider: correctModelProvider(model.provider),
      model_id: model.name,
      mode: model.mode,
      configs: {
        prompt_template: modelConfig.pre_prompt || '',
        prompt_variables: userInputsFormToPromptVariables(
          [
            ...modelConfig.user_input_form,
            ...(modelConfig.external_data_tools?.length
              ? modelConfig.external_data_tools.map((item: any) => {
                return {
                  external_data_tool: {
                    variable: item.variable as string,
                    label: item.label as string,
                    enabled: item.enabled,
                    type: item.type as string,
                    config: item.config,
                    required: true,
                    icon: item.icon,
                    icon_background: item.icon_background
                  }
                }
              })
              : [])
          ],
          modelConfig.dataset_query_variable
        )
      },
      more_like_this: modelConfig.more_like_this,
      opening_statement: modelConfig.opening_statement,
      suggested_questions: modelConfig.suggested_questions,
      sensitive_word_avoidance: modelConfig.sensitive_word_avoidance,
      speech_to_text: modelConfig.speech_to_text,
      text_to_speech: modelConfig.text_to_speech,
      file_upload: modelConfig.file_upload,
      suggested_questions_after_answer: modelConfig.suggested_questions_after_answer,
      retriever_resource: modelConfig.retriever_resource,
      annotation_reply: modelConfig.annotation_reply,
      external_data_tools: modelConfig.external_data_tools,
      dataSets: datasets || [],
      agentConfig:
        res.mode === 'agent-chat'
          ? {
            max_iteration: DEFAULT_AGENT_SETTING.max_iteration,
            ...modelConfig.agent_mode,
            // remove dataset
            enabled: true, // modelConfig.agent_mode?.enabled is not correct. old app: the value of app with dataset's is always true
            tools: modelConfig.agent_mode?.tools
              .filter((tool: any) => {
                return !tool.dataset
              })
              .map((tool: any) => {
                const toolInCollectionList = collectionList.value.find((c) => tool.provider_id === c.id)
                return {
                  ...tool,
                  isDeleted: res.deleted_tools?.some(
                    (deletedTool: any) => deletedTool.id === tool.id && deletedTool.tool_name === tool.tool_name
                  ),
                  notAuthor: toolInCollectionList?.is_team_authorization === false,
                  ...(tool.provider_type === 'builtin'
                    ? {
                      provider_id: correctToolProvider(tool.provider_name, !!toolInCollectionList),
                      provider_name: correctToolProvider(tool.provider_name, !!toolInCollectionList)
                    }
                    : {})
                }
              })
          }
          : DEFAULT_AGENT_SETTING
    },
    completionParams: model.completion_params
  }

  setModelConfig(config.modelConfig)
  setCompletionParams(config.completionParams)
  const retrievalConfig = getMultipleRetrievalConfig(
    {
      ...modelConfig.dataset_configs,
      reranking_model: modelConfig.dataset_configs.reranking_model && {
        provider: modelConfig.dataset_configs.reranking_model.reranking_provider_name,
        model: modelConfig.dataset_configs.reranking_model.reranking_model_name
      }
    }, datasets, datasets, {
      provider: currentProvider.value?.provider,
      model: currentModel.value?.model
    }
  )
  console.log('retrievalConfig:', retrievalConfig)

  datasetConfigs.value = {
    retrieval_model: RETRIEVE_TYPE.multiWay,
    ...modelConfig.dataset_configs,
    ...retrievalConfig,
    ...(retrievalConfig.reranking_model ? {
      reranking_model: {
        reranking_model_name: retrievalConfig.reranking_model.model,
        reranking_provider_name: correctModelProvider(retrievalConfig.reranking_model.provider)
      }
    } : {})
  }
  console.log('datasetConfigs:', datasetConfigs.value)
}
const setModelConfig = (newModelConfig: any) => {
  Object.assign(modelConfig, newModelConfig)
  console.log('configuration更新后的值modelConfig：', modelConfig)
}

// 模型参数的form值（对象格式）
const setCompletionParams = (newModelConfig) => {
  providerStore.provider.completion_params = newModelConfig
}
const setAnnotationConfig = (config, notSetFormatChanged?: boolean) => {
  annotationConfig.value = config
  if (!notSetFormatChanged) {
    // TODO-todo: xxx
  }
}
getAppDetail()

/**
 * 模型
 */
const providerStore = useProviderStore()
// 获取llm模型list
const getModelList = async () => {
  try {
    await providerStore.getModelList(ModelTypeEnum.textGeneration)

    const obj = useTextGenerationCurrentProviderAndModelAndModelList({
      provider: modelConfig.provider,
      model: modelConfig.model_id
    })
    // currentProvider.value = obj.currentProvider
    // currentModel.value = obj.currentModel
    providerStore.provider.currentProvider = obj.currentProvider
    providerStore.provider.currentModel = obj.currentModel

    await providerStore.getParamsList()
  } catch (error) {
    console.log(error)
  }
}

const handleSave = () => {
  console.log('save:', modelConfig)
}
const getModelAndDefault = async () => {
  const res = await useModelListAndDefaultModelAndCurrentProviderAndModel(ModelTypeEnum.rerank)
  currentModel.value = res.currentModel
  currentProvider.value = res.currentProvider
}
onMounted(() => {
  getModelList()
  getModelAndDefault()
})
</script>
<style scoped lang="scss"></style>
