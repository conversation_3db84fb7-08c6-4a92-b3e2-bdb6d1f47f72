<template>
  <div class="error-strategy">
    <Field :fieldTitle="'异常处理'" tooltip="配置异常处理策略，当节点发生异常时触发。">
      <template #operation>
        <a-select
          :default-value="nodeInfo?.error_strategy || 'none'"
          class="error-strategy-select"
          @change="handleChangeErrorStrategy"
        >
          <a-option v-for="option in errorStrategyList" :value="option.value" :label="option.label" class="w-full">
            <a-space direction="vertical" :size="0" class="w-full">
              <span>{{ option.label }}</span>
              <a-typography-text
                type="secondary"
                :ellipsis="{ showTooltip: true }"
                style="font-size: 12px; width: 100%"
              >
                {{ option.desc }}
              </a-typography-text>
            </a-space>
          </a-option>
        </a-select>
      </template>

      <div>
        <template v-if="nodeInfo.error_strategy === 'default-value'">
          <a-typography-text type="secondary">
            当节点发生异常时，将自动执行失败分支。
            <a-link>了解更多</a-link>
          </a-typography-text>

          <div v-for="item in nodeInfo.default_value" class="error-form-item">
            <a-space direction="vertical" fill :size="4">
              <a-space>
                <a-typography-text>
                  {{ item.key }}
                </a-typography-text>
                <a-typography-text type="secondary">
                  {{ item.type }}
                </a-typography-text>
              </a-space>

              <template v-if="item.type === 'text'">
                <a-input v-model="item.value" placeholder="请输入" />
              </template>
              <template v-else-if="item.type === 'number'">
                <a-input-number v-model="item.value" placeholder="请输入" class="input-demo" />
              </template>
              <template v-else-if="item.type === 'object'">
                <a-textarea v-model="item.value" placeholder="请输入" />
              </template>
              <template v-else>
                <a-input v-model="item.value" placeholder="请输入" />
              </template>
            </a-space>
          </div>
        </template>
        <template v-else-if="nodeInfo.error_strategy == 'fail-branch'">
          <a-card class="error-strategy-branch">
            <template #cover>
              <div class="error-strategy-avatar">
                <a-avatar>
                  <icon-share-alt />
                </a-avatar>
              </div>
            </template>
            <a-card-meta>
              <template #avatar>
                <div :style="{ display: 'flex', alignItems: 'center', color: '#1D2129' }">
                  <!--<a-avatar>-->
                  <!--  <icon-share-alt />-->
                  <!--</a-avatar>-->
                  <!--<a-typography-text>当节点发生异常时，将自动执行失败分支。</a-typography-text>-->
                </div>
              </template>
              <template #title>在画布自定义失败分支逻辑。</template>
              <template #description>
                当节点发生异常时，将自动执行失败分支。失败分支允许您灵活地提供错误消息、报告、修复或跳过操作。
                <a-link>了解更多</a-link>
              </template>
            </a-card-meta>
          </a-card>
        </template>
        <template v-else />
      </div>
    </Field>
  </div>
</template>

<script setup lang="ts">
import Field from '@/views/app/workflow/nodes/http/components/Field.vue'

const props = defineProps(['nodeInfo'])
const errorStrategyList = [
  { label: '无', value: 'none', desc: '当发生异常且未处理时，节点将停止运行' },
  { label: '默认值', value: 'default-value', desc: '当发生异常时，指定默认输出内容。' },
  { label: '异常分支', value: 'fail-branch', desc: '当发生异常时，将执行异常分支' }
]

/**
 * 切换异常处理：
 *  none: 异常的相关字段都没有；
 * @param val
 */
const handleChangeErrorStrategy = (val: string) => {
  console.log('val:', val)
  if (val === 'none') {
    props.nodeInfo.error_strategy = undefined
    props.nodeInfo.default_value = undefined
    return false
  } else if (val === 'fail-branch') {
    props.nodeInfo.default_value = undefined
  }
  props.nodeInfo.error_strategy = val
  if (!props.nodeInfo.default_value) {
    if (props.nodeInfo.error_strategy === 'default-value') {
      if (props.nodeInfo.type === 'http-request') {
        props.nodeInfo.default_value = [
          { key: 'body', type: 'string', value: '' },
          { key: 'status_code', type: 'number', value: 0 },
          { key: 'headers', type: 'object', value: '{}' }
        ]
      } else if (props.nodeInfo.type === 'llm') {
        props.nodeInfo.default_value = [{ key: 'text', type: 'string', value: '' }]
      }
    }
  }
}
</script>

<style scoped lang="scss">
.error-strategy-avatar {
  padding: 16px 16px 0 16px;
}

.error-strategy-branch {
  border-radius: 8px;
  background: linear-gradient(90deg, rgba(200, 206, 218, 0.2), rgba(200, 206, 218, 0.04));
}

.error-form-item {
  margin: 12px 0;
}
.error-strategy {
  :deep(.error-strategy-select) {
    width: 240px;
    .arco-select-option-content {
      width: 100%;
    }
  }
}
</style>
