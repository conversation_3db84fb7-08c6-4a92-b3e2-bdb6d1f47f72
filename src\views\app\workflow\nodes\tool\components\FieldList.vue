<template>
  <div class="space-y-1">
    <div class="system-sm-semibold-uppercase text-text-secondary">输入变量</div>
    <div v-for="item in nodeInfo.paramSchemas" :key="item.name" class="input-box">
      <div class="flex h-[18px] items-center space-x-2">
        <span class="code-sm-semibold text-text-secondary">{{ item.label.zh_Hans }}</span>
        <span class="system-xs-regular text-text-tertiary">{{ item.type }}</span>
        <span class="system-xs-regular text-util-colors-orange-dark-orange-dark-600" style="color: #e62e05">
          Required
        </span>
      </div>
      <div style="margin: 5px 0; display: flex">
        <a-select
          v-if="item.type == 'number'"
          v-model="nodeInfo.tool_parameters[item.name].type"
          :options="['Variable', 'Constant']"
          :style="{ width: '160px' }"
        />
        <a-input
          v-model="nodeInfo.tool_parameters[item.name].value"
          :type="item.type == 'number' ? 'number' : 'text'"
          :style="{ width: '100%' }"
          placeholder="Please enter something"
          allow-clear
        >
          <template #suffix>
            <icon-info-circle />
          </template>
        </a-input>
      </div>
    </div>

    <a-collapse style="margin-top: 20px" :default-active-key="['1']" expand-icon-position="right" :bordered="false">
      <a-collapse-item key="1" header="输出变量">
        <template #header>
          <span class="system-sm-semibold-uppercase text-text-secondary">输出变量</span>
        </template>
        <div class="flex">
          <div class="py-1">
            <div class="flex">
              <div class="flex items-center leading-[18px]">
                <div class="code-sm-semibold text-text-secondary">text</div>
                <div class="system-xs-regular ml-2 text-text-tertiary">string</div>
              </div>
            </div>
            <div class="system-xs-regular mt-0.5 text-text-tertiary">工具生成的内容</div>
          </div>
        </div>
        <div class="flex">
          <div class="py-1">
            <div class="flex">
              <div class="flex items-center leading-[18px]">
                <div class="code-sm-semibold text-text-secondary">json</div>
                <div class="system-xs-regular ml-2 text-text-tertiary">array[object]</div>
              </div>
            </div>
            <div class="system-xs-regular mt-0.5 text-text-tertiary">工具生成的json</div>
          </div>
        </div>
        <div class="flex">
          <div class="py-1">
            <div class="flex">
              <div class="flex items-center leading-[18px]">
                <div class="code-sm-semibold text-text-secondary">text</div>
                <div class="system-xs-regular ml-2 text-text-tertiary">string</div>
              </div>
            </div>
            <div class="system-xs-regular mt-0.5 text-text-tertiary">工具生成的内容</div>
          </div>
        </div>
      </a-collapse-item>
    </a-collapse>
    <a-divider />
    <!--retryConfig-->
    <RetryConfig :nodeInfo="nodeInfo" />

    <a-divider />

    <!--异常处理-->
    <ErrorStrategy :nodeInfo="nodeInfo" />
    <a-divider />
  </div>
</template>

<script setup lang="ts">
import RetryConfig from '@/views/app/workflow/nodes/http/components/RetryConfig.vue'
import ErrorStrategy from '@/views/app/workflow/nodes/http/components/ErrorStrategy.vue'
interface FieldType {
  label: string
  max_length: number
  options: string[]
  required: boolean
  type: string
  variable: string
}

const props = defineProps<{
  list: FieldType[]
  isChatMode?: false
  nodeInfo?: any
}>()
if (!props.nodeInfo.tool_parameters || Object.keys(props.nodeInfo.tool_parameters).length === 0) {
  props.nodeInfo.tool_parameters = props.nodeInfo.paramSchemas.reduce((acc, obj) => {
    acc[obj.name] = {
      type: obj.type == 'number' ? 'Constant' : 'mixed',
      value: '' as any
    } // 将 name 作为键，对象作为值
    return acc
  }, {})
}

const fillVisable = ref(false)
const retryCount = ref(3)
const retryInterval = ref(3000)
const list = ref([
  {
    id: 1,
    type: 'mixed',
    label: '功能模块',
    value: ''
  },
  {
    id: 2,
    type: 'mixed',
    value: '',
    label: '公共规则'
  },
  {
    id: 3,
    type: 'mixed',
    value: '',
    label: '功能既有规则'
  }
])
const defaultList = [
  {
    label: '',
    required: false,
    readonly: true,
    type: 'string',
    variable: 'sys.user_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'array[file]',
    variable: 'sys.files'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.app_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_id'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.workflow_run_id'
  }
]
const isChatModeDefaultList = [
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.query'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.dialogue_count'
  },
  {
    label: '',
    readonly: true,
    required: false,
    type: 'string',
    variable: 'sys.conversation_id'
  }
]
const processedList = computed(() => {
  if (props.isChatMode) {
  }
  return [
    ...props.list.map((item) => {
      const processed = {
        ...item,
        readonly: false
      }
      return processed
    }),
    ...defaultList,
    ...(props.isChatMode ? isChatModeDefaultList : [])
  ]
})
onMounted(() => {
  console.log(
    props.list.map((e) => {
      console.log(e)
    })
  )
})
</script>
<style scoped lang="scss">
.space-y-1 {
  .input-box {
    width: 100%;
    margin-top: 10px;
  }
  .fill-title {
    display: flex;
    margin-top: 20px;
    justify-content: space-between;
    align-items: center;
  }
  .file-content {
    padding: 10px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    v-deep .arco-input-append {
      padding: 0;
    }
  }
}
</style>
