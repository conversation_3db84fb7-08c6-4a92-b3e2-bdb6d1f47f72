export default [
  {
    dictCode: 'APP_MANAGER_TABS',
    dictName: '请求类型',
    elements: [
      {
        value: 'arrange',
        name: '编排',
        code: 'ARRANGE'
      },
      {
        value: 'workflow',
        name: '编排',
        code: 'WORKFLOW'
      },
      {
        value: 'dialog_flow',
        name: '编排',
        code: 'DIALOGUE_FLOW'
      },
      {
        value: 'access',
        name: '对外接口',
        code: 'ACCESS'
      },
      {
        value: 'logs',
        name: '会话日志',
        code: 'LOGS'
      },
      {
        value: 'tagging',
        name: '标注',
        code: 'TAGGING'
      },
      {
        value: 'overview',
        name: '总览',
        code: 'OVERVIEW'
      }
    ],
    rules: []
  },
  {
    dictCode: 'MODEL_MANAGER_BUILT_IN_TABS',
    dictName: '请求类型',
    elements: [
      {
        value: 'llm',
        name: '大语言模型',
        code: 'LLM'
      },
      {
        value: 'text-embedding',
        name: '文本嵌入模型',
        code: 'TEXT_EMBEDDING'
      },
      {
        value: 'rerank',
        name: '重排序模型',
        code: 'RERANK'
      }
    ],
    rules: []
  },
  {
    dictCode: 'MODEL_CREDENTIAL_SCHEMA',
    dictName: '添加模型设置切换',
    elements: [
      {
        value: 'provider_credential_schema',
        name: '模型配置设置',
        code: 'PROVIDER_CREDENTIAL_SCHEMA'
      },
      {
        value: 'model_credential_schema',
        name: '添加子模型',
        code: 'MODEL_CREDENTIAL_SCHEMA'
      }
    ],
    rules: []
  },
  {
    dictCode: 'KNOWLEDGE_TABS',
    dictName: '知识库类型',
    elements: [
      {
        value: 'MY',
        name: '我的',
        code: 'MY'
      },
      {
        value: 'SHARE',
        name: '分享',
        code: 'SHARE'
      }
    ],
    rules: []
  },
  {
    dictCode: 'DATASET_TABS',
    dictName: '数据集类型',
    elements: [
      {
        value: 'TRAIN',
        name: '训练数据集',
        code: 'TRAIN'
      },
      {
        value: 'EVALUATION',
        name: '评测数据集',
        code: 'EVALUATION'
      }
    ],
    rules: []
  },
  {
    dictCode: 'DATA_PROCESS_JOB_TABS',
    dictName: '数据处理任务类型',
    elements: [
      {
        value: 0,
        name: '数据清洗',
        code: 'DATA_CLEAN'
      },
      {
        value: 1,
        name: '数据增强',
        code: 'DATA_STRENGTHEN'
      },
      {
        value: 2,
        name: '文本分类',
        code: 'TEXT_CLASSIFY'
      }
    ]
  },
  {
    dictCode: 'DATA_PROCESS_JOB_HANDLE_TABS',
    dictName: '数据处理任务处理类型',
    elements: [
      {
        value: 1,
        name: '处理详情',
        code: 'DETAIL'
      },
      {
        value: 2,
        name: '处理结果',
        code: 'RESULT'
      }
    ]
  }
]
