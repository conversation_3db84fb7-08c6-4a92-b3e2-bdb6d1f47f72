<template>
  <a-select
    :style="{ width: '70%' }"
    :default-value="getDefaultValue"
    placeholder="Please select ..."
    @change="handleChangeContext"
  >
    <a-option
      v-for="items in defaultList"
      :key="items.value_selector.join('.')"
      :value="items.value_selector.join('.')"
      :label="items.variable"
    />
  </a-select>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'

const props = defineProps<{
  nodeitem?: { variable_selector: any }
  defaultList?: any[]
}>()
const customList = ref<any[]>([])
// 获取默认值的函数
const getDefaultValue = props.nodeitem.variable_selector.join('.')
const emit = defineEmits<{
  (e: 'changeoptionlist', value: string): void
}>()
const handleChangeContext = (value: string) => {
  console.log('Selected value:', value)
  if (value) {
    const newSelector = value.split('.')
    emit('changeoptionlist', newSelector.join('.'))
    // if(value=='sys.files'){
    // emit('changeoptionlist', props.nodeitem)
    // }
  } else {
    emit('changeoptionlist', '')
  }

  // 你可以在这里处理选择变化的逻辑
}
</script>

<style scoped lang="scss">
/* 你可以根据需要添加样式 */
</style>
