<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 500 ? 500 : '100%'"
    draggable
    @before-ok="save"
    @close="reset"
  >
    <AiForm ref="formRef" v-model="form" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { saveUsingPost, updateUsingPut, getUsingGet } from '@/apis/template'
import { type ColumnItem, AiForm } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'

const props = defineProps<{
  modalVisible: boolean
  appId: string
}>()

const emit = defineEmits<{
  (e: 'save-success'): void
  (e: 'hide-modal'): void
}>()

const { width } = useWindowSize()

const visible = ref(props.modalVisible)
const title = computed(() => (props.appId ? '修改' : '新增'))
const formRef = ref<InstanceType<typeof AiForm>>()

const [form, resetForm] = useResetReactive({
  name: '',
  type: 1,
  description: '',
  version: '',
  status: 1
})

const columns = reactive<ColumnItem[]>([
  {
    label: '评测集名称',
    field: 'name',
    type: 'input',
    span: 24,
    required: true,
    props: {
      maxLength: 100,
      placeholder: '请输入评测集名称'
    }
  },
  {
    label: '版本',
    field: 'version',
    type: 'input',
    span: 24,
    props: {
      maxLength: 100,
      placeholder: '请输入版本'
    }
  },
  {
    label: '描述',
    field: 'description',
    type: 'input',
    span: 24,
    props: {
      maxLength: 100,
      placeholder: '请输入描述'
    }
  }
])

onMounted(async () => {
  if (props.appId) {
    const { data } = await getUsingGet(props.appId)
    Object.assign(form, data)
  }
})

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
  emit('hide-modal')
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    if (props.appId) {
      await updateUsingPut({ ...form, id: props.appId })
      Message.success('修改成功')
    } else {
      await saveUsingPost(form)
      Message.success('新增成功')
    }
    emit('save-success')
    return true
  } catch (error) {
    return false
  }
}

defineExpose({ visible })
</script>
