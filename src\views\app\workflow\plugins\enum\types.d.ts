export type ENUM_DATA_LIST = Array<ENUM_DATA>

export interface ENUM_DATA {
  dictCode: string
  dictName: string
  elements: Array<ENUM_ELEMENT>
  rules: Array<ENUM_RULE>
}

export type ENUM_ELEMENTS = Array<ENUM_ELEMENT>

export interface ENUM_ELEMENT {
  value: ENUM_VALUE
  name: string
  code: string
  [key: string | symbol]: any
}

export interface ENUM_RULE {
  [key: string | symbol]: Array<ENUM_VALUE>
}

export type ENUM_VALUE = string | number | boolean

export interface TRANSFORM_ENUM_DATA_LIST {
  _level: 0
  [key: string]: number | TRANSFORM_ENUM_DATA | ProxyHandler<TRANSFORM_ENUM_DATA> // 其他属性名为字符串，属性值为 ProxyHandler<TRANSFORM_ENUM_DATA>
}

export interface TRANSFORM_ENUM_DATA {
  $NAME?: string
  $RULE?: ENUM_RULE
  $LIST?: null | Array<TRANSFORM_ENUM_ELEMENT>
  $MATCH?: null | ((value: any, key: string) => any)
  $ATTRIBUTE?: ATTRIBUTE
  $VALUE_TYPE?: VALUE_TYPE
  _level?: 1
  [key: string | symbol]: any
}

type VALUE_TYPE = 'number' | 'string' | 'boolean'

export type ATTRIBUTE = Map<string, ProxyHandler<TRANSFORM_ENUM_ELEMENT>>

export interface TRANSFORM_ENUM_ELEMENT extends ENUM_ELEMENT {
  _key?: string
  _level?: 2
  $NAME?: string
}

export type TRANSFORM_ENUM_ELEMENTS = Array<TRANSFORM_ENUM_ELEMENT>
