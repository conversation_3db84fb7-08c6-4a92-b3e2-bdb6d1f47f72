<template>
  <div class="api-tab">
    <!-- API服务信息 -->
    <div class="api-service-info">
      <div class="info-label">API 服务器</div>
      <div class="info-content">
        <span class="api-url">http://172.16.10.116/v1</span>
        <a-button size="mini" shape="circle">
          <template #icon><icon-copy /></template>
        </a-button>
      </div>
      <a-tag size="small" color="green">运行中</a-tag>
      <a-button type="text" size="small">
        API 密钥
        <template #icon><icon-right /></template>
      </a-button>
    </div>

    <!-- API文档主体 -->
    <div class="api-doc-container">
      <div class="api-doc-content">
        <h1 class="api-title">知识库 API</h1>

        <!-- 鉴权部分 -->
        <div class="api-section">
          <h2 class="section-title">鉴权</h2>
          <p class="section-desc">
            使用
            <code>API-Key</code>
            进行鉴权。
          </p>
          <p class="section-desc">
            建议开发者将
            <code>API-Key</code>
            放在后端存储，而非分享或泄露在客户端使用，以免
            <code>API-Key</code>
            泄露，导致财产损失。
          </p>
          <p class="section-desc">
            所有 API 请求都必须在
            <code>Authorization</code>
            HTTP Header 中包含的
            <code>API-Key</code>
            ，如下所示:
          </p>

          <div class="code-block">
            <div class="code-header">Code</div>
            <pre class="code-content">Authorization: Bearer {API_KEY}</pre>
          </div>
        </div>

        <!-- 通过文本创建文档 -->
        <div class="api-section">
          <h2 class="section-title">通过文本创建文档</h2>
          <p class="section-desc">此接口基于已存在知识库，在此知识库的基础上通过文本创建新文档</p>

          <div class="api-endpoint">
            <span class="method post">POST</span>
            <span class="endpoint-path">/datasets/{dataset_id}/document/create-by-text</span>
          </div>

          <h3 class="subsection-title">Path</h3>

          <div class="request-container">
            <h3 class="subsection-title">Request</h3>
            <div class="code-block request-block">
              <div class="code-header">
                <span class="method post">POST</span>
                <span class="endpoint-path">/datasets/{dataset_id}/document/create-by-text</span>
              </div>
              <pre class="code-content">
curl --location --request POST 'http://172.16.10.116/v1/datasets/{dataset_id}/document/create-by-text' \
--header 'Authorization: Bearer {API_KEY}' \
--header 'Content-Type: application/json' \
--data-raw '{
  "name": "document name",
  "text": "document content here",
  "metadata": {
    "source": "source of this document",
    "author": "author of this document"
  }
}'</pre
              >
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧目录 -->
      <div class="api-doc-toc">
        <div class="toc-header">
          <h3 class="toc-title">目录</h3>
          <a-button type="text" size="mini" class="toc-close">
            <template #icon><icon-close /></template>
          </a-button>
        </div>

        <ul class="toc-list">
          <li class="toc-item active">通过文本创建文档</li>
          <li class="toc-item">通过文件创建文档</li>
          <li class="toc-item">创建空知识库</li>
          <li class="toc-item">知识库列表</li>
          <li class="toc-item">查看知识库详情</li>
          <li class="toc-item">修改知识库详情</li>
          <li class="toc-item">删除知识库</li>
          <li class="toc-item">通过文本更新文档</li>
          <li class="toc-item">通过文件更新文档</li>
          <li class="toc-item">获取文档解析状态（进度）</li>
          <li class="toc-item">删除文档</li>
          <li class="toc-item">知识库文档列表</li>
          <li class="toc-item">新增分段</li>
          <li class="toc-item">查询文档分段</li>
          <li class="toc-item">删除文档分段</li>
          <li class="toc-item">更新文档分段</li>
          <li class="toc-item">新增文档子分段</li>
          <li class="toc-item">查询文档子分段</li>
          <li class="toc-item">删除文档子分段</li>
          <li class="toc-item">更新文档子分段</li>
          <li class="toc-item">获取上传文件</li>
          <li class="toc-item">检索知识库</li>
          <li class="toc-item">新增元数据</li>
          <li class="toc-item">更新元数据</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// ApiTab组件
</script>

<style scoped lang="scss">
.api-tab {
  .api-service-info {
    display: flex;
    align-items: center;
    background-color: var(--color-fill-2);
    padding: 8px 16px;
    border-radius: 8px;
    margin-bottom: 24px;

    .info-label {
      font-size: 14px;
      color: var(--color-text-2);
      margin-right: 12px;
    }

    .info-content {
      display: flex;
      align-items: center;
      margin-right: 16px;

      .api-url {
        font-family: monospace;
        background-color: var(--color-fill-3);
        padding: 2px 8px;
        border-radius: 4px;
        margin-right: 4px;
      }
    }
  }

  .api-doc-container {
    display: flex;
    border: 1px solid var(--color-border-2);
    border-radius: 12px;
    overflow: hidden;

    .api-doc-content {
      flex: 1;
      padding: 24px;
      overflow-y: auto;
      max-height: calc(100vh - 300px);

      .api-title {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 24px;
      }

      .api-section {
        margin-bottom: 32px;

        .section-title {
          font-size: 20px;
          font-weight: 500;
          margin-bottom: 16px;
          padding-bottom: 8px;
          border-bottom: 1px solid var(--color-border-2);
        }

        .section-desc {
          font-size: 14px;
          line-height: 1.6;
          margin-bottom: 12px;
          color: var(--color-text-2);

          code {
            background-color: var(--color-fill-2);
            padding: 2px 4px;
            border-radius: 4px;
            font-family: monospace;
          }
        }

        .code-block {
          background-color: #1c1c1c;
          border-radius: 8px;
          overflow: hidden;
          margin-bottom: 20px;

          .code-header {
            padding: 8px 16px;
            background-color: #2a2a2a;
            color: #e0e0e0;
            font-family: monospace;
          }

          .code-content {
            padding: 16px;
            margin: 0;
            color: #e0e0e0;
            font-family: monospace;
            white-space: pre-wrap;
            font-size: 13px;
            line-height: 1.5;
          }
        }

        .api-endpoint {
          display: flex;
          align-items: center;
          margin-bottom: 16px;

          .method {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-right: 8px;

            &.post {
              background-color: #49cc90;
              color: #fff;
            }
          }

          .endpoint-path {
            font-family: monospace;
            font-size: 14px;
          }
        }

        .subsection-title {
          font-size: 16px;
          font-weight: 500;
          margin: 16px 0 8px;
        }

        .request-block {
          .code-header {
            display: flex;
            align-items: center;

            .method {
              padding: 2px 6px;
              border-radius: 3px;
              font-size: 12px;
              font-weight: 500;
              margin-right: 8px;

              &.post {
                background-color: #49cc90;
                color: #fff;
              }
            }
          }
        }
      }
    }

    .api-doc-toc {
      width: 280px;
      border-left: 1px solid var(--color-border-2);
      background-color: var(--color-bg-1);

      .toc-header {
        padding: 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid var(--color-border-2);

        .toc-title {
          font-size: 16px;
          font-weight: 500;
          margin: 0;
        }
      }

      .toc-list {
        list-style: none;
        padding: 0;
        margin: 0;
        max-height: calc(100vh - 300px);
        overflow-y: auto;

        .toc-item {
          padding: 10px 16px;
          border-bottom: 1px solid var(--color-border-3);
          font-size: 14px;
          cursor: pointer;
          transition: all 0.2s;

          &:hover {
            background-color: var(--color-fill-2);
          }

          &.active {
            color: var(--color-primary);
            background-color: var(--color-fill-2);
            border-left: 3px solid var(--color-primary);
          }
        }
      }
    }
  }
}
</style>
