<template>
  <section class="chat-logo" :class="{ collapsed: props.collapsed }">
    <!-- <img v-if="logo" class="logo" :src="logo" alt="logo" /> -->
    <!-- <img class="logo" src="/logo.svg" alt="logo" /> -->
    <a-avatar class="badgetyle" shape="square">
      <span>
        <IconUser />
      </span>
    </a-avatar>
    <span class="system-name ai_line_1">工作流对话</span>
  </section>
</template>

<script setup lang="ts">
import { useAppStore } from '@/stores'

const props = withDefaults(defineProps<Props>(), {
  collapsed: false
})
interface Props {
  collapsed?: boolean
}
</script>

<style scoped lang="scss">
.chat-logo {
  height: 56px;
  color: var(--color-text-1);
  font-size: 18px;
  font-weight: 500;
  line-height: 1;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  cursor: pointer;
  user-select: none;
  box-sizing: border-box;

  &.collapsed {
    padding: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    .system-name {
      display: none;
    }
  }
  .badgetyle {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    margin: 12px 0;
    background: linear-gradient(rgb(var(--primary-6)) 0%, rgb(var(--primary-4)) 100%);
    transition: all 0.2s;
    overflow: hidden;
    flex-shrink: 0;
  }
  .system-name {
    padding-left: 6px;
    white-space: nowrap;
    transition: color 0.3s;
    line-height: 1.5;
    display: inline-flex;
    align-items: center;
    font-size: 16px;
    padding-left: 16px;
    &:hover {
      color: $color-theme !important;
      cursor: pointer;
    }
  }
}
</style>
