import type { IOtherOptions } from '@/apis/workflow/type'
import { useWorkflowRunEvent } from '@/views/app/workflow/hooks/use-workflow-run-event/use-workflow-run-event'
import { useWorkflowStore } from '@/stores'
import { WorkflowRunningStatus } from '@/views/app/workflow/types/workflow'
import { shareRun } from '@/apis/workflow/share'

const {
  handleWorkflowStarted,
  handleWorkflowFinished,
  handleWorkflowFailed,
  handleWorkflowNodeStarted,
  handleWorkflowNodeFinished,
  handleWorkflowNodeIterationStarted,
  handleWorkflowNodeIterationNext,
  handleWorkflowNodeIterationFinished,
  handleWorkflowNodeRetry,
  handleWorkflowAgentLog,
  handleWorkflowTextChunk,
  handleWorkflowTextReplace
} = useWorkflowRunEvent()

export const handleShareRun = async (appId: string, params: any, callback?: IOtherOptions) => {
  const workflowStore = useWorkflowStore()
  const { workflowRunningData, setHistoryWorkflowData, setWorkflowRunningData } = workflowStore
  const {
    onWorkflowStarted,
    onWorkflowFinished,
    onNodeStarted,
    onNodeFinished,
    onIterationStart,
    onIterationNext,
    onIterationFinish,
    onLoopStart,
    onLoopNext,
    onLoopFinish,
    onNodeRetry,
    onAgentLog,
    onError,
    onTextChunk,
    ...restCallback
  } = callback || {}

  await setWorkflowRunningData({
    result: {
      status: WorkflowRunningStatus.Running
    },
    tracing: [],
    resultText: ''
  })
  shareRun(
    appId,
    {
      body: params
    },
    {
      onWorkflowStarted: (params) => {
        handleWorkflowStarted(params)

        if (onWorkflowStarted) onWorkflowStarted(params)
      },
      onWorkflowFinished: (params) => {
        handleWorkflowFinished(params)

        if (onWorkflowFinished) onWorkflowFinished(params)
      },
      onError: (params) => {
        handleWorkflowFailed()

        if (onError) onError(params)
      },
      onNodeStarted: (params) => {
        handleWorkflowNodeStarted(params)
        if (onNodeStarted) onNodeStarted(params)
      },
      onNodeFinished: (params) => {
        handleWorkflowNodeFinished(params)

        if (onNodeFinished) onNodeFinished(params)
      },
      onIterationStart: (params) => {
        handleWorkflowNodeIterationStarted(params)

        if (onIterationStart) onIterationStart(params)
      },
      onIterationNext: (params) => {
        handleWorkflowNodeIterationNext(params)

        if (onIterationNext) onIterationNext(params)
      },
      onIterationFinish: (params) => {
        handleWorkflowNodeIterationFinished(params)

        if (onIterationFinish) onIterationFinish(params)
      },
      onNodeRetry: (params) => {
        handleWorkflowNodeRetry(params)

        if (onNodeRetry) onNodeRetry(params)
      },
      onAgentLog: (params) => {
        handleWorkflowAgentLog(params)

        if (onAgentLog) onAgentLog(params)
      },
      onTextChunk: (params) => {
        handleWorkflowTextChunk(params)
        if (onTextChunk) {
          onTextChunk(params)
        }
      },
      onTextReplace: (params) => {
        handleWorkflowTextReplace(params)
      },
      ...restCallback
    }
  )
}
