<template>
  <a-layout class="layout layout-default" :class="{ mobile: isMobile }">
    <Asider />
    <a-layout class="layout-default-right">
      <Header />
      <!-- <Tabs></Tabs> -->
      <Main />
    </a-layout>
  </a-layout>
</template>

<script setup lang="ts">
import Asider from './components/Asider/index.vue'
import Header from './components/Header/index.vue'
import Main from './components/Main.vue'
// import Tabs from './components/Tabs/index.vue'
import { useDevice } from '@/hooks'

defineOptions({ name: 'LayoutDefault' })
const { isMobile } = useDevice()
</script>

<style scoped lang="scss">
.layout {
  height: 100%;
}

.layout-default {
  flex-direction: row;

  &-right {
    overflow: hidden;
  }
}
</style>
