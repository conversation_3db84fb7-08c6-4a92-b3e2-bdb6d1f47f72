<template>
  <div v-if="nodeStatuses.size > 0" class="node-execution-panel mb-4">
    <a-collapse :default-active-key="[]" :bordered="false">
      <a-collapse-item v-for="[nodeId, status] in nodeStatuses" :key="nodeId">
        <template #header>
          <div class="custom-header flex items-center">
            <div class="step-icon" :style="{ backgroundColor: getNodeColor(status.type) }">
              <AiSvgIcon style="width: 14px; height: 14px" :name="`workflow-${status.type}`" />
            </div>
            <span class="step-title">{{ status.title }}</span>
          </div>
        </template>
        <template #extra>
          <div class="step-extra flex items-center space-x-3">
            <span v-if="status.elapsed_time" class="step-duration text-xs text-gray-500">
              {{ status.elapsed_time.toFixed(3) }}s
            </span>
            <span class="step-status">
              <icon-exclamation-circle-fill v-if="status.status === 'error'" class="text-red-500" />
              <icon-check-circle-fill v-else-if="status.status === 'success'" class="text-green-500" />
              <icon-loading v-else-if="status.status === 'running'" class="text-blue-500 animate-spin" />
              <icon-minus-circle-fill v-else-if="status.status === 'stopped'" class="text-gray-500" />
            </span>
          </div>
        </template>

        <!-- 节点详细信息 -->
        <div class="node-details space-y-3">
          <!-- 错误信息 -->
          <a-alert v-if="status.error" :show-icon="false" type="error" class="error-message">
            {{ status.error }}
          </a-alert>

          <!-- 输入信息 -->
          <div v-if="status.inputs" class="step-io-container">
            <Code :title="'输入'" :inputs="formatJson(status.inputs)" />
          </div>
          <!-- 执行元数据 -->
          <div v-if="status.execution_metadata" class="step-io-container">
            <Code :title="'执行信息'" :inputs="formatJson(status.execution_metadata)" />
          </div>
          <!-- 输出信息 -->
          <div v-if="status.outputs" class="step-io-container">
            <!-- <div class="io-header">输出</div> -->
            <Code :title="'输出'" :inputs="formatJson(status.outputs)" />
          </div>
        </div>
      </a-collapse-item>
    </a-collapse>
  </div>
</template>

<script setup lang="ts">
import { nodeColor } from '@/views/app/workflow/types/workflow'
import Code from './code.vue'

interface Props {
  nodeStatuses: ReadonlyMap<string, any>
}

const props = defineProps<Props>()
console.log(props.nodeStatuses)
// 格式化JSON显示
const formatJson = (json: any) => {
  if (typeof json === 'string') {
    try {
      return JSON.stringify(JSON.parse(json), null, 2)
    } catch {
      return json
    }
  }
  return JSON.stringify(json, null, 2)
}

// 获取节点颜色
const getNodeColor = (nodeType: string) => {
  return nodeColor[nodeType] || '#6B7280'
}
</script>

<style scoped lang="scss">
.node-execution-panel {
  width: 100%;
}

.step-icon {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  color: var(--color-text-1);
  background-color: var(--color-fill-3);
  margin-right: 10px;
}

.step-title {
  font-weight: 500;
  color: var(--color-text-1);
}

.step-extra {
  display: flex;
  align-items: center;
  gap: 12px;
}

.step-duration {
  color: var(--color-text-3);
  font-size: 12px;
}

.step-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
}

.node-details {
  // padding: 12px 0;
}

.step-io-container {
  // margin: 12px 0;
  // border: 1px solid var(--color-border-2);
  border-radius: 4px;
  overflow: hidden;
}

.io-header {
  padding: 8px 12px;
  font-weight: 500;
  background-color: var(--color-fill-1);
  border-bottom: 1px solid var(--color-border-2);
  font-size: 12px;
  color: var(--color-text-2);
}

:deep(.arco-collapse) {
  border: none;
  background: transparent;
}

:deep(.arco-collapse-item) {
  border: 1px solid var(--color-border-2);
  border-radius: 6px;
  margin-bottom: 8px;
  background: white;
}

:deep(.arco-collapse-item-header) {
  background: transparent;
  border-bottom: none;
  padding-top: 4px;
  padding-bottom: 4px;
  font-size: 12px;
}

:deep(.arco-collapse-item-content) {
  padding: 0 16px 16px;
  border-top: 1px solid var(--color-border-2);
}

:deep(.arco-collapse-item-header-title) {
  flex: 1;
}

:deep(.arco-collapse-item-header-extra) {
  margin-left: auto;
}

:deep(.arco-textarea-wrapper) {
  border: none;
  background: transparent;
}

:deep(.arco-textarea) {
  border: none;
  background: transparent;
  padding: 8px 12px;
}
</style>
