.arco-modal-title .arco-icon-exclamation-circle-fill {
  font-size: 24px;
}

.arco-modal-title-icon {
  display: flex;
  justify-content: center;
  align-items: center;
}

.arco-modal {
  .arco-modal-header {
    .arco-modal-title-align-center {
      justify-content: space-between;
    }
  }
  .arco-modal-body {
    max-height: 80vh;
    overflow-y: auto;
  }
}

.arco-modal-simple {
  width: 90%;
  max-width: 400px;
  padding: 14px 16px;
  box-sizing: border-box;
  .arco-modal-header {
    .arco-modal-title {
      // justify-content: flex-start;
      font-size: 18px;
    }
    .arco-modal-title-align-center {
      justify-content: start;
    }
  }
  .arco-modal-body {
    font-size: 16px;
  }
  .arco-modal-footer {
    margin-top: 15px;
    text-align: right;
  }
}

.arco-drawer-header {
  height: 56px;
  padding: 0 24px;

  .arco-drawer-title {
    font-size: 18px;
  }

  .arco-drawer-close-btn {
    font-size: 20px;
  }
}

.arco-drawer-body {
  padding: 16px 24px;
}