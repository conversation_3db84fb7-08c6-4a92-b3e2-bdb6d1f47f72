<template>
  <div>
    <a-modal :visible="visible" @ok="handleCloseModal('ok')" @cancel="handleCloseModal('cancel')">
      <template #title>Agent 设置</template>

      <div>
        <ItemPanel class="mb-4" name="Agent Mode" description="设置代理的推理模式类型">
          <template #icon>
            <icon-robot :size="24" class="h-4 w-4 text-indigo-600" />
          </template>
          <div class="text-[13px] font-medium leading-[18px] text-text-primary">
            {{ isFunctionCall ? 'Function Calling' : 'ReAct' }}
          </div>
        </ItemPanel>

        <ItemPanel class="mb-4" name="最大迭代次数" description="限制代理型助手执行迭代的次数">
          <template #icon>
            <icon-relation :size="24" class="h-4 w-4 text-[#FB6514]" />
          </template>
          <div class="flex items-center">
            <a-slider
              v-model="tempPayload.max_iteration"
              class="mr-3 w-[156px]"
              :min="minIterations"
              :max="maxIterations"
              :step="1"
            />
            <a-input-number
              v-model="tempPayload.max_iteration"
              :style="{ width: '60px' }"
              placeholder="请输入"
              class="input-demo"
              :min="minIterations"
              :max="maxIterations"
              :step="1"
            />
          </div>
        </ItemPanel>

        <div class="rounded-xl bg-background-section-burn py-2 shadow-xs">
          <div class="flex h-8 items-center px-4 font-semibold leading-6 text-text-secondary">提示词</div>
          <div class="h-[396px] overflow-y-auto whitespace-pre-line px-4 font-normal leading-5 text-text-secondary">
            {{ isChatModel ? DEFAULT_AGENT_PROMPT.chat : DEFAULT_AGENT_PROMPT.completion }}
          </div>
          <div class="px-4">
            <div
              class="inline-flex h-5 items-center rounded-md bg-components-input-bg-normal px-1 text-sm font-medium leading-[18px] text-text-tertiary"
            >
              {{ (isChatModel ? DEFAULT_AGENT_PROMPT.chat : DEFAULT_AGENT_PROMPT.completion).length }}
            </div>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script setup lang="ts">
import ItemPanel from './ItemPanel.vue'
import { DEFAULT_AGENT_PROMPT } from '@/views/app/workflow/constant/configuration'

const props = defineProps({
  agentConfig: {
    type: Object
  },
  isFunctionCall: Boolean,
  isChatModel: Boolean
})
const emits = defineEmits(['closeAgentSetting'])
const visible = ref(true)
const tempPayload = reactive({ ...props.agentConfig })
const minIterations = 1
const maxIterations = 5

const handleCloseModal = (type: string) => {
  emits('closeAgentSetting', type, tempPayload)
}
</script>
<style scoped lang="scss">
.bg-background-section-burn {
  //background: var(--color-background-section-burn);
  background: #f2f4f7;
}
</style>
