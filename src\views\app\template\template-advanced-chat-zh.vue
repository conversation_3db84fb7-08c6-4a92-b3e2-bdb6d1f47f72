<template>
  <div class="w">
    <h1>对话流应用 API</h1>
    <p>对话应用支持会话持久化，可将之前的聊天记录作为上下进行回答，可适用于聊天/客服 AI 等。</p>
    <h3>基础 URL</h3>
    <CodeGroup title="Code">
      <pre>{{ props.baseUrl }}</pre>
    </CodeGroup>
    <h3>鉴权</h3>
    <p>
      Service API 使用
      <code>API-Key</code>
      进行鉴权。
      <i>
        <strong>
          强烈建议开发者把
          <code>API-Key</code>
          放在后端存储，而非分享或者放在客户端存储，以免
          <code>API-Key</code>
          泄露，导致财产损失。
        </strong>
      </i>
      所有 API 请求都应在
      <strong><code>Authorization</code></strong>
      HTTP Header 中包含您的
      <code>API-Key</code>
      ，如下所示：
    </p>
    <CodeGroup title="Code">
      <pre>Authorization: Bearer {API_KEY}</pre>
    </CodeGroup>
  </div>
  <hr />
  <div class="w">
    <Heading url="/chat-messages" method="POST" title="发送对话消息" name="#Create-Chat-Message" />

    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>创建会话消息。</p>
        <h3>Request Body</h3>
        <Property name="query" type="string">
          <p>用户输入/提问内容。</p>
        </Property>
        <Property key="inputs" name="inputs" type="object">
          <p>
            允许传入 App 定义的各变量值。 inputs 参数包含了多组键值对（Key/Value
            pairs），每组的键对应一个特定变量，每组的值则是该变量的具体值。 默认
            <code>{{ props.inputs }}</code>
          </p>
        </Property>
        <Property key="response_mode" name="response_mode" type="string">
          <ul>
            <li>
              <code>streaming</code>
              流式模式（推荐）。基于 SSE（
              <strong>
                <a href="https://developer.mozilla.org/en-US/docs/Web/API/Server-sent_events/Using_server-sent_events">
                  Server-Sent Events
                </a>
              </strong>
              ）实现类似打字机输出方式的流式返回。
            </li>
            <li>
              <code>blocking</code>
              阻塞模式，等待执行完毕后返回结果。（请求若流程较长可能会被中断）。 注：Agent模式下不允许blocking。
            </li>
          </ul>
        </Property>
        <Property key="user" name="user" type="string">
          <p>用户标识，用于定义终端用户的身份，方便检索、统计。由开发者定义规则，需保证用户标识在应用内唯一。</p>
        </Property>
        <Property key="conversation_id" name="conversation_id" type="string">
          <p>（选填）会话 ID，需要基于之前的聊天记录继续对话，必须传之前消息的 conversation_id。</p>
        </Property>
        <!-- <Property key="files" name="files" type="array[object]">
          <p>上传的文件。</p>
          <ul>
            <li
              ><code>type</code> (string) 支持类型：图片 <code>image</code>（目前仅支持图片格式）
              。</li
            >
            <li
              ><code>transfer_method</code> (string) 传递方式:
              <ul>
                <li><code>remote_url</code>: 图片地址。</li>
                <li><code>local_file</code>: 上传文件。</li>
              </ul>
            </li>
            <li><code>url</code> 图片地址。（仅当传递方式为 <code>remote_url</code> 时）。</li>
            <li
              ><code>upload_file_id</code> 上传文件 ID。（仅当传递方式为
              <code>local_file </code>时）。</li
            >
          </ul>
        </Property> -->
        <Property key="auto_generate_name" name="auto_generate_name" type="bool">
          <p>
            （选填）自动生成标题，默认 `true`。 若设置为 `false`，则可通过调用会话重命名接口并设置 `auto_generate` 为
            `true` 实现异步生成标题。
          </p>
        </Property>

        <h3>Response</h3>
        <p>
          当
          <code>response_mode</code>
          为
          <code>blocking</code>
          时，返回 ChatCompletionResponse object。 当
          <code>response_mode</code>
          为
          <code>streaming</code>
          时，返回 ChunkChatCompletionResponse object 流式序列。
        </p>
        <h3>ChatCompletionResponse</h3>
        <p>
          返回完整的 App 结果，
          <code>Content-Type</code>
          为
          <code>application/json</code>
          。
        </p>
        <ul>
          <li>
            <code>message_id</code>
            (string) 消息唯一 ID
          </li>
          <li>
            <code>conversation_id</code>
            (string) 会话 ID
          </li>
          <li>
            <code>mode</code>
            (string) App 模式，固定为 chat
          </li>
          <li>
            <code>answer</code>
            (string) 完整回复内容
          </li>
          <li>
            <code>metadata</code>
            (object) 元数据
            <ul>
              <li>
                <code>usage</code>
                (Usage) 模型用量信息
              </li>
              <li>
                <code>retriever_resources</code>
                (array[RetrieverResource]) 引用和归属分段列表
              </li>
            </ul>
          </li>
          <li>
            <code>created_at</code>
            (int) 消息创建时间戳，如：1705395332
          </li>
        </ul>
        <h3>ChunkChatCompletionResponse</h3>
        <p>
          返回 App 输出的流式块，
          <code>Content-Type</code>
          为
          <code>text/event-stream</code>
          。 每个流式块均为 data: 开头，块之间以 \n\n 即两个换行符分隔，如下所示：
        </p>
        <CodeGroup>
          <pre>
data: {"event": "message", "task_id": "900bbd43-dc0b-4383-a372-aa6e6c414227", "id": "663c5084-a254-4040-8ad3-51f2a3c1a77c", "answer": "Hi", "created_at": 1705398420}\n\n</pre
          >
        </CodeGroup>
        <p>流式块中根据 event 不同，结构也不同：</p>
        <ul>
          <li>
            <code>event: message</code>
            大模型 返回文本块事件，即：完整的文本以分块的方式输出。
            <ul>
              <li>
                <code>task_id</code>
                (string) 任务 ID，用于请求跟踪和下方的停止响应接口
              </li>
              <li>
                <code>message_id</code>
                (string) 消息唯一 ID
              </li>
              <li>
                <code>conversation_id</code>
                (string) 会话 ID
              </li>
              <li>
                <code>answer</code>
                (string) 大模型 返回文本块内容
              </li>
              <li>
                <code>created_at</code>
                (int) 创建时间戳，如：1705395332
              </li>
            </ul>
          </li>
          <li>
            <code>event: agent_message</code>
            Agent模式下返回文本块事件，即：在Agent模式下，文章的文本以分块的方式输出（仅Agent模式下使用）
            <ul>
              <li>
                <code>task_id</code>
                (string) 任务 ID，用于请求跟踪和下方的停止响应接口
              </li>
              <li>
                <code>message_id</code>
                (string) 消息唯一 ID
              </li>
              <li>
                <code>conversation_id</code>
                (string) 会话 ID
              </li>
              <li>
                <code>answer</code>
                (string) 大模型 返回文本块内容
              </li>
              <li>
                <code>created_at</code>
                (int) 创建时间戳，如：1705395332
              </li>
            </ul>
          </li>
          <li>
            <code>event: agent_thought</code>
            Agent模式下有关Agent思考步骤的相关内容，涉及到工具调用（仅Agent模式下使用）
            <ul>
              <li>
                <code>id</code>
                (string) agent_thought ID，每一轮Agent迭代都会有一个唯一的id
              </li>
              <li>
                <code>task_id</code>
                (string) 任务ID，用于请求跟踪下方的停止响应接口
              </li>
              <li>
                <code>message_id</code>
                (string) 消息唯一ID
              </li>
              <li>
                <code>position</code>
                (int) agent_thought在消息中的位置，如第一轮迭代position为1
              </li>
              <li>
                <code>thought</code>
                (string) agent的思考内容
              </li>
              <li>
                <code>observation</code>
                (string) 工具调用的返回结果
              </li>
              <li>
                <code>tool</code>
                (string) 使用的工具列表，以 ; 分割多个工具
              </li>
              <li>
                <code>tool_input</code>
                (string) 工具的输入，JSON格式的字符串(object)。如：
                <code>{"dalle3": {"prompt": "a cute cat"}}</code>
              </li>
              <li>
                <code>created_at</code>
                (int) 创建时间戳，如：1705395332
              </li>
              <li>
                <code>message_files</code>
                (array[string]) 当前
                <code>agent_thought</code>
                关联的文件ID
                <ul>
                  <li>
                    <code>file_id</code>
                    (string) 文件ID
                  </li>
                </ul>
              </li>
              <li>
                <code>conversation_id</code>
                (string) 会话ID
              </li>
            </ul>
          </li>
          <li>
            <code>event: message_file</code>
            文件事件，表示有新文件需要展示
            <ul>
              <li>
                <code>id</code>
                (string) 文件唯一ID
              </li>
              <li>
                <code>type</code>
                (string) 文件类型，目前仅为image
              </li>
              <li>
                <code>belongs_to</code>
                (string) 文件归属，user或assistant，该接口返回仅为
                <code>assistant</code>
              </li>
              <li>
                <code>url</code>
                (string) 文件访问地址
              </li>
              <li>
                <code>conversation_id</code>
                (string) 会话ID
              </li>
            </ul>
          </li>
          <li>
            <code>event: message_end</code>
            消息结束事件，收到此事件则代表流式返回结束。
            <ul>
              <li>
                <code>task_id</code>
                (string) 任务 ID，用于请求跟踪和下方的停止响应接口
              </li>
              <li>
                <code>message_id</code>
                (string) 消息唯一 ID
              </li>
              <li>
                <code>conversation_id</code>
                (string) 会话 ID
              </li>
              <li>
                <code>metadata</code>
                (object) 元数据
                <ul>
                  <li>
                    <code>usage</code>
                    (Usage) 模型用量信息
                  </li>
                  <li>
                    <code>retriever_resources</code>
                    (array[RetrieverResource]) 引用和归属分段列表
                  </li>
                </ul>
              </li>
            </ul>
          </li>
          <li>
            <code>event: message_replace</code>
            消息内容替换事件。 开启内容审查和审查输出内容时，若命中了审查条件，则会通过此事件替换消息内容为预设回复。
            <ul>
              <li>
                <code>task_id</code>
                (string) 任务 ID，用于请求跟踪和下方的停止响应接口
              </li>
              <li>
                <code>message_id</code>
                (string) 消息唯一 ID
              </li>
              <li>
                <code>conversation_id</code>
                (string) 会话 ID
              </li>
              <li>
                <code>answer</code>
                (string) 替换内容（直接替换 大模型 所有回复文本）
              </li>
              <li>
                <code>created_at</code>
                (int) 创建时间戳，如：1705395332
              </li>
            </ul>
          </li>
          <li>
            <code>event: error</code>
            流式输出过程中出现的异常会以 stream event 形式输出，收到异常事件后即结束。
            <ul>
              <li>
                <code>task_id</code>
                (string) 任务 ID，用于请求跟踪和下方的停止响应接口
              </li>
              <li>
                <code>message_id</code>
                (string) 消息唯一 ID
              </li>
              <li>
                <code>status</code>
                (int) HTTP 状态码
              </li>
              <li>
                <code>code</code>
                (string) 错误码
              </li>
              <li>
                <code>message</code>
                (string) 错误消息
              </li>
            </ul>
          </li>
          <li>
            <code>event: ping</code>
            每 10s 一次的 ping 事件，保持连接存活。
          </li>
        </ul>
        <h3>Errors</h3>
        <ul>
          <li>404，对话不存在</li>
          <li>
            400，
            <code>invalid_param</code>
            ，传入参数异常
          </li>
          <li>
            400，
            <code>app_unavailable</code>
            ，App 配置不可用
          </li>
          <li>
            400，
            <code>provider_not_initialize</code>
            ，无可用模型凭据配置
          </li>
          <li>
            400，
            <code>provider_quota_exceeded</code>
            ，模型调用额度不足
          </li>
          <li>
            400，
            <code>model_currently_not_support</code>
            ，当前模型不可用
          </li>
          <li>
            400，
            <code>completion_request_error</code>
            ，文本生成失败
          </li>
          <li>500，服务内部异常</li>
        </ul>
      </a-col>

      <!--  -->
      <a-col :span="11">
        <CodeGroup title="Request" tag="POST" label="/chat-messages">
          <pre>
curl -X POST '{{ props.baseUrl }}/chat-messages' \
-H 'Authorization: Bearer {api_key}' \
-H 'Content-Type: application/json' \
--data-raw '{
    "inputs": {{ props.inputs }},
    "query": "What are the specs of the iPhone 13 Pro Max?",
    "response_mode": "streaming",
    "user": "abc-123"
}'</pre
          >
        </CodeGroup>
        <h3>阻塞模式</h3>
        <CodeGroup title="Response">
          <pre>
{
    "event": "message",
    "message_id": "9da23599-e713-473b-982c-4328d4f5c78a",
    "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2",
    "mode": "chat",
    "answer": "iPhone 13 Pro Max specs are listed heere:...",
    "metadata": {
        "usage": {
            "prompt_tokens": 1033,
            "prompt_unit_price": "0.001",
            "prompt_price_unit": "0.001",
            "prompt_price": "0.0010330",
            "completion_tokens": 128,
            "completion_unit_price": "0.002",
            "completion_price_unit": "0.001",
            "completion_price": "0.0002560",
            "total_tokens": 1161,
            "total_price": "0.0012890",
            "currency": "USD",
            "latency": 0.7682376249867957
        },
        "retriever_resources": [
            {
                "position": 1,
                "dataset_id": "101b4c97-fc2e-463c-90b1-5261a4cdcafb",
                "dataset_name": "iPhone",
                "document_id": "8dd1ad74-0b5f-4175-b735-7d98bbbb4e00",
                "document_name": "iPhone List",
                "segment_id": "ed599c7f-2766-4294-9d1d-e5235a61270a",
                "score": 0.98457545,
                "content": "\"Model\",\"Release Date\",\"Display Size\",\"Resolution\",\"Processor\",\"RAM\",\"Storage\",\"Camera\",\"Battery\",\"Operating System\"\n\"iPhone 13 Pro Max\",\"September 24, 2021\",\"6.7 inch\",\"1284 x 2778\",\"Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard)\",\"6 GB\",\"128, 256, 512 GB, 1TB\",\"12 MP\",\"4352 mAh\",\"iOS 15\""
            }
        ]
    },
    "created_at": 1705407629
}</pre
          >
        </CodeGroup>
        <h3>流式模式（基础助手）</h3>
        <CodeGroup title="Response">
          <pre>
  data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " I", "created_at": 1679586595}
  data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": "'m", "created_at": 1679586595}
  data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " glad", "created_at": 1679586595}
  data: {"event": "message", "message_id": "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " to", "created_at": 1679586595}
  data: {"event": "message", "message_id": : "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " meet", "created_at": 1679586595}
  data: {"event": "message", "message_id": : "5ad4cb98-f0c7-4085-b384-88c403be6290", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "answer": " you", "created_at": 1679586595}
  data: {"event": "message_end", "id": "5e52ce04-874b-4d27-9045-b3bc80def685", "conversation_id": "45701982-8118-4bc5-8e9b-64562b4555f2", "metadata": {"usage": {"prompt_tokens": 1033, "prompt_unit_price": "0.001", "prompt_price_unit": "0.001", "prompt_price": "0.0010330", "completion_tokens": 135, "completion_unit_price": "0.002", "completion_price_unit": "0.001", "completion_price": "0.0002700", "total_tokens": 1168, "total_price": "0.0013030", "currency": "USD", "latency": 1.381760165997548, "retriever_resources": [{"position": 1, "dataset_id": "101b4c97-fc2e-463c-90b1-5261a4cdcafb", "dataset_name": "iPhone", "document_id": "8dd1ad74-0b5f-4175-b735-7d98bbbb4e00", "document_name": "iPhone List", "segment_id": "ed599c7f-2766-4294-9d1d-e5235a61270a", "score": 0.98457545, "content": "\"Model\",\"Release Date\",\"Display Size\",\"Resolution\",\"Processor\",\"RAM\",\"Storage\",\"Camera\",\"Battery\",\"Operating System\"\n\"iPhone 13 Pro Max\",\"September 24, 2021\",\"6.7 inch\",\"1284 x 2778\",\"Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard)\",\"6 GB\",\"128, 256, 512 GB, 1TB\",\"12 MP\",\"4352 mAh\",\"iOS 15\""}]}}}
          </pre>
        </CodeGroup>
        <h3>流式模式（智能助手）</h3>
        <CodeGroup title="Response">
          <pre>
  data: {"event": "agent_thought", "id": "8dcf3648-fbad-407a-85dd-73a6f43aeb9f", "task_id": "9cf1ddd7-f94b-459b-b942-b77b26c59e9b", "message_id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "position": 1, "thought": "", "observation": "", "tool": "", "tool_input": "", "created_at": 1705639511, "message_files": [], "conversation_id": "c216c595-2d89-438c-b33c-aae5ddddd142"}
  data: {"event": "agent_thought", "id": "8dcf3648-fbad-407a-85dd-73a6f43aeb9f", "task_id": "9cf1ddd7-f94b-459b-b942-b77b26c59e9b", "message_id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "position": 1, "thought": "", "observation": "", "tool": "dalle3", "tool_input": "{\"dalle3\": {\"prompt\": \"cute Japanese anime girl with white hair, blue eyes, bunny girl suit\"}}", "created_at": 1705639511, "message_files": [], "conversation_id": "c216c595-2d89-438c-b33c-aae5ddddd142"}
  data: {"event": "message_file", "id": "d75b7a5c-ce5e-442e-ab1b-d6a5e5b557b0", "type": "image", "belongs_to": "assistant", "url": "http://127.0.0.1:5001/files/tools/d75b7a5c-ce5e-442e-ab1b-d6a5e5b557b0.png?timestamp=1705639526&nonce=70423256c60da73a9c96d1385ff78487&sign=7B5fKV9890YJuqchQvrABvW4AIupDvDvxGdu1EOJT94=", "conversation_id": "c216c595-2d89-438c-b33c-aae5ddddd142"}
  data: {"event": "agent_thought", "id": "8dcf3648-fbad-407a-85dd-73a6f43aeb9f", "task_id": "9cf1ddd7-f94b-459b-b942-b77b26c59e9b", "message_id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "position": 1, "thought": "", "observation": "image has been created and sent to user already, you should tell user to check it now.", "tool": "dalle3", "tool_input": "{\"dalle3\": {\"prompt\": \"cute Japanese anime girl with white hair, blue eyes, bunny girl suit\"}}", "created_at": 1705639511, "message_files": ["d75b7a5c-ce5e-442e-ab1b-d6a5e5b557b0"], "conversation_id": "c216c595-2d89-438c-b33c-aae5ddddd142"}
  data: {"event": "agent_thought", "id": "67a99dc1-4f82-42d3-b354-18d4594840c8", "task_id": "9cf1ddd7-f94b-459b-b942-b77b26c59e9b", "message_id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "position": 2, "thought": "", "observation": "", "tool": "", "tool_input": "", "created_at": 1705639511, "message_files": [], "conversation_id": "c216c595-2d89-438c-b33c-aae5ddddd142"}
  data: {"event": "agent_message", "id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "task_id": "9cf1ddd7-f94b-459b-b942-b77b26c59e9b", "message_id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "answer": "I have created an image of a cute Japanese", "created_at": 1705639511, "conversation_id": "c216c595-2d89-438c-b33c-aae5ddddd142"}
  data: {"event": "agent_message", "id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "task_id": "9cf1ddd7-f94b-459b-b942-b77b26c59e9b", "message_id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "answer": " anime girl with white hair and blue", "created_at": 1705639511, "conversation_id": "c216c595-2d89-438c-b33c-aae5ddddd142"}
  data: {"event": "agent_message", "id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "task_id": "9cf1ddd7-f94b-459b-b942-b77b26c59e9b", "message_id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "answer": " eyes wearing a bunny girl" ,"created_at": 1705639511, "conversation_id": "c216c595-2d89-438c-b33c-aae5ddddd142"}
  data: {"event": "agent_message", "id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "task_id": "9cf1ddd7-f94b-459b-b942-b77b26c59e9b", "message_id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "answer": " suit .", "created_at": 1705639511, "conversation_id": "c216c595-2d89-438c-b33c-aae5ddddd142"}
  data: {"event": "agent_thought", "id": "67a99dc1-4f82-42d3-b354-18d4594840c8", "task_id": "9cf1ddd7-f94b-459b-b942-b77b26c59e9b", "message_id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "position": 2, "thought": "I have created an image of a cute Japanese anime girl with white hair and blue eyes wearing a bunny girl suit.", "observation": "", "tool": "", "tool_input": "", "created_at": 1705639511, "message_files": [], "conversation_id": "c216c595-2d89-438c-b33c-aae5ddddd142"}
  data: {"event": "message_end", "task_id": "9cf1ddd7-f94b-459b-b942-b77b26c59e9b", "id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "message_id": "1fb10045-55fd-4040-99e6-d048d07cbad3", "conversation_id": "c216c595-2d89-438c-b33c-aae5ddddd142", "metadata": {"usage": {"prompt_tokens": 305, "prompt_unit_price": "0.001", "prompt_price_unit": "0.001", "prompt_price": "0.0003050", "completion_tokens": 97, "completion_unit_price": "0.002", "completion_price_unit": "0.001", "completion_price": "0.0001940", "total_tokens": 184, "total_price": "0.0002290", "currency": "USD", "latency": 1.771092874929309}}}</pre
          >
        </CodeGroup>
      </a-col>
    </a-row>
  </div>

  <hr />
  <div class="w">
    <Heading url="/files/upload" method="POST" title="上传文件" name="#files-upload" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>
          上传文件（目前仅支持图片）并在发送消息时使用，可实现图文多模态理解。 支持 png, jpg, jpeg 格式。
          <i>上传的文件仅供当前终端用户使用。</i>
        </p>
        <h3>Request Body</h3>
        <p>
          该接口需使用
          <code>multipart/form-data</code>
          进行请求。
        </p>
        <Property key="file" name="file" type="file" p>要上传的文件。</Property>
        <Property key="user" name="user" type="string" p>
          用户标识，用于定义终端用户的身份，必须和发送消息接口传入 user 保持一致。
        </Property>
        <h3>Response</h3>
        <p>成功上传后，服务器会返回文件的 ID 和相关信息。</p>
        <ul>
          <li>
            <code>id</code>
            (uuid) ID
          </li>
          <li>
            <code>name</code>
            (string) 文件名
          </li>
          <li>
            <code>size</code>
            (int) 文件大小（byte）
          </li>
          <li>
            <code>extension</code>
            (string) 文件后缀
          </li>
          <li>
            <code>mime_type</code>
            (string) 文件 mime-type
          </li>
          <li>
            <code>created_by</code>
            (uuid) 上传人 ID
          </li>
          <li>
            <code>created_at</code>
            (timestamp) 上传时间
          </li>
        </ul>
        <h3>Errors</h3>
        <ul>
          <li>
            400，
            <code>no_file_uploaded</code>
            ，必须提供文件
          </li>
          <li>
            400，
            <code>too_many_files</code>
            ，目前只接受一个文件
          </li>
          <li>
            400，
            <code>unsupported_preview</code>
            ，该文件不支持预览
          </li>
          <li>
            400，
            <code>unsupported_estimate</code>
            ，该文件不支持估算
          </li>
          <li>
            413，
            <code>file_too_large</code>
            ，文件太大
          </li>
          <li>
            415，
            <code>unsupported_file_type</code>
            ，不支持的扩展名，当前只接受文档类文件
          </li>
          <li>
            503，
            <code>s3_connection_failed</code>
            ，无法连接到 S3 服务
          </li>
          <li>
            503，
            <code>s3_permission_denied</code>
            ，无权限上传文件到 S3
          </li>
          <li>
            503，
            <code>s3_file_too_large</code>
            ，文件超出 S3 大小限制
          </li>
        </ul>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="POST" label="/files/upload" gap>
          <pre>
curl -X POST '{{ props.baseUrl }}/files/upload' \
--header 'Authorization: Bearer {api_key}' \
--form 'file=@localfile;type=image/[png|jpeg|jpg] \
--form 'user=abc-123'
          </pre>
        </CodeGroup>
        <CodeGroup title="Response">
          <pre>
{
  "id": "72fa9618-8f89-4a37-9b33-7e1178a24a67",
  "name": "example.png",
  "size": 1024,
  "extension": "png",
  "mime_type": "image/png",
  "created_by": 123,
  "created_at": 1577836800,
}

          </pre>
        </CodeGroup>
      </a-col>
    </a-row>
  </div>

  <hr />
  <div class="w">
    <Heading url="/chat-messages/:task_id/stop" method="POST" title="停止响应" name="#Stop" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>仅支持流式模式。</p>
        <h3>Path</h3>
        <ul>
          <li>
            <code>task_id</code>
            (string) 任务 ID，可在流式返回 Chunk 中获取
          </li>
        </ul>
        <h3>Request Body</h3>
        <ul>
          <li>
            <code>user</code>
            (string) Required 用户标识，用于定义终端用户的身份，必须和发送消息接口传入 user 保持一致。
          </li>
        </ul>
        <h3>Response</h3>
        <ul>
          <li>
            <code>result</code>
            (string) 固定返回 success
          </li>
        </ul>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="POST" label="/chat-messages/:task_id/stop" gap>
          <pre>
curl -X POST '{{ props.baseUrl }}/chat-messages/:task_id/stop' \
-H 'Authorization: Bearer {api_key}' \
-H 'Content-Type: application/json' \
--data-raw '{ "user": "abc-123"}'</pre
          >
        </CodeGroup>
        <CodeGroup title="Response">
          <pre>
{
  "result": "success"
}</pre
          >
        </CodeGroup>
      </a-col>
    </a-row>
  </div>
  <hr />
  <div class="w">
    <Heading url="/messages/:message_id/feedbacks" method="POST" title="消息反馈（点赞）" name="#feedbacks" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>消息终端用户反馈、点赞，方便应用开发者优化输出预期。</p>
        <h3>Path Params</h3>
        <Property key="message_id" name="message_id" type="string" p>消息 ID</Property>
        <h3>Request Body</h3>
        <Property key="rating" name="rating" type="string" p>点赞 like, 点踩 dislike, 撤销点赞 null</Property>
        <Property key="user" name="user" type="string" p>
          用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
        </Property>
        <h3>Response</h3>
        <ul>
          <li>
            <code>result</code>
            (string) 固定返回 success
          </li>
        </ul>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="POST" label="/messages/:message_id/feedbacks" gap>
          <pre>
curl -X POST '{{ props.baseUrl }}/messages/:message_id/feedbacks \
--header 'Authorization: Bearer {api_key}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "rating": "like",
    "user": "abc-123"
}'</pre
          >
        </CodeGroup>

        <CodeGroup title="Response">
          <pre>
{
  "result": "success"
}</pre
          >
        </CodeGroup>
      </a-col>
    </a-row>
  </div>
  <hr />
  <div class="w">
    <Heading url="/messages/{message_id}/suggested" method="GET" title="获取下一轮建议问题列表" name="#suggested" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>获取下一轮建议问题列表。</p>
        <h3>Path Params</h3>
        <Property key="message_id" name="message_id" type="string" p>Message ID</Property>
        <h3>Query</h3>
        <Property key="user" name="user" type="string" p>
          用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
        </Property>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="GET" label="/messages/{message_id}/suggested" gap>
          <pre>
curl --location --request GET '{{ props.baseUrl }}/messages/{message_id}/suggested?user=abc-123 \
--header 'Authorization: Bearer ENTER-YOUR-SECRET-KEY' \
--header 'Content-Type: application/json'</pre
          >
        </CodeGroup>
        <CodeGroup title="Response">
          <pre>
{
  "result": "success",
  "data": [
        "a",
        "b",
        "c"
    ]
}</pre
          >
        </CodeGroup>
      </a-col>
    </a-row>
  </div>
  <hr />
  <div class="w">
    <Heading url="/messages" method="GET" title="获取会话历史消息" name="#messages" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>
          滚动加载形式返回历史聊天记录，第一页返回最新
          <code>limit</code>
          条，即：倒序返回。
        </p>
        <h3>Query</h3>
        <Property key="conversation_id" name="conversation_id" type="string" p>会话 ID</Property>
        <Property key="user" name="user" type="string" p>
          用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
        </Property>
        <Property key="first_id" name="first_id" type="string" p>当前页第一条聊天记录的 ID，默认 null</Property>
        <Property key="limit" name="limit" type="int" p>一次请求返回多少条聊天记录，默认 20 条。</Property>
        <h3>Response</h3>
        <ul>
          <li>
            <code>data</code>
            (array[object]) 消息列表
          </li>
          <li>
            <code>id</code>
            (string) 消息 ID
          </li>
          <li>
            <code>conversation_id</code>
            (string) 会话 ID
          </li>
          <li>
            <code>inputs</code>
            (array[object]) 用户输入参数。
          </li>
          <li>
            <code>query</code>
            (string) 用户输入 / 提问内容。
          </li>
          <li>
            <code>message_files</code>
            (array[object]) 消息文件
            <ul>
              <li>
                <code>id</code>
                (string) ID
              </li>
              <li>
                <code>type</code>
                (string) 文件类型，image 图片
              </li>
              <li>
                <code>url</code>
                (string) 预览图片地址
              </li>
              <li>
                <code>belongs_to</code>
                (string) 文件归属方，user 或 assistant
              </li>
              <li>
                <code>agent_thoughts</code>
                (array[object]) Agent思考内容（仅Agent模式下不为空）
                <ul>
                  <li>
                    <code>id</code>
                    (string) agent_thought ID，每一轮Agent迭代都会有一个唯一的id
                  </li>
                  <li>
                    <code>message_id</code>
                    (string) 消息唯一ID
                  </li>
                  <li>
                    <code>position</code>
                    (int) agent_thought在消息中的位置，如第一轮迭代position为1
                  </li>
                  <li>
                    <code>thought</code>
                    (string) agent的思考内容
                  </li>
                  <li>
                    <code>observation</code>
                    (string) 工具调用的返回结果
                  </li>
                  <li>
                    <code>tool</code>
                    (string) 使用的工具列表，以 ; 分割多个工具
                  </li>
                  <li>
                    <code>tool_input</code>
                    (string) 工具的输入，JSON格式的字符串(object)。如：
                    <code>{"dalle3": {"prompt": "a cute cat"}}</code>
                  </li>
                  <li>
                    <code>created_at</code>
                    (int) 创建时间戳，如：1705395332
                  </li>
                  <li>
                    <code>message_files</code>
                    (array[string]) 当前agent_thought 关联的文件ID
                    <ul>
                      <li>
                        <code>file_id</code>
                        (string) 文件ID
                      </li>
                    </ul>
                  </li>
                  <li>
                    <code>conversation_id</code>
                    (string) 会话ID
                  </li>
                </ul>
              </li>
            </ul>
          </li>
          <li>
            <code>answer</code>
            (string) 回答消息内容
          </li>
          <li>
            <code>created_at</code>
            (timestamp) 创建时间
          </li>
          <li>
            <code>feedback</code>
            (object) 反馈信息
            <ul>
              <li>
                <code>rating</code>
                (string) 点赞 like / 点踩 dislike
              </li>
            </ul>
          </li>
          <li>
            <code>retriever_resources</code>
            (array[RetrieverResource]) 引用和归属分段列表
          </li>
          <li>
            <code>has_more</code>
            (bool) 是否存在下一页
          </li>
          <li>
            <code>limit</code>
            (int) 返回条数，若传入超过系统限制，返回系统限制数量
          </li>
        </ul>
      </a-col>

      <a-col :span="11">
        <h3>Request Example</h3>
        <CodeGroup title="Request" tag="GET" label="/messages">
          <pre>
curl -X GET '{{ props.baseUrl }}/messages?user=abc-123&conversation_id=' \
--header 'Authorization: Bearer {api_key}'</pre
          >
        </CodeGroup>
        <h3>Response Example(基础助手)</h3>
        <CodeGroup title="Response">
          <pre>
{
"limit": 20,
"has_more": false,
"data": [
    {
        "id": "a076a87f-31e5-48dc-b452-0061adbbc922",
        "conversation_id": "cd78daf6-f9e4-4463-9ff2-54257230a0ce",
        "inputs": {
            "name": "arcana"
        },
        "query": "iphone 13 pro",
        "answer": "The iPhone 13 Pro, released on September 24, 2021, features a 6.1-inch display with a resolution of 1170 x 2532. It is equipped with a Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard) processor, 6 GB of RAM, and offers storage options of 128 GB, 256 GB, 512 GB, and 1 TB. The camera is 12 MP, the battery capacity is 3095 mAh, and it runs on iOS 15.",
        "message_files": [],
        "feedback": null,
        "retriever_resources": [
            {
                "position": 1,
                "dataset_id": "101b4c97-fc2e-463c-90b1-5261a4cdcafb",
                "dataset_name": "iPhone",
                "document_id": "8dd1ad74-0b5f-4175-b735-7d98bbbb4e00",
                "document_name": "iPhone List",
                "segment_id": "ed599c7f-2766-4294-9d1d-e5235a61270a",
                "score": 0.98457545,
                "content": "\"Model\",\"Release Date\",\"Display Size\",\"Resolution\",\"Processor\",\"RAM\",\"Storage\",\"Camera\",\"Battery\",\"Operating System\"\n\"iPhone 13 Pro Max\",\"September 24, 2021\",\"6.7 inch\",\"1284 x 2778\",\"Hexa-core (2x3.23 GHz Avalanche + 4x1.82 GHz Blizzard)\",\"6 GB\",\"128, 256, 512 GB, 1TB\",\"12 MP\",\"4352 mAh\",\"iOS 15\""
            }
        ],
        "agent_thoughts": [],
        "created_at": 1705569239
    }
  ]
}</pre
          >
        </CodeGroup>
        <h3>Response Example(智能助手)</h3>
        <CodeGroup title="Response">
          <pre>
{
"limit": 20,
"has_more": false,
"data": [
    {
        "id": "d35e006c-7c4d-458f-9142-be4930abdf94",
        "conversation_id": "957c068b-f258-4f89-ba10-6e8a0361c457",
        "inputs": {},
        "query": "draw a cat",
        "answer": "I have generated an image of a cat for you. Please check your messages to view the image.",
        "message_files": [
            {
                "id": "976990d2-5294-47e6-8f14-7356ba9d2d76",
                "type": "image",
                "url": "http://127.0.0.1:5001/files/tools/976990d2-5294-47e6-8f14-7356ba9d2d76.png?timestamp=1705988524&nonce=55df3f9f7311a9acd91bf074cd524092&sign=z43nMSO1L2HBvoqADLkRxr7Biz0fkjeDstnJiCK1zh8=",
                "belongs_to": "assistant"
            }
        ],
        "feedback": null,
        "retriever_resources": [],
        "created_at": 1705988187,
        "agent_thoughts": [
            {
                "id": "592c84cf-07ee-441c-9dcc-ffc66c033469",
                "chain_id": null,
                "message_id": "d35e006c-7c4d-458f-9142-be4930abdf94",
                "position": 1,
                "thought": "",
                "tool": "dalle2",
                "tool_input": "{\"dalle2\": {\"prompt\": \"cat\"}}",
                "created_at": 1705988186,
                "observation": "image has been created and sent to user already, you should tell user to check it now.",
                "message_files": [
                    "976990d2-5294-47e6-8f14-7356ba9d2d76"
                ]
            },
            {
                "id": "73ead60d-2370-4780-b5ed-532d2762b0e5",
                "chain_id": null,
                "message_id": "d35e006c-7c4d-458f-9142-be4930abdf94",
                "position": 2,
                "thought": "I have generated an image of a cat for you. Please check your messages to view the image.",
                "tool": "",
                "tool_input": "",
                "created_at": 1705988199,
                "observation": "",
                "message_files": []
            }
        ]
    }
    ]
}</pre
          >
        </CodeGroup>
      </a-col>
    </a-row>
  </div>
  <hr />
  <div class="w">
    <Heading url="/conversations" method="GET" title="获取会话列表" name="#conversations" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>获取当前用户的会话列表，默认返回最近的 20 条。</p>
        <h3>Query</h3>
        <Property key="user" name="user" type="string" p>
          用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
        </Property>
        <Property key="last_id" name="last_id" type="string" p>当前页最后面一条记录的 ID，默认 null</Property>
        <Property key="limit" name="limit" type="int" p>一次请求返回多少条记录</Property>
        <Property key="pinned" name="pinned" type="bool" p>只返回置顶 true，只返回非置顶 false</Property>
        <h3>Response</h3>
        <ul>
          <li>
            <code>data</code>
            (array[object]) 会话列表
            <ul>
              <li>
                <code>id</code>
                (string) 会话 ID
              </li>
              <li>
                <code>name</code>
                (string) 会话名称，默认为会话中用户最开始问题的截取。
              </li>
              <li>
                <code>inputs</code>
                (array[object]) 用户输入参数。
              </li>
              <li>
                <code>introduction</code>
                (string) 开场白
              </li>
              <li>
                <code>created_at</code>
                (timestamp) 创建时间
              </li>
            </ul>
          </li>
          <li>
            <code>has_more</code>
            (bool)
          </li>
          <li>
            <code>limit</code>
            (int) 返回条数，若传入超过系统限制，返回系统限制数量
          </li>
        </ul>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="GET" label="/conversations" gap>
          <pre>
curl -X GET '{{ props.baseUrl }}/conversations?user=abc-123&last_id=&limit=20'\
--header 'Authorization: Bearer {api_key}'</pre
          >
        </CodeGroup>
        <CodeGroup title="Response">
          <pre>
{
  "limit": 20,
  "has_more": false,
  "data": [
    {
      "id": "10799fb8-64f7-4296-bbf7-b42bfbe0ae54",
      "name": "New chat",
      "inputs": {
          "book": "book",
          "myName": "Lucy"
      },
      "status": "normal",
      "created_at": 1679667915
    },
    {
      "id": "hSIhXBhNe8X1d8Et"
      // ...
    }
  ]
}
</pre
          >
        </CodeGroup>
      </a-col>
    </a-row>
  </div>

  <hr />
  <div class="w">
    <Heading url="/conversations/:conversation_id" method="DELETE" title="删除会话" name="#delete" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>删除会话。</p>
        <h3>Path</h3>
        <ul>
          <li>
            <code>conversation_id</code>
            (string) 会话 ID
          </li>
        </ul>
        <h3>Request Body</h3>
        <Property key="user" name="user" type="string" p>
          用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
        </Property>
        <h3>Response</h3>
        <ul>
          <li>
            <code>result</code>
            (string) 固定返回 success
          </li>
        </ul>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="DELETE" label="/conversations/:conversation_id" gap>
          <pre>
curl -X DELETE '{{ props.baseUrl }}/conversations/:conversation_id' \
--header 'Authorization: Bearer {api_key}' \
--header 'Content-Type: application/json' \
--data-raw '{
 "user": "abc-123"
}'
          </pre>
        </CodeGroup>
        <CodeGroup title="Response">
          <pre>
{
  "result": "success"
}
          </pre>
        </CodeGroup>
      </a-col>
    </a-row>
  </div>
  <hr />
  <div class="w">
    <Heading url="/conversations/:conversation_id/name" method="POST" title="会话重命名" name="#rename" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>对会话进行重命名，会话名称用于显示在支持多会话的客户端上。</p>
        <h3>Request Body</h3>
        <Property key="name" name="name" type="string" p>
          名称，若 `auto_generate` 为 `true` 时，该参数可不传。
        </Property>
        <Property key="auto_generate" name="auto_generate" type="string" p>自动生成标题，默认 false。</Property>
        <Property key="user" name="user" type="string" p>
          用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
        </Property>
        <h3>Response</h3>
        <ul>
          <li>
            <code>id</code>
            (string) 会话 ID
          </li>
          <li>
            <code>name</code>
            (string) 会话名称
          </li>
          <li>
            <code>inputs</code>
            array[object] 用户输入参数。
          </li>
          <li>
            <code>introduction</code>
            (string) 开场白
          </li>
          <li>
            <code>created_at</code>
            (timestamp) 创建时间
          </li>
        </ul>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="POST" label="/conversations/:conversation_id/name" gap>
          <pre>
curl -X POST '{{ props.baseUrl }}/conversations/name' \
--header 'Authorization: Bearer {api_key}' \
--header 'Content-Type: application/json' \
--data-raw '{
 "name": "",
 "user": "abc-123"
}'
          </pre>
        </CodeGroup>
        <CodeGroup title="Response">
          <pre>
{
  "result": "success"
}
          </pre>
        </CodeGroup>
      </a-col>
    </a-row>
  </div>
  <!-- <hr />
  <div class="w">
    <Heading url="/audio-to-text" method="POST" title="语音转文字" name="#audio" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <h3>Request Body</h3>
        <p>该接口需使用 <code>multipart/form-data</code> 进行请求。</p>
        <Property key="file" name="file" type="file">
          <p
            >语音文件。 支持格式：<code>['mp3', 'mp4', 'mpeg', 'mpga', 'm4a', 'wav', 'webm']</code>
            文件大小限制：15MB</p
          >
        </Property>
        <Property key="user" name="user" type="string" p>
          用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
        </Property>
        <h3>Response</h3>
        <ul>
          <li><code>text</code> (string) 输出文字</li>
        </ul>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="POST" label="/audio-to-text" gap>
          <pre>
curl -X POST '{{ props.baseUrl }}/audio-to-text' \
--header 'Authorization: Bearer {api_key}' \
--form 'file=@localfile;type=audio/[mp3|mp4|mpeg|mpga|m4a|wav|webm]
          </pre>
        </CodeGroup>
        <CodeGroup title="Response">
          <pre>
{
  "text": "hello"
}
          </pre>
        </CodeGroup>
      </a-col>
    </a-row>
  </div>
  <hr />
  <div class="w">
    <Heading url="/text-to-audio" method="POST" title="文字转语音" name="#audio" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>文字转语音。</p>
        <h3>Request Body</h3>
        <Property key="text" name="text" type="str" p> 语音生成内容。 </Property>
        <Property key="user" name="user" type="string" p>
          用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
        </Property>
        <Property key="streaming" name="streaming" type="bool" p>
          是否启用流式输出true、false。
        </Property>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="POST" label="/text-to-audio" gap>
          <pre>
curl --location --request POST '{{ props.baseUrl }}/text-to-audio' \
--header 'Authorization: Bearer ENTER-YOUR-SECRET-KEY' \
--form 'text=你好arcana;user=abc-123;streaming=false
          </pre>
        </CodeGroup>
        <CodeGroup title="Response">
          <pre>
{
  "Content-Type": "audio/wav"
}
          </pre>
        </CodeGroup>
      </a-col>
    </a-row>
  </div> -->

  <hr />
  <div class="w">
    <Heading url="/parameters" method="GET" title="获取应用配置信息" name="#parameters" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>用于进入页面一开始，获取功能开关、输入参数名称、类型及默认值等使用。</p>
        <h3>Query</h3>
        <Property key="user" name="user" type="string" p>
          用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
        </Property>
        <h3>Response</h3>
        <ul>
          <li>
            <code>opening_statement</code>
            (string) 开场白
          </li>
          <li>
            <code>suggested_questions</code>
            (array[string]) 开场推荐问题列表
          </li>
          <li>
            <code>suggested_questions_after_answer</code>
            (object) 启用回答后给出推荐问题。
            <ul>
              <li>
                <code>enabled</code>
                (bool) 是否开启
              </li>
            </ul>
          </li>
          <li>
            <code>speech_to_text</code>
            (object) 语音转文本
            <ul>
              <li>
                <code>enabled</code>
                (bool) 是否开启
              </li>
            </ul>
          </li>
          <li>
            <code>retriever_resource</code>
            (object) 引用和归属
            <ul>
              <li>
                <code>enabled</code>
                (bool) 是否开启
              </li>
            </ul>
          </li>
          <li>
            <code>annotation_reply</code>
            (object) 标记回复
            <ul>
              <li>
                <code>enabled</code>
                (bool) 是否开启
              </li>
            </ul>
          </li>
          <li>
            <code>user_input_form</code>
            (array[object]) 用户输入表单配置
            <ul>
              <li>
                <code>text-input</code>
                (object) 文本输入控件
                <ul>
                  <li>
                    <code>label</code>
                    (string) 控件展示标签名
                  </li>
                  <li>
                    <code>variable</code>
                    (string) 控件 ID
                  </li>
                  <li>
                    <code>required</code>
                    (bool) 是否必填
                  </li>
                  <li>
                    <code>default</code>
                    (string) 默认值
                  </li>
                </ul>
              </li>
              <li>
                <code>paragraph</code>
                (object) 段落文本输入控件
                <ul>
                  <li>
                    <code>label</code>
                    (string) 控件展示标签名
                  </li>
                  <li>
                    <code>variable</code>
                    (string) 控件 ID
                  </li>
                  <li>
                    <code>required</code>
                    (bool) 是否必填
                  </li>
                  <li>
                    <code>default</code>
                    (string) 默认值
                  </li>
                </ul>
              </li>
              <li>
                <code>select</code>
                (object) 下拉控件
                <ul>
                  <li>
                    <code>label</code>
                    (string) 控件展示标签名
                  </li>
                  <li>
                    <code>variable</code>
                    (string) 控件 ID
                  </li>
                  <li>
                    <code>required</code>
                    (bool) 是否必填
                  </li>
                  <li>
                    <code>default</code>
                    (string) 默认值
                  </li>
                  <li>
                    <code>options</code>
                    (array[string]) 选项值
                  </li>
                </ul>
              </li>
            </ul>
          </li>
          <li>
            <code>file_upload</code>
            (object) 文件上传配置
            <ul>
              <li>
                <code>image</code>
                (object) 图片设置 当前仅支持图片类型：
                <code>png</code>
                ,
                <code>jpg</code>
                ,
                <code>jpeg</code>
                <ul>
                  <li>
                    <code>enabled</code>
                    (bool) 是否开启
                  </li>
                  <!-- <li><code>number_limits</code> (int) 图片数量限制，默认 3</li> -->
                  <li>
                    <code>transfer_methods</code>
                    (array[string]) 传递方式列表，remote_url , local_file，必选一个
                  </li>
                </ul>
              </li>
            </ul>
          </li>
          <li>
            <code>system_parameters</code>
            (object) 系统参数
            <ul>
              <li>
                <code>image_file_size_limit</code>
                (string) 图片文件上传大小限制（MB）
              </li>
            </ul>
          </li>
        </ul>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="GET" label="/parameters" gap>
          <pre>
curl -X GET '{{ props.baseUrl }}/parameters'\
--header 'Authorization: Bearer {api_key}'
          </pre>
        </CodeGroup>
        <CodeGroup title="Response">
          <pre>
{
  "introduction": "nice to meet you",
  "user_input_form": [
    {
      "text-input": {
        "label": "a",
        "variable": "a",
        "required": true,
        "max_length": 48,
        "default": ""
      }
    },
    {
      // ...
    }
  ],
  "file_upload": {
    "image": {
      "enabled": true,
      "number_limits": 3,
      "transfer_methods": [
        "remote_url",
        "local_file"
      ]
    }
  }
}
          </pre>
        </CodeGroup>
      </a-col>
    </a-row>
  </div>

  <hr />
  <div class="w">
    <Heading url="/meta" method="GET" title="获取应用Meta信息" name="#meta" />
    <a-row :gutter="[20, 0]">
      <a-col :span="13">
        <p>用于获取工具icon</p>
        <h3>Query</h3>
        <Property key="user" name="user" type="string" p>
          用户标识，由开发者定义规则，需保证用户标识在应用内唯一。
        </Property>
        <h3>Response</h3>
        <ul>
          <li>
            <code>tool_icons</code>
            (object[string]) 工具图标
            <ul>
              <li>
                <code>工具名称</code>
                (string)
                <ul>
                  <li>
                    <code>icon</code>
                    (object|string)
                    <ul>
                      <li>
                        (object) 图标
                        <ul>
                          <li>
                            <code>background</code>
                            (string) hex格式的背景色
                          </li>
                          <li>
                            <code>content</code>
                            (string) emoji
                          </li>
                        </ul>
                      </li>
                      <li>(string) 图标URL</li>
                    </ul>
                  </li>
                </ul>
              </li>
            </ul>
          </li>
        </ul>
      </a-col>
      <a-col :span="11">
        <CodeGroup title="Request" tag="POST" label="/meta" gap>
          <pre>
curl -X GET '{{ props.baseUrl }}/meta?user=abc-123' \
-H 'Authorization: Bearer {api_key}'
          </pre>
        </CodeGroup>
        <CodeGroup title="Response">
          <pre>
{
  "tool_icons": {
      "dalle2": "https://cloud.arcana.ai/console/api/workspaces/current/tool-provider/builtin/dalle/icon",
      "api_tool": {
          "background": "#252525",
          "content": "😁"
      }
  }
}
          </pre>
        </CodeGroup>
      </a-col>
    </a-row>
  </div>

  <!-- <hr />
  <div class="w">
    <a-row :gutter="[20, 0]">
      <a-col :span="13"></a-col>
      <a-col :span="11"></a-col>
    </a-row>
  </div> -->
  <!-- <h3>阻塞模式</h3>
        <CodeGroup title="Response">
          <pre>

          </pre>
        </CodeGroup> -->
</template>
<script setup lang="ts" name="template-advanced-chat-zh">
import CodeGroup from './components/code-group.vue'
import Heading from './components/heading.vue'
import Property from './components/property.vue'

const props = withDefaults(
  defineProps<{
    baseUrl?: string
    inputs?: object
  }>(),
  {
    baseUrl: '',
    inputs: () => ({})
  }
)
</script>
