<template>
  <div class="select-var">
    <!--TODO: 使用dropdown实现，V2，暂时未完成。-->
    <a-dropdown @select="handleSelect">
      <div class="var-item-value cursor-pointer">
        <template v-if="getName">
          <div class="flex justify-between items-center cursor-pointer">
            <a-tag>{{ getName }}</a-tag>
            <icon-delete @click="handleClearValue(item, $event)" />
          </div>
        </template>
        <template v-else>
          <span class="text-gray-500">设置变量</span>
        </template>
      </div>
      <template #content>
        <a-doption>Option 1</a-doption>
        <a-doption disabled>Option 2</a-doption>
        <a-doption :value="{ value: 'Option3' }">Option 3</a-doption>
      </template>
    </a-dropdown>

    <a-popover position="left" trigger="click">
      <!-- @click="showPop(item)"-->
      <div class="var-item-value cursor-pointer">
        <template v-if="getName">
          <div class="flex justify-between items-center cursor-pointer">
            <a-tag>{{ getName }}</a-tag>
            <icon-delete @click="handleClearValue(item, $event)" />
          </div>
        </template>
        <template v-else>
          <span class="text-gray-500">设置变量</span>
        </template>
      </div>
      <template #content>
        <div v-for="groupItem in varList" class="full-w">
          <a-typography-text>{{ groupItem.title }}</a-typography-text>
          <div class="pop-var-item-container">
            <a-space direction="vertical" fill>
              <!--hover:bg-state-base-hover-->
              <div
                v-for="(varItem, varIndex) in groupItem.vars"
                class="pop-var-item flex justify-between pr-[18px] relative h-6 w-full cursor-pointer items-center rounded-md pl-3"
                @click="selectValue(groupItem, varItem)"
              >
                <span>{{ varItem.variable }}</span>
                <span>{{ varItem.type }}</span>
              </div>
            </a-space>
          </div>
        </div>
      </template>
    </a-popover>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { getBeforeNodesInSameBranch, toNodeOutputVars } from '@/views/app/workflow/utils/variable'

const props = defineProps(['nodeInfo', 'item'])
const getName = computed(() => {
  const item = props.item
  const value = item.value_selector[0] == 'sys' ? item.value_selector.join('.') : item.value_selector[1]
  const groupCategory =
    item.value_selector[0] == 'sys'
      ? varList.value.filter((v) => v.isStartNode)
      : varList.value.filter((v) => v.nodeId === item.value_selector[0])
  console.log('groupCategory:', groupCategory)
  let name = ''
  if (groupCategory.length > 0) {
    groupCategory[0].vars.forEach((item, index) => {
      if (item.variable == value) {
        name = groupCategory[0].title + '/' + item.variable
      }
    })
    console.log('name:', name)
  }
  return name
})

// 删除变量的值
const handleClearValue = (item, e) => {
  e.stopPropagation()
  console.log('item:', item, e)
  item.value_selector = []
}
const varList = ref<any[]>([])
onMounted(() => {
  // TODO-todo: 没有获取到id。
  const beforeList = getBeforeNodesInSameBranch('1747724606665')
  console.log('beforeList:', beforeList)

  const res = toNodeOutputVars(beforeList, false)
  console.log('处理后的变量res：', res)
  varList.value = res
    .map((node) => ({
      ...node
      // vars: node.isStartNode ? node.vars.filter(v => !v.variable.startsWith('sys.')) : node.vars,
    }))
    .filter((item) => item.vars.length > 0)

  // varList.value.forEach(item => {
  //   // if(item.isStartNode){
  //   //   // item.variable
  //   // }
  // })

  console.log('过滤后的变量', varList.value)
})

const visible = ref(false)
const selectValue = (group, item) => {
  console.log('选择：', group, item)

  props.item.value_selector = [group.nodeId, item.variable]
  visible.value = false
}

const handleSelect = (v) => {
  console.log(v)
}
</script>
<style scoped lang="scss">
.var-item-value {
  border: 1px solid #ccc;
  height: 36px;
  //line-height: 32px;
  border-radius: 4px;
  padding: 5px 12px;
}

.pop-var-item {
  line-height: 32px;
  height: 32px;

  &:hover {
    background: #cbceda;
  }
}
</style>
