<template>
  <a-modal :visible="visible" :mask-closable="false" @ok="handleOk" @cancel="handleCancel">
    <template #title>
      {{ dialogTitle }}
    </template>

    <template #footer>
      <a-button v-if="isConfigured" type="primary" status="danger" @click="handleDelete">移除</a-button>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" status="normal" @click="handleOk">确定</a-button>
    </template>
    <div>
      <CustomForms
        ref="customFormRef"
        :providerItem="providerItem"
        :formSchemas="apiKeySchemas"
        :pageMode="'add'"
        :form="form"
      />
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import CustomForms from '@/views/model-mgmt/components/CustomForms.vue'
import { deleteApiKeyHttp, getApiKeyDefaultHttp, saveApiKeyHttp } from '@/apis/model-mgmt'

const props = defineProps(['apiKeySchemas', 'providerItem', 'isConfigured'])
const emits = defineEmits(['closeConfigApiKeyModal'])
const visible = ref(true)
const dialogTitle = ref('设置')

// 表单的form：新增的时候，直接取key，然后给default
const form = reactive({})
const keys = props.apiKeySchemas.map((v) => {
  form[v.variable] = v.default || ''
})
const getDefaultConfig = async () => {
  let res = await getApiKeyDefaultHttp(props.providerItem.provider)
  Object.assign(form, res.credentials)
}
if (props.isConfigured) {
  getDefaultConfig()
}
const customFormRef = ref()
const handleOk = async () => {
  const valid = await customFormRef.value.validForm()
  if (!valid) {
    const params = {
      config_from: 'predefined-model',
      credentials: form,
      load_balancing: { enabled: false, configs: [] }
    }
    try {
      const res = await saveApiKeyHttp(props.providerItem.provider, params)
      emits('closeConfigApiKeyModal', 'ok')
    } catch (err) {
      console.log(err)
    }
  }
}
const handleCancel = () => {
  emits('closeConfigApiKeyModal')
}

// 删除api key。TODO-todo:加二次确认。
const handleDelete = async () => {
  try {
    const res = await deleteApiKeyHttp(props.providerItem.provider)
    emits('closeConfigApiKeyModal', 'ok')
  } catch (err) {
    console.log(err)
  }
}
</script>

<style scoped lang="scss"></style>
