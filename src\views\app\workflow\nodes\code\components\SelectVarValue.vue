<template>
  <div id="parentNode" ref="inputRef" class="select-var">
    <!--单选变量-->
    <a-popover
      position="left"
      trigger="click"
      class="ks-popover"
      content-class="ks-popover2"
      popup-container="#parentNode"
    >
      <div class="var-item-value cursor-pointer">
        <!--变量名字-->
        <template v-if="getName">
          <div class="flex justify-between items-center cursor-pointer">
            <a-tag>{{ getName }}</a-tag>
            <icon-close @click="handleClearValue($event)" />
          </div>
        </template>
        <template v-else>
          <span class="text-gray-500">{{ placeholder }}</span>
        </template>
      </div>
      <template #content>
        <VarList @handleSelectVar="handleSelectVar" />
      </template>
    </a-popover>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { useNodesStore } from '@/stores/modules/workflow/nodes'
import VarList from '@/views/app/workflow/nodes/http/components/rich-text/VarList.vue'

const props = withDefaults(
  defineProps<{
    nodeInfo?: any
    varValue?: any
    placeholder?: string //
  }>(),
  {
    nodeInfo: () => ({}),
    varValue: [],
    placeholder: '设置变量' //
  }
)

// 不清楚是否会兼容其他组件使用，后续可以导出值，自己更新？？
const emits = defineEmits(['change'])
// 获取变量的名字:名字要加上分组的数据。因为获取到的数据，没有做处理，所以在这里做一下 处理吧。（todo可以在获取数据后，处理后再渲染）
const getName = computed(() => {
  const varValue = props.varValue
  const value = varValue[0] == 'sys' ? varValue.join('.') : varValue[1]
  const groupCategory =
    varValue[0] == 'sys'
      ? varList.value.filter((v) => v.isStartNode)
      : varList.value.filter((v) => v.nodeId === varValue[0])

  let name = ''
  if (groupCategory.length > 0) {
    groupCategory[0].vars.forEach((item) => {
      if (item.variable == value) {
        name = groupCategory[0].title + '/' + item.variable
      }
    })
  }
  return name
})

// 变量下拉
const varList = ref<any[]>([])
const nodesStore = useNodesStore()
onMounted(() => {
  varList.value = nodesStore.parentNodesVarList
})

// 选择变量后，更新选中的值
const handleSelectVar = (groupItem, option) => {
  console.log('选择：', groupItem, option)
  // return false

  // 处理sys和非sys的变量：TODO-todo：结构怎么处理会更好？
  let groupId = ''
  let varId = ''
  if (option.variable.startsWith('sys.')) {
    groupId = 'sys'
    varId = option.variable.replace('sys.', '')
  } else {
    groupId = groupItem.nodeId
    varId = option.variable
  }
  emits('change', [groupId, varId])
}

// 删除变量的值
const handleClearValue = (e) => {
  e.stopPropagation()
  // varItem[props.varCode] = []
  emits('change', [])
}

const inputRef = ref()
</script>
<style scoped lang="scss">
.var-item-value {
  border: 1px solid var(--color-border-3);
  height: 36px;
  //line-height: 32px;
  border-radius: 4px;
  padding: 5px 12px;
}
</style>
