.step2-content {
  .split-container {
    display: flex;
    gap: 24px;
    height: calc(100vh - 140px);
    overflow: hidden;
    .settings-panel {
      flex: 1;
      overflow-y: auto;
      padding-right: 16px;
      h3 {
        font-size: 14px;
        font-weight: 500;
        margin-top: 16px;
        margin-bottom: 12px;
        color: var(--color-text-1);
      }
      .segment-mode-cards {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-bottom: 24px;
        .segment-mode-card {
          border: 1px solid var(--color-border-2);
          border-radius: 8px;
          overflow: hidden;
          transition: border-color 0.3s ease;
          cursor: pointer;
          &.active {
            border-color: var(--color-primary);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          }
          .card-header {
            display: flex;
            align-items: center;
            padding: 16px;
            background-color: var(--color-fill-1);
            .mode-icon {
              width: 36px;
              height: 36px;
              border-radius: 50%;
              background-color: var(--color-bg-2);
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 12px;
              .icon {
                font-size: 18px;
                color: var(--color-text-1);
              }
            }
            .mode-info {
              flex: 1;
              .mode-title {
                font-weight: 500;
                margin-bottom: 4px;
              }
              .mode-desc {
                font-size: 12px;
                color: var(--color-text-3);
              }
            }
          }
          .card-content {
            padding: 16px;
            background-color: #fff;
            border-top: 1px solid var(--color-border-2);
          }
          .parent-child-section {
            margin-bottom: 24px;
            background-color: var(--color-fill-1);
            border-radius: 6px;
            overflow: hidden;
            .section-header {
              display: flex;
              align-items: center;
              padding: 12px 16px 0 16px;
              .section-icon {
                margin-right: 10px;
                .icon {
                  font-size: 16px;
                  color: var(--color-text-1);
                }
              }
              .section-title {
                flex: 1;
                font-weight: 500;
                font-size: 14px;
              }
            }
            .section-content {
              padding: 16px;
            }
            .section-content1 {
              display: flex;
              .flex-1 {
                flex: 1;
              }
            }
          }
        }
      }
      // 设置卡片样式
      .setting-card {
        background-color: var(--color-bg-2);
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        .setting-card-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          .setting-icon {
            color: var(--color-primary);
          }
          .setting-title {
            font-weight: 500;
          }
        }
        .setting-desc {
          font-size: 12px;
          color: var(--color-text-3);
          margin-bottom: 16px;
        }
      }
      // 索引选项
      .index-options {
        display: flex;
        flex-direction: row;
        gap: 12px;
        .index-option {
          display: flex;
          align-items: flex-start;
          padding: 12px;
          border-radius: 6px;
          border: 1px solid var(--color-border-2);
          cursor: pointer;
          transition: all 0.2s;
          flex: 1;
          &.active {
            border-color: var(--color-primary);
            background-color: var(--color-primary-light-1);
          }
          .option-radio {
            margin-right: 8px;
            padding-top: 2px;
          }
          .option-content {
            display: flex;
            flex: 1;
            .option-icon {
              font-size: 16px;
              margin-right: 8px;
              color: var(--color-text-2);
            }
            .option-info {
              flex: 1;
              .option-title {
                font-weight: 500;
                margin-bottom: 4px;
                display: flex;
                align-items: center;
                gap: 6px;
              }
              .option-desc {
                font-size: 12px;
                color: var(--color-text-3);
                line-height: 1.5;
              }
            }
          }
        }
      }
      // 检索设置容器
      .retrieval-settings-container {
        display: flex;
        flex-direction: column;
        gap: 16px;
        margin-bottom: 20px;
        .retrieval-option {
          border: 1px solid var(--color-border-2);
          border-radius: 8px;
          overflow: hidden;
          transition: all 0.3s;
          &.active {
            border-color: var(--color-primary);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          }
          .option-header {
            display: flex;
            align-items: center;
            padding: 16px;
            background-color: var(--color-fill-1);
            cursor: pointer;
            .option-icon {
              width: 36px;
              height: 36px;
              border-radius: 50%;
              background-color: #fff;
              display: flex;
              align-items: center;
              justify-content: center;
              margin-right: 12px;
              .icon {
                font-size: 18px;
                color: var(--color-text-1);
              }
            }
            .option-info {
              flex: 1;
              .option-title {
                font-weight: 500;
                margin-bottom: 4px;
                display: flex;
                align-items: center;
                gap: 6px;
              }
              .option-desc {
                font-size: 12px;
                color: var(--color-text-3);
              }
            }
          }
          .option-content {
            padding: 16px;
            background-color: #fff;
            border-top: 1px solid var(--color-border-2);
            .content-section {
              .content-detail {
                font-size: 13px;
                color: var(--color-text-2);
                margin-bottom: 16px;
                line-height: 1.6;
              }
              .hybrid-modes {
                display: flex;
                flex-direction: row;
                gap: 12px;
                margin-bottom: 20px;
                .hybrid-mode-option {
                  flex: 1;
                  display: flex;
                  align-items: center;
                  padding: 12px;
                  border: 1px solid var(--color-border-2);
                  border-radius: 6px;
                  cursor: pointer;
                  &.selected {
                    border-color: var(--color-primary);
                    background-color: var(--color-primary-light-1);
                  }
                  .mode-icon {
                    font-size: 18px;
                    margin-right: 12px;
                    color: var(--color-text-2);
                  }
                  .mode-info {
                    flex: 1;
                    .mode-name {
                      font-weight: 500;
                      margin-bottom: 4px;
                    }
                    .mode-desc {
                      font-size: 12px;
                      color: var(--color-text-3);
                      line-height: 1.5;
                    }
                  }
                }
              }
              .weight-settings {
                padding: 12px;
                background-color: var(--color-fill-1);
                border-radius: 6px;
                .weight-setting-item {
                  display: flex;
                  align-items: center;
                  margin-bottom: 8px;
                  .weight-label {
                    width: 120px;
                    font-size: 13px;
                  }
                  .arco-slider {
                    flex: 1;
                    margin: 0 12px;
                  }
                  .weight-value {
                    width: 50px;
                    font-size: 12px;
                  }
                }
                .weight-note {
                  font-size: 12px;
                  color: var(--color-text-3);
                  text-align: right;
                  padding-right: 30px;
                }
              }
              .rerank-settings {
                .rerank-header {
                  display: flex;
                  align-items: center;
                  margin-bottom: 12px;
                  .hint-icon {
                    font-size: 14px;
                    color: var(--color-text-3);
                    margin-left: 4px;
                    cursor: pointer;
                  }
                }
                .select-label {
                  font-size: 13px;
                  margin-bottom: 8px;
                }
                .arco-select {
                  width: 100%;
                  margin-bottom: 16px;
                }
                .params-config {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 16px;
                  margin-top: 12px;
                  .param-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    .param-label {
                      font-size: 13px;
                      min-width: 80px;
                    }
                    .param-label1 {
                      min-width: 100px;
                      display: flex;
                      align-items: center;
                    }
                    .hint-icon {
                      font-size: 14px;
                      color: var(--color-text-3);
                      cursor: pointer;
                    }
                  }
                }
              }
            }
          }
        }
      }
      // 搜索方法样式
      .search-settings {
        display: flex;
        flex-direction: column;
        gap: 12px;
        .search-method-option {
          display: flex;
          align-items: flex-start;
          padding: 12px;
          border-radius: 6px;
          border: 1px solid var(--color-border-2);
          cursor: pointer;
          transition: all 0.2s;
          &.active {
            border-color: var(--color-primary);
            background-color: var(--color-primary-light-1);
          }
          .option-radio {
            margin-right: 8px;
            padding-top: 2px;
          }
          .option-content {
            display: flex;
            flex: 1;
            .search-icon {
              font-size: 16px;
              margin-right: 12px;
              color: var(--color-text-2);
            }
            .search-info {
              flex: 1;
              .search-title {
                font-weight: 500;
                margin-bottom: 4px;
                display: flex;
                align-items: center;
                gap: 6px;
              }
              .search-desc {
                font-size: 12px;
                color: var(--color-text-3);
                line-height: 1.5;
              }
            }
          }
        }
      }
      .form-item {
        margin-bottom: 16px;
        .form-label {
          font-size: 13px;
          margin-bottom: 8px;
          display: flex;
          align-items: center;
          .hint-icon {
            font-size: 14px;
            color: var(--color-text-3);
            margin-left: 4px;
            cursor: pointer;
          }
        }
        .input-with-unit {
          display: flex;
          align-items: center;
          .unit {
            margin-left: 8px;
            color: var(--color-text-3);
            font-size: 13px;
          }
        }
        .form-label-with-checkbox {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;
          .hint-icon {
            font-size: 14px;
            color: var(--color-text-3);
            cursor: pointer;
          }
        }
        .checkbox-list {
          display: flex;
          flex-direction: column;
          gap: 8px;
        }
      }
      .form-row {
        display: flex;
        gap: 16px;
        margin-bottom: 16px;
        .flex-1 {
          flex: 1;
        }
      }
      .form-actions {
        display: flex;
        justify-content: flex-start;
        margin-top: 16px;
      }
      .model-select {
        width: 100%;
        background-color: var(--color-fill-2);
        border-radius: 4px;
        &:hover {
          background-color: var(--color-fill-3);
        }
      }
    }
    .preview-panel {
      flex: 1;
      .title {
        color: #155aef;
        font-size: 10px;
        font-weight: 500;
      }
      .file-selection {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 20px;
      }
      .segment-preview-container {
        height: calc(100% - 50px);
        overflow-y: auto;
        padding: 16px;
        background-color: #f9f9fa;
        border-radius: 8px;
        border: 1px solid var(--color-border-2);
        .empty-preview {
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          .empty-state {
            text-align: center;
            .empty-icon {
              font-size: 32px;
              margin-bottom: 12px;
            }
            p {
              color: var(--color-text-3);
              margin: 0;
            }
          }
        }
        .blocks-preview, .segment-results {
          .segment-summary {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 20px;
            padding: 12px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            .summary-icon {
              font-size: 24px;
              color: var(--color-primary-light-4);
            }
            .summary-text {
              p {
                margin: 0;
                &.summary-hint {
                  font-size: 12px;
                  color: var(--color-text-3);
                  margin-top: 4px;
                }
              }
              .highlight {
                color: var(--color-primary);
                font-weight: 600;
              }
            }
          }
          .segment-list {
            .segment-item {
              margin-bottom: 16px;
              background-color: #fff;
              border-radius: 4px;
              padding: 12px;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
              .segment-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 8px;
                padding-bottom: 8px;
                border-bottom: 1px solid var(--color-border-2);
                .segment-index {
                  font-weight: 500;
                  color: var(--color-primary);
                }
                .segment-length {
                  font-size: 12px;
                  color: var(--color-text-3);
                }
              }
              .segment-content {
                font-size: 14px;
                color: var(--color-text-1);
                white-space: pre-wrap;
                word-break: break-word;
              }
            }
          }
        }
      }
    }
  }
  .step-footer {
    display: flex;
    justify-content: space-between;
    gap: 12px;
    margin: 24px 0;
    .prev-btn, .next-btn {
      min-width: 80px;
      height: 36px;
      border-radius: 6px;
    }
  }
}

.mode-option {
  display: flex;
  flex-direction: column;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--color-border-2);
  background-color: #fff;
  margin-bottom: 16px;
  &.selected {
    border-color: var(--color-primary);
    background-color: var(--color-primary-light-1);
  }
  .mode-option-header {
    display: flex;
    align-items: flex-start;
    .mode-icon {
      margin-right: 12px;
      color: var(--color-text-2);
    }
    .mode-info {
      flex: 1;
      .mode-name {
        font-weight: 500;
        margin-bottom: 4px;
      }
      .mode-desc {
        font-size: 12px;
        color: var(--color-text-3);
        line-height: 1.5;
      }
    }
  }
  .mode-option-content {
    padding: 16px;
    margin-top: 12px;
    background-color: var(--color-fill-1);
    border-radius: 4px;
  }
}

.tooltip-content {
  max-width: 300px;
  font-size: 12px;
  line-height: 1.5;
  color: #fff;
  white-space: normal;
  text-align: left;
}

.error-preview {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--color-fill-1);
  border-radius: 8px;
  .error-state {
    text-align: center;
    .error-icon {
      font-size: 40px;
      margin-bottom: 16px;
    }
    p {
      margin: 4px 0;
      color: var(--color-text-3);
    }
    .error-desc {
      font-size: 12px;
    }
  }
}