<template>
  <div v-for="(item, index) in extendedParameters" :key="index">
    <div class="flex h-[18px] items-center space-x-2">
      <span class="code-sm-semibold text-text-secondary">{{ item.name }}</span>
      <span class="system-xs-regular text-text-tertiary">{{ item.type }}</span>
      <span class="system-xs-regular text-util-colors-orange-dark-orange-dark-600" style="color: #e62e05">
        {{ item.required ? 'Required' : '' }}
      </span>
    </div>
    <div class="mt-0.5 text-xs font-normal leading-[18px] text-text-tertiary" style="padding: 0 10px">
      {{ item.description }}
    </div>
  </div>
</template>
<script setup lang="ts">
const props = defineProps<{
  nodeInfo?: any
}>()

// 使用 computed 扩展原始数据，添加两个新的 item
const extendedParameters = computed(() => {
  const originalItems = props.nodeInfo?.parameters || []
  // 添加两个新的 item
  return [
    ...originalItems,
    { name: '__is_success', type: 'number', description: '是否成功。成功时值为 1，失败时值为 0。' },
    { name: '__reason', type: 'string', description: '错误原因' }
  ]
})
</script>

<style scoped lang="scss"></style>
