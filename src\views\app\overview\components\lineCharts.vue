<template>
  <div ref="chart" class="echarts-container"></div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts'
import { ref, onMounted, onBeforeUnmount, watch } from 'vue'

const props = defineProps({
  xData: {
    type: Array as () => string[],
    required: true,
  },
  yData: {
    type: Array as () => number[],
    required: true,
  },
  lineColor: {
    type: String,
    default: '#5470c6',
  },
  xAxisName: {
    type: String,
    default: '',
  },
  yAxisName: {
    type: String,
    default: '',
  },
})

const chart = ref<HTMLElement | null>(null)
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chart.value) return
  chartInstance = echarts.init(chart.value)

  const option = {
    tooltip: {
      trigger: 'axis',
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      data: props.xData,
      name: props.xAxisName,
     
    },
    yAxis: {
      type: 'value',
      name: props.yAxisName,
    },
    series: [
      {
        name: '数据',
        type: 'line',
        data: props.yData,
        smooth: true,
        itemStyle: {
          color: props.lineColor,
        },
        lineStyle: {
          color: props.lineColor,
        },
      },
    ],
  }

  chartInstance.setOption(option)
}

watch(
  () => [props.xData, props.yData, props.lineColor],
  () => {
    if (chartInstance) {
      chartInstance.setOption({
        xAxis: {
          data: props.xData,
        },
        yAxis: {},
        series: [
          {
            data: props.yData,
            itemStyle: {
              color: props.lineColor,
            },
            lineStyle: {
              color: props.lineColor,
            },
          },
        ],
      })
    }
  },
  { deep: true }
)

const resizeHandler = () => {
  if (chartInstance) {
    chartInstance.resize()
  }
}

onMounted(() => {
  initChart()
  window.addEventListener('resize', resizeHandler)
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', resizeHandler)
  if (chartInstance) {
    chartInstance.dispose()
    chartInstance = null
  }
})
</script>

<style scoped>
.echarts-container {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>