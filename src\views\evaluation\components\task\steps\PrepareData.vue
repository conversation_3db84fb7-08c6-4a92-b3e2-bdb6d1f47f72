<template>
  <div class="prepare-data">
    <AiTable
      v-model:selectedKeys="selectedKeys"
      row-key="id"
      :data="dataList || []"
      :row-selection="{ type: 'radio' }"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%' }"
      :pagination="pagination"
      :disabled-tools="['setting', 'refresh', 'fullscreen']"
      :disabled-column-keys="['name']"
      style="flex: 1"
      @refresh="search"
      @select="select"
    >
      <template #inputName="{ record }">
        <span>{{ record.inputFileName || '-' }}</span>
      </template>
      <template #outputName="{ record }">
        <span>{{ record.outputFileName || '-' }}</span>
      </template>
      <template #action="{ record }">
        <a-space>
          <a-link title="修改" @click="downloadFile(record)">下载</a-link>
        </a-space>
      </template>
    </AiTable>
    <!-- 底部按钮 -->
    <div class="action-buttons">
      <a-space>
        <a-button class="prev-btn" @click="handlePrev">上一步</a-button>
        <a-button type="primary" class="next-btn" :disabled="!isFormValid" @click="handleNext">下一步</a-button>
      </a-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useTable } from '@/hooks'
import { getEvaluationSetsPage } from '@/apis/evaluation/evaluation-sets'
import { RIPageEvalDataset } from '@/apis/evaluation/evaluation-sets-type'
import { EvalTaskSaveDTO } from '@/apis/evaluation/task-types'

defineOptions({ name: 'PrepareData' })

const props = defineProps({
  formData: {
    type: Object as () => EvalTaskSaveDTO,
    required: true
  }
})

const emit = defineEmits(['prev', 'next', 'saveForm'])

const route = useRoute()
const localFormData = ref<EvalTaskSaveDTO>({
  datasetData: {
    datasetId: '',
    taskId: ''
  }
})

const selectedKeys = ref([])

const {
  tableData: dataList,
  loading,
  pagination,
  search
} = useTable<RIPageEvalDataset>(
  (page) => getEvaluationSetsPage({ ...page, model: { type: route?.query?.taskType === '1' ? 2 : 1 } }),
  {
    immediate: true
  }
)

const columns = [
  { title: '评测集名称', dataIndex: 'name', ellipsis: true, tooltip: true, width: 180 },
  { title: '输入文件', dataIndex: 'inputFileName', slotName: 'inputName' },
  route?.query?.taskType === '1' && { title: '输出文件', dataIndex: 'outputFileName', slotName: 'outputName' },
  {
    title: '操作',
    dataIndex: 'action',
    slotName: 'action',
    width: 80,
    align: 'left'
  }
]
const select = (rowKey, rowKeys, record) => {
  localFormData.value.datasetData.datasetId = rowKeys
}
const downloadFile = (record) => {}

const isFormValid = computed(() => {
  return !!localFormData.value.datasetData.datasetId
})

const handlePrev = () => {
  emit('prev')
}

const handleNext = () => {
  if (isFormValid.value) {
    emit('saveForm', localFormData.value)
    emit('next')
  }
}

watchEffect(() => {
  if (props.formData) {
    localFormData.value = { ...localFormData.value, ...props.formData }
  }
})

onMounted(() => {
  if (props.formData?.datasetData?.datasetId) {
    selectedKeys.value = [props.formData?.datasetData?.datasetId]
  }
})
</script>

<style scoped lang="scss">
.prepare-data {
  width: 1000px;
  margin: auto;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.action-buttons {
  margin-top: 20px;
  text-align: right;
}
</style>
