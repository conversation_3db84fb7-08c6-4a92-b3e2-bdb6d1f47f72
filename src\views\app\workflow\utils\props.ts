import { type GraphNode, useVueFlow } from '@vue-flow/core'

export function findMaxXY(
  items: {
    x: number
    y: number
    width: number
    height: number
  }[]
): { max_x: number; max_y: number } {
  let maxX = 0
  let maxY = 0

  for (const item of items) {
    const currentX = item.x + item.width
    const currentY = item.y + item.height

    if (currentX > maxX) {
      maxX = currentX
    }

    if (currentY > maxY) {
      maxY = currentY
    }
  }

  return { max_x: Math.ceil(maxX), max_y: Math.ceil(maxY) }
}
