<template>
  <div class="preview-panel">
    <span class="title">预览</span>
    <!-- 文件选择下拉框 -->
    <div class="file-selection">
      <a-select
        :model-value="selectedPreviewFileId"
        placeholder="请选择要预览的文件"
        style="width: 60%"
        :bordered="false"
        @change="handlePreviewFileChange"
      >
        <a-option
          v-for="file in uploadedFiles"
          :key="file.response?.id"
          :value="file.response?.id"
          :disabled="!file.response?.id"
        >
          {{ file.name }}
        </a-option>
      </a-select>
      <a-tag v-if="blocksPreviewTotal">{{ blocksPreviewTotal }}个块</a-tag>
    </div>
    <!-- 分段预览结果区域 -->
    <div class="segment-preview-container">
      <a-spin v-if="previewLoading || previewingBlocks" />
      <!-- 块预览结果 -->
      <div v-else-if="blocksPreviewContent.length > 0" class="blocks-preview">
        <div class="segment-summary">
          <div class="summary-icon">📊</div>
          <div class="summary-text">
            <p>
              文档将被分成
              <span class="highlight">{{ blocksPreviewTotal }}</span>
              个块
            </p>
            <p class="summary-hint">分段模式: {{ segmentMode === 'normal' ? '通用' : '父子分段' }}</p>
          </div>
        </div>
        <div class="segment-list">
          <div v-for="(block, index) in blocksPreviewContent" :key="index" class="segment-item">
            <div class="segment-header">
              <span class="segment-index">块 #{{ index + 1 }}</span>
              <span class="segment-length">{{ block.content.length }} 字符</span>
            </div>
            <div class="segment-content">{{ block.content }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
import { ref, watch, onMounted, computed, nextTick } from 'vue'

// 文件类型接口定义
interface UploadFile {
  uid: string
  name: string
  size: number
  status?: string
  response?: {
    id: string
    [key: string]: any
  }
  [key: string]: any
}

// 分段内容类型接口定义
interface SegmentBlock {
  content: string
  child_chunks?: any[] | null
  [key: string]: any
}

// 接收的props
const props = defineProps({
  selectedPreviewFileId: {
    type: String,
    default: ''
  },
  uploadedFiles: {
    type: Array as () => Array<UploadFile>,
    default: () => []
  },
  totalSegments: {
    type: Number,
    default: 0
  },
  previewLoading: {
    type: Boolean,
    default: false
  },
  previewingBlocks: {
    type: Boolean,
    default: false
  },
  blocksPreviewContent: {
    type: Array as () => Array<SegmentBlock>,
    default: () => []
  },
  blocksPreviewTotal: {
    type: Number,
    default: 0
  },
  segmentMode: {
    type: String,
    default: 'normal'
  }
})

// 定义事件
const emit = defineEmits([
  'update:selectedPreviewFileId', // v-model双向绑定事件
  'preview-blocks' // 预览块的事件
])

// 处理文件选择变更
const handlePreviewFileChange = (fileId: string) => {
  console.log('下拉框选择文件ID:', fileId)
  if (fileId) {
    // 更新父组件的selectedPreviewFileId
    emit('update:selectedPreviewFileId', fileId)

    // 当选择了文件后，调用父组件的预览块方法
    nextTick(() => {
      triggerPreviewBlocks(fileId)
    })
  }
}

// 调用父组件的预览块方法
const triggerPreviewBlocks = (fileId: string) => {
  console.log('触发预览块方法，文件ID:', fileId, '模式:', props.segmentMode)
  emit('preview-blocks', props.segmentMode)
}

// 查找第一个可用的文件ID
const findFirstAvailableFileId = (): string => {
  if (!props.uploadedFiles || props.uploadedFiles.length === 0) {
    return ''
  }
  const firstFile = props.uploadedFiles.find((file) => file?.response?.id)
  return firstFile?.response?.id || ''
}

// 自动选择第一个文件（如果有且未选择文件）
const selectFirstFileIfNone = () => {
  // 防止在自动选择过程中重复执行
  if (isAutoSelecting.value) return

  if ((!props.selectedPreviewFileId || props.selectedPreviewFileId === '') && props.uploadedFiles.length > 0) {
    const firstFileId = findFirstAvailableFileId()
    if (firstFileId && firstFileId !== props.selectedPreviewFileId) {
      console.log('自动选择第一个文件:', firstFileId)
      // 更新父组件的selectedPreviewFileId
      emit('update:selectedPreviewFileId', firstFileId)

      // 触发预览 - 使用nextTick确保DOM更新后再触发
      nextTick(() => {
        if (!isAutoSelecting.value) {
          triggerPreviewBlocks(firstFileId)
        }
      })
    }
  }
}

// 防止递归更新的标志
const isAutoSelecting = ref(false)

// 优化文件列表和选择ID监听逻辑
watch(
  () => props.uploadedFiles.length,
  (newLength, oldLength) => {
    console.log('文件列表长度变化:', newLength, '当前选择:', props.selectedPreviewFileId)

    // 只在文件数量增加且未选择文件时，选择第一个文件
    if (
      newLength > 0 &&
      newLength > (oldLength || 0) &&
      (!props.selectedPreviewFileId || props.selectedPreviewFileId === '') &&
      !isAutoSelecting.value
    ) {
      isAutoSelecting.value = true
      setTimeout(() => {
        selectFirstFileIfNone()
        setTimeout(() => {
          isAutoSelecting.value = false
        }, 200)
      }, 100)
    }
  },
  { immediate: true }
)

// 组件挂载时检查
onMounted(() => {
  console.log('FilePreview组件挂载, 文件数:', props.uploadedFiles.length, '当前选择:', props.selectedPreviewFileId)
  // 延迟执行，确保父组件完全初始化
  setTimeout(() => {
    if (!isAutoSelecting.value && props.uploadedFiles.length > 0 && !props.selectedPreviewFileId) {
      isAutoSelecting.value = true
      selectFirstFileIfNone()
      setTimeout(() => {
        isAutoSelecting.value = false
      }, 200)
    }
  }, 500)
})
</script>
