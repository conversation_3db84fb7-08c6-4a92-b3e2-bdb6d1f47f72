<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    title-align="start"
    :mask-closable="false"
    :esc-to-close="false"
    :width="width >= 500 ? 500 : '100%'"
    draggable
    @before-ok="save"
    @cancel="handleCancel"
    @close="reset"
  >
    <AiForm ref="formRef" v-model="form" layout="vertical" :columns="columns" />
  </a-modal>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'
import { useWindowSize } from '@vueuse/core'
import { AiForm, type ColumnItem } from '@/components/AiForm'
import { useResetReactive } from '@/hooks'
import { createApp } from '@/apis'

const emit = defineEmits<{
  (e: 'save-success'): void
}>()

const { width } = useWindowSize()

const dataId = ref('')
const visible = ref(false)
const isUpdate = computed(() => !!dataId.value)
const formRef = ref<InstanceType<typeof AiForm>>()

const [form, resetForm] = useResetReactive({
  name: '' as string,
  description: '' as string,
  mode: '',
  icon: '🤖',
  icon_background: '#FFEAD5',
  icon_type: 'emoji'
})
const title = computed(() => (isUpdate.value ? '修改应用' : `创建应用`))

const columns = reactive<ColumnItem[]>([
  {
    label: '应用类型',
    field: 'mode',
    type: 'radio-group',
    required: true,
    span: 24,
    props: {
      type: 'radio',
      options: [
        { label: '工作流', value: 'workflow' },
        { label: 'AGENT', value: 'agent-chat' },
        { label: '聊天助手', value: 'chat' }
      ]
    }
  },
  {
    label: '应用名称',
    field: 'name',
    type: 'input',
    span: 24,
    required: true,
    props: {
      maxLength: 100
    }
  },
  {
    label: '应用描述',
    field: 'description',
    type: 'textarea',
    span: 24
  }
])

// 重置
const reset = () => {
  formRef.value?.formRef?.resetFields()
  resetForm()
}

// 处理取消
const handleCancel = () => {
  visible.value = false
  reset()
}

// 保存
const save = async () => {
  try {
    const isInvalid = await formRef.value?.formRef?.validate()
    if (isInvalid) return false
    await createApp(form)
    Message.success('新建成功')
    visible.value = false
    emit('save-success')
    reset()
    return true
  } catch (error) {
    return false
  }
}

// 新增
const onAdd = () => {
  reset()
  visible.value = true
}

defineExpose({ onAdd })
</script>
