import { LOCAL_ENUM_DATA } from '../../enums/localEnum'

import { PROXY_RULE } from './props'
import type { ENUM_DATA_LIST, TRANSFORM_ENUM_DATA, TRANSFORM_ENUM_DATA_LIST, TRANSFORM_ENUM_ELEMENT } from './types'
import { Type } from '@/views/app/workflow/utils/common'

class Enum {
  constructor(ENUM_DATA: ENUM_DATA_LIST) {
    const observeData = this.#observe(ENUM_DATA, 0)
    this.#value = observeData
  }
  public isAddRemote = false
  #value: ProxyHandler<TRANSFORM_ENUM_DATA_LIST> | null = null

  addEnum = this.#dealData('add')

  updateEnum = this.#dealData('update')

  set value(val: ProxyHandler<TRANSFORM_ENUM_DATA_LIST> | null) {
    if (!this.value && val) {
      this.#value = val
    } else {
      throw Error('禁止重新赋值')
    }
  }

  get value() {
    return this.#value
  }

  #dealData(type = 'add', isRemote = false) {
    return (ENUM_DATA: ENUM_DATA_LIST = []) => {
      if (!Array.isArray(ENUM_DATA)) throw new Error('ENUM TypeError: 参数类型错误')
      try {
        const observeData: TRANSFORM_ENUM_DATA_LIST = this.#observe(ENUM_DATA, 0)
        for (const key in observeData) {
          if (key.startsWith('_')) continue
          const value = observeData[key]
          switch (type) {
            case 'add': {
              isRemote && (this.isAddRemote = true)
              Reflect.set(this.value as ProxyHandler<TRANSFORM_ENUM_DATA_LIST>, key, value)
              const hasKey = Reflect.has(this.value as ProxyHandler<TRANSFORM_ENUM_DATA_LIST>, key)
              if (hasKey) continue
              break
            }
            case 'update': {
              const isInner =
                // eslint-disable-next-line @typescript-eslint/no-empty-object-type
                Type.isObject(this.value) && Object.keys(this.value as {}).filter((i) => i !== '_key').length === 0
              if (!isInner && LOCAL_ENUM_DATA.find((i) => i?.dictCode === key))
                throw new Error(`ENUM TypeError: 不能更新前端本地枚举 ${key}`)
              Reflect.set(this.value as ProxyHandler<TRANSFORM_ENUM_DATA_LIST>, key, {
                isUpdate: true,
                value
              })
              break
            }
          }
        }
      } catch (error) {
        console.error(error)
      }
    }
  }

  #observe(data: ENUM_DATA_LIST | TRANSFORM_ENUM_ELEMENT | TRANSFORM_ENUM_DATA, level: 0 | 1 | 2) {
    if ((!Type.isObject(data) && !Array.isArray(data)) || ![0, 1, 2].includes(level)) {
      console.error('Observer ERROR: 参数错误')
      return false
    }
    let observeData
    if (Type.isObject(data) && [1, 2].includes(level)) {
      observeData = this.#proxyData(level as 1 | 2, data as TRANSFORM_ENUM_ELEMENT | TRANSFORM_ENUM_DATA)
    } else if (Array.isArray(data) && level === 0) {
      observeData = this.#formatData(data as ENUM_DATA_LIST)
    }
    return observeData
  }

  #formatData = (data: ENUM_DATA_LIST): TRANSFORM_ENUM_DATA_LIST => {
    if (!Array.isArray(data)) throw Error('formatData ERROR: 参数错误')
    const FORMATE_ENUM_DATA: TRANSFORM_ENUM_DATA_LIST = { _level: 0 }
    for (let a = 0; a < data.length; a++) {
      const { dictCode, elements = [], rules = [] } = data[a] || {}
      const $VALUE_TYPE = Type.isObject(elements[0])
        ? Type.isNumber(elements[0].value)
          ? 'number'
          : Type.isString(elements[0].value)
            ? 'string'
            : 'boolean'
        : 'string'

      FORMATE_ENUM_DATA[dictCode] = {
        $NAME: dictCode,
        $RULE: {},
        $LIST: null,
        $MATCH: null,
        $ATTRIBUTE: new Map(),
        $VALUE_TYPE,
        _level: 1
      }

      const ENUM_DATA = FORMATE_ENUM_DATA[dictCode] as TRANSFORM_ENUM_DATA

      elements.forEach((item) => {
        const { code, value } = item
        const ENUM_ITEM: TRANSFORM_ENUM_ELEMENT = {
          ...item,
          _key: `${dictCode}_${code}_${value}`,
          $NAME: `${dictCode}.${code}`,
          _level: 2
        }

        ENUM_DATA.$ATTRIBUTE?.set(code, new Proxy(this.#observe(ENUM_ITEM, 2), PROXY_RULE))
      })

      rules.forEach((rule = {}) => {
        const ruleKeys = Object.keys(rule)
        if (ruleKeys.length === 0) return
        ruleKeys.forEach((key) => {
          const Rule = rule[key].map((ruleItem) => {
            ruleItem =
              $VALUE_TYPE === 'number'
                ? Number(ruleItem)
                : ruleItem === 'boolean'
                  ? Boolean(ruleItem)
                  : String(ruleItem)
            return ruleItem
          })
          ;(ENUM_DATA.$RULE as TRANSFORM_ENUM_DATA)[key] = Rule
        })
      })
    }
    this.#proxyData(0, FORMATE_ENUM_DATA)
    return FORMATE_ENUM_DATA
  }

  // 数据代理
  #proxyData = (level: 0 | 1 | 2, data: TRANSFORM_ENUM_DATA_LIST | TRANSFORM_ENUM_ELEMENT | TRANSFORM_ENUM_DATA) => {
    for (const key in data) {
      const item = data[key]
      switch (level) {
        case 0: {
          if (Type.isObject(item) && !key.startsWith('$') && !key.startsWith('_')) {
            data[key] = new Proxy(this.#observe(item as TRANSFORM_ENUM_DATA, 1), PROXY_RULE)
          }
          break
        }
      }
    }
    return data
  }
}

export default Enum
