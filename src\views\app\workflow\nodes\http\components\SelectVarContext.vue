<template>
  <div class="select-var">
    <!--TODO-todo:暂时没有用到-->
    <a-popover position="left" trigger="click">
      <!-- @click="showPop(item)"-->
      <div class="var-item-value cursor-pointer">
        <!--变量名字-->
        <template v-if="getName">
          <div class="flex justify-between items-center cursor-pointer">
            <a-tag>{{ getName }}</a-tag>
            <icon-delete @click="handleClearValue(varItem, $event)" />
          </div>
        </template>
        <template v-else>
          <span class="text-gray-500">设置变量</span>
        </template>
      </div>
      <template #content>
        <!--下拉弹框-->
        <div v-for="(groupItem, groupIndex) in varList" :key="groupIndex" class="w-full">
          <a-typography-text>{{ groupItem.title }}</a-typography-text>
          <div class="pop-var-item-container">
            <a-space direction="vertical" fill>
              <!--hover:bg-state-base-hover-->
              <div
                v-for="(option, optionIndex) in groupItem.vars"
                :key="optionIndex"
                class="pop-var-item flex justify-between pr-[18px] relative h-6 w-full cursor-pointer items-center rounded-md pl-3"
                @click="selectValue(groupItem, option)"
              >
                <span>{{ option.variable }}</span>
                <span>{{ option.type }}</span>
              </div>
            </a-space>
          </div>
        </div>
      </template>
    </a-popover>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { getBeforeNodesInSameBranch, toNodeOutputVars } from '@/views/app/workflow/utils/variable'

// const props = defineProps(['nodeInfo', 'varItem'])
const props = withDefaults(
  defineProps<{
    nodeInfo?: any
    varItem?: any
    varCode?: string
  }>(),
  {
    nodeInfo: () => ({}),
    varItem: () => ({}),
    varCode: 'value_selector'
  }
)
console.log('props:', props)

const emits = defineEmits(['handleSelectedVar'])
// 获取变量的名字:名字要加上分组的数据。因为获取到的数据，没有做处理，所以在这里做一下 处理吧。（todo可以在获取数据后，处理后再渲染）
const getName = computed(() => {
  const varItem = props.varItem
  const value =
    varItem.variable_selector[0] == 'sys' ? varItem.variable_selector.join('.') : varItem.variable_selector[1]
  const groupCategory =
    varItem.variable_selector[0] == 'sys'
      ? varList.value.filter((v) => v.isStartNode)
      : varList.value.filter((v) => v.nodeId === varItem.variable_selector[0])

  let name = ''
  if (groupCategory.length > 0) {
    groupCategory[0].vars.forEach((item, index) => {
      if (item.variable == value) {
        name = groupCategory[0].title + '/' + item.variable
      }
    })
  }
  return name
})

const varList = ref<any[]>([])
onMounted(() => {
  // TODO-todo: 没有获取到id。
  const beforeList = getBeforeNodesInSameBranch('1747724606665')
  console.log('beforeList:', beforeList)

  const res = toNodeOutputVars(beforeList, false)
  console.log('处理后的变量res：', res)
  varList.value = res
    .map((node) => ({
      ...node
      // vars: node.isStartNode ? node.vars.filter(v => !v.variable.startsWith('sys.')) : node.vars,
    }))
    .filter((item) => item.vars.length > 0)

  // TODO-todo:开始节点的sys值在哪里处理？
  console.log('过滤后的变量', varList.value)
})

// 选择变量后，更新选中的值
const selectValue = (groupItem, option) => {
  console.log('选择：', groupItem, option)
  props.varItem.variable_selector = [groupItem.nodeId, option.variable]
  emits('handleSelectedVar', [groupItem.nodeId, option.variable])
}
// 删除变量的值
const handleClearValue = (varItem, e) => {
  e.stopPropagation()
  varItem.variable_selector = []
  emits('handleSelectedVar', [])
}
</script>
<style scoped lang="scss">
.var-item-value {
  border: 1px solid var(--color-border-3);
  height: 36px;
  //line-height: 32px;
  border-radius: 4px;
  padding: 5px 12px;
}

.pop-var-item {
  line-height: 32px;
  height: 32px;

  &:hover {
    background: rgba(203, 206, 218, 0.4);
  }
}
</style>
