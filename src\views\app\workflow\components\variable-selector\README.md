# 变量选择器组件 (Variable Selector)

这个组件用于在工作流节点编辑中选择变量。它允许用户从可用的变量列表中选择一个变量，并显示已选择的变量。

## 组件结构

- `VariableSelector.vue`: 主组件，包含变量选择器的主要逻辑和UI
- `VariableTag.vue`: 显示已选择的变量
- `VariableList.vue`: 显示可选择的变量列表
- `Example.vue`: 使用示例

## 使用方法

### 基本用法

```vue
<template>
  <VariableSelector
    :node-id="nodeId"
    :value-selector="selectedVariable"
    @change="handleVariableChange"
  />
</template>

<script setup>
import { ref } from 'vue'
import VariableSelector from '@/components/workflow/variable-selector'

const nodeId = ref('node-id')
const selectedVariable = ref([])

const handleVariableChange = (valueSelector, varItem) => {
  selectedVariable.value = valueSelector
  console.log('Selected variable:', valueSelector, varItem)
}
</script>
```

### 只读模式

```vue
<VariableSelector
  :node-id="nodeId"
  :value-selector="['node1', 'variable1']"
  :readonly="true"
/>
```

### 变量类型过滤

```vue
<VariableSelector
  :node-id="nodeId"
  :value-selector="stringVariable"
  :var-type="VarType.string"
  @change="handleStringVariableChange"
/>
```

### 自定义占位符

```vue
<VariableSelector
  :node-id="nodeId"
  placeholder="请选择一个变量"
/>
```

## 属性

| 属性名 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| nodeId | string | - | 当前节点ID，用于获取可用的变量 |
| valueSelector | string[] | [] | 已选择的变量，格式为 [nodeId, variableName] |
| varType | VarType | VarType.string | 变量类型，用于过滤可选择的变量 |
| readonly | boolean | false | 是否为只读模式 |
| isShowNodeName | boolean | true | 是否显示节点名称 |
| placeholder | string | '选择变量' | 未选择变量时的占位符文本 |
| popupContainer | string | 'body' | 弹出层的容器 |
| availableNodes | any[] | [] | 可用的节点列表，如果不提供则自动获取 |
| nodesOutputVars | any[] | [] | 可用的变量列表，如果不提供则自动获取 |
| filterVar | Function | () => true | 自定义变量过滤函数 |

## 事件

| 事件名 | 参数 | 说明 |
| --- | --- | --- |
| change | (valueSelector: string[], varItem: any) | 选择变量时触发 |
| open | - | 打开变量选择器时触发 |

## 样式自定义

组件使用 SCSS 进行样式定义，可以通过覆盖以下类名来自定义样式：

- `.variable-selector`: 整个选择器容器
- `.variable-selector-trigger`: 触发器样式
- `.variable-selector-placeholder`: 占位符样式
- `.variable-tag`: 变量标签样式
- `.variable-list`: 变量列表样式
- `.variable-list-item`: 变量列表项样式
