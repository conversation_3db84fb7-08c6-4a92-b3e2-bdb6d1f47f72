import { useVueFlow } from '@vue-flow/core'
import { useWorkflowStore } from '@/stores'
import { NodeRunningStatus, type IterationStartedResponse } from '@/views/app/workflow/types/workflow'

export const useWorkflowNodeIterationStarted = () => {
  const workflowStore = useWorkflowStore()

  const handleWorkflowNodeIterationStarted = (params: IterationStartedResponse) => {
    const { data } = params
    const { workflowRunningData, setWorkflowRunningData, setIterTimes } = workflowStore
    const { nodes, setNodes, edges, setEdges } = useVueFlow()
    const workflowData = workflowRunningData
    workflowData?.tracing.push({
      ...data,
      status: NodeRunningStatus.Running
    })
    setWorkflowRunningData(workflowData)
    setIterTimes(1)

    if (nodes.value?.length) {
      const newNodes = nodes.value.map((node) => {
        if (node.id === data.node_id) {
          node.data._runningStatus = NodeRunningStatus.Running
          node.data._iterationLength = data.metadata.iterator_length
          node.data._waitingRun = false
        }
        return node
      })
      setNodes(newNodes)
    }

    if (edges.value?.length) {
      const newEdges = edges.value.map((edge) => {
        if (edge.target === data.node_id) {
          edge.data = {
            ...edge.data,
            _sourceRunningStatus: nodes.value.find((node) => node.id === edge.source)!.data._runningStatus,
            _targetRunningStatus: NodeRunningStatus.Running,
            _waitingRun: false
          }
        }
        return edge
      })
      setEdges(newEdges)
    }
  }

  return {
    handleWorkflowNodeIterationStarted
  }
}
