<template>
  <AiPageLayout>
    <a-tabs
      ref="tabsRef"
      v-model:active-key="tabsKey"
      :default-active-key="tabType"
      lazy-load
      justify
      size="large"
      @change="tableChange"
    >
      <template #extra>
        <a-space v-if="tabsKey === 'workflow'">
          <a-button>
            <template #icon><icon-refresh /></template>
            <template #default>还原</template>
          </a-button>
          <a-button-group>
            <a-button @click="runFlow">
              <template #icon><icon-play-arrow /></template>
              <template #default>运行</template>
            </a-button>
            <a-tooltip content="运行历史" placement="bottom">
              <a-button>
                <template #icon>
                  <a-popover
                    v-model:popup-visible="visible"
                    trigger="click"
                    position="bottom"
                    :content-style="{ padding: '8px', borderRadius: '8px' }"
                  >
                    <icon-history @click="getWorkflowRunsData" />
                    <template #content>
                      <div
                        class="sticky top-0 flex items-center justify-between px-4 pt-3 text-base font-semibold text-text-primary"
                      >
                        <div class="grow">运行历史</div>
                        <div class="flex h-6 w-6 shrink-0 cursor-pointer items-center justify-center">
                          <icon-close @click="close" />
                        </div>
                      </div>
                      <div class="node-history">
                        <div
                          v-for="item in workflowRuns"
                          :key="item.id"
                          class="mb-0.5 flex cursor-pointer rounded-lg px-2 py-[7px] hover:bg-slate-100"
                          @click="getworkflowHistory(item)"
                        >
                          <icon-check-circle
                            v-if="item.status == 'succeeded'"
                            class="text-[#12B76A] mr-1.5 mt-0.5 h-3.5 w-3.5"
                          />

                          <icon-exclamation-circle
                            v-else-if="item.status == 'stopped'"
                            class="text-[#F79009] mr-1.5 mt-0.5 h-3.5 w-3.5"
                          />

                          <icon-exclamation-circle
                            v-else-if="item.status == 'failed'"
                            class="text-[#F04438] mr-1.5 mt-0.5 h-3.5 w-3.5"
                          />
                          <div>
                            <div class="flex items-center text-[13px] font-medium leading-[18px]">
                              Test Run#{{ item.sequence_number }}
                            </div>
                            <div class="flex items-center text-xs leading-[18px] text-gray-500">
                              {{ item.created_by_account?.name }} ·
                              {{
                                dayjs((item?.finished_at || item?.created_at) * 1000)
                                  .locale('zh-cn')
                                  .fromNow()
                              }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                  </a-popover>
                </template>
              </a-button>
            </a-tooltip>

            <a-tooltip content="功能" placement="bottom">
              <a-button>
                <template #icon>
                  <icon-list />
                </template>
              </a-button>
            </a-tooltip>
          </a-button-group>
          <a-button>
            <template #icon>
              <icon-mind-mapping />
            </template>
            <template #default>功能</template>
          </a-button>
          <a-dropdown @select="handleSelect">
            <a-button type="primary">
              <template #icon>
                <icon-send />
              </template>
              <template #default>发布</template>
            </a-button>
            <template #content>
              <a-doption :value="{ value: 'publish' }">发布</a-doption>
              <a-doption :value="{ value: '2' }">保存</a-doption>
              <a-doption :value="{ value: 'run' }">运行</a-doption>
              <a-doption :value="{ value: 'publishToll' }">发布为工具</a-doption>
            </template>
          </a-dropdown>
          <a-tooltip content="版本历史" placement="bottom">
            <a-button @click="versionhistory('', 'historylist')">
              <template #icon>
                <icon-history />
              </template>
            </a-button>
          </a-tooltip>
        </a-space>
      </template>
      <a-tab-pane key="workflow" title="编排">
        <WorkFlow v-if="appType === 'workflow'" ref="workflow" />
        <Configuration v-if="appType === 'configuration'" />
      </a-tab-pane>

      <a-tab-pane key="develop" title="访问API">
        <Develop :appInfo="appInfo" />
      </a-tab-pane>
      <a-tab-pane key="logs" title="日志">
        <Logs :appInfo="appInfo" />
      </a-tab-pane>
      <a-tab-pane key="overview" title="监测">
        <Overview :appInfo="appInfo" @tab-change="handleDevelop" />
      </a-tab-pane>
    </a-tabs>
  </AiPageLayout>
</template>

<script setup lang="ts">
import WorkFlow from './workflow/index.vue'
import Develop from './develop/index.vue'
import Logs from './log/index.vue'
import Overview from './overview/index.vue'
import {
  workflowhistoryRun,
  getversionhistory,
  getWorkflowRuns,
  workflowPublish,
  getAppConfig,
  type AppConfigResponse
} from '@/apis'
import { useAppStore } from '@/stores'

import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import 'dayjs/locale/zh-cn'
dayjs.extend(relativeTime)
import Configuration from '@/views/app/configuration/index.vue'
import { Message } from '@arco-design/web-vue'
import { log } from 'console'
defineOptions({ name: 'Flow' })

defineExpose({ dayjs })
interface HistoryWorkflowData {
  id: string
  sequence_number: number
  status: string
  conversation_id?: string
  finished_at: number
  created_at: number
  created_by_account: {
    name: string
  }
}
const route = useRoute()
const router = useRouter()
const appStore = useAppStore()

const workflowRuns = ref<HistoryWorkflowData[]>([])
const appId = route.params.appId as string
const appType = route.params.appType as string
const tabType = route.query.type as string
const appInfo = ref({})

const getWorkflowRunsData = async () => {
  const res = await getWorkflowRuns(appId)
  workflowRuns.value = res.data
}

const visible = ref(false)
const workflow = ref()
const tabsKey = ref('workflow')
const handleDevelop = async () => {
  tabsKey.value = 'develop'
  await nextTick()
  router.push({ path: `/appsManage/flow/${appId}/${appType}`, query: { type: 'develop' } })
}
const close = () => {
  visible.value = false
}

const tableChange = (key) => {
  tabsKey.value = key
  // router.replace({ path: '/workspace/ai/explore', query: { appId: item.id } })
  router.push({ path: `/appsManage/flow/${appId}/${appType}`, query: { type: key } })
}
const publish = async () => {
  try {
    await workflowPublish(appId)
    workflow.value?.getWorkflowData()
    Message.success('发布成功')
  } catch {
    Message.error('发布失败')
  }
}
const runFlow = () => {
  workflow.value?.showRunPanel()
}
const historyData = ref({
  page: 1,
  limit: 100,
  userid: '',
  namedonly: false
})
const historyList = ref([])
const versionhistory = (item, type) => {
  if (type && type == 'id') {
    historyData.value.userid = item
  }
  if (type && type == 'switch') {
    historyData.value.namedonly = item
  }
  getversionhistory(appId, {
    ...historyData.value
  }).then((res) => {
    console.log(res)

    historyList.value = res.items
    workflow.value?.versionHistoryPanel(res)
  })
}
const getworkflowHistory = (item) => {
  console.log(item)
  workflowhistoryRun(route.params.appId, item.id).then((res) => {
    console.log(res)
    workflow.value?.showRunHistoryPanel(res)
  })
}
const handleSelect = (v, item) => {
  if (v.value === 'publish') {
    publish()
  } else if (v.value === 'run') {
    runFlow()
  } else if (v.value === 'publishToll') {
    workflow.value?.showPublishTollPanel()
  }
}
const getAppConfigFn = async () => {
  const res = await getAppConfig({ appId: appId })

  appInfo.value = res
  // 将app信息存储到store中
  appStore.setCurrentAppInfo(res)
}
provide('rootMethods', { getAppConfigFn, versionhistory })

onMounted(() => {
  getAppConfigFn()
})
</script>

<style scoped lang="scss">
.node-history {
  width: 180px;
  min-height: 200px;
  max-height: 400px;
  overflow: hidden;
  overflow-y: auto;
}
</style>
