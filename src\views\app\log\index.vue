<template>
  <AiPageLayout>
    <AiTable
      row-key="id"
      :data="dataList || []"
      :columns="columns"
      :loading="loading"
      :scroll="{ x: '100%', y: '100%' }"
      :pagination="pagination"
      :disabled-tools="['size']"
      :disabled-column-keys="['id']"
      @row-click="handleViewDetail"
      @refresh="search"
    >
      <template #toolbar-left>
        <a-space>
          <!-- 关键字搜索 -->
          <a-input-search
            v-model="queryForm.keyword"
            placeholder="搜索关键字"
            allow-clear
            style="width: 200px"
            @search="search"
          />

          <!-- 状态筛选 - 根据应用类型显示不同选项 -->
          <a-select
            v-if="appMode === 'workflow'"
            v-model="queryForm.status"
            placeholder="状态"
            allow-clear
            style="width: 120px"
            @change="search"
          >
            <a-option value="all">全部</a-option>
            <a-option value="succeeded">成功</a-option>
            <a-option value="failed">失败</a-option>
            <a-option value="stopped">已停止</a-option>
          </a-select>

          <a-select
            v-else
            v-model="queryForm.status"
            placeholder="状态"
            allow-clear
            style="width: 120px"
            @change="search"
          >
            <a-option value="all">全部</a-option>
          </a-select>

          <!-- 日期筛选 -->
          <a-range-picker v-model="dateRange" style="width: 240px" @change="handleDateChange" />

          <!-- 重置按钮 -->
          <a-button @click="reset">
            <template #icon><icon-refresh /></template>
            重置
          </a-button>
        </a-space>
      </template>
    </AiTable>

    <!-- 日志详情弹窗 -->
    <a-modal
      v-model:visible="showDetailModal"
      title="日志详情"
      width="800px"
      :footer="false"
      @cancel="handleCloseDetail"
    >
      <LogDetail v-if="currentLog" :log="currentLog" :app-mode="appMode" @close="handleCloseDetail" />
    </a-modal>
  </AiPageLayout>
</template>

<script setup lang="ts">
import { ref, reactive, h, computed } from 'vue'
import { useRoute } from 'vue-router'
import type { TableInstance } from '@arco-design/web-vue'
import { Tag } from '@arco-design/web-vue'
import {
  getChatConversations,
  getWorkflowConversations,
  type LogEntry,
  type LogQueryParams,
  type LogStatus
} from '@/apis/apps'
import { useTable } from '@/hooks'
import { isMobile, dateFormat } from '@/utils'
import LogDetail from './components/LogDetail.vue'

defineOptions({ name: 'Logs' })

const props = withDefaults(
  defineProps<{
    appInfo: {
      mode?: string
      id?: string
    }
  }>(),
  {}
)

const route = useRoute()
const appId = computed(() => props.appInfo?.id || (route.params.appId as string))
const appMode = computed(() => props.appInfo?.mode || 'workflow')

// 查询表单
const queryForm = reactive<LogQueryParams>({
  status: 'all'
})

// 日期范围
const dateRange = ref<[string, string] | undefined>()

// 当前日志详情
const currentLog = ref<LogEntry | null>(null)
const showDetailModal = ref(false)

// 根据应用类型选择API
const getLogsAPI = (params: LogQueryParams) => {
  const apiParams = {
    ...params,
    ...(params.status === 'all' ? {} : { status: params.status })
  }

  switch (appMode.value) {
    case 'workflow':
      return getWorkflowConversations(appId.value, apiParams)
    case 'advanced-chat':
    case 'agent-chat':
    default:
      return getChatConversations(appId.value, apiParams)
  }
}

// 表格数据
const {
  tableData: dataList,
  loading,
  pagination,
  search
} = useTable<LogEntry>(
  (page) => {
    const pageInfo = {
      page: page.current,
      limit: page.size
    }
    const params = {
      ...queryForm,
      ...pageInfo
    }
    if (params.status === 'all') {
      delete params.status
    }
    return getLogsAPI(params)
  },
  { immediate: true }
)

// 状态颜色映射
const getStatusColor = (status: LogStatus): string => {
  const colorMap = {
    success: 'green',
    succeeded: 'green',
    error: 'red',
    failed: 'red',
    stopped: 'orange',
    running: 'blue'
  }
  return colorMap[status] || 'gray'
}

// 状态文本映射
const getStatusText = (status: LogStatus): string => {
  const textMap = {
    success: '成功',
    succeeded: '成功',
    error: '错误',
    failed: '失败',
    stopped: '已停止',
    running: '运行中'
  }
  return textMap[status] || status
}

// 表格列定义 - 根据应用类型动态生成
const columns = computed<TableInstance['columns']>(() => {
  const baseColumns: TableInstance['columns'] = [
    {
      title: '开始时间',
      dataIndex: 'created_at',
      width: 160,
      render: ({ record }) => {
        const timestamp = record.created_at
        if (!timestamp) return '-'
        // 处理时间戳（秒或毫秒）
        const date =
          typeof timestamp === 'number'
            ? new Date(timestamp < *********** ? timestamp * 1000 : timestamp)
            : new Date(timestamp)
        return h('span', {}, dateFormat(date, 'yyyy-MM-dd HH:mm'))
      }
    },
    {
      title: '用户',
      dataIndex: 'account',
      width: 100,
      render: ({ record }) => {
        const user = record.created_by_account?.name || '-'
        return h('span', { title: user }, user.length > 10 ? `${user.substring(0, 10)}...` : user)
      }
    }
  ]

  // 根据应用类型添加不同的列
  if (appMode.value === 'workflow') {
    // 工作流模式的列
    baseColumns.push(
      {
        title: '结束时间',
        dataIndex: 'finished_at',
        width: 80,
        render: ({ record }) => {
          const timestamp = record.workflow_run.finished_at
          if (!timestamp) return '-'
          const date =
            typeof timestamp === 'number'
              ? new Date(timestamp < *********** ? timestamp * 1000 : timestamp)
              : new Date(timestamp)
          return h('span', {}, dateFormat(date, 'yyyy-MM-dd HH:mm'))
        }
      },
      {
        title: '状态',
        dataIndex: 'status',
        width: 80,
        render: ({ record }) =>
          h(
            Tag,
            {
              color: getStatusColor(record.workflow_run?.status)
            },
            () => getStatusText(record.workflow_run?.status)
          )
      },
      {
        title: '运行时间',
        dataIndex: 'elapsed_time',
        width: 80,
        render: ({ record }) => h('span', {}, `${(record.workflow_run?.elapsed_time || 0).toFixed(3)}s`)
      },
      {
        title: 'Token数',
        dataIndex: 'total_tokens',
        width: 60,
        render: ({ record }) => h('span', {}, record.workflow_run?.total_tokens || '-')
      }
    )
  } else {
    // 对话流/智能会话模式的列
    baseColumns.push(
      {
        title: '会话主题',
        dataIndex: 'name',
        width: 150,
        ellipsis: true,
        render: ({ record }) => {
          const name = record.name || '-'
          return h('span', { title: name }, name.length > 30 ? `${name.substring(0, 30)}...` : name)
        }
      },
      {
        title: '消息数',
        dataIndex: 'message_count',
        width: 100,
        render: ({ record }) => h('span', {}, record.message_count || '-')
      },
      {
        title: '用户反馈',
        dataIndex: 'user_feedback_stats',
        width: 120,
        render: ({ record }) => {
          const feedback = record.user_feedback_stats
          if (!feedback || (!feedback.like && !feedback.dislike)) {
            return h('span', {}, '-')
          }
          return h(
            'a-space',
            {},
            [
              feedback.like ? h('span', { style: 'color: #00b42a' }, `👍 ${feedback.like}`) : null,
              feedback.dislike ? h('span', { style: 'color: #f53f3f' }, `👎 ${feedback.dislike}`) : null
            ].filter(Boolean)
          )
        }
      },
      {
        title: '操作',
        width: 100,
        fixed: !isMobile() ? 'right' : undefined,
        render: ({ record }) =>
          h('a-space', {}, [
            h(
              'a-button',
              {
                type: 'text',
                size: 'small',
                onClick: () => handleViewDetail(record)
              },
              () => '详情'
            )
          ])
      }
    )
  }

  // 添加操作列
  // baseColumns.push({
  //   title: '操作',
  //   width: 100,
  //   fixed: !isMobile() ? 'right' : undefined,
  //   render: ({ record }) => h('a-space', {}, [
  //     h('a-button', {
  //       type: 'text',
  //       size: 'small',
  //       onClick: () => handleViewDetail(record)
  //     }, () => '详情')
  //   ])
  // })

  return baseColumns
})

// 查看详情
const handleViewDetail = (log: LogEntry) => {
  currentLog.value = log
  showDetailModal.value = true
}

// 关闭详情
const handleCloseDetail = () => {
  showDetailModal.value = false
  currentLog.value = null
}

// 处理日期变化
const handleDateChange = (dates: [string, string] | undefined) => {
  if (dates && dates.length === 2) {
    queryForm.created_at__after = dates[0]
    queryForm.created_at__before = dates[1]
  } else {
    delete queryForm.created_at__after
    delete queryForm.created_at__before
  }
  search()
}

// 重置筛选
const reset = () => {
  queryForm.keyword = ''
  queryForm.status = 'all'
  delete queryForm.created_at__after
  delete queryForm.created_at__before
  dateRange.value = undefined
  search()
}
</script>

<style scoped lang="scss">
.ai-table {
  :deep(.arco-table-cell) {
    .arco-tag {
      font-size: 12px;
      font-weight: 500;
    }
  }
}
</style>
