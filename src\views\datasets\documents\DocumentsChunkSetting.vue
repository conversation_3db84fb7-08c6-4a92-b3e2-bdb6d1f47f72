<template>
  <div class="chunk-setting-container">
    <div class="chunk-setting-header">
      <a-button class="back-btn" @click="goBack">
        <template #icon><icon-left /></template>
        返回
      </a-button>
      <div class="header-title">文档分段设置</div>
    </div>

    <div class="chunk-setting-content">
      <!-- 引入StepSecond作为子组件，添加ref引用 -->
      <step-second
        ref="stepSecondRef"
        v-model:selectedPreviewFileId="selectedPreviewFileId"
        :uploadedFiles="uploadedFiles"
        :segmentPreview="segmentPreview"
        :totalSegments="totalSegments"
        :previewLoading="previewLoading"
        :isAddingDocument="true"
        :isFrom="'DocumentsChunkSetting'"
        :datasetId="datasetId"
        @prev-step="goBack"
        @next-step="saveSettingsAndGoToDetail"
        @preview-blocks="handlePreviewBlocks"
        @prev-fetch-dataset-detail="handleFetchDatasetDetail"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import {
  getDatasetDetail,
  previewDocumentSegments,
  getFileContent,
  getDocumentDetail,
  addDocumentsToDataset,
  getDatasetIndexingStatus
} from '@/apis/datasets'
import StepSecond from '@/views/datasets/create/StepSecond.vue'

// 路由相关
const router = useRouter()
const route = useRoute()
const datasetId = ref('')

// StepSecond组件引用
const stepSecondRef = ref<{
  getSettings: () => any
  [key: string]: any
} | null>(null)

// 文档列表
const uploadedFiles = ref<any[]>([])
const selectedPreviewFileId = ref('')
const previewLoading = ref(false)
const segmentPreview = ref<any[]>([])
const totalSegments = ref(0)
const datasetDetail = ref<any>({})

// 定义处理状态变量
const processingStatus = ref({
  loading: false,
  percent: 0,
  completed: false,
  error: false,
  errorMessage: '',
  datasetId: '',
  batchId: ''
})

// 定时检查器
let processingStatusChecker: ReturnType<typeof setInterval> | null = null

// 组件挂载时初始化
onMounted(async () => {
  // 从路由参数获取datasetId
  datasetId.value = route.params.datasetId as string
  if (!datasetId.value) {
    Message.error('未能获取知识库ID')
    return
  }

  // 获取知识库详情
  await fetchDatasetDetail()

  // 从查询参数获取documentId
  const documentId = route.query.documentId as string
  if (documentId) {
    console.log('从查询参数获取到文档ID:', documentId)

    // 尝试获取文档信息并加载到文档列表中
    try {
      const docDetail = await getDocumentDetail(datasetId.value, documentId, { metadata: 'without' })
      if (docDetail) {
        // 将文档信息添加到上传文件列表中，用于预览
        uploadedFiles.value = [
          {
            uid: documentId,
            name: docDetail.name || '文档',
            size: docDetail.data_source_info?.upload_file?.size || 0,
            response: {
              id: documentId
            }
          }
        ]

        // 设置选中的预览文件ID
        selectedPreviewFileId.value = documentId
      }
    } catch (error) {
      console.error('获取文档详情失败:', error)
      Message.error('获取文档详情失败')

      // 使用默认文档信息
      uploadedFiles.value = [
        {
          uid: documentId,
          name: '当前文档',
          size: 0,
          response: {
            id: documentId
          }
        }
      ]
      selectedPreviewFileId.value = documentId
    }
  } else {
    // 没有文档ID，显示默认示例数据
    uploadedFiles.value = [
      {
        uid: '1',
        name: '示例文档.pdf',
        size: 1024 * 1024,
        response: {
          id: 'doc-001'
        }
      }
    ]

    if (uploadedFiles.value.length > 0) {
      selectedPreviewFileId.value = uploadedFiles.value[0].response?.id || ''
    }
  }
})

// 获取知识库详情
const fetchDatasetDetail = async () => {
  try {
    const response = await getDatasetDetail(datasetId.value)
    datasetDetail.value = response
    console.log('获取知识库详情成功:', response)
  } catch (error) {
    console.error('获取知识库详情失败:', error)
    Message.error('获取知识库详情失败')
  }
}

// 处理预览块
const handlePreviewBlocks = async (params: any) => {
  try {
    previewLoading.value = true

    // 如果选中的预览文件ID是文档ID，确保添加dataset_id参数
    const documentId = route.query.documentId as string
    if (documentId && params.info_list?.file_info_list?.file_ids?.[0] === documentId && !params.dataset_id) {
      params.dataset_id = datasetId.value
    }

    const response = await previewDocumentSegments(params)
    segmentPreview.value = response.preview
    totalSegments.value = response.total_segments
    return response
  } catch (error) {
    console.error('获取分段预览失败:', error)
    // Message.error('获取分段预览失败');
    throw error
  } finally {
    previewLoading.value = false
  }
}

// 处理获取知识库详情事件
const handleFetchDatasetDetail = async (id: string) => {
  try {
    const response = await getDatasetDetail(id)
    datasetDetail.value = response
    console.log('获取知识库详情成功:', response)
  } catch (error) {
    console.error('获取知识库详情失败:', error)
    Message.error('获取知识库详情失败')
  }
}

// 返回上一页
const goBack = () => {
  router.push({
    name: 'documents',
    params: { datasetId: datasetId.value }
  })
}

// 保存设置,并跳转到分块详情页面
const saveSettingsAndGoToDetail = async () => {
  try {
    // 从StepSecond组件中获取设置值
    const settings = stepSecondRef.value?.getSettings()

    if (!settings) {
      Message.warning('无法获取设置信息')
      return
    }

    console.log('获取到的设置信息:', settings)

    // 构建处理规则
    const buildProcessRule = () => {
      let processRule: any = {
        rules: {
          pre_processing_rules: [
            {
              id: 'remove_extra_spaces',
              enabled: settings.processingRules.removeSpaces
            },
            {
              id: 'remove_urls_emails',
              enabled: settings.processingRules.removeUrls
            }
          ]
        },
        mode: settings.segmentMode === 'normal' ? 'custom' : 'hierarchical'
      }

      if (settings.segmentMode === 'normal') {
        processRule.rules.segmentation = {
          separator: settings.normalSegmentConfig.separator,
          max_tokens: settings.normalSegmentConfig.maxLength,
          chunk_overlap: settings.normalSegmentConfig.minLength
        }
      } else {
        // 父子分段模式
        processRule.rules.segmentation = {
          separator: settings.parentChildConfig.parent.separator,
          max_tokens: settings.parentChildConfig.parent.maxLength
        }
        processRule.rules.parent_mode = settings.parentMode === 'fulltext' ? 'full-doc' : 'paragraph'
        processRule.rules.subchunk_segmentation = {
          separator: settings.parentChildConfig.child.separator,
          max_tokens: settings.parentChildConfig.child.maxLength
        }
      }

      return processRule
    }

    // 文档ID，从路由或其他地方获取
    const documentId = route.query.documentId as string

    if (!documentId) {
      Message.warning('未能获取文档ID')
      return
    }

    // 获取文档语言（可能需要根据实际情况调整）
    const docLanguage = 'Chinese Simplified'

    // 获取embedding模型，使用默认值
    const embeddingModel = ''
    const embeddingProviderName = ''

    // 构建retrieval_model参数
    const buildRetrievalModel = () => {
      return {
        search_method: 'semantic_search',
        reranking_enable: false,
        reranking_mode: '',
        top_k: 6,
        score_threshold: 0.5,
        score_threshold_enable: false
      }
    }

    // 构建API参数
    const requestParams = {
      original_document_id: documentId,
      doc_form: 'text_model',
      doc_language: 'Chinese Simplified',
      data_source: {
        type: 'document',
        info_list: {
          data_source_type: 'upload_file',
          file_info_list: {
            file_ids: [documentId]
          }
        }
      },
      process_rule: {
        rules: {
          pre_processing_rules: [
            {
              id: 'remove_extra_spaces',
              enabled: true
            },
            {
              id: 'remove_urls_emails',
              enabled: false
            }
          ],
          segmentation: {
            separator: '\\n\\n',
            max_tokens: 1024,
            chunk_overlap: 50
          }
        },
        mode: 'custom' as 'custom' | 'hierarchical'
      },
      retrieval_model: {
        search_method: 'semantic_search',
        reranking_enable: false,
        reranking_mode: '',
        reranking_model: {
          reranking_provider_name: '',
          reranking_model_name: ''
        },
        weights: undefined,
        top_k: 2,
        score_threshold_enabled: false,
        score_threshold: 0.5
      },
      embedding_model: '',
      embedding_model_provider: '',
      indexing_technique: 'economy'
    }

    console.log('重新处理文档参数:', JSON.stringify(requestParams))

    // 调用API重新处理文档
    const response = await addDocumentsToDataset(datasetId.value, requestParams)

    console.log('重新处理文档响应:', response)

    // 保存批处理ID到本地存储，以便在详情页面使用
    if (response && response.batch) {
      localStorage.setItem(
        `document_processing_${datasetId.value}_${documentId}`,
        JSON.stringify({
          batchId: response.batch,
          timestamp: Date.now()
        })
      )

      // 更新处理状态信息
      processingStatus.value.datasetId = datasetId.value
      processingStatus.value.batchId = response.batch
      processingStatus.value.loading = true
      processingStatus.value.percent = 0
      processingStatus.value.completed = false
      processingStatus.value.error = false
      processingStatus.value.errorMessage = ''

      // 开始检查处理状态
      checkIndexingStatus(datasetId.value, response.batch)
      startProcessingStatusChecker()
    }

    Message.success('设置已保存，文档正在重新处理中...')

    // 跳转到文档详情页面
    router.push({
      name: 'documentsChunkDetail',
      params: {
        datasetId: datasetId.value,
        documentId: documentId
      }
    })
  } catch (error) {
    console.error('保存设置失败:', error)
    Message.error('保存设置失败')
  }
}

// 检查索引状态
const checkIndexingStatus = async (datasetId: string, batchId: string) => {
  try {
    const response = await getDatasetIndexingStatus(datasetId, batchId)
    if (response.data && response.data.length > 0) {
      const status = response.data[0]
      // 计算进度百分比
      if (status.total_segments > 0) {
        processingStatus.value.percent = Math.floor((status.completed_segments / status.total_segments) * 100)
      }
      // 检查是否完成
      if (status.completed_at) {
        processingStatus.value.completed = true
        processingStatus.value.loading = false
        stopProcessingStatusChecker()
      } else if (status.error) {
        processingStatus.value.error = true
        processingStatus.value.errorMessage = status.error
        processingStatus.value.loading = false
        stopProcessingStatusChecker()
      }
    }
  } catch (error) {
    console.error('获取索引状态失败:', error)
  }
}

// 开始定时检查处理状态
const startProcessingStatusChecker = () => {
  // 清除可能存在的旧定时器
  stopProcessingStatusChecker()
  // 创建新的定时器，每5秒检查一次
  processingStatusChecker = setInterval(() => {
    if (processingStatus.value.datasetId && processingStatus.value.batchId) {
      checkIndexingStatus(processingStatus.value.datasetId, processingStatus.value.batchId)
    }
  }, 5000)
}

// 停止定时检查
const stopProcessingStatusChecker = () => {
  if (processingStatusChecker) {
    clearInterval(processingStatusChecker)
    processingStatusChecker = null
  }
}

// 组件卸载时清除定时器
onUnmounted(() => {
  stopProcessingStatusChecker()
})
</script>

<style scoped lang="scss">
.chunk-setting-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .chunk-setting-header {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid var(--color-border-2);

    .back-btn {
      margin-right: 16px;
    }

    .header-title {
      font-size: 18px;
      font-weight: 500;
    }
  }

  .chunk-setting-content {
    flex: 1;
    padding: 16px;
    overflow: auto;
  }
}
</style>
